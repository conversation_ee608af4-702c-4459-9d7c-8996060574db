{"ast": null, "code": "import * as React from 'react';\nimport { useLocalizationContext } from '../useUtils';\nexport function useValidation(props, validate, isSameError) {\n  const {\n    value,\n    onError\n  } = props;\n  const adapter = useLocalizationContext();\n  const previousValidationErrorRef = React.useRef(null);\n  const validationError = validate({\n    adapter,\n    value,\n    props\n  });\n  React.useEffect(() => {\n    if (onError && !isSameError(validationError, previousValidationErrorRef.current)) {\n      onError(validationError, value);\n    }\n    previousValidationErrorRef.current = validationError;\n  }, [isSameError, onError, previousValidationErrorRef, validationError, value]);\n  return validationError;\n}", "map": {"version": 3, "names": ["React", "useLocalizationContext", "useValidation", "props", "validate", "isSameError", "value", "onError", "adapter", "previousValidationErrorRef", "useRef", "validationError", "useEffect", "current"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/hooks/validation/useValidation.js"], "sourcesContent": ["import * as React from 'react';\nimport { useLocalizationContext } from '../useUtils';\nexport function useValidation(props, validate, isSameError) {\n  const {\n    value,\n    onError\n  } = props;\n  const adapter = useLocalizationContext();\n  const previousValidationErrorRef = React.useRef(null);\n  const validationError = validate({\n    adapter,\n    value,\n    props\n  });\n  React.useEffect(() => {\n    if (onError && !isSameError(validationError, previousValidationErrorRef.current)) {\n      onError(validationError, value);\n    }\n\n    previousValidationErrorRef.current = validationError;\n  }, [isSameError, onError, previousValidationErrorRef, validationError, value]);\n  return validationError;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,QAAQ,aAAa;AACpD,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EAC1D,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,OAAO,GAAGP,sBAAsB,CAAC,CAAC;EACxC,MAAMQ,0BAA0B,GAAGT,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EACrD,MAAMC,eAAe,GAAGP,QAAQ,CAAC;IAC/BI,OAAO;IACPF,KAAK;IACLH;EACF,CAAC,CAAC;EACFH,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,IAAIL,OAAO,IAAI,CAACF,WAAW,CAACM,eAAe,EAAEF,0BAA0B,CAACI,OAAO,CAAC,EAAE;MAChFN,OAAO,CAACI,eAAe,EAAEL,KAAK,CAAC;IACjC;IAEAG,0BAA0B,CAACI,OAAO,GAAGF,eAAe;EACtD,CAAC,EAAE,CAACN,WAAW,EAAEE,OAAO,EAAEE,0BAA0B,EAAEE,eAAe,EAAEL,KAAK,CAAC,CAAC;EAC9E,OAAOK,eAAe;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}