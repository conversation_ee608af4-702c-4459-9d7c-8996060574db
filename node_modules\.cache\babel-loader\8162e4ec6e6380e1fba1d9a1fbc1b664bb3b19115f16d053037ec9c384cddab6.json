{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getMenuItemUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuItem', slot);\n}\nexport const menuItemClasses = generateUtilityClasses('MuiMenuItem', ['root', 'disabled', 'focusVisible']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMenuItemUtilityClass", "slot", "menuItemClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/MenuItem/menuItemClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getMenuItemUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuItem', slot);\n}\nexport const menuItemClasses = generateUtilityClasses('MuiMenuItem', ['root', 'disabled', 'focusVisible']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,OAAO,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}