{"ast": null, "code": "/**\n * Extracts event handlers from a given object.\n * A prop is considered an event handler if it is a function and its name starts with `on`.\n *\n * @param object An object to extract event handlers from.\n * @param excludeKeys An array of keys to exclude from the returned object.\n */\nexport function extractEventHandlers(object) {\n  let excludeKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => prop.match(/^on[A-Z]/) && typeof object[prop] === 'function' && !excludeKeys.includes(prop)).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}", "map": {"version": 3, "names": ["extractEventHandlers", "object", "excludeKeys", "arguments", "length", "undefined", "result", "Object", "keys", "filter", "prop", "match", "includes", "for<PERSON>ach"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/utils/extractEventHandlers.js"], "sourcesContent": ["/**\n * Extracts event handlers from a given object.\n * A prop is considered an event handler if it is a function and its name starts with `on`.\n *\n * @param object An object to extract event handlers from.\n * @param excludeKeys An array of keys to exclude from the returned object.\n */\nexport function extractEventHandlers(object, excludeKeys = []) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => prop.match(/^on[A-Z]/) && typeof object[prop] === 'function' && !excludeKeys.includes(prop)).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,oBAAoBA,CAACC,MAAM,EAAoB;EAAA,IAAlBC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAC3D,IAAIF,MAAM,KAAKI,SAAS,EAAE;IACxB,OAAO,CAAC,CAAC;EACX;EACA,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBC,MAAM,CAACC,IAAI,CAACP,MAAM,CAAC,CAACQ,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,UAAU,CAAC,IAAI,OAAOV,MAAM,CAACS,IAAI,CAAC,KAAK,UAAU,IAAI,CAACR,WAAW,CAACU,QAAQ,CAACF,IAAI,CAAC,CAAC,CAACG,OAAO,CAACH,IAAI,IAAI;IAC9IJ,MAAM,CAACI,IAAI,CAAC,GAAGT,MAAM,CAACS,IAAI,CAAC;EAC7B,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}