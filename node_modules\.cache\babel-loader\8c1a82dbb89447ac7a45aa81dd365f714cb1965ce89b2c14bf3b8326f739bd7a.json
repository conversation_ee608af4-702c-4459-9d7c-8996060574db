{"ast": null, "code": "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "map": {"version": 3, "names": ["getUAString", "isLayoutViewport", "test"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js"], "sourcesContent": ["import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,uBAAuB;AAC/C,eAAe,SAASC,gBAAgBA,CAAA,EAAG;EACzC,OAAO,CAAC,gCAAgC,CAACC,IAAI,CAACF,WAAW,CAAC,CAAC,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}