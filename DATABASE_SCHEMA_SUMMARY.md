# Internal Complaints Portal - Complete Database Schema

## 📋 **OVERVIEW**
This document provides a complete list of all tables, triggers, stored procedures, and views required for the Internal Complaints Portal to function properly.

## 🗃️ **CORE TABLES**

### 1. **Department**
- **Purpose**: Core department master data
- **Key Fields**: DeptID (PK), DeptName, DeptPosition, active
- **Usage**: Referenced by Employee table, provides department information

### 2. **Employee** 
- **Purpose**: Core employee master data
- **Key Fields**: EmpId (PK), EmpCode, EmpName, DeptId (FK), Password
- **Usage**: Main employee data source, synced to Complaints_Employee

### 3. **Complaints_Employee**
- **Purpose**: Simplified employee data for complaints system
- **Key Fields**: EmpID (PK), EmpCode, EmpName, Password, DeptID, DeptName
- **Usage**: Authentication and employee lookup for complaints system

### 4. **Complaints**
- **Purpose**: Main complaints data storage
- **Key Fields**: ComplaintId (PK), ComplaintNumber, Title, Description, SubmittedByEmpCode, StatusId, Priority
- **Usage**: Core complaint information and tracking

### 5. **ComplaintAssignments**
- **Purpose**: Track complaint assignments to employees
- **Key Fields**: AssignmentId (PK), ComplaintId (FK), AssignedToEmpCode, AssignedByEmpCode, AssignmentDate
- **Usage**: Assignment history and current assignee tracking

### 6. **ComplaintStatusHistory**
- **Purpose**: Log all status changes for complaints
- **Key Fields**: HistoryId (PK), ComplaintId (FK), OldStatusId, NewStatusId, ChangedByEmpCode, ChangeDate
- **Usage**: Audit trail for status changes

### 7. **ComplaintAttachments**
- **Purpose**: Store file attachment information
- **Key Fields**: AttachmentId (PK), ComplaintId (FK), FileName, FilePath, UploadedByEmpCode
- **Usage**: File attachment management

### 8. **AuthorizedDepartments**
- **Purpose**: Define which departments can handle complaints
- **Key Fields**: Id (PK), DeptName, IsActive
- **Usage**: Department authorization control

### 9. **EmployeePermissions**
- **Purpose**: Define user permissions for complaints system
- **Key Fields**: Id (PK), EmpCode, CanAssign, CanUpdateStatus, CanViewDashboard
- **Usage**: Role-based access control

## ⚡ **TRIGGERS**

### 1. **TR_Employee_Insert**
- **Table**: Employee
- **Type**: AFTER INSERT
- **Purpose**: Automatically create Complaints_Employee record when new employee is added
- **Action**: Inserts EmpID, EmpCode, EmpName, DeptID, DeptName with default password '1234'

### 2. **TR_Employee_AfterUpdate**
- **Table**: Employee  
- **Type**: AFTER UPDATE
- **Purpose**: Sync employee updates to Complaints_Employee table
- **Action**: Calls SyncEmployeesToComplaints stored procedure

### 3. **TR_Complaints_UpdateDate**
- **Table**: Complaints
- **Type**: AFTER UPDATE
- **Purpose**: Automatically update LastUpdateDate when complaint is modified
- **Action**: Sets LastUpdateDate = GETDATE()

### 4. **TR_Complaints_StatusChange**
- **Table**: Complaints
- **Type**: AFTER UPDATE
- **Purpose**: Log status changes in ComplaintStatusHistory
- **Action**: Inserts record when StatusId changes

## 🔧 **STORED PROCEDURES**

### 1. **SyncEmployeesToComplaints**
- **Purpose**: Manually sync Employee data to Complaints_Employee
- **Parameters**: None
- **Action**: Updates existing records and inserts new employees

### 2. **GenerateComplaintNumber**
- **Purpose**: Generate unique complaint numbers
- **Parameters**: @ComplaintNumber OUTPUT
- **Format**: COMP-YYYYMM-NNNN (e.g., COMP-202412-0001)

## 📊 **VIEWS**

### 1. **vw_ComplaintSummary**
- **Purpose**: Comprehensive complaint view with employee details
- **Includes**: Complaint info, submitter details, assignee details, status names
- **Usage**: Reporting and dashboard queries

## 🔗 **KEY RELATIONSHIPS**

```
Department (DeptID) ←→ Employee (DeptId)
Employee (EmpId) ←→ Complaints_Employee (EmpID)
Complaints_Employee (EmpCode) ←→ Complaints (SubmittedByEmpCode)
Complaints (ComplaintId) ←→ ComplaintAssignments (ComplaintId)
Complaints (ComplaintId) ←→ ComplaintStatusHistory (ComplaintId)
Complaints (ComplaintId) ←→ ComplaintAttachments (ComplaintId)
```

## 📈 **INDEXES FOR PERFORMANCE**

- **IX_Complaints_SubmittedBy**: On Complaints(SubmittedByEmpCode)
- **IX_Complaints_Status**: On Complaints(StatusId)
- **IX_Complaints_SubmissionDate**: On Complaints(SubmissionDate)
- **IX_ComplaintAssignments_AssignedTo**: On ComplaintAssignments(AssignedToEmpCode)
- **IX_ComplaintsEmployee_EmpCode**: On Complaints_Employee(EmpCode)

## 🎯 **STATUS CODES**

- **1**: New
- **2**: Assigned  
- **3**: In Progress
- **4**: Resolved
- **5**: Rejected

## 🔐 **SECURITY FEATURES**

- **Role-based Access**: EmployeePermissions table controls user capabilities
- **Department Authorization**: AuthorizedDepartments controls which departments can handle complaints
- **Audit Trail**: ComplaintStatusHistory logs all status changes
- **Data Isolation**: Users can only see complaints they submitted or are assigned to (unless admin)

## 🚀 **DEPLOYMENT NOTES**

1. **Run the complete schema script** (`COMPLETE_DATABASE_SCHEMA.sql`) on your SQL Server
2. **Verify all triggers are enabled** and functioning
3. **Check that Employee and Department tables have data** before using the portal
4. **Set up initial admin permissions** in EmployeePermissions table
5. **Configure authorized departments** in AuthorizedDepartments table

## ✅ **VERIFICATION CHECKLIST**

- [ ] All 9 core tables created
- [ ] All 4 triggers created and enabled
- [ ] Both stored procedures created
- [ ] View created successfully
- [ ] Indexes created for performance
- [ ] Initial data inserted (authorized departments, admin permissions)
- [ ] Foreign key relationships established
- [ ] Employee data synced to Complaints_Employee table

## 📞 **SUPPORT**

This schema supports all features of the Internal Complaints Portal including:
- ✅ User authentication and authorization
- ✅ Complaint submission and tracking
- ✅ Assignment management
- ✅ Status tracking and history
- ✅ File attachments
- ✅ Dashboard and reporting
- ✅ Excel export functionality
- ✅ Real-time updates via Socket.IO
- ✅ Mobile and desktop responsive design
- ✅ Date range filtering
- ✅ Role-based permissions

The database is now ready for production use! 🎉
