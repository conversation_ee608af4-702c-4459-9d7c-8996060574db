import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from '../utils/axiosConfig';

const AuthContext = createContext(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('token');
        console.log('Initializing auth with token:', token ? 'exists' : 'none');
        
        if (!token) {
          setUser(null);
          setLoading(false);
          setInitialized(true);
          return;
        }

        // Set token in axios defaults
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        
        // Verify token and get user data
        console.log('Verifying token...');
        const response = await axios.get('/api/auth/me');
        console.log('Verification response:', response.data);
        
        if (response.data?.user) {
          // Ensure admin users always have dashboard permission
          const userData = response.data.user;
          if (userData.isAdmin) {
            if (!userData.permissions) userData.permissions = {};
            userData.permissions.canViewDashboard = true;
          }

          setUser({
            empCode: userData.empCode,
            name: userData.name,
            department: userData.department,
            isAdmin: userData.isAdmin,
            permissions: userData.permissions
          });
          console.log('User state set:', userData);
        } else {
          console.log('No user data in response, clearing auth state');
          localStorage.removeItem('token');
          delete axios.defaults.headers.common['Authorization'];
          setUser(null);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        localStorage.removeItem('token');
        delete axios.defaults.headers.common['Authorization'];
        setUser(null);
      } finally {
        setLoading(false);
        setInitialized(true);
      }
    };

    initializeAuth();
  }, []);

  const login = async (empCode, password) => {
    try {
      setLoading(true);
      console.log('AuthContext: Starting login process');

      const response = await axios.post('/api/auth/login', {
        empCode: empCode.trim(),
        password: password.trim()
      });

      console.log('AuthContext: Login response received', response.data);

      const { token, user: userData } = response.data;
      if (!token || !userData) {
        throw new Error('Invalid response from server');
      }

      // Store token
      localStorage.setItem('token', token);
      console.log('AuthContext: Token stored');

      // Set token in axios headers
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      // Ensure admin users always have dashboard permission
      if (userData.isAdmin) {
        if (!userData.permissions) userData.permissions = {};
        userData.permissions.canViewDashboard = true;
      }

      // Update user state with proper structure
      const userState = {
        empCode: userData.empCode,
        name: userData.empName,
        department: userData.deptName,
        isAdmin: userData.isAdmin,
        permissions: userData.permissions
      };

      setUser(userState);
      console.log('AuthContext: User state updated', userState);

      return { success: true };
    } catch (error) {
      console.error('AuthContext: Login error:', error);

      // Clear any partial state
      localStorage.removeItem('token');
      delete axios.defaults.headers.common['Authorization'];
      setUser(null);

      // Better error messages for mobile
      let errorMessage = 'Login failed';

      if (error.code === 'NETWORK_ERROR' || !error.response) {
        errorMessage = 'Network connection error. Please check your internet connection and try again.';
      } else if (error.response?.status === 401) {
        errorMessage = error.response.data?.message || 'Invalid employee code or password';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again in a few moments.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Call logout endpoint to clean up server session
      await axios.post('/api/auth/logout');
    } catch (error) {
      console.error('Error during logout:', error);
      // Continue with local cleanup even if server call fails
    }

    // Clear token
    localStorage.removeItem('token');
    delete axios.defaults.headers.common['Authorization'];

    // Clear user state
    setUser(null);
  };

  const refreshUserPermissions = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      console.log('Refreshing user permissions...');
      const response = await axios.get('/api/auth/me');
      console.log('Permission refresh response:', response.data);

      if (response.data?.user) {
        const userData = response.data.user;
        if (userData.isAdmin) {
          if (!userData.permissions) userData.permissions = {};
          userData.permissions.canViewDashboard = true;
        }

        setUser({
          empCode: userData.empCode,
          name: userData.name,
          department: userData.department,
          isAdmin: userData.isAdmin,
          permissions: userData.permissions
        });
        console.log('User permissions refreshed:', userData);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error refreshing user permissions:', error);
      return false;
    }
  };

  const value = {
    user,
    loading,
    initialized,
    login,
    logout,
    refreshUserPermissions
  };

  // Don't render children until auth is initialized
  if (!initialized) {
    return null;
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext; 