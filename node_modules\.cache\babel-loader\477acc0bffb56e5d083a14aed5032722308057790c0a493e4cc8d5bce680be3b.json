{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, CssBaseline } from '@mui/material';\nimport { useAuth } from './contexts/AuthContext';\nimport { SocketProvider } from './contexts/SocketContext';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\nimport theme from './theme';\n\n// Pages\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport ComplaintsList from './pages/ComplaintsList';\nimport ComplaintDetails from './pages/ComplaintDetails';\nimport AuthorityManagement from './pages/AuthorityManagement';\nimport ChangePassword from './pages/ChangePassword';\nimport Layout from './components/Layout';\nimport NewComplaint from './pages/NewComplaint';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  var _user$permissions;\n  const {\n    user,\n    loading,\n    initialized\n  } = useAuth();\n\n  // Show nothing while auth is initializing\n  if (!initialized) {\n    return null;\n  }\n\n  // Show loading state\n  if (loading) {\n    return null; // Or a loading spinner\n  }\n\n  // Check if user should see dashboard\n  const canViewDashboard = (user === null || user === void 0 ? void 0 : user.isAdmin) || (user === null || user === void 0 ? void 0 : (_user$permissions = user.permissions) === null || _user$permissions === void 0 ? void 0 : _user$permissions.canViewDashboard) === true;\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n      dateAdapter: AdapterDateFns,\n      children: /*#__PURE__*/_jsxDEV(SocketProvider, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n              to: canViewDashboard ? \"/dashboard\" : \"/complaints\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 29\n            }, this) : /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 105\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 11\n          }, this), user ? /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 38\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              index: true,\n              element: canViewDashboard ? /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/dashboard\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/complaints\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"dashboard\",\n              element: canViewDashboard ? /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/complaints\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"complaints\",\n              element: /*#__PURE__*/_jsxDEV(ComplaintsList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"complaints/new\",\n              element: /*#__PURE__*/_jsxDEV(NewComplaint, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"complaints/:id\",\n              element: /*#__PURE__*/_jsxDEV(ComplaintDetails, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"authority-management\",\n              element: user.isAdmin ? /*#__PURE__*/_jsxDEV(AuthorityManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/complaints\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"change-password\",\n              element: /*#__PURE__*/_jsxDEV(ChangePassword, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this) :\n          /*#__PURE__*/\n          // If not authenticated, redirect to login\n          _jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/login\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: user ? canViewDashboard ? \"/dashboard\" : \"/complaints\" : \"/login\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"scqRZSipSuJhXLYwgHoJq/UgQQ8=\", false, function () {\n  return [useAuth];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "ThemeProvider", "CssBaseline", "useAuth", "SocketProvider", "LocalizationProvider", "AdapterDateFns", "theme", "<PERSON><PERSON>", "Dashboard", "ComplaintsList", "ComplaintDetails", "AuthorityManagement", "ChangePassword", "Layout", "NewComplaint", "jsxDEV", "_jsxDEV", "App", "_s", "_user$permissions", "user", "loading", "initialized", "canViewDashboard", "isAdmin", "permissions", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dateAdapter", "path", "element", "to", "replace", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { Routes, Route, Navigate } from 'react-router-dom';\r\nimport { ThemeProvider, CssBaseline } from '@mui/material';\r\nimport { useAuth } from './contexts/AuthContext';\r\nimport { SocketProvider } from './contexts/SocketContext';\r\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\r\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\r\nimport theme from './theme';\r\n\r\n// Pages\r\nimport Login from './pages/Login';\r\nimport Dashboard from './pages/Dashboard';\r\nimport ComplaintsList from './pages/ComplaintsList';\r\nimport ComplaintDetails from './pages/ComplaintDetails';\r\nimport AuthorityManagement from './pages/AuthorityManagement';\r\nimport ChangePassword from './pages/ChangePassword';\r\nimport Layout from './components/Layout';\r\nimport NewComplaint from './pages/NewComplaint';\r\n\r\nfunction App() {\r\n  const { user, loading, initialized } = useAuth();\r\n\r\n  // Show nothing while auth is initializing\r\n  if (!initialized) {\r\n    return null;\r\n  }\r\n\r\n  // Show loading state\r\n  if (loading) {\r\n    return null; // Or a loading spinner\r\n  }\r\n\r\n  // Check if user should see dashboard\r\n  const canViewDashboard = user?.isAdmin || (user?.permissions?.canViewDashboard === true);\r\n\r\n  return (\r\n    <ThemeProvider theme={theme}>\r\n      <CssBaseline />\r\n      <LocalizationProvider dateAdapter={AdapterDateFns}>\r\n        <SocketProvider>\r\n          <Routes>\r\n          {/* Public route - Login */}\r\n          <Route \r\n            path=\"/login\" \r\n            element={user ? <Navigate to={canViewDashboard ? \"/dashboard\" : \"/complaints\"} replace /> : <Login />}\r\n          />\r\n\r\n          {/* Protected routes - Must be authenticated */}\r\n          {user ? (\r\n            <Route path=\"/\" element={<Layout />}>\r\n              <Route \r\n                index \r\n                element={\r\n                  canViewDashboard ?\r\n                    <Navigate to=\"/dashboard\" replace /> : \r\n                    <Navigate to=\"/complaints\" replace />\r\n                } \r\n              />\r\n              <Route \r\n                path=\"dashboard\" \r\n                element={\r\n                  canViewDashboard ?\r\n                    <Dashboard /> : \r\n                    <Navigate to=\"/complaints\" replace />\r\n                } \r\n              />\r\n              <Route path=\"complaints\" element={<ComplaintsList />} />\r\n              <Route path=\"complaints/new\" element={<NewComplaint />} />\r\n              <Route path=\"complaints/:id\" element={<ComplaintDetails />} />\r\n              <Route \r\n                path=\"authority-management\" \r\n                element={\r\n                  user.isAdmin ? \r\n                    <AuthorityManagement /> : \r\n                    <Navigate to=\"/complaints\" replace />\r\n                } \r\n              />\r\n              <Route path=\"change-password\" element={<ChangePassword />} />\r\n            </Route>\r\n          ) : (\r\n            // If not authenticated, redirect to login\r\n            <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\r\n          )}\r\n\r\n          {/* Catch all route - redirect to login if not authenticated, otherwise to dashboard/complaints */}\r\n          <Route \r\n            path=\"*\" \r\n            element={\r\n              <Navigate \r\n                to={\r\n                  user ? \r\n                    (canViewDashboard ? \"/dashboard\" : \"/complaints\") :\r\n                    \"/login\"\r\n                } \r\n                replace \r\n              />\r\n            } \r\n          />\r\n          </Routes>\r\n        </SocketProvider>\r\n      </LocalizationProvider>\r\n    </ThemeProvider>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,aAAa,EAAEC,WAAW,QAAQ,eAAe;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,cAAc,QAAQ,oCAAoC;AACnE,OAAOC,KAAK,MAAM,SAAS;;AAE3B;AACA,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,iBAAA;EACb,MAAM;IAAEC,IAAI;IAAEC,OAAO;IAAEC;EAAY,CAAC,GAAGpB,OAAO,CAAC,CAAC;;EAEhD;EACA,IAAI,CAACoB,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;;EAEA;EACA,IAAID,OAAO,EAAE;IACX,OAAO,IAAI,CAAC,CAAC;EACf;;EAEA;EACA,MAAME,gBAAgB,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,OAAO,KAAK,CAAAJ,IAAI,aAAJA,IAAI,wBAAAD,iBAAA,GAAJC,IAAI,CAAEK,WAAW,cAAAN,iBAAA,uBAAjBA,iBAAA,CAAmBI,gBAAgB,MAAK,IAAK;EAExF,oBACEP,OAAA,CAAChB,aAAa;IAACM,KAAK,EAAEA,KAAM;IAAAoB,QAAA,gBAC1BV,OAAA,CAACf,WAAW;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfd,OAAA,CAACZ,oBAAoB;MAAC2B,WAAW,EAAE1B,cAAe;MAAAqB,QAAA,eAChDV,OAAA,CAACb,cAAc;QAAAuB,QAAA,eACbV,OAAA,CAACnB,MAAM;UAAA6B,QAAA,gBAEPV,OAAA,CAAClB,KAAK;YACJkC,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAEb,IAAI,gBAAGJ,OAAA,CAACjB,QAAQ;cAACmC,EAAE,EAAEX,gBAAgB,GAAG,YAAY,GAAG,aAAc;cAACY,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGd,OAAA,CAACT,KAAK;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC,EAGDV,IAAI,gBACHJ,OAAA,CAAClB,KAAK;YAACkC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEjB,OAAA,CAACH,MAAM;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,gBAClCV,OAAA,CAAClB,KAAK;cACJsC,KAAK;cACLH,OAAO,EACLV,gBAAgB,gBACdP,OAAA,CAACjB,QAAQ;gBAACmC,EAAE,EAAC,YAAY;gBAACC,OAAO;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBACpCd,OAAA,CAACjB,QAAQ;gBAACmC,EAAE,EAAC,aAAa;gBAACC,OAAO;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACvC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFd,OAAA,CAAClB,KAAK;cACJkC,IAAI,EAAC,WAAW;cAChBC,OAAO,EACLV,gBAAgB,gBACdP,OAAA,CAACR,SAAS;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBACbd,OAAA,CAACjB,QAAQ;gBAACmC,EAAE,EAAC,aAAa;gBAACC,OAAO;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACvC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFd,OAAA,CAAClB,KAAK;cAACkC,IAAI,EAAC,YAAY;cAACC,OAAO,eAAEjB,OAAA,CAACP,cAAc;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDd,OAAA,CAAClB,KAAK;cAACkC,IAAI,EAAC,gBAAgB;cAACC,OAAO,eAAEjB,OAAA,CAACF,YAAY;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1Dd,OAAA,CAAClB,KAAK;cAACkC,IAAI,EAAC,gBAAgB;cAACC,OAAO,eAAEjB,OAAA,CAACN,gBAAgB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9Dd,OAAA,CAAClB,KAAK;cACJkC,IAAI,EAAC,sBAAsB;cAC3BC,OAAO,EACLb,IAAI,CAACI,OAAO,gBACVR,OAAA,CAACL,mBAAmB;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBACvBd,OAAA,CAACjB,QAAQ;gBAACmC,EAAE,EAAC,aAAa;gBAACC,OAAO;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACvC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFd,OAAA,CAAClB,KAAK;cAACkC,IAAI,EAAC,iBAAiB;cAACC,OAAO,eAAEjB,OAAA,CAACJ,cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;UAAA;UAER;UACAd,OAAA,CAAClB,KAAK;YAACkC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEjB,OAAA,CAACjB,QAAQ;cAACmC,EAAE,EAAC,QAAQ;cAACC,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC7D,eAGDd,OAAA,CAAClB,KAAK;YACJkC,IAAI,EAAC,GAAG;YACRC,OAAO,eACLjB,OAAA,CAACjB,QAAQ;cACPmC,EAAE,EACAd,IAAI,GACDG,gBAAgB,GAAG,YAAY,GAAG,aAAa,GAChD,QACH;cACDY,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEpB;AAACZ,EAAA,CApFQD,GAAG;EAAA,QAC6Bf,OAAO;AAAA;AAAAmC,EAAA,GADvCpB,GAAG;AAsFZ,eAAeA,GAAG;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}