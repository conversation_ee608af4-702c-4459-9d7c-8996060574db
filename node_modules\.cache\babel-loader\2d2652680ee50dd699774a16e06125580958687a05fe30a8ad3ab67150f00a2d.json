{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getPickersYearUtilityClass(slot) {\n  // TODO v6: Rename 'PrivatePickersYear' to 'MuiPickersYear' to follow convention\n  return generateUtilityClass('PrivatePickersYear', slot);\n} // TODO v6: Rename 'PrivatePickersYear' to 'MuiPickersYear' to follow convention\n\nexport const pickersYearClasses = generateUtilityClasses('PrivatePickersYear', ['root', 'modeDesktop', 'modeMobile', 'yearButton', 'selected', 'disabled']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getPickersYearUtilityClass", "slot", "pickersYearClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/YearPicker/pickersYearClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getPickersYearUtilityClass(slot) {\n  // TODO v6: Rename 'PrivatePickersYear' to 'MuiPickersYear' to follow convention\n  return generateUtilityClass('PrivatePickersYear', slot);\n} // TODO v6: Rename 'PrivatePickersYear' to 'MuiPickersYear' to follow convention\n\nexport const pickersYearClasses = generateUtilityClasses('PrivatePickersYear', ['root', 'modeDesktop', 'modeMobile', 'yearButton', 'selected', 'disabled']);"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,eAAe;AAC5E,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C;EACA,OAAOH,oBAAoB,CAAC,oBAAoB,EAAEG,IAAI,CAAC;AACzD,CAAC,CAAC;;AAEF,OAAO,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}