import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
    Box,
    Card,
    CardContent,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Button,
    Typography,
    Alert,
    Snackbar,
    CircularProgress,
    TextField,
    InputAdornment,
    Paper,
    Chip,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Grid,
    Switch
} from '@mui/material';
import {
    Search as SearchIcon,
    Save as SaveIcon,
    Refresh as RefreshIcon
} from '@mui/icons-material';
import axios from '../utils/axiosConfig';
import config from '../config';
import { useAuth } from '../contexts/AuthContext';

// Memoized Employee Row Component
const EmployeeRow = React.memo(({ employee, onPermissionChange, isAdmin }) => (
    <TableRow
        hover
        sx={{
            '&:nth-of-type(odd)': {
                bgcolor: 'action.hover',
            }
        }}
    >
        <TableCell>
            <Chip 
                label={employee.EmpCode}
                size="small"
                color="primary"
                variant="outlined"
            />
        </TableCell>
        <TableCell>{employee.EmpName}</TableCell>
        <TableCell>
            <Chip 
                label={employee.DeptName}
                size="small"
                color="secondary"
                variant="outlined"
            />
        </TableCell>
        <TableCell align="center">
            <Box sx={{ 
                display: 'flex', 
                justifyContent: 'flex-start', 
                gap: 3,
                flexWrap: 'nowrap',
                minWidth: '400px'
            }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: '100px' }}>
                    <Typography variant="body2">Assign:</Typography>
                    <Switch
                        size="small"
                        checked={employee.CanAssign || false}
                        onChange={() => onPermissionChange(employee.EmpCode, 'CanAssign')}
                        disabled={isAdmin || employee.DeptName?.toLowerCase().includes('admin')}
                        color="primary"
                    />
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: '100px' }}>
                    <Typography variant="body2">Update:</Typography>
                    <Switch
                        size="small"
                        checked={employee.CanUpdateStatus || false}
                        onChange={() => onPermissionChange(employee.EmpCode, 'CanUpdateStatus')}
                        disabled={isAdmin || employee.DeptName?.toLowerCase().includes('admin')}
                        color="primary"
                    />
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: '120px' }}>
                    <Typography variant="body2">Dashboard:</Typography>
                    <Switch
                        size="small"
                        checked={employee.CanViewDashboard || false}
                        onChange={() => onPermissionChange(employee.EmpCode, 'CanViewDashboard')}
                        disabled={isAdmin || employee.DeptName?.toLowerCase().includes('admin')}
                        color="primary"
                    />
                </Box>
            </Box>
        </TableCell>
    </TableRow>
));

const AuthorityManagement = () => {
    const { refreshUserPermissions } = useAuth();
    const [employees, setEmployees] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [message, setMessage] = useState({ text: '', type: 'info' });
    const [showMessage, setShowMessage] = useState(false);
    const [loading, setLoading] = useState(true);
    const [hasChanges, setHasChanges] = useState(false);
    const [departmentFilter, setDepartmentFilter] = useState('all');

    // Memoize departments list
    const departments = useMemo(() => {
        const uniqueDepartments = [...new Set(employees.map(emp => emp.DeptName))];
        return uniqueDepartments.sort();
    }, [employees]);

    // Memoize filtered employees
    const filteredEmployees = useMemo(() => {
        return employees.filter(emp => {
            const matchesSearch = 
                (emp.EmpName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                (emp.EmpCode?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                (emp.DeptName?.toLowerCase() || '').includes(searchTerm.toLowerCase());
            
            const matchesDepartment = 
                departmentFilter === 'all' || emp.DeptName === departmentFilter;

            return matchesSearch && matchesDepartment;
        }).sort((a, b) => {
            if (a.DeptName !== b.DeptName) {
                return a.DeptName.localeCompare(b.DeptName);
            }
            return a.EmpName.localeCompare(b.EmpName);
        });
    }, [employees, searchTerm, departmentFilter]);

    const fetchEmployees = useCallback(async () => {
        try {
            setLoading(true);
            const response = await axios.get('/api/employees/permissions');
            
            // Filter out invalid employees
            const validEmployees = response.data.filter(emp => {
                const cleanName = emp.EmpName?.trim() || '';
                return cleanName !== '' && !/^[-]+$/.test(cleanName);
            });
            
            setEmployees(validEmployees);
            setHasChanges(false);
        } catch (error) {
            console.error('Error fetching employees:', error);
            showNotification('Failed to fetch employees', 'error');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchEmployees();
    }, [fetchEmployees]);

    const handlePermissionChange = useCallback((empCode, permission) => {
        setEmployees(prevEmployees => 
            prevEmployees.map(emp => 
                emp.EmpCode === empCode 
                    ? { ...emp, [permission]: !emp[permission] }
                    : emp
            )
        );
        setHasChanges(true);
    }, []);

    const handleSave = useCallback(async () => {
        try {
            setLoading(true);
            await axios.post('/api/employees/permissions',
                { permissions: employees },
                {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                }
            );

            // Refresh user permissions for all logged-in users
            await refreshUserPermissions();

            showNotification('Permissions updated successfully. Users may need to refresh their page to see changes.', 'success');
            setHasChanges(false);
        } catch (error) {
            console.error('Error updating permissions:', error);
            showNotification('Failed to update permissions', 'error');
        } finally {
            setLoading(false);
        }
    }, [employees, refreshUserPermissions]);

    const showNotification = useCallback((text, type) => {
        setMessage({ text, type });
        setShowMessage(true);
    }, []);

    const handleSearchChange = useCallback((e) => {
        setSearchTerm(e.target.value);
    }, []);

    const handleDepartmentChange = useCallback((e) => {
        setDepartmentFilter(e.target.value);
    }, []);

    return (
        <Box sx={{ p: 3 }}>
            <Card elevation={3}>
                <CardContent>
                    <Box sx={{ mb: 4 }}>
                        <Box sx={{ 
                            display: 'flex', 
                            justifyContent: 'space-between', 
                            alignItems: 'center',
                            mb: 3 
                        }}>
                            <Typography variant="h5" component="h2" 
                                sx={{ 
                                    color: 'primary.main',
                                    fontWeight: 'bold'
                                }}
                            >
                                Authority Management
                            </Typography>
                            <Box>
                                <Button
                                    startIcon={<RefreshIcon />}
                                    onClick={fetchEmployees}
                                    sx={{ mr: 2 }}
                                    disabled={loading}
                                >
                                    Refresh
                                </Button>
                                <Button
                                    variant="contained"
                                    startIcon={<SaveIcon />}
                                    onClick={handleSave}
                                    disabled={!hasChanges || loading}
                                    color="primary"
                                >
                                    Save Changes
                                </Button>
                            </Box>
                        </Box>

                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <TextField
                                    fullWidth
                                    variant="outlined"
                                    placeholder="Search by employee code, name or department..."
                                    value={searchTerm}
                                    onChange={handleSearchChange}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <SearchIcon color="action" />
                                            </InputAdornment>
                                        )
                                    }}
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <FormControl fullWidth>
                                    <InputLabel>Department</InputLabel>
                                    <Select
                                        value={departmentFilter}
                                        onChange={handleDepartmentChange}
                                        label="Department"
                                    >
                                        <MenuItem value="all">All Departments</MenuItem>
                                        {departments.map(dept => (
                                            <MenuItem key={dept} value={dept}>
                                                {dept}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                        </Grid>
                    </Box>

                    {loading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <TableContainer component={Paper} elevation={0}>
                            <Table>
                                <TableHead>
                                    <TableRow sx={{ bgcolor: 'primary.light' }}>
                                        <TableCell>Employee Code</TableCell>
                                        <TableCell>Name</TableCell>
                                        <TableCell>Department</TableCell>
                                        <TableCell align="center">Permissions</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {filteredEmployees.map((employee) => (
                                        <EmployeeRow
                                            key={employee.EmpCode}
                                            employee={employee}
                                            onPermissionChange={handlePermissionChange}
                                            isAdmin={employee.DeptName?.toLowerCase().includes('admin')}
                                        />
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    )}
                </CardContent>
            </Card>

            <Snackbar
                open={showMessage}
                autoHideDuration={6000}
                onClose={() => setShowMessage(false)}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            >
                <Alert 
                    severity={message.type} 
                    onClose={() => setShowMessage(false)}
                    sx={{ width: '100%' }}
                    variant="filled"
                >
                    {message.text}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default AuthorityManagement; 