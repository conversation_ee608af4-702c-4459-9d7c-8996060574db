{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"actions\", \"anchor\", \"children\", \"onItemsChange\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { HTMLElementType, refType } from '@mui/utils';\nimport { getMenuUtilityClass } from './menuClasses';\nimport { useMenu } from '../useMenu';\nimport { MenuProvider } from '../useMenu/MenuProvider';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { Popper } from '../Popper';\nimport { useSlotProps } from '../utils/useSlotProps';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { ListActionTypes } from '../useList';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction useUtilityClasses(ownerState) {\n  const {\n    open\n  } = ownerState;\n  const slots = {\n    root: ['root', open && 'expanded'],\n    listbox: ['listbox', open && 'expanded']\n  };\n  return composeClasses(slots, useClassNamesOverride(getMenuUtilityClass));\n}\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/)\n *\n * API:\n *\n * - [Menu API](https://mui.com/base-ui/react-menu/components-api/#menu)\n */\nconst Menu = /*#__PURE__*/React.forwardRef(function Menu(props, forwardedRef) {\n  var _slots$root, _slots$listbox;\n  const {\n      actions,\n      anchor: anchorProp,\n      children,\n      onItemsChange,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    contextValue,\n    getListboxProps,\n    dispatch,\n    open,\n    triggerElement\n  } = useMenu({\n    onItemsChange\n  });\n  const anchor = anchorProp != null ? anchorProp : triggerElement;\n  React.useImperativeHandle(actions, () => ({\n    dispatch,\n    resetHighlight: () => dispatch({\n      type: ListActionTypes.resetHighlight,\n      event: null\n    })\n  }), [dispatch]);\n  const ownerState = _extends({}, props, {\n    open\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef,\n      role: undefined\n    },\n    className: classes.root,\n    ownerState\n  });\n  const Listbox = (_slots$listbox = slots.listbox) != null ? _slots$listbox : 'ul';\n  const listboxProps = useSlotProps({\n    elementType: Listbox,\n    getSlotProps: getListboxProps,\n    externalSlotProps: slotProps.listbox,\n    className: classes.listbox,\n    ownerState\n  });\n  if (open === true && anchor == null) {\n    return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n      children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n        children: /*#__PURE__*/_jsx(MenuProvider, {\n          value: contextValue,\n          children: children\n        })\n      }))\n    }));\n  }\n  return /*#__PURE__*/_jsx(Popper, _extends({}, rootProps, {\n    open: open,\n    anchorEl: anchor,\n    slots: {\n      root: Root\n    },\n    children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n      children: /*#__PURE__*/_jsx(MenuProvider, {\n        value: contextValue,\n        children: children\n      })\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Menu.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * A ref with imperative actions that can be performed on the menu.\n   */\n  actions: refType,\n  /**\n   * The element based on which the menu is positioned.\n   */\n  anchor: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.object, PropTypes.func]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Function called when the items displayed in the menu change.\n   */\n  onItemsChange: PropTypes.func,\n  /**\n   * The props used for each slot inside the Menu.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Menu.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    listbox: PropTypes.elementType,\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { Menu };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "HTMLElementType", "refType", "getMenuUtilityClass", "useMenu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unstable_composeClasses", "composeClasses", "<PERSON><PERSON>", "useSlotProps", "useClassNamesOverride", "ListActionTypes", "jsx", "_jsx", "useUtilityClasses", "ownerState", "open", "slots", "root", "listbox", "<PERSON><PERSON>", "forwardRef", "props", "forwardedRef", "_slots$root", "_slots$listbox", "actions", "anchor", "anchorProp", "children", "onItemsChange", "slotProps", "other", "contextValue", "getListboxProps", "dispatch", "triggerElement", "useImperativeHandle", "resetHighlight", "type", "event", "classes", "Root", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "ref", "role", "undefined", "className", "Listbox", "listboxProps", "getSlotProps", "value", "anchorEl", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "object", "func", "node", "string", "shape"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Menu/Menu.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"actions\", \"anchor\", \"children\", \"onItemsChange\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { HTMLElementType, refType } from '@mui/utils';\nimport { getMenuUtilityClass } from './menuClasses';\nimport { useMenu } from '../useMenu';\nimport { MenuProvider } from '../useMenu/MenuProvider';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { Popper } from '../Popper';\nimport { useSlotProps } from '../utils/useSlotProps';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { ListActionTypes } from '../useList';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction useUtilityClasses(ownerState) {\n  const {\n    open\n  } = ownerState;\n  const slots = {\n    root: ['root', open && 'expanded'],\n    listbox: ['listbox', open && 'expanded']\n  };\n  return composeClasses(slots, useClassNamesOverride(getMenuUtilityClass));\n}\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/)\n *\n * API:\n *\n * - [Menu API](https://mui.com/base-ui/react-menu/components-api/#menu)\n */\nconst Menu = /*#__PURE__*/React.forwardRef(function Menu(props, forwardedRef) {\n  var _slots$root, _slots$listbox;\n  const {\n      actions,\n      anchor: anchorProp,\n      children,\n      onItemsChange,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    contextValue,\n    getListboxProps,\n    dispatch,\n    open,\n    triggerElement\n  } = useMenu({\n    onItemsChange\n  });\n  const anchor = anchorProp != null ? anchorProp : triggerElement;\n  React.useImperativeHandle(actions, () => ({\n    dispatch,\n    resetHighlight: () => dispatch({\n      type: ListActionTypes.resetHighlight,\n      event: null\n    })\n  }), [dispatch]);\n  const ownerState = _extends({}, props, {\n    open\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef,\n      role: undefined\n    },\n    className: classes.root,\n    ownerState\n  });\n  const Listbox = (_slots$listbox = slots.listbox) != null ? _slots$listbox : 'ul';\n  const listboxProps = useSlotProps({\n    elementType: Listbox,\n    getSlotProps: getListboxProps,\n    externalSlotProps: slotProps.listbox,\n    className: classes.listbox,\n    ownerState\n  });\n  if (open === true && anchor == null) {\n    return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n      children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n        children: /*#__PURE__*/_jsx(MenuProvider, {\n          value: contextValue,\n          children: children\n        })\n      }))\n    }));\n  }\n  return /*#__PURE__*/_jsx(Popper, _extends({}, rootProps, {\n    open: open,\n    anchorEl: anchor,\n    slots: {\n      root: Root\n    },\n    children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n      children: /*#__PURE__*/_jsx(MenuProvider, {\n        value: contextValue,\n        children: children\n      })\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Menu.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * A ref with imperative actions that can be performed on the menu.\n   */\n  actions: refType,\n  /**\n   * The element based on which the menu is positioned.\n   */\n  anchor: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.object, PropTypes.func]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Function called when the items displayed in the menu change.\n   */\n  onItemsChange: PropTypes.func,\n  /**\n   * The props used for each slot inside the Menu.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Menu.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    listbox: PropTypes.elementType,\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { Menu };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,CAAC;AAC1F,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,eAAe,EAAEC,OAAO,QAAQ,YAAY;AACrD,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,MAAM,QAAQ,WAAW;AAClC,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,eAAe,QAAQ,YAAY;AAC5C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EACrC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,IAAI,IAAI,UAAU,CAAC;IAClCG,OAAO,EAAE,CAAC,SAAS,EAAEH,IAAI,IAAI,UAAU;EACzC,CAAC;EACD,OAAOT,cAAc,CAACU,KAAK,EAAEP,qBAAqB,CAACP,mBAAmB,CAAC,CAAC;AAC1E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiB,IAAI,GAAG,aAAarB,KAAK,CAACsB,UAAU,CAAC,SAASD,IAAIA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAC5E,IAAIC,WAAW,EAAEC,cAAc;EAC/B,MAAM;MACFC,OAAO;MACPC,MAAM,EAAEC,UAAU;MAClBC,QAAQ;MACRC,aAAa;MACbC,SAAS,GAAG,CAAC,CAAC;MACdd,KAAK,GAAG,CAAC;IACX,CAAC,GAAGK,KAAK;IACTU,KAAK,GAAGnC,6BAA6B,CAACyB,KAAK,EAAExB,SAAS,CAAC;EACzD,MAAM;IACJmC,YAAY;IACZC,eAAe;IACfC,QAAQ;IACRnB,IAAI;IACJoB;EACF,CAAC,GAAGhC,OAAO,CAAC;IACV0B;EACF,CAAC,CAAC;EACF,MAAMH,MAAM,GAAGC,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGQ,cAAc;EAC/DrC,KAAK,CAACsC,mBAAmB,CAACX,OAAO,EAAE,OAAO;IACxCS,QAAQ;IACRG,cAAc,EAAEA,CAAA,KAAMH,QAAQ,CAAC;MAC7BI,IAAI,EAAE5B,eAAe,CAAC2B,cAAc;MACpCE,KAAK,EAAE;IACT,CAAC;EACH,CAAC,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;EACf,MAAMpB,UAAU,GAAGnB,QAAQ,CAAC,CAAC,CAAC,EAAE0B,KAAK,EAAE;IACrCN;EACF,CAAC,CAAC;EACF,MAAMyB,OAAO,GAAG3B,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM2B,IAAI,GAAG,CAAClB,WAAW,GAAGP,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGM,WAAW,GAAG,KAAK;EACrE,MAAMmB,SAAS,GAAGlC,YAAY,CAAC;IAC7BmC,WAAW,EAAEF,IAAI;IACjBG,iBAAiB,EAAEd,SAAS,CAACb,IAAI;IACjC4B,sBAAsB,EAAEd,KAAK;IAC7Be,eAAe,EAAE;MACfC,GAAG,EAAEzB,YAAY;MACjB0B,IAAI,EAAEC;IACR,CAAC;IACDC,SAAS,EAAEV,OAAO,CAACvB,IAAI;IACvBH;EACF,CAAC,CAAC;EACF,MAAMqC,OAAO,GAAG,CAAC3B,cAAc,GAAGR,KAAK,CAACE,OAAO,KAAK,IAAI,GAAGM,cAAc,GAAG,IAAI;EAChF,MAAM4B,YAAY,GAAG5C,YAAY,CAAC;IAChCmC,WAAW,EAAEQ,OAAO;IACpBE,YAAY,EAAEpB,eAAe;IAC7BW,iBAAiB,EAAEd,SAAS,CAACZ,OAAO;IACpCgC,SAAS,EAAEV,OAAO,CAACtB,OAAO;IAC1BJ;EACF,CAAC,CAAC;EACF,IAAIC,IAAI,KAAK,IAAI,IAAIW,MAAM,IAAI,IAAI,EAAE;IACnC,OAAO,aAAad,IAAI,CAAC6B,IAAI,EAAE9C,QAAQ,CAAC,CAAC,CAAC,EAAE+C,SAAS,EAAE;MACrDd,QAAQ,EAAE,aAAahB,IAAI,CAACuC,OAAO,EAAExD,QAAQ,CAAC,CAAC,CAAC,EAAEyD,YAAY,EAAE;QAC9DxB,QAAQ,EAAE,aAAahB,IAAI,CAACR,YAAY,EAAE;UACxCkD,KAAK,EAAEtB,YAAY;UACnBJ,QAAQ,EAAEA;QACZ,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAahB,IAAI,CAACL,MAAM,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAE+C,SAAS,EAAE;IACvD3B,IAAI,EAAEA,IAAI;IACVwC,QAAQ,EAAE7B,MAAM;IAChBV,KAAK,EAAE;MACLC,IAAI,EAAEwB;IACR,CAAC;IACDb,QAAQ,EAAE,aAAahB,IAAI,CAACuC,OAAO,EAAExD,QAAQ,CAAC,CAAC,CAAC,EAAEyD,YAAY,EAAE;MAC9DxB,QAAQ,EAAE,aAAahB,IAAI,CAACR,YAAY,EAAE;QACxCkD,KAAK,EAAEtB,YAAY;QACnBJ,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF4B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvC,IAAI,CAACwC,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACElC,OAAO,EAAExB,OAAO;EAChB;AACF;AACA;EACEyB,MAAM,EAAE3B,SAAS,CAAC,sCAAsC6D,SAAS,CAAC,CAAC5D,eAAe,EAAED,SAAS,CAAC8D,MAAM,EAAE9D,SAAS,CAAC+D,IAAI,CAAC,CAAC;EACtH;AACF;AACA;EACElC,QAAQ,EAAE7B,SAAS,CAACgE,IAAI;EACxB;AACF;AACA;EACEb,SAAS,EAAEnD,SAAS,CAACiE,MAAM;EAC3B;AACF;AACA;EACEnC,aAAa,EAAE9B,SAAS,CAAC+D,IAAI;EAC7B;AACF;AACA;AACA;EACEhC,SAAS,EAAE/B,SAAS,CAACkE,KAAK,CAAC;IACzB/C,OAAO,EAAEnB,SAAS,CAAC6D,SAAS,CAAC,CAAC7D,SAAS,CAAC+D,IAAI,EAAE/D,SAAS,CAAC8D,MAAM,CAAC,CAAC;IAChE5C,IAAI,EAAElB,SAAS,CAAC6D,SAAS,CAAC,CAAC7D,SAAS,CAAC+D,IAAI,EAAE/D,SAAS,CAAC8D,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE7C,KAAK,EAAEjB,SAAS,CAACkE,KAAK,CAAC;IACrB/C,OAAO,EAAEnB,SAAS,CAAC4C,WAAW;IAC9B1B,IAAI,EAAElB,SAAS,CAAC4C;EAClB,CAAC;AACH,CAAC,GAAG,KAAK,CAAC;AACV,SAASxB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}