{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, CssBaseline } from '@mui/material';\nimport { useAuth } from './contexts/AuthContext';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\nimport theme from './theme';\n\n// Pages\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport ComplaintsList from './pages/ComplaintsList';\nimport ComplaintDetails from './pages/ComplaintDetails';\nimport AuthorityManagement from './pages/AuthorityManagement';\nimport ChangePassword from './pages/ChangePassword';\nimport Layout from './components/Layout';\nimport NewComplaint from './pages/NewComplaint';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  var _user$permissions;\n  const {\n    user,\n    loading,\n    initialized\n  } = useAuth();\n\n  // Show nothing while auth is initializing\n  if (!initialized) {\n    return null;\n  }\n\n  // Show loading state\n  if (loading) {\n    return null; // Or a loading spinner\n  }\n\n  // Check if user should see dashboard\n  const canViewDashboard = (user === null || user === void 0 ? void 0 : user.isAdmin) || (user === null || user === void 0 ? void 0 : (_user$permissions = user.permissions) === null || _user$permissions === void 0 ? void 0 : _user$permissions.canViewDashboard) === true;\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n      dateAdapter: AdapterDateFns,\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n            to: canViewDashboard ? \"/dashboard\" : \"/complaints\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 105\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), user ? /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 38\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            index: true,\n            element: canViewDashboard ? /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/complaints\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"dashboard\",\n            element: canViewDashboard ? /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/complaints\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"complaints\",\n            element: /*#__PURE__*/_jsxDEV(ComplaintsList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"complaints/new\",\n            element: /*#__PURE__*/_jsxDEV(NewComplaint, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"complaints/:id\",\n            element: /*#__PURE__*/_jsxDEV(ComplaintDetails, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"authority-management\",\n            element: user.isAdmin ? /*#__PURE__*/_jsxDEV(AuthorityManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/complaints\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"change-password\",\n            element: /*#__PURE__*/_jsxDEV(ChangePassword, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 54\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this) :\n        /*#__PURE__*/\n        // If not authenticated, redirect to login\n        _jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: user ? canViewDashboard ? \"/dashboard\" : \"/complaints\" : \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"scqRZSipSuJhXLYwgHoJq/UgQQ8=\", false, function () {\n  return [useAuth];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "ThemeProvider", "CssBaseline", "useAuth", "LocalizationProvider", "AdapterDateFns", "theme", "<PERSON><PERSON>", "Dashboard", "ComplaintsList", "ComplaintDetails", "AuthorityManagement", "ChangePassword", "Layout", "NewComplaint", "jsxDEV", "_jsxDEV", "App", "_s", "_user$permissions", "user", "loading", "initialized", "canViewDashboard", "isAdmin", "permissions", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dateAdapter", "path", "element", "to", "replace", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { Routes, Route, Navigate } from 'react-router-dom';\r\nimport { ThemeProvider, CssBaseline } from '@mui/material';\r\nimport { useAuth } from './contexts/AuthContext';\r\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\r\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\r\nimport theme from './theme';\r\n\r\n// Pages\r\nimport Login from './pages/Login';\r\nimport Dashboard from './pages/Dashboard';\r\nimport ComplaintsList from './pages/ComplaintsList';\r\nimport ComplaintDetails from './pages/ComplaintDetails';\r\nimport AuthorityManagement from './pages/AuthorityManagement';\r\nimport ChangePassword from './pages/ChangePassword';\r\nimport Layout from './components/Layout';\r\nimport NewComplaint from './pages/NewComplaint';\r\n\r\nfunction App() {\r\n  const { user, loading, initialized } = useAuth();\r\n\r\n  // Show nothing while auth is initializing\r\n  if (!initialized) {\r\n    return null;\r\n  }\r\n\r\n  // Show loading state\r\n  if (loading) {\r\n    return null; // Or a loading spinner\r\n  }\r\n\r\n  // Check if user should see dashboard\r\n  const canViewDashboard = user?.isAdmin || (user?.permissions?.canViewDashboard === true);\r\n\r\n  return (\r\n    <ThemeProvider theme={theme}>\r\n      <CssBaseline />\r\n      <LocalizationProvider dateAdapter={AdapterDateFns}>\r\n        <Routes>\r\n          {/* Public route - Login */}\r\n          <Route \r\n            path=\"/login\" \r\n            element={user ? <Navigate to={canViewDashboard ? \"/dashboard\" : \"/complaints\"} replace /> : <Login />}\r\n          />\r\n\r\n          {/* Protected routes - Must be authenticated */}\r\n          {user ? (\r\n            <Route path=\"/\" element={<Layout />}>\r\n              <Route \r\n                index \r\n                element={\r\n                  canViewDashboard ?\r\n                    <Navigate to=\"/dashboard\" replace /> : \r\n                    <Navigate to=\"/complaints\" replace />\r\n                } \r\n              />\r\n              <Route \r\n                path=\"dashboard\" \r\n                element={\r\n                  canViewDashboard ?\r\n                    <Dashboard /> : \r\n                    <Navigate to=\"/complaints\" replace />\r\n                } \r\n              />\r\n              <Route path=\"complaints\" element={<ComplaintsList />} />\r\n              <Route path=\"complaints/new\" element={<NewComplaint />} />\r\n              <Route path=\"complaints/:id\" element={<ComplaintDetails />} />\r\n              <Route \r\n                path=\"authority-management\" \r\n                element={\r\n                  user.isAdmin ? \r\n                    <AuthorityManagement /> : \r\n                    <Navigate to=\"/complaints\" replace />\r\n                } \r\n              />\r\n              <Route path=\"change-password\" element={<ChangePassword />} />\r\n            </Route>\r\n          ) : (\r\n            // If not authenticated, redirect to login\r\n            <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\r\n          )}\r\n\r\n          {/* Catch all route - redirect to login if not authenticated, otherwise to dashboard/complaints */}\r\n          <Route \r\n            path=\"*\" \r\n            element={\r\n              <Navigate \r\n                to={\r\n                  user ? \r\n                    (canViewDashboard ? \"/dashboard\" : \"/complaints\") :\r\n                    \"/login\"\r\n                } \r\n                replace \r\n              />\r\n            } \r\n          />\r\n        </Routes>\r\n      </LocalizationProvider>\r\n    </ThemeProvider>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,aAAa,EAAEC,WAAW,QAAQ,eAAe;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,cAAc,QAAQ,oCAAoC;AACnE,OAAOC,KAAK,MAAM,SAAS;;AAE3B;AACA,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,iBAAA;EACb,MAAM;IAAEC,IAAI;IAAEC,OAAO;IAAEC;EAAY,CAAC,GAAGnB,OAAO,CAAC,CAAC;;EAEhD;EACA,IAAI,CAACmB,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;;EAEA;EACA,IAAID,OAAO,EAAE;IACX,OAAO,IAAI,CAAC,CAAC;EACf;;EAEA;EACA,MAAME,gBAAgB,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,OAAO,KAAK,CAAAJ,IAAI,aAAJA,IAAI,wBAAAD,iBAAA,GAAJC,IAAI,CAAEK,WAAW,cAAAN,iBAAA,uBAAjBA,iBAAA,CAAmBI,gBAAgB,MAAK,IAAK;EAExF,oBACEP,OAAA,CAACf,aAAa;IAACK,KAAK,EAAEA,KAAM;IAAAoB,QAAA,gBAC1BV,OAAA,CAACd,WAAW;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfd,OAAA,CAACZ,oBAAoB;MAAC2B,WAAW,EAAE1B,cAAe;MAAAqB,QAAA,eAChDV,OAAA,CAAClB,MAAM;QAAA4B,QAAA,gBAELV,OAAA,CAACjB,KAAK;UACJiC,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAEb,IAAI,gBAAGJ,OAAA,CAAChB,QAAQ;YAACkC,EAAE,EAAEX,gBAAgB,GAAG,YAAY,GAAG,aAAc;YAACY,OAAO;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGd,OAAA,CAACT,KAAK;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG,CAAC,EAGDV,IAAI,gBACHJ,OAAA,CAACjB,KAAK;UAACiC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEjB,OAAA,CAACH,MAAM;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,gBAClCV,OAAA,CAACjB,KAAK;YACJqC,KAAK;YACLH,OAAO,EACLV,gBAAgB,gBACdP,OAAA,CAAChB,QAAQ;cAACkC,EAAE,EAAC,YAAY;cAACC,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACpCd,OAAA,CAAChB,QAAQ;cAACkC,EAAE,EAAC,aAAa;cAACC,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACvC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFd,OAAA,CAACjB,KAAK;YACJiC,IAAI,EAAC,WAAW;YAChBC,OAAO,EACLV,gBAAgB,gBACdP,OAAA,CAACR,SAAS;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACbd,OAAA,CAAChB,QAAQ;cAACkC,EAAE,EAAC,aAAa;cAACC,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACvC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFd,OAAA,CAACjB,KAAK;YAACiC,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEjB,OAAA,CAACP,cAAc;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDd,OAAA,CAACjB,KAAK;YAACiC,IAAI,EAAC,gBAAgB;YAACC,OAAO,eAAEjB,OAAA,CAACF,YAAY;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1Dd,OAAA,CAACjB,KAAK;YAACiC,IAAI,EAAC,gBAAgB;YAACC,OAAO,eAAEjB,OAAA,CAACN,gBAAgB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9Dd,OAAA,CAACjB,KAAK;YACJiC,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,EACLb,IAAI,CAACI,OAAO,gBACVR,OAAA,CAACL,mBAAmB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACvBd,OAAA,CAAChB,QAAQ;cAACkC,EAAE,EAAC,aAAa;cAACC,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACvC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFd,OAAA,CAACjB,KAAK;YAACiC,IAAI,EAAC,iBAAiB;YAACC,OAAO,eAAEjB,OAAA,CAACJ,cAAc;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;QAAA;QAER;QACAd,OAAA,CAACjB,KAAK;UAACiC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEjB,OAAA,CAAChB,QAAQ;YAACkC,EAAE,EAAC,QAAQ;YAACC,OAAO;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC7D,eAGDd,OAAA,CAACjB,KAAK;UACJiC,IAAI,EAAC,GAAG;UACRC,OAAO,eACLjB,OAAA,CAAChB,QAAQ;YACPkC,EAAE,EACAd,IAAI,GACDG,gBAAgB,GAAG,YAAY,GAAG,aAAa,GAChD,QACH;YACDY,OAAO;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEpB;AAACZ,EAAA,CAlFQD,GAAG;EAAA,QAC6Bd,OAAO;AAAA;AAAAkC,EAAA,GADvCpB,GAAG;AAoFZ,eAAeA,GAAG;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}