{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback,useMemo}from'react';import{Box,Card,CardContent,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Button,Typography,Alert,Snackbar,CircularProgress,TextField,InputAdornment,Paper,Chip,FormControl,InputLabel,Select,MenuItem,Grid,Switch}from'@mui/material';import{Search as SearchIcon,Save as SaveIcon,Refresh as RefreshIcon}from'@mui/icons-material';import axios from'../utils/axiosConfig';import config from'../config';import{useAuth}from'../contexts/AuthContext';// Memoized Employee Row Component\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EmployeeRow=/*#__PURE__*/React.memo(_ref=>{var _employee$DeptName,_employee$DeptName2,_employee$DeptName3;let{employee,onPermissionChange,isAdmin}=_ref;return/*#__PURE__*/_jsxs(TableRow,{hover:true,sx:{'&:nth-of-type(odd)':{bgcolor:'action.hover'}},children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:employee.EmpCode,size:\"small\",color:\"primary\",variant:\"outlined\"})}),/*#__PURE__*/_jsx(TableCell,{children:employee.EmpName}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:employee.DeptName,size:\"small\",color:\"secondary\",variant:\"outlined\"})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'flex-start',gap:3,flexWrap:'nowrap',minWidth:'400px'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,minWidth:'100px'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Assign:\"}),/*#__PURE__*/_jsx(Switch,{size:\"small\",checked:employee.CanAssign||false,onChange:()=>onPermissionChange(employee.EmpCode,'CanAssign'),disabled:isAdmin||((_employee$DeptName=employee.DeptName)===null||_employee$DeptName===void 0?void 0:_employee$DeptName.toLowerCase().includes('admin')),color:\"primary\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,minWidth:'100px'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Update:\"}),/*#__PURE__*/_jsx(Switch,{size:\"small\",checked:employee.CanUpdateStatus||false,onChange:()=>onPermissionChange(employee.EmpCode,'CanUpdateStatus'),disabled:isAdmin||((_employee$DeptName2=employee.DeptName)===null||_employee$DeptName2===void 0?void 0:_employee$DeptName2.toLowerCase().includes('admin')),color:\"primary\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,minWidth:'120px'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Dashboard:\"}),/*#__PURE__*/_jsx(Switch,{size:\"small\",checked:employee.CanViewDashboard||false,onChange:()=>onPermissionChange(employee.EmpCode,'CanViewDashboard'),disabled:isAdmin||((_employee$DeptName3=employee.DeptName)===null||_employee$DeptName3===void 0?void 0:_employee$DeptName3.toLowerCase().includes('admin')),color:\"primary\"})]})]})})]});});const AuthorityManagement=()=>{const{refreshUserPermissions}=useAuth();const[employees,setEmployees]=useState([]);const[searchTerm,setSearchTerm]=useState('');const[message,setMessage]=useState({text:'',type:'info'});const[showMessage,setShowMessage]=useState(false);const[loading,setLoading]=useState(true);const[hasChanges,setHasChanges]=useState(false);const[departmentFilter,setDepartmentFilter]=useState('all');// Memoize departments list\nconst departments=useMemo(()=>{const uniqueDepartments=[...new Set(employees.map(emp=>emp.DeptName))];return uniqueDepartments.sort();},[employees]);// Memoize filtered employees\nconst filteredEmployees=useMemo(()=>{return employees.filter(emp=>{var _emp$EmpName,_emp$EmpCode,_emp$DeptName;const matchesSearch=(((_emp$EmpName=emp.EmpName)===null||_emp$EmpName===void 0?void 0:_emp$EmpName.toLowerCase())||'').includes(searchTerm.toLowerCase())||(((_emp$EmpCode=emp.EmpCode)===null||_emp$EmpCode===void 0?void 0:_emp$EmpCode.toLowerCase())||'').includes(searchTerm.toLowerCase())||(((_emp$DeptName=emp.DeptName)===null||_emp$DeptName===void 0?void 0:_emp$DeptName.toLowerCase())||'').includes(searchTerm.toLowerCase());const matchesDepartment=departmentFilter==='all'||emp.DeptName===departmentFilter;return matchesSearch&&matchesDepartment;}).sort((a,b)=>{if(a.DeptName!==b.DeptName){return a.DeptName.localeCompare(b.DeptName);}return a.EmpName.localeCompare(b.EmpName);});},[employees,searchTerm,departmentFilter]);const fetchEmployees=useCallback(async()=>{try{setLoading(true);const response=await axios.get('/api/employees/permissions');// Filter out invalid employees\nconst validEmployees=response.data.filter(emp=>{var _emp$EmpName2;const cleanName=((_emp$EmpName2=emp.EmpName)===null||_emp$EmpName2===void 0?void 0:_emp$EmpName2.trim())||'';return cleanName!==''&&!/^[-]+$/.test(cleanName);});setEmployees(validEmployees);setHasChanges(false);}catch(error){console.error('Error fetching employees:',error);showNotification('Failed to fetch employees','error');}finally{setLoading(false);}},[]);useEffect(()=>{fetchEmployees();},[fetchEmployees]);const handlePermissionChange=useCallback((empCode,permission)=>{setEmployees(prevEmployees=>prevEmployees.map(emp=>emp.EmpCode===empCode?_objectSpread(_objectSpread({},emp),{},{[permission]:!emp[permission]}):emp));setHasChanges(true);},[]);const handleSave=useCallback(async()=>{try{setLoading(true);await axios.post('/api/employees/permissions',{permissions:employees},{headers:{'Authorization':\"Bearer \".concat(localStorage.getItem('token'))}});// Refresh user permissions for all logged-in users\nawait refreshUserPermissions();showNotification('Permissions updated successfully. Users may need to refresh their page to see changes.','success');setHasChanges(false);}catch(error){console.error('Error updating permissions:',error);showNotification('Failed to update permissions','error');}finally{setLoading(false);}},[employees,refreshUserPermissions]);const showNotification=useCallback((text,type)=>{setMessage({text,type});setShowMessage(true);},[]);const handleSearchChange=useCallback(e=>{setSearchTerm(e.target.value);},[]);const handleDepartmentChange=useCallback(e=>{setDepartmentFilter(e.target.value);},[]);return/*#__PURE__*/_jsxs(Box,{sx:{p:3},children:[/*#__PURE__*/_jsx(Card,{elevation:3,children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:4},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"h2\",sx:{color:'primary.main',fontWeight:'bold'},children:\"Authority Management\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(RefreshIcon,{}),onClick:fetchEmployees,sx:{mr:2},disabled:loading,children:\"Refresh\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(SaveIcon,{}),onClick:handleSave,disabled:!hasChanges||loading,color:\"primary\",children:\"Save Changes\"})]})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,variant:\"outlined\",placeholder:\"Search by employee code, name or department...\",value:searchTerm,onChange:handleSearchChange,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(SearchIcon,{color:\"action\"})})}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Department\"}),/*#__PURE__*/_jsxs(Select,{value:departmentFilter,onChange:handleDepartmentChange,label:\"Department\",children:[/*#__PURE__*/_jsx(MenuItem,{value:\"all\",children:\"All Departments\"}),departments.map(dept=>/*#__PURE__*/_jsx(MenuItem,{value:dept,children:dept},dept))]})]})})]})]}),loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',p:3},children:/*#__PURE__*/_jsx(CircularProgress,{})}):/*#__PURE__*/_jsx(TableContainer,{component:Paper,elevation:0,children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{sx:{bgcolor:'primary.light'},children:[/*#__PURE__*/_jsx(TableCell,{children:\"Employee Code\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Name\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Department\"}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:\"Permissions\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:filteredEmployees.map(employee=>{var _employee$DeptName4;return/*#__PURE__*/_jsx(EmployeeRow,{employee:employee,onPermissionChange:handlePermissionChange,isAdmin:(_employee$DeptName4=employee.DeptName)===null||_employee$DeptName4===void 0?void 0:_employee$DeptName4.toLowerCase().includes('admin')},employee.EmpCode);})})]})})]})}),/*#__PURE__*/_jsx(Snackbar,{open:showMessage,autoHideDuration:6000,onClose:()=>setShowMessage(false),anchorOrigin:{vertical:'bottom',horizontal:'right'},children:/*#__PURE__*/_jsx(Alert,{severity:message.type,onClose:()=>setShowMessage(false),sx:{width:'100%'},variant:\"filled\",children:message.text})})]});};export default AuthorityManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Snackbar", "CircularProgress", "TextField", "InputAdornment", "Paper", "Chip", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "Switch", "Search", "SearchIcon", "Save", "SaveIcon", "Refresh", "RefreshIcon", "axios", "config", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "EmployeeRow", "memo", "_ref", "_employee$DeptName", "_employee$DeptName2", "_employee$DeptName3", "employee", "onPermissionChange", "isAdmin", "hover", "sx", "bgcolor", "children", "label", "EmpCode", "size", "color", "variant", "EmpName", "DeptName", "align", "display", "justifyContent", "gap", "flexWrap", "min<PERSON><PERSON><PERSON>", "alignItems", "checked", "CanAssign", "onChange", "disabled", "toLowerCase", "includes", "CanUpdateStatus", "CanViewDashboard", "AuthorityManagement", "refreshUserPermissions", "employees", "setEmployees", "searchTerm", "setSearchTerm", "message", "setMessage", "text", "type", "showMessage", "setShowMessage", "loading", "setLoading", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "setDepartmentFilter", "departments", "uniqueDepartments", "Set", "map", "emp", "sort", "filteredEmployees", "filter", "_emp$EmpName", "_emp$EmpCode", "_emp$DeptName", "matchesSearch", "matchesDepartment", "a", "b", "localeCompare", "fetchEmployees", "response", "get", "validEmployees", "data", "_emp$EmpName2", "cleanName", "trim", "test", "error", "console", "showNotification", "handlePermissionChange", "empCode", "permission", "prevEmployees", "_objectSpread", "handleSave", "post", "permissions", "headers", "concat", "localStorage", "getItem", "handleSearchChange", "e", "target", "value", "handleDepartmentChange", "p", "elevation", "mb", "component", "fontWeight", "startIcon", "onClick", "mr", "container", "spacing", "item", "xs", "md", "fullWidth", "placeholder", "InputProps", "startAdornment", "position", "dept", "_employee$DeptName4", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/pages/AuthorityManagement.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport {\r\n    Box,\r\n    Card,\r\n    CardContent,\r\n    Table,\r\n    TableBody,\r\n    TableCell,\r\n    TableContainer,\r\n    TableHead,\r\n    TableRow,\r\n    Button,\r\n    Typography,\r\n    Alert,\r\n    Snackbar,\r\n    CircularProgress,\r\n    TextField,\r\n    InputAdornment,\r\n    Paper,\r\n    Chip,\r\n    FormControl,\r\n    InputLabel,\r\n    Select,\r\n    MenuItem,\r\n    Grid,\r\n    Switch\r\n} from '@mui/material';\r\nimport {\r\n    Search as SearchIcon,\r\n    Save as SaveIcon,\r\n    Refresh as RefreshIcon\r\n} from '@mui/icons-material';\r\nimport axios from '../utils/axiosConfig';\r\nimport config from '../config';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\n// Memoized Employee Row Component\r\nconst EmployeeRow = React.memo(({ employee, onPermissionChange, isAdmin }) => (\r\n    <TableRow\r\n        hover\r\n        sx={{\r\n            '&:nth-of-type(odd)': {\r\n                bgcolor: 'action.hover',\r\n            }\r\n        }}\r\n    >\r\n        <TableCell>\r\n            <Chip \r\n                label={employee.EmpCode}\r\n                size=\"small\"\r\n                color=\"primary\"\r\n                variant=\"outlined\"\r\n            />\r\n        </TableCell>\r\n        <TableCell>{employee.EmpName}</TableCell>\r\n        <TableCell>\r\n            <Chip \r\n                label={employee.DeptName}\r\n                size=\"small\"\r\n                color=\"secondary\"\r\n                variant=\"outlined\"\r\n            />\r\n        </TableCell>\r\n        <TableCell align=\"center\">\r\n            <Box sx={{ \r\n                display: 'flex', \r\n                justifyContent: 'flex-start', \r\n                gap: 3,\r\n                flexWrap: 'nowrap',\r\n                minWidth: '400px'\r\n            }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: '100px' }}>\r\n                    <Typography variant=\"body2\">Assign:</Typography>\r\n                    <Switch\r\n                        size=\"small\"\r\n                        checked={employee.CanAssign || false}\r\n                        onChange={() => onPermissionChange(employee.EmpCode, 'CanAssign')}\r\n                        disabled={isAdmin || employee.DeptName?.toLowerCase().includes('admin')}\r\n                        color=\"primary\"\r\n                    />\r\n                </Box>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: '100px' }}>\r\n                    <Typography variant=\"body2\">Update:</Typography>\r\n                    <Switch\r\n                        size=\"small\"\r\n                        checked={employee.CanUpdateStatus || false}\r\n                        onChange={() => onPermissionChange(employee.EmpCode, 'CanUpdateStatus')}\r\n                        disabled={isAdmin || employee.DeptName?.toLowerCase().includes('admin')}\r\n                        color=\"primary\"\r\n                    />\r\n                </Box>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: '120px' }}>\r\n                    <Typography variant=\"body2\">Dashboard:</Typography>\r\n                    <Switch\r\n                        size=\"small\"\r\n                        checked={employee.CanViewDashboard || false}\r\n                        onChange={() => onPermissionChange(employee.EmpCode, 'CanViewDashboard')}\r\n                        disabled={isAdmin || employee.DeptName?.toLowerCase().includes('admin')}\r\n                        color=\"primary\"\r\n                    />\r\n                </Box>\r\n            </Box>\r\n        </TableCell>\r\n    </TableRow>\r\n));\r\n\r\nconst AuthorityManagement = () => {\r\n    const { refreshUserPermissions } = useAuth();\r\n    const [employees, setEmployees] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [message, setMessage] = useState({ text: '', type: 'info' });\r\n    const [showMessage, setShowMessage] = useState(false);\r\n    const [loading, setLoading] = useState(true);\r\n    const [hasChanges, setHasChanges] = useState(false);\r\n    const [departmentFilter, setDepartmentFilter] = useState('all');\r\n\r\n    // Memoize departments list\r\n    const departments = useMemo(() => {\r\n        const uniqueDepartments = [...new Set(employees.map(emp => emp.DeptName))];\r\n        return uniqueDepartments.sort();\r\n    }, [employees]);\r\n\r\n    // Memoize filtered employees\r\n    const filteredEmployees = useMemo(() => {\r\n        return employees.filter(emp => {\r\n            const matchesSearch = \r\n                (emp.EmpName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||\r\n                (emp.EmpCode?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||\r\n                (emp.DeptName?.toLowerCase() || '').includes(searchTerm.toLowerCase());\r\n            \r\n            const matchesDepartment = \r\n                departmentFilter === 'all' || emp.DeptName === departmentFilter;\r\n\r\n            return matchesSearch && matchesDepartment;\r\n        }).sort((a, b) => {\r\n            if (a.DeptName !== b.DeptName) {\r\n                return a.DeptName.localeCompare(b.DeptName);\r\n            }\r\n            return a.EmpName.localeCompare(b.EmpName);\r\n        });\r\n    }, [employees, searchTerm, departmentFilter]);\r\n\r\n    const fetchEmployees = useCallback(async () => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await axios.get('/api/employees/permissions');\r\n            \r\n            // Filter out invalid employees\r\n            const validEmployees = response.data.filter(emp => {\r\n                const cleanName = emp.EmpName?.trim() || '';\r\n                return cleanName !== '' && !/^[-]+$/.test(cleanName);\r\n            });\r\n            \r\n            setEmployees(validEmployees);\r\n            setHasChanges(false);\r\n        } catch (error) {\r\n            console.error('Error fetching employees:', error);\r\n            showNotification('Failed to fetch employees', 'error');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        fetchEmployees();\r\n    }, [fetchEmployees]);\r\n\r\n    const handlePermissionChange = useCallback((empCode, permission) => {\r\n        setEmployees(prevEmployees => \r\n            prevEmployees.map(emp => \r\n                emp.EmpCode === empCode \r\n                    ? { ...emp, [permission]: !emp[permission] }\r\n                    : emp\r\n            )\r\n        );\r\n        setHasChanges(true);\r\n    }, []);\r\n\r\n    const handleSave = useCallback(async () => {\r\n        try {\r\n            setLoading(true);\r\n            await axios.post('/api/employees/permissions',\r\n                { permissions: employees },\r\n                {\r\n                    headers: {\r\n                        'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n                    }\r\n                }\r\n            );\r\n\r\n            // Refresh user permissions for all logged-in users\r\n            await refreshUserPermissions();\r\n\r\n            showNotification('Permissions updated successfully. Users may need to refresh their page to see changes.', 'success');\r\n            setHasChanges(false);\r\n        } catch (error) {\r\n            console.error('Error updating permissions:', error);\r\n            showNotification('Failed to update permissions', 'error');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [employees, refreshUserPermissions]);\r\n\r\n    const showNotification = useCallback((text, type) => {\r\n        setMessage({ text, type });\r\n        setShowMessage(true);\r\n    }, []);\r\n\r\n    const handleSearchChange = useCallback((e) => {\r\n        setSearchTerm(e.target.value);\r\n    }, []);\r\n\r\n    const handleDepartmentChange = useCallback((e) => {\r\n        setDepartmentFilter(e.target.value);\r\n    }, []);\r\n\r\n    return (\r\n        <Box sx={{ p: 3 }}>\r\n            <Card elevation={3}>\r\n                <CardContent>\r\n                    <Box sx={{ mb: 4 }}>\r\n                        <Box sx={{ \r\n                            display: 'flex', \r\n                            justifyContent: 'space-between', \r\n                            alignItems: 'center',\r\n                            mb: 3 \r\n                        }}>\r\n                            <Typography variant=\"h5\" component=\"h2\" \r\n                                sx={{ \r\n                                    color: 'primary.main',\r\n                                    fontWeight: 'bold'\r\n                                }}\r\n                            >\r\n                                Authority Management\r\n                            </Typography>\r\n                            <Box>\r\n                                <Button\r\n                                    startIcon={<RefreshIcon />}\r\n                                    onClick={fetchEmployees}\r\n                                    sx={{ mr: 2 }}\r\n                                    disabled={loading}\r\n                                >\r\n                                    Refresh\r\n                                </Button>\r\n                                <Button\r\n                                    variant=\"contained\"\r\n                                    startIcon={<SaveIcon />}\r\n                                    onClick={handleSave}\r\n                                    disabled={!hasChanges || loading}\r\n                                    color=\"primary\"\r\n                                >\r\n                                    Save Changes\r\n                                </Button>\r\n                            </Box>\r\n                        </Box>\r\n\r\n                        <Grid container spacing={2}>\r\n                            <Grid item xs={12} md={6}>\r\n                                <TextField\r\n                                    fullWidth\r\n                                    variant=\"outlined\"\r\n                                    placeholder=\"Search by employee code, name or department...\"\r\n                                    value={searchTerm}\r\n                                    onChange={handleSearchChange}\r\n                                    InputProps={{\r\n                                        startAdornment: (\r\n                                            <InputAdornment position=\"start\">\r\n                                                <SearchIcon color=\"action\" />\r\n                                            </InputAdornment>\r\n                                        )\r\n                                    }}\r\n                                />\r\n                            </Grid>\r\n                            <Grid item xs={12} md={6}>\r\n                                <FormControl fullWidth>\r\n                                    <InputLabel>Department</InputLabel>\r\n                                    <Select\r\n                                        value={departmentFilter}\r\n                                        onChange={handleDepartmentChange}\r\n                                        label=\"Department\"\r\n                                    >\r\n                                        <MenuItem value=\"all\">All Departments</MenuItem>\r\n                                        {departments.map(dept => (\r\n                                            <MenuItem key={dept} value={dept}>\r\n                                                {dept}\r\n                                            </MenuItem>\r\n                                        ))}\r\n                                    </Select>\r\n                                </FormControl>\r\n                            </Grid>\r\n                        </Grid>\r\n                    </Box>\r\n\r\n                    {loading ? (\r\n                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>\r\n                            <CircularProgress />\r\n                        </Box>\r\n                    ) : (\r\n                        <TableContainer component={Paper} elevation={0}>\r\n                            <Table>\r\n                                <TableHead>\r\n                                    <TableRow sx={{ bgcolor: 'primary.light' }}>\r\n                                        <TableCell>Employee Code</TableCell>\r\n                                        <TableCell>Name</TableCell>\r\n                                        <TableCell>Department</TableCell>\r\n                                        <TableCell align=\"center\">Permissions</TableCell>\r\n                                    </TableRow>\r\n                                </TableHead>\r\n                                <TableBody>\r\n                                    {filteredEmployees.map((employee) => (\r\n                                        <EmployeeRow\r\n                                            key={employee.EmpCode}\r\n                                            employee={employee}\r\n                                            onPermissionChange={handlePermissionChange}\r\n                                            isAdmin={employee.DeptName?.toLowerCase().includes('admin')}\r\n                                        />\r\n                                    ))}\r\n                                </TableBody>\r\n                            </Table>\r\n                        </TableContainer>\r\n                    )}\r\n                </CardContent>\r\n            </Card>\r\n\r\n            <Snackbar\r\n                open={showMessage}\r\n                autoHideDuration={6000}\r\n                onClose={() => setShowMessage(false)}\r\n                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\r\n            >\r\n                <Alert \r\n                    severity={message.type} \r\n                    onClose={() => setShowMessage(false)}\r\n                    sx={{ width: '100%' }}\r\n                    variant=\"filled\"\r\n                >\r\n                    {message.text}\r\n                </Alert>\r\n            </Snackbar>\r\n        </Box>\r\n    );\r\n};\r\n\r\nexport default AuthorityManagement; "], "mappings": "0JAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,OAAO,KAAQ,OAAO,CACxE,OACIC,GAAG,CACHC,IAAI,CACJC,WAAW,CACXC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,QAAQ,CACRC,gBAAgB,CAChBC,SAAS,CACTC,cAAc,CACdC,KAAK,CACLC,IAAI,CACJC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,IAAI,CACJC,MAAM,KACH,eAAe,CACtB,OACIC,MAAM,GAAI,CAAAC,UAAU,CACpBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,OAAO,GAAI,CAAAC,WAAW,KACnB,qBAAqB,CAC5B,MAAO,CAAAC,KAAK,KAAM,sBAAsB,CACxC,MAAO,CAAAC,MAAM,KAAM,WAAW,CAC9B,OAASC,OAAO,KAAQ,yBAAyB,CAEjD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,WAAW,cAAG1C,KAAK,CAAC2C,IAAI,CAACC,IAAA,OAAAC,kBAAA,CAAAC,mBAAA,CAAAC,mBAAA,IAAC,CAAEC,QAAQ,CAAEC,kBAAkB,CAAEC,OAAQ,CAAC,CAAAN,IAAA,oBACrEH,KAAA,CAAC5B,QAAQ,EACLsC,KAAK,MACLC,EAAE,CAAE,CACA,oBAAoB,CAAE,CAClBC,OAAO,CAAE,cACb,CACJ,CAAE,CAAAC,QAAA,eAEFf,IAAA,CAAC7B,SAAS,EAAA4C,QAAA,cACNf,IAAA,CAACjB,IAAI,EACDiC,KAAK,CAAEP,QAAQ,CAACQ,OAAQ,CACxBC,IAAI,CAAC,OAAO,CACZC,KAAK,CAAC,SAAS,CACfC,OAAO,CAAC,UAAU,CACrB,CAAC,CACK,CAAC,cACZpB,IAAA,CAAC7B,SAAS,EAAA4C,QAAA,CAAEN,QAAQ,CAACY,OAAO,CAAY,CAAC,cACzCrB,IAAA,CAAC7B,SAAS,EAAA4C,QAAA,cACNf,IAAA,CAACjB,IAAI,EACDiC,KAAK,CAAEP,QAAQ,CAACa,QAAS,CACzBJ,IAAI,CAAC,OAAO,CACZC,KAAK,CAAC,WAAW,CACjBC,OAAO,CAAC,UAAU,CACrB,CAAC,CACK,CAAC,cACZpB,IAAA,CAAC7B,SAAS,EAACoD,KAAK,CAAC,QAAQ,CAAAR,QAAA,cACrBb,KAAA,CAACpC,GAAG,EAAC+C,EAAE,CAAE,CACLW,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,YAAY,CAC5BC,GAAG,CAAE,CAAC,CACNC,QAAQ,CAAE,QAAQ,CAClBC,QAAQ,CAAE,OACd,CAAE,CAAAb,QAAA,eACEb,KAAA,CAACpC,GAAG,EAAC+C,EAAE,CAAE,CAAEW,OAAO,CAAE,MAAM,CAAEK,UAAU,CAAE,QAAQ,CAAEH,GAAG,CAAE,CAAC,CAAEE,QAAQ,CAAE,OAAQ,CAAE,CAAAb,QAAA,eAC1Ef,IAAA,CAACxB,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAAAL,QAAA,CAAC,SAAO,CAAY,CAAC,cAChDf,IAAA,CAACX,MAAM,EACH6B,IAAI,CAAC,OAAO,CACZY,OAAO,CAAErB,QAAQ,CAACsB,SAAS,EAAI,KAAM,CACrCC,QAAQ,CAAEA,CAAA,GAAMtB,kBAAkB,CAACD,QAAQ,CAACQ,OAAO,CAAE,WAAW,CAAE,CAClEgB,QAAQ,CAAEtB,OAAO,IAAAL,kBAAA,CAAIG,QAAQ,CAACa,QAAQ,UAAAhB,kBAAA,iBAAjBA,kBAAA,CAAmB4B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC,CACxEhB,KAAK,CAAC,SAAS,CAClB,CAAC,EACD,CAAC,cACNjB,KAAA,CAACpC,GAAG,EAAC+C,EAAE,CAAE,CAAEW,OAAO,CAAE,MAAM,CAAEK,UAAU,CAAE,QAAQ,CAAEH,GAAG,CAAE,CAAC,CAAEE,QAAQ,CAAE,OAAQ,CAAE,CAAAb,QAAA,eAC1Ef,IAAA,CAACxB,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAAAL,QAAA,CAAC,SAAO,CAAY,CAAC,cAChDf,IAAA,CAACX,MAAM,EACH6B,IAAI,CAAC,OAAO,CACZY,OAAO,CAAErB,QAAQ,CAAC2B,eAAe,EAAI,KAAM,CAC3CJ,QAAQ,CAAEA,CAAA,GAAMtB,kBAAkB,CAACD,QAAQ,CAACQ,OAAO,CAAE,iBAAiB,CAAE,CACxEgB,QAAQ,CAAEtB,OAAO,IAAAJ,mBAAA,CAAIE,QAAQ,CAACa,QAAQ,UAAAf,mBAAA,iBAAjBA,mBAAA,CAAmB2B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC,CACxEhB,KAAK,CAAC,SAAS,CAClB,CAAC,EACD,CAAC,cACNjB,KAAA,CAACpC,GAAG,EAAC+C,EAAE,CAAE,CAAEW,OAAO,CAAE,MAAM,CAAEK,UAAU,CAAE,QAAQ,CAAEH,GAAG,CAAE,CAAC,CAAEE,QAAQ,CAAE,OAAQ,CAAE,CAAAb,QAAA,eAC1Ef,IAAA,CAACxB,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAAAL,QAAA,CAAC,YAAU,CAAY,CAAC,cACnDf,IAAA,CAACX,MAAM,EACH6B,IAAI,CAAC,OAAO,CACZY,OAAO,CAAErB,QAAQ,CAAC4B,gBAAgB,EAAI,KAAM,CAC5CL,QAAQ,CAAEA,CAAA,GAAMtB,kBAAkB,CAACD,QAAQ,CAACQ,OAAO,CAAE,kBAAkB,CAAE,CACzEgB,QAAQ,CAAEtB,OAAO,IAAAH,mBAAA,CAAIC,QAAQ,CAACa,QAAQ,UAAAd,mBAAA,iBAAjBA,mBAAA,CAAmB0B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC,CACxEhB,KAAK,CAAC,SAAS,CAClB,CAAC,EACD,CAAC,EACL,CAAC,CACC,CAAC,EACN,CAAC,EACd,CAAC,CAEF,KAAM,CAAAmB,mBAAmB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAEC,sBAAuB,CAAC,CAAGzC,OAAO,CAAC,CAAC,CAC5C,KAAM,CAAC0C,SAAS,CAAEC,YAAY,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACgF,UAAU,CAAEC,aAAa,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACkF,OAAO,CAAEC,UAAU,CAAC,CAAGnF,QAAQ,CAAC,CAAEoF,IAAI,CAAE,EAAE,CAAEC,IAAI,CAAE,MAAO,CAAC,CAAC,CAClE,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGvF,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACwF,OAAO,CAAEC,UAAU,CAAC,CAAGzF,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC0F,UAAU,CAAEC,aAAa,CAAC,CAAG3F,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC4F,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7F,QAAQ,CAAC,KAAK,CAAC,CAE/D;AACA,KAAM,CAAA8F,WAAW,CAAG3F,OAAO,CAAC,IAAM,CAC9B,KAAM,CAAA4F,iBAAiB,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAAClB,SAAS,CAACmB,GAAG,CAACC,GAAG,EAAIA,GAAG,CAACtC,QAAQ,CAAC,CAAC,CAAC,CAC1E,MAAO,CAAAmC,iBAAiB,CAACI,IAAI,CAAC,CAAC,CACnC,CAAC,CAAE,CAACrB,SAAS,CAAC,CAAC,CAEf;AACA,KAAM,CAAAsB,iBAAiB,CAAGjG,OAAO,CAAC,IAAM,CACpC,MAAO,CAAA2E,SAAS,CAACuB,MAAM,CAACH,GAAG,EAAI,KAAAI,YAAA,CAAAC,YAAA,CAAAC,aAAA,CAC3B,KAAM,CAAAC,aAAa,CACf,CAAC,EAAAH,YAAA,CAAAJ,GAAG,CAACvC,OAAO,UAAA2C,YAAA,iBAAXA,YAAA,CAAa9B,WAAW,CAAC,CAAC,GAAI,EAAE,EAAEC,QAAQ,CAACO,UAAU,CAACR,WAAW,CAAC,CAAC,CAAC,EACrE,CAAC,EAAA+B,YAAA,CAAAL,GAAG,CAAC3C,OAAO,UAAAgD,YAAA,iBAAXA,YAAA,CAAa/B,WAAW,CAAC,CAAC,GAAI,EAAE,EAAEC,QAAQ,CAACO,UAAU,CAACR,WAAW,CAAC,CAAC,CAAC,EACrE,CAAC,EAAAgC,aAAA,CAAAN,GAAG,CAACtC,QAAQ,UAAA4C,aAAA,iBAAZA,aAAA,CAAchC,WAAW,CAAC,CAAC,GAAI,EAAE,EAAEC,QAAQ,CAACO,UAAU,CAACR,WAAW,CAAC,CAAC,CAAC,CAE1E,KAAM,CAAAkC,iBAAiB,CACnBd,gBAAgB,GAAK,KAAK,EAAIM,GAAG,CAACtC,QAAQ,GAAKgC,gBAAgB,CAEnE,MAAO,CAAAa,aAAa,EAAIC,iBAAiB,CAC7C,CAAC,CAAC,CAACP,IAAI,CAAC,CAACQ,CAAC,CAAEC,CAAC,GAAK,CACd,GAAID,CAAC,CAAC/C,QAAQ,GAAKgD,CAAC,CAAChD,QAAQ,CAAE,CAC3B,MAAO,CAAA+C,CAAC,CAAC/C,QAAQ,CAACiD,aAAa,CAACD,CAAC,CAAChD,QAAQ,CAAC,CAC/C,CACA,MAAO,CAAA+C,CAAC,CAAChD,OAAO,CAACkD,aAAa,CAACD,CAAC,CAACjD,OAAO,CAAC,CAC7C,CAAC,CAAC,CACN,CAAC,CAAE,CAACmB,SAAS,CAAEE,UAAU,CAAEY,gBAAgB,CAAC,CAAC,CAE7C,KAAM,CAAAkB,cAAc,CAAG5G,WAAW,CAAC,SAAY,CAC3C,GAAI,CACAuF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAA7E,KAAK,CAAC8E,GAAG,CAAC,4BAA4B,CAAC,CAE9D;AACA,KAAM,CAAAC,cAAc,CAAGF,QAAQ,CAACG,IAAI,CAACb,MAAM,CAACH,GAAG,EAAI,KAAAiB,aAAA,CAC/C,KAAM,CAAAC,SAAS,CAAG,EAAAD,aAAA,CAAAjB,GAAG,CAACvC,OAAO,UAAAwD,aAAA,iBAAXA,aAAA,CAAaE,IAAI,CAAC,CAAC,GAAI,EAAE,CAC3C,MAAO,CAAAD,SAAS,GAAK,EAAE,EAAI,CAAC,QAAQ,CAACE,IAAI,CAACF,SAAS,CAAC,CACxD,CAAC,CAAC,CAEFrC,YAAY,CAACkC,cAAc,CAAC,CAC5BtB,aAAa,CAAC,KAAK,CAAC,CACxB,CAAE,MAAO4B,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDE,gBAAgB,CAAC,2BAA2B,CAAE,OAAO,CAAC,CAC1D,CAAC,OAAS,CACNhC,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAAE,EAAE,CAAC,CAENxF,SAAS,CAAC,IAAM,CACZ6G,cAAc,CAAC,CAAC,CACpB,CAAC,CAAE,CAACA,cAAc,CAAC,CAAC,CAEpB,KAAM,CAAAY,sBAAsB,CAAGxH,WAAW,CAAC,CAACyH,OAAO,CAAEC,UAAU,GAAK,CAChE7C,YAAY,CAAC8C,aAAa,EACtBA,aAAa,CAAC5B,GAAG,CAACC,GAAG,EACjBA,GAAG,CAAC3C,OAAO,GAAKoE,OAAO,CAAAG,aAAA,CAAAA,aAAA,IACZ5B,GAAG,MAAE,CAAC0B,UAAU,EAAG,CAAC1B,GAAG,CAAC0B,UAAU,CAAC,GACxC1B,GACV,CACJ,CAAC,CACDP,aAAa,CAAC,IAAI,CAAC,CACvB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAoC,UAAU,CAAG7H,WAAW,CAAC,SAAY,CACvC,GAAI,CACAuF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAvD,KAAK,CAAC8F,IAAI,CAAC,4BAA4B,CACzC,CAAEC,WAAW,CAAEnD,SAAU,CAAC,CAC1B,CACIoD,OAAO,CAAE,CACL,eAAe,WAAAC,MAAA,CAAYC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC5D,CACJ,CACJ,CAAC,CAED;AACA,KAAM,CAAAxD,sBAAsB,CAAC,CAAC,CAE9B4C,gBAAgB,CAAC,wFAAwF,CAAE,SAAS,CAAC,CACrH9B,aAAa,CAAC,KAAK,CAAC,CACxB,CAAE,MAAO4B,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDE,gBAAgB,CAAC,8BAA8B,CAAE,OAAO,CAAC,CAC7D,CAAC,OAAS,CACNhC,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAAE,CAACX,SAAS,CAAED,sBAAsB,CAAC,CAAC,CAEvC,KAAM,CAAA4C,gBAAgB,CAAGvH,WAAW,CAAC,CAACkF,IAAI,CAAEC,IAAI,GAAK,CACjDF,UAAU,CAAC,CAAEC,IAAI,CAAEC,IAAK,CAAC,CAAC,CAC1BE,cAAc,CAAC,IAAI,CAAC,CACxB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA+C,kBAAkB,CAAGpI,WAAW,CAAEqI,CAAC,EAAK,CAC1CtD,aAAa,CAACsD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CACjC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,sBAAsB,CAAGxI,WAAW,CAAEqI,CAAC,EAAK,CAC9C1C,mBAAmB,CAAC0C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CACvC,CAAC,CAAE,EAAE,CAAC,CAEN,mBACIjG,KAAA,CAACpC,GAAG,EAAC+C,EAAE,CAAE,CAAEwF,CAAC,CAAE,CAAE,CAAE,CAAAtF,QAAA,eACdf,IAAA,CAACjC,IAAI,EAACuI,SAAS,CAAE,CAAE,CAAAvF,QAAA,cACfb,KAAA,CAAClC,WAAW,EAAA+C,QAAA,eACRb,KAAA,CAACpC,GAAG,EAAC+C,EAAE,CAAE,CAAE0F,EAAE,CAAE,CAAE,CAAE,CAAAxF,QAAA,eACfb,KAAA,CAACpC,GAAG,EAAC+C,EAAE,CAAE,CACLW,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BI,UAAU,CAAE,QAAQ,CACpB0E,EAAE,CAAE,CACR,CAAE,CAAAxF,QAAA,eACEf,IAAA,CAACxB,UAAU,EAAC4C,OAAO,CAAC,IAAI,CAACoF,SAAS,CAAC,IAAI,CACnC3F,EAAE,CAAE,CACAM,KAAK,CAAE,cAAc,CACrBsF,UAAU,CAAE,MAChB,CAAE,CAAA1F,QAAA,CACL,sBAED,CAAY,CAAC,cACbb,KAAA,CAACpC,GAAG,EAAAiD,QAAA,eACAf,IAAA,CAACzB,MAAM,EACHmI,SAAS,cAAE1G,IAAA,CAACL,WAAW,GAAE,CAAE,CAC3BgH,OAAO,CAAEnC,cAAe,CACxB3D,EAAE,CAAE,CAAE+F,EAAE,CAAE,CAAE,CAAE,CACd3E,QAAQ,CAAEiB,OAAQ,CAAAnC,QAAA,CACrB,SAED,CAAQ,CAAC,cACTf,IAAA,CAACzB,MAAM,EACH6C,OAAO,CAAC,WAAW,CACnBsF,SAAS,cAAE1G,IAAA,CAACP,QAAQ,GAAE,CAAE,CACxBkH,OAAO,CAAElB,UAAW,CACpBxD,QAAQ,CAAE,CAACmB,UAAU,EAAIF,OAAQ,CACjC/B,KAAK,CAAC,SAAS,CAAAJ,QAAA,CAClB,cAED,CAAQ,CAAC,EACR,CAAC,EACL,CAAC,cAENb,KAAA,CAACd,IAAI,EAACyH,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA/F,QAAA,eACvBf,IAAA,CAACZ,IAAI,EAAC2H,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlG,QAAA,cACrBf,IAAA,CAACpB,SAAS,EACNsI,SAAS,MACT9F,OAAO,CAAC,UAAU,CAClB+F,WAAW,CAAC,gDAAgD,CAC5DhB,KAAK,CAAEzD,UAAW,CAClBV,QAAQ,CAAEgE,kBAAmB,CAC7BoB,UAAU,CAAE,CACRC,cAAc,cACVrH,IAAA,CAACnB,cAAc,EAACyI,QAAQ,CAAC,OAAO,CAAAvG,QAAA,cAC5Bf,IAAA,CAACT,UAAU,EAAC4B,KAAK,CAAC,QAAQ,CAAE,CAAC,CACjB,CAExB,CAAE,CACL,CAAC,CACA,CAAC,cACPnB,IAAA,CAACZ,IAAI,EAAC2H,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlG,QAAA,cACrBb,KAAA,CAAClB,WAAW,EAACkI,SAAS,MAAAnG,QAAA,eAClBf,IAAA,CAACf,UAAU,EAAA8B,QAAA,CAAC,YAAU,CAAY,CAAC,cACnCb,KAAA,CAAChB,MAAM,EACHiH,KAAK,CAAE7C,gBAAiB,CACxBtB,QAAQ,CAAEoE,sBAAuB,CACjCpF,KAAK,CAAC,YAAY,CAAAD,QAAA,eAElBf,IAAA,CAACb,QAAQ,EAACgH,KAAK,CAAC,KAAK,CAAApF,QAAA,CAAC,iBAAe,CAAU,CAAC,CAC/CyC,WAAW,CAACG,GAAG,CAAC4D,IAAI,eACjBvH,IAAA,CAACb,QAAQ,EAAYgH,KAAK,CAAEoB,IAAK,CAAAxG,QAAA,CAC5BwG,IAAI,EADMA,IAEL,CACb,CAAC,EACE,CAAC,EACA,CAAC,CACZ,CAAC,EACL,CAAC,EACN,CAAC,CAELrE,OAAO,cACJlD,IAAA,CAAClC,GAAG,EAAC+C,EAAE,CAAE,CAAEW,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAE4E,CAAC,CAAE,CAAE,CAAE,CAAAtF,QAAA,cACzDf,IAAA,CAACrB,gBAAgB,GAAE,CAAC,CACnB,CAAC,cAENqB,IAAA,CAAC5B,cAAc,EAACoI,SAAS,CAAE1H,KAAM,CAACwH,SAAS,CAAE,CAAE,CAAAvF,QAAA,cAC3Cb,KAAA,CAACjC,KAAK,EAAA8C,QAAA,eACFf,IAAA,CAAC3B,SAAS,EAAA0C,QAAA,cACNb,KAAA,CAAC5B,QAAQ,EAACuC,EAAE,CAAE,CAAEC,OAAO,CAAE,eAAgB,CAAE,CAAAC,QAAA,eACvCf,IAAA,CAAC7B,SAAS,EAAA4C,QAAA,CAAC,eAAa,CAAW,CAAC,cACpCf,IAAA,CAAC7B,SAAS,EAAA4C,QAAA,CAAC,MAAI,CAAW,CAAC,cAC3Bf,IAAA,CAAC7B,SAAS,EAAA4C,QAAA,CAAC,YAAU,CAAW,CAAC,cACjCf,IAAA,CAAC7B,SAAS,EAACoD,KAAK,CAAC,QAAQ,CAAAR,QAAA,CAAC,aAAW,CAAW,CAAC,EAC3C,CAAC,CACJ,CAAC,cACZf,IAAA,CAAC9B,SAAS,EAAA6C,QAAA,CACL+C,iBAAiB,CAACH,GAAG,CAAElD,QAAQ,OAAA+G,mBAAA,oBAC5BxH,IAAA,CAACG,WAAW,EAERM,QAAQ,CAAEA,QAAS,CACnBC,kBAAkB,CAAE0E,sBAAuB,CAC3CzE,OAAO,EAAA6G,mBAAA,CAAE/G,QAAQ,CAACa,QAAQ,UAAAkG,mBAAA,iBAAjBA,mBAAA,CAAmBtF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAE,EAHvD1B,QAAQ,CAACQ,OAIjB,CAAC,EACL,CAAC,CACK,CAAC,EACT,CAAC,CACI,CACnB,EACQ,CAAC,CACZ,CAAC,cAEPjB,IAAA,CAACtB,QAAQ,EACL+I,IAAI,CAAEzE,WAAY,CAClB0E,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAEA,CAAA,GAAM1E,cAAc,CAAC,KAAK,CAAE,CACrC2E,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAE,CAAA/G,QAAA,cAE1Df,IAAA,CAACvB,KAAK,EACFsJ,QAAQ,CAAEnF,OAAO,CAACG,IAAK,CACvB4E,OAAO,CAAEA,CAAA,GAAM1E,cAAc,CAAC,KAAK,CAAE,CACrCpC,EAAE,CAAE,CAAEmH,KAAK,CAAE,MAAO,CAAE,CACtB5G,OAAO,CAAC,QAAQ,CAAAL,QAAA,CAEf6B,OAAO,CAACE,IAAI,CACV,CAAC,CACF,CAAC,EACV,CAAC,CAEd,CAAC,CAED,cAAe,CAAAR,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}