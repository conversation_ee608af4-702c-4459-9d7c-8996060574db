{"version": 3, "file": "static/css/main.dbde08c4.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CAGA,IAEE,WAAY,CADZ,cAEF,CAGA,oBAGE,UAAW,CAEX,SAAU,CADV,eAAgB,CAHhB,iBAAkB,CAClB,SAAU,CAIV,UACF,CAGA,EACE,qBAAsB,CACtB,uDACF,CAEA,gBACE,WACF", "sources": ["index.css"], "sourcesContent": ["body {\r\n  margin: 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n    monospace;\r\n}\r\n\r\n/* Ensure images are loaded properly */\r\nimg {\r\n  max-width: 100%;\r\n  height: auto;\r\n}\r\n\r\n/* Background image preload */\r\n.preload-background {\r\n  position: absolute;\r\n  width: 1px;\r\n  height: 1px;\r\n  overflow: hidden;\r\n  opacity: 0;\r\n  z-index: -1;\r\n}\r\n\r\n/* Smooth transitions */\r\n* {\r\n  box-sizing: border-box;\r\n  transition: background-color 0.3s ease, transform 0.3s ease;\r\n}\r\n\r\nhtml, body, #root {\r\n  height: 100%;\r\n} "], "names": [], "sourceRoot": ""}