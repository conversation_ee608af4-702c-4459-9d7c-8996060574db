{"ast": null, "code": "export function areArraysEqual(array1, array2) {\n  let itemComparer = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (a, b) => a === b;\n  return array1.length === array2.length && array1.every((value, index) => itemComparer(value, array2[index]));\n}", "map": {"version": 3, "names": ["areArraysEqual", "array1", "array2", "itemComparer", "arguments", "length", "undefined", "a", "b", "every", "value", "index"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/utils/areArraysEqual.js"], "sourcesContent": ["export function areArraysEqual(array1, array2, itemComparer = (a, b) => a === b) {\n  return array1.length === array2.length && array1.every((value, index) => itemComparer(value, array2[index]));\n}"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAoC;EAAA,IAAlCC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAACG,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC;EAC7E,OAAOP,MAAM,CAACI,MAAM,KAAKH,MAAM,CAACG,MAAM,IAAIJ,MAAM,CAACQ,KAAK,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKR,YAAY,CAACO,KAAK,EAAER,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC;AAC9G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}