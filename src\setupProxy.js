const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Determine the backend URL based on the current host
  const getBackendUrl = (req) => {
    const host = req.get('host');
    if (host) {
      const hostname = host.split(':')[0];
      // If accessing from localhost, use localhost backend
      if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return 'http://localhost:1976';
      }
      // Otherwise, use the same hostname with backend port
      return `http://${hostname}:1976`;
    }
    // Fallback to localhost
    return 'http://localhost:1976';
  };

  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:1976', // Default target
      changeOrigin: true,
      pathRewrite: {
        '^/api': '/api'
      },
      router: (req) => {
        // Dynamically determine the backend URL
        const backendUrl = getBackendUrl(req);
        console.log(`Proxying ${req.method} ${req.url} to ${backendUrl}`);
        return backendUrl;
      },
      onProxyReq: (proxyReq, req, res) => {
        // Log proxy requests for debugging
        console.log('Proxying request:', req.method, req.url, 'to', getBackendUrl(req));
      },
      onError: (err, req, res) => {
        console.error('Proxy error:', err);
        res.writeHead(500, {
          'Content-Type': 'text/plain',
        });
        res.end('Backend connection error: ' + err.message);
      }
    })
  );
};