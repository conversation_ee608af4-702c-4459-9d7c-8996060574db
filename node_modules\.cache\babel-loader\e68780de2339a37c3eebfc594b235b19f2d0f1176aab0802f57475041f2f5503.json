{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getTabPanelUtilityClass(slot) {\n  return generateUtilityClass('MuiTabPanel', slot);\n}\nexport const tabPanelClasses = generateUtilityClasses('MuiTabPanel', ['root', 'hidden']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabPanelUtilityClass", "slot", "tabPanelClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/TabPanel/tabPanelClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getTabPanelUtilityClass(slot) {\n  return generateUtilityClass('MuiTabPanel', slot);\n}\nexport const tabPanelClasses = generateUtilityClasses('MuiTabPanel', ['root', 'hidden']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,OAAO,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}