{"ast": null, "code": "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n  get name() {\n    return \"webtransport\";\n  }\n  doOpen() {\n    try {\n      // @ts-ignore\n      this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n    } catch (err) {\n      return this.emitReserved(\"error\", err);\n    }\n    this._transport.closed.then(() => {\n      this.onClose();\n    }).catch(err => {\n      this.onError(\"webtransport error\", err);\n    });\n    // note: we could have used async/await, but that would require some additional polyfills\n    this._transport.ready.then(() => {\n      this._transport.createBidirectionalStream().then(stream => {\n        const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n        const reader = stream.readable.pipeThrough(decoderStream).getReader();\n        const encoderStream = createPacketEncoderStream();\n        encoderStream.readable.pipeTo(stream.writable);\n        this._writer = encoderStream.writable.getWriter();\n        const read = () => {\n          reader.read().then(({\n            done,\n            value\n          }) => {\n            if (done) {\n              return;\n            }\n            this.onPacket(value);\n            read();\n          }).catch(err => {});\n        };\n        read();\n        const packet = {\n          type: \"open\"\n        };\n        if (this.query.sid) {\n          packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n        }\n        this._writer.write(packet).then(() => this.onOpen());\n      });\n    });\n  }\n  write(packets) {\n    this.writable = false;\n    for (let i = 0; i < packets.length; i++) {\n      const packet = packets[i];\n      const lastPacket = i === packets.length - 1;\n      this._writer.write(packet).then(() => {\n        if (lastPacket) {\n          nextTick(() => {\n            this.writable = true;\n            this.emitReserved(\"drain\");\n          }, this.setTimeoutFn);\n        }\n      });\n    }\n  }\n  doClose() {\n    var _a;\n    (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n  }\n}", "map": {"version": 3, "names": ["Transport", "nextTick", "createPacketDecoderStream", "createPacketEncoderStream", "WT", "name", "doOpen", "_transport", "WebTransport", "createUri", "opts", "transportOptions", "err", "emit<PERSON><PERSON><PERSON><PERSON>", "closed", "then", "onClose", "catch", "onError", "ready", "createBidirectionalStream", "stream", "decoderStream", "Number", "MAX_SAFE_INTEGER", "socket", "binaryType", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "writable", "_writer", "getWriter", "read", "done", "value", "onPacket", "packet", "type", "query", "sid", "data", "write", "onOpen", "packets", "i", "length", "lastPacket", "setTimeoutFn", "doClose", "_a", "close"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/engine.io-client/build/esm/transports/webtransport.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,yBAAyB,EAAEC,yBAAyB,QAAS,kBAAkB;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,SAASJ,SAAS,CAAC;EAC9B,IAAIK,IAAIA,CAAA,EAAG;IACP,OAAO,cAAc;EACzB;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI;MACA;MACA,IAAI,CAACC,UAAU,GAAG,IAAIC,YAAY,CAAC,IAAI,CAACC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,CAACC,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACN,IAAI,CAAC,CAAC;IACtG,CAAC,CACD,OAAOO,GAAG,EAAE;MACR,OAAO,IAAI,CAACC,YAAY,CAAC,OAAO,EAAED,GAAG,CAAC;IAC1C;IACA,IAAI,CAACL,UAAU,CAACO,MAAM,CACjBC,IAAI,CAAC,MAAM;MACZ,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC,CACGC,KAAK,CAAEL,GAAG,IAAK;MAChB,IAAI,CAACM,OAAO,CAAC,oBAAoB,EAAEN,GAAG,CAAC;IAC3C,CAAC,CAAC;IACF;IACA,IAAI,CAACL,UAAU,CAACY,KAAK,CAACJ,IAAI,CAAC,MAAM;MAC7B,IAAI,CAACR,UAAU,CAACa,yBAAyB,CAAC,CAAC,CAACL,IAAI,CAAEM,MAAM,IAAK;QACzD,MAAMC,aAAa,GAAGpB,yBAAyB,CAACqB,MAAM,CAACC,gBAAgB,EAAE,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC;QAChG,MAAMC,MAAM,GAAGN,MAAM,CAACO,QAAQ,CAACC,WAAW,CAACP,aAAa,CAAC,CAACQ,SAAS,CAAC,CAAC;QACrE,MAAMC,aAAa,GAAG5B,yBAAyB,CAAC,CAAC;QACjD4B,aAAa,CAACH,QAAQ,CAACI,MAAM,CAACX,MAAM,CAACY,QAAQ,CAAC;QAC9C,IAAI,CAACC,OAAO,GAAGH,aAAa,CAACE,QAAQ,CAACE,SAAS,CAAC,CAAC;QACjD,MAAMC,IAAI,GAAGA,CAAA,KAAM;UACfT,MAAM,CACDS,IAAI,CAAC,CAAC,CACNrB,IAAI,CAAC,CAAC;YAAEsB,IAAI;YAAEC;UAAM,CAAC,KAAK;YAC3B,IAAID,IAAI,EAAE;cACN;YACJ;YACA,IAAI,CAACE,QAAQ,CAACD,KAAK,CAAC;YACpBF,IAAI,CAAC,CAAC;UACV,CAAC,CAAC,CACGnB,KAAK,CAAEL,GAAG,IAAK,CACpB,CAAC,CAAC;QACN,CAAC;QACDwB,IAAI,CAAC,CAAC;QACN,MAAMI,MAAM,GAAG;UAAEC,IAAI,EAAE;QAAO,CAAC;QAC/B,IAAI,IAAI,CAACC,KAAK,CAACC,GAAG,EAAE;UAChBH,MAAM,CAACI,IAAI,GAAG,WAAW,IAAI,CAACF,KAAK,CAACC,GAAG,IAAI;QAC/C;QACA,IAAI,CAACT,OAAO,CAACW,KAAK,CAACL,MAAM,CAAC,CAACzB,IAAI,CAAC,MAAM,IAAI,CAAC+B,MAAM,CAAC,CAAC,CAAC;MACxD,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAD,KAAKA,CAACE,OAAO,EAAE;IACX,IAAI,CAACd,QAAQ,GAAG,KAAK;IACrB,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAMR,MAAM,GAAGO,OAAO,CAACC,CAAC,CAAC;MACzB,MAAME,UAAU,GAAGF,CAAC,KAAKD,OAAO,CAACE,MAAM,GAAG,CAAC;MAC3C,IAAI,CAACf,OAAO,CAACW,KAAK,CAACL,MAAM,CAAC,CAACzB,IAAI,CAAC,MAAM;QAClC,IAAImC,UAAU,EAAE;UACZjD,QAAQ,CAAC,MAAM;YACX,IAAI,CAACgC,QAAQ,GAAG,IAAI;YACpB,IAAI,CAACpB,YAAY,CAAC,OAAO,CAAC;UAC9B,CAAC,EAAE,IAAI,CAACsC,YAAY,CAAC;QACzB;MACJ,CAAC,CAAC;IACN;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAAC9C,UAAU,MAAM,IAAI,IAAI8C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,KAAK,CAAC,CAAC;EAC1E;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}