{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(null);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [initialized, setInitialized] = useState(false);\n\n  // Initialize auth state on mount\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        var _response$data;\n        const token = localStorage.getItem('token');\n        console.log('Initializing auth with token:', token ? 'exists' : 'none');\n        if (!token) {\n          setUser(null);\n          setLoading(false);\n          setInitialized(true);\n          return;\n        }\n\n        // Set token in axios defaults\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n        // Verify token and get user data\n        console.log('Verifying token...');\n        const response = await axios.get('/api/auth/me');\n        console.log('Verification response:', response.data);\n        if ((_response$data = response.data) !== null && _response$data !== void 0 && _response$data.user) {\n          // Ensure admin users always have dashboard permission\n          const userData = response.data.user;\n          if (userData.isAdmin) {\n            if (!userData.permissions) userData.permissions = {};\n            userData.permissions.canViewDashboard = true;\n          }\n          setUser({\n            empCode: userData.empCode,\n            name: userData.name,\n            department: userData.department,\n            isAdmin: userData.isAdmin,\n            permissions: userData.permissions\n          });\n          console.log('User state set:', userData);\n        } else {\n          console.log('No user data in response, clearing auth state');\n          localStorage.removeItem('token');\n          delete axios.defaults.headers.common['Authorization'];\n          setUser(null);\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n        localStorage.removeItem('token');\n        delete axios.defaults.headers.common['Authorization'];\n        setUser(null);\n      } finally {\n        setLoading(false);\n        setInitialized(true);\n      }\n    };\n    initializeAuth();\n  }, []);\n  const login = async (empCode, password) => {\n    try {\n      setLoading(true);\n      console.log('AuthContext: Starting login process');\n      const response = await axios.post('/api/auth/login', {\n        empCode: empCode.trim(),\n        password: password.trim()\n      });\n      console.log('AuthContext: Login response received', response.data);\n      const {\n        token,\n        user: userData\n      } = response.data;\n      if (!token || !userData) {\n        throw new Error('Invalid response from server');\n      }\n\n      // Store token\n      localStorage.setItem('token', token);\n      console.log('AuthContext: Token stored');\n\n      // Set token in axios headers\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n      // Ensure admin users always have dashboard permission\n      if (userData.isAdmin) {\n        if (!userData.permissions) userData.permissions = {};\n        userData.permissions.canViewDashboard = true;\n      }\n\n      // Update user state with proper structure\n      const userState = {\n        empCode: userData.empCode,\n        name: userData.empName,\n        department: userData.deptName,\n        isAdmin: userData.isAdmin,\n        permissions: userData.permissions\n      };\n      setUser(userState);\n      console.log('AuthContext: User state updated', userState);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response3$data;\n      console.error('AuthContext: Login error:', error);\n\n      // Clear any partial state\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      setUser(null);\n\n      // Better error messages for mobile\n      let errorMessage = 'Login failed';\n      if (error.code === 'NETWORK_ERROR' || !error.response) {\n        errorMessage = 'Network connection error. Please check your internet connection and try again.';\n      } else if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        var _error$response$data;\n        errorMessage = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Invalid employee code or password';\n      } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) >= 500) {\n        errorMessage = 'Server error. Please try again in a few moments.';\n      } else if ((_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.message) {\n        errorMessage = error.response.data.message;\n      }\n      return {\n        success: false,\n        message: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const logout = () => {\n    // Clear token\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n\n    // Clear user state\n    setUser(null);\n  };\n  const value = {\n    user,\n    loading,\n    initialized,\n    login,\n    logout\n  };\n\n  // Don't render children until auth is initialized\n  if (!initialized) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"FKjX8Ko0uy7WJEUSeSk+H5pWLtA=\");\n_c = AuthProvider;\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "initialized", "setInitialized", "initializeAuth", "_response$data", "token", "localStorage", "getItem", "console", "log", "defaults", "headers", "common", "response", "get", "data", "userData", "isAdmin", "permissions", "canViewDashboard", "empCode", "name", "department", "removeItem", "error", "login", "password", "post", "trim", "setItem", "userState", "empName", "deptName", "success", "_error$response", "_error$response2", "_error$response3", "_error$response3$data", "errorMessage", "code", "status", "_error$response$data", "message", "logout", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport axios from '../utils/axiosConfig';\r\n\r\nconst AuthContext = createContext(null);\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (!context) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const AuthProvider = ({ children }) => {\r\n  const [user, setUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [initialized, setInitialized] = useState(false);\r\n\r\n  // Initialize auth state on mount\r\n  useEffect(() => {\r\n    const initializeAuth = async () => {\r\n      try {\r\n        const token = localStorage.getItem('token');\r\n        console.log('Initializing auth with token:', token ? 'exists' : 'none');\r\n        \r\n        if (!token) {\r\n          setUser(null);\r\n          setLoading(false);\r\n          setInitialized(true);\r\n          return;\r\n        }\r\n\r\n        // Set token in axios defaults\r\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n        \r\n        // Verify token and get user data\r\n        console.log('Verifying token...');\r\n        const response = await axios.get('/api/auth/me');\r\n        console.log('Verification response:', response.data);\r\n        \r\n        if (response.data?.user) {\r\n          // Ensure admin users always have dashboard permission\r\n          const userData = response.data.user;\r\n          if (userData.isAdmin) {\r\n            if (!userData.permissions) userData.permissions = {};\r\n            userData.permissions.canViewDashboard = true;\r\n          }\r\n\r\n          setUser({\r\n            empCode: userData.empCode,\r\n            name: userData.name,\r\n            department: userData.department,\r\n            isAdmin: userData.isAdmin,\r\n            permissions: userData.permissions\r\n          });\r\n          console.log('User state set:', userData);\r\n        } else {\r\n          console.log('No user data in response, clearing auth state');\r\n          localStorage.removeItem('token');\r\n          delete axios.defaults.headers.common['Authorization'];\r\n          setUser(null);\r\n        }\r\n      } catch (error) {\r\n        console.error('Auth initialization error:', error);\r\n        localStorage.removeItem('token');\r\n        delete axios.defaults.headers.common['Authorization'];\r\n        setUser(null);\r\n      } finally {\r\n        setLoading(false);\r\n        setInitialized(true);\r\n      }\r\n    };\r\n\r\n    initializeAuth();\r\n  }, []);\r\n\r\n  const login = async (empCode, password) => {\r\n    try {\r\n      setLoading(true);\r\n      console.log('AuthContext: Starting login process');\r\n\r\n      const response = await axios.post('/api/auth/login', {\r\n        empCode: empCode.trim(),\r\n        password: password.trim()\r\n      });\r\n\r\n      console.log('AuthContext: Login response received', response.data);\r\n\r\n      const { token, user: userData } = response.data;\r\n      if (!token || !userData) {\r\n        throw new Error('Invalid response from server');\r\n      }\r\n\r\n      // Store token\r\n      localStorage.setItem('token', token);\r\n      console.log('AuthContext: Token stored');\r\n\r\n      // Set token in axios headers\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n\r\n      // Ensure admin users always have dashboard permission\r\n      if (userData.isAdmin) {\r\n        if (!userData.permissions) userData.permissions = {};\r\n        userData.permissions.canViewDashboard = true;\r\n      }\r\n\r\n      // Update user state with proper structure\r\n      const userState = {\r\n        empCode: userData.empCode,\r\n        name: userData.empName,\r\n        department: userData.deptName,\r\n        isAdmin: userData.isAdmin,\r\n        permissions: userData.permissions\r\n      };\r\n\r\n      setUser(userState);\r\n      console.log('AuthContext: User state updated', userState);\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('AuthContext: Login error:', error);\r\n\r\n      // Clear any partial state\r\n      localStorage.removeItem('token');\r\n      delete axios.defaults.headers.common['Authorization'];\r\n      setUser(null);\r\n\r\n      // Better error messages for mobile\r\n      let errorMessage = 'Login failed';\r\n\r\n      if (error.code === 'NETWORK_ERROR' || !error.response) {\r\n        errorMessage = 'Network connection error. Please check your internet connection and try again.';\r\n      } else if (error.response?.status === 401) {\r\n        errorMessage = error.response.data?.message || 'Invalid employee code or password';\r\n      } else if (error.response?.status >= 500) {\r\n        errorMessage = 'Server error. Please try again in a few moments.';\r\n      } else if (error.response?.data?.message) {\r\n        errorMessage = error.response.data.message;\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: errorMessage\r\n      };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const logout = () => {\r\n    // Clear token\r\n    localStorage.removeItem('token');\r\n    delete axios.defaults.headers.common['Authorization'];\r\n    \r\n    // Clear user state\r\n    setUser(null);\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    loading,\r\n    initialized,\r\n    login,\r\n    logout\r\n  };\r\n\r\n  // Don't render children until auth is initialized\r\n  if (!initialized) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport default AuthContext; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,IAAI,CAAC;AAEvC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QAAA,IAAAC,cAAA;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEJ,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;QAEvE,IAAI,CAACA,KAAK,EAAE;UACVP,OAAO,CAAC,IAAI,CAAC;UACbE,UAAU,CAAC,KAAK,CAAC;UACjBE,cAAc,CAAC,IAAI,CAAC;UACpB;QACF;;QAEA;QACAhB,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUP,KAAK,EAAE;;QAElE;QACAG,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;QACjC,MAAMI,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,GAAG,CAAC,cAAc,CAAC;QAChDN,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,QAAQ,CAACE,IAAI,CAAC;QAEpD,KAAAX,cAAA,GAAIS,QAAQ,CAACE,IAAI,cAAAX,cAAA,eAAbA,cAAA,CAAeP,IAAI,EAAE;UACvB;UACA,MAAMmB,QAAQ,GAAGH,QAAQ,CAACE,IAAI,CAAClB,IAAI;UACnC,IAAImB,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAACD,QAAQ,CAACE,WAAW,EAAEF,QAAQ,CAACE,WAAW,GAAG,CAAC,CAAC;YACpDF,QAAQ,CAACE,WAAW,CAACC,gBAAgB,GAAG,IAAI;UAC9C;UAEArB,OAAO,CAAC;YACNsB,OAAO,EAAEJ,QAAQ,CAACI,OAAO;YACzBC,IAAI,EAAEL,QAAQ,CAACK,IAAI;YACnBC,UAAU,EAAEN,QAAQ,CAACM,UAAU;YAC/BL,OAAO,EAAED,QAAQ,CAACC,OAAO;YACzBC,WAAW,EAAEF,QAAQ,CAACE;UACxB,CAAC,CAAC;UACFV,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEO,QAAQ,CAAC;QAC1C,CAAC,MAAM;UACLR,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5DH,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;UAChC,OAAOrC,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;UACrDd,OAAO,CAAC,IAAI,CAAC;QACf;MACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;QACdhB,OAAO,CAACgB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDlB,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;QAChC,OAAOrC,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;QACrDd,OAAO,CAAC,IAAI,CAAC;MACf,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;QACjBE,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC;IAEDC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMsB,KAAK,GAAG,MAAAA,CAAOL,OAAO,EAAEM,QAAQ,KAAK;IACzC,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChBQ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAElD,MAAMI,QAAQ,GAAG,MAAM3B,KAAK,CAACyC,IAAI,CAAC,iBAAiB,EAAE;QACnDP,OAAO,EAAEA,OAAO,CAACQ,IAAI,CAAC,CAAC;QACvBF,QAAQ,EAAEA,QAAQ,CAACE,IAAI,CAAC;MAC1B,CAAC,CAAC;MAEFpB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEI,QAAQ,CAACE,IAAI,CAAC;MAElE,MAAM;QAAEV,KAAK;QAAER,IAAI,EAAEmB;MAAS,CAAC,GAAGH,QAAQ,CAACE,IAAI;MAC/C,IAAI,CAACV,KAAK,IAAI,CAACW,QAAQ,EAAE;QACvB,MAAM,IAAIvB,KAAK,CAAC,8BAA8B,CAAC;MACjD;;MAEA;MACAa,YAAY,CAACuB,OAAO,CAAC,OAAO,EAAExB,KAAK,CAAC;MACpCG,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACAvB,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUP,KAAK,EAAE;;MAElE;MACA,IAAIW,QAAQ,CAACC,OAAO,EAAE;QACpB,IAAI,CAACD,QAAQ,CAACE,WAAW,EAAEF,QAAQ,CAACE,WAAW,GAAG,CAAC,CAAC;QACpDF,QAAQ,CAACE,WAAW,CAACC,gBAAgB,GAAG,IAAI;MAC9C;;MAEA;MACA,MAAMW,SAAS,GAAG;QAChBV,OAAO,EAAEJ,QAAQ,CAACI,OAAO;QACzBC,IAAI,EAAEL,QAAQ,CAACe,OAAO;QACtBT,UAAU,EAAEN,QAAQ,CAACgB,QAAQ;QAC7Bf,OAAO,EAAED,QAAQ,CAACC,OAAO;QACzBC,WAAW,EAAEF,QAAQ,CAACE;MACxB,CAAC;MAEDpB,OAAO,CAACgC,SAAS,CAAC;MAClBtB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEqB,SAAS,CAAC;MAEzD,OAAO;QAAEG,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAU,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd7B,OAAO,CAACgB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;MAEjD;MACAlB,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;MAChC,OAAOrC,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;MACrDd,OAAO,CAAC,IAAI,CAAC;;MAEb;MACA,IAAIwC,YAAY,GAAG,cAAc;MAEjC,IAAId,KAAK,CAACe,IAAI,KAAK,eAAe,IAAI,CAACf,KAAK,CAACX,QAAQ,EAAE;QACrDyB,YAAY,GAAG,gFAAgF;MACjG,CAAC,MAAM,IAAI,EAAAJ,eAAA,GAAAV,KAAK,CAACX,QAAQ,cAAAqB,eAAA,uBAAdA,eAAA,CAAgBM,MAAM,MAAK,GAAG,EAAE;QAAA,IAAAC,oBAAA;QACzCH,YAAY,GAAG,EAAAG,oBAAA,GAAAjB,KAAK,CAACX,QAAQ,CAACE,IAAI,cAAA0B,oBAAA,uBAAnBA,oBAAA,CAAqBC,OAAO,KAAI,mCAAmC;MACpF,CAAC,MAAM,IAAI,EAAAP,gBAAA,GAAAX,KAAK,CAACX,QAAQ,cAAAsB,gBAAA,uBAAdA,gBAAA,CAAgBK,MAAM,KAAI,GAAG,EAAE;QACxCF,YAAY,GAAG,kDAAkD;MACnE,CAAC,MAAM,KAAAF,gBAAA,GAAIZ,KAAK,CAACX,QAAQ,cAAAuB,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,eAApBA,qBAAA,CAAsBK,OAAO,EAAE;QACxCJ,YAAY,GAAGd,KAAK,CAACX,QAAQ,CAACE,IAAI,CAAC2B,OAAO;MAC5C;MAEA,OAAO;QACLT,OAAO,EAAE,KAAK;QACdS,OAAO,EAAEJ;MACX,CAAC;IACH,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2C,MAAM,GAAGA,CAAA,KAAM;IACnB;IACArC,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOrC,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;;IAErD;IACAd,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAM8C,KAAK,GAAG;IACZ/C,IAAI;IACJE,OAAO;IACPE,WAAW;IACXwB,KAAK;IACLkB;EACF,CAAC;;EAED;EACA,IAAI,CAAC1C,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,oBACEb,OAAA,CAACC,WAAW,CAACwD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAjD,QAAA,EAChCA;EAAQ;IAAAmD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACrD,GAAA,CAnKWF,YAAY;AAAAwD,EAAA,GAAZxD,YAAY;AAqKzB,eAAeL,WAAW;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}