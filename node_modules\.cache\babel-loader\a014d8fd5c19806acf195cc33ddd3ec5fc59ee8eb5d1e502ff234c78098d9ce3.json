{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\components\\\\PermissionNotification.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Snackbar, Alert, Slide } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SlideTransition(props) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    ...props,\n    direction: \"down\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 12\n  }, this);\n}\n_c = SlideTransition;\nconst PermissionNotification = () => {\n  _s();\n  const [open, setOpen] = useState(false);\n  const [message, setMessage] = useState('');\n  useEffect(() => {\n    // Create a global function to show permission update notifications\n    window.showPermissionUpdateNotification = msg => {\n      setMessage(msg);\n      setOpen(true);\n    };\n\n    // Cleanup\n    return () => {\n      delete window.showPermissionUpdateNotification;\n    };\n  }, []);\n  const handleClose = (event, reason) => {\n    if (reason === 'clickaway') {\n      return;\n    }\n    setOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Snackbar, {\n    open: open,\n    autoHideDuration: 5000,\n    onClose: handleClose,\n    anchorOrigin: {\n      vertical: 'top',\n      horizontal: 'center'\n    },\n    TransitionComponent: SlideTransition,\n    sx: {\n      mt: 8\n    } // Add margin top to avoid overlapping with app bar\n    ,\n    children: /*#__PURE__*/_jsxDEV(Alert, {\n      onClose: handleClose,\n      severity: \"success\",\n      variant: \"filled\",\n      sx: {\n        width: '100%'\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 9\n  }, this);\n};\n_s(PermissionNotification, \"7/LNLafE+BOvoy4sAU0RRYuVc6w=\");\n_c2 = PermissionNotification;\nexport default PermissionNotification;\nvar _c, _c2;\n$RefreshReg$(_c, \"SlideTransition\");\n$RefreshReg$(_c2, \"PermissionNotification\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Snackbar", "<PERSON><PERSON>", "Slide", "jsxDEV", "_jsxDEV", "SlideTransition", "props", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "PermissionNotification", "_s", "open", "<PERSON><PERSON><PERSON>", "message", "setMessage", "window", "showPermissionUpdateNotification", "msg", "handleClose", "event", "reason", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "TransitionComponent", "sx", "mt", "children", "severity", "variant", "width", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/components/PermissionNotification.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n    Snackbar,\n    Alert,\n    Slide\n} from '@mui/material';\n\nfunction SlideTransition(props) {\n    return <Slide {...props} direction=\"down\" />;\n}\n\nconst PermissionNotification = () => {\n    const [open, setOpen] = useState(false);\n    const [message, setMessage] = useState('');\n\n    useEffect(() => {\n        // Create a global function to show permission update notifications\n        window.showPermissionUpdateNotification = (msg) => {\n            setMessage(msg);\n            setOpen(true);\n        };\n\n        // Cleanup\n        return () => {\n            delete window.showPermissionUpdateNotification;\n        };\n    }, []);\n\n    const handleClose = (event, reason) => {\n        if (reason === 'clickaway') {\n            return;\n        }\n        setOpen(false);\n    };\n\n    return (\n        <Snackbar\n            open={open}\n            autoHideDuration={5000}\n            onClose={handleClose}\n            anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\n            TransitionComponent={SlideTransition}\n            sx={{ mt: 8 }} // Add margin top to avoid overlapping with app bar\n        >\n            <Alert \n                onClose={handleClose} \n                severity=\"success\" \n                variant=\"filled\"\n                sx={{ width: '100%' }}\n            >\n                {message}\n            </Alert>\n        </Snackbar>\n    );\n};\n\nexport default PermissionNotification;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACIC,QAAQ,EACRC,KAAK,EACLC,KAAK,QACF,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC5B,oBAAOF,OAAA,CAACF,KAAK;IAAA,GAAKI,KAAK;IAAEC,SAAS,EAAC;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAChD;AAACC,EAAA,GAFQP,eAAe;AAIxB,MAAMQ,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACZ;IACAoB,MAAM,CAACC,gCAAgC,GAAIC,GAAG,IAAK;MAC/CH,UAAU,CAACG,GAAG,CAAC;MACfL,OAAO,CAAC,IAAI,CAAC;IACjB,CAAC;;IAED;IACA,OAAO,MAAM;MACT,OAAOG,MAAM,CAACC,gCAAgC;IAClD,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACnC,IAAIA,MAAM,KAAK,WAAW,EAAE;MACxB;IACJ;IACAR,OAAO,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,oBACIZ,OAAA,CAACJ,QAAQ;IACLe,IAAI,EAAEA,IAAK;IACXU,gBAAgB,EAAE,IAAK;IACvBC,OAAO,EAAEJ,WAAY;IACrBK,YAAY,EAAE;MAAEC,QAAQ,EAAE,KAAK;MAAEC,UAAU,EAAE;IAAS,CAAE;IACxDC,mBAAmB,EAAEzB,eAAgB;IACrC0B,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE,CAAC;IAAA;IAAAC,QAAA,eAEf7B,OAAA,CAACH,KAAK;MACFyB,OAAO,EAAEJ,WAAY;MACrBY,QAAQ,EAAC,SAAS;MAClBC,OAAO,EAAC,QAAQ;MAChBJ,EAAE,EAAE;QAAEK,KAAK,EAAE;MAAO,CAAE;MAAAH,QAAA,EAErBhB;IAAO;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB,CAAC;AAACG,EAAA,CA3CID,sBAAsB;AAAAwB,GAAA,GAAtBxB,sBAAsB;AA6C5B,eAAeA,sBAAsB;AAAC,IAAAD,EAAA,EAAAyB,GAAA;AAAAC,YAAA,CAAA1B,EAAA;AAAA0B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}