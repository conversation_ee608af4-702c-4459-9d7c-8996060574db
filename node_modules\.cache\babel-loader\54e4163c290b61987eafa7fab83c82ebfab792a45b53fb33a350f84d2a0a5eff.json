{"ast": null, "code": "export * from './Dropdown';\nexport * from './Dropdown.types';", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Dropdown/index.js"], "sourcesContent": ["export * from './Dropdown';\nexport * from './Dropdown.types';"], "mappings": "AAAA,cAAc,YAAY;AAC1B,cAAc,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}