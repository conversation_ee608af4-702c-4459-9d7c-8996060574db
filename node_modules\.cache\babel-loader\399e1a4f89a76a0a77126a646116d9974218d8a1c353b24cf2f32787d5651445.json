{"ast": null, "code": "export const buildDeprecatedPropsWarning = message => {\n  let alreadyWarned = false;\n  if (process.env.NODE_ENV === 'production') {\n    return () => {};\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  return deprecatedProps => {\n    const deprecatedKeys = Object.entries(deprecatedProps).filter(_ref => {\n      let [, value] = _ref;\n      return value !== undefined;\n    }).map(_ref2 => {\n      let [key] = _ref2;\n      return \"- \".concat(key);\n    });\n    if (!alreadyWarned && deprecatedKeys.length > 0) {\n      alreadyWarned = true;\n      console.warn([cleanMessage, 'deprecated props observed:', ...deprecatedKeys].join('\\n'));\n    }\n  };\n};", "map": {"version": 3, "names": ["buildDeprecatedPropsWarning", "message", "alreadyWarned", "process", "env", "NODE_ENV", "cleanMessage", "Array", "isArray", "join", "deprecatedProps", "deprecatedKeys", "Object", "entries", "filter", "_ref", "value", "undefined", "map", "_ref2", "key", "concat", "length", "console", "warn"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/utils/warning.js"], "sourcesContent": ["export const buildDeprecatedPropsWarning = message => {\n  let alreadyWarned = false;\n\n  if (process.env.NODE_ENV === 'production') {\n    return () => {};\n  }\n\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  return deprecatedProps => {\n    const deprecatedKeys = Object.entries(deprecatedProps).filter(([, value]) => value !== undefined).map(([key]) => `- ${key}`);\n\n    if (!alreadyWarned && deprecatedKeys.length > 0) {\n      alreadyWarned = true;\n      console.warn([cleanMessage, 'deprecated props observed:', ...deprecatedKeys].join('\\n'));\n    }\n  };\n};"], "mappings": "AAAA,OAAO,MAAMA,2BAA2B,GAAGC,OAAO,IAAI;EACpD,IAAIC,aAAa,GAAG,KAAK;EAEzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAO,MAAM,CAAC,CAAC;EACjB;EAEA,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACP,OAAO,CAAC,GAAGA,OAAO,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAGR,OAAO;EAC1E,OAAOS,eAAe,IAAI;IACxB,MAAMC,cAAc,GAAGC,MAAM,CAACC,OAAO,CAACH,eAAe,CAAC,CAACI,MAAM,CAACC,IAAA;MAAA,IAAC,GAAGC,KAAK,CAAC,GAAAD,IAAA;MAAA,OAAKC,KAAK,KAAKC,SAAS;IAAA,EAAC,CAACC,GAAG,CAACC,KAAA;MAAA,IAAC,CAACC,GAAG,CAAC,GAAAD,KAAA;MAAA,YAAAE,MAAA,CAAUD,GAAG;IAAA,CAAE,CAAC;IAE5H,IAAI,CAAClB,aAAa,IAAIS,cAAc,CAACW,MAAM,GAAG,CAAC,EAAE;MAC/CpB,aAAa,GAAG,IAAI;MACpBqB,OAAO,CAACC,IAAI,CAAC,CAAClB,YAAY,EAAE,4BAA4B,EAAE,GAAGK,cAAc,CAAC,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1F;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}