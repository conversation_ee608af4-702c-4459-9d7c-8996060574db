{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { FormControlContext } from './FormControlContext';\n/**\n *\n * Demos:\n *\n * - [Form Control](https://mui.com/base-ui/react-form-control/#hook)\n *\n * API:\n *\n * - [useFormControlContext API](https://mui.com/base-ui/react-form-control/hooks-api/#use-form-control-context)\n */\nexport function useFormControlContext() {\n  return React.useContext(FormControlContext);\n}", "map": {"version": 3, "names": ["React", "FormControlContext", "useFormControlContext", "useContext"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/FormControl/useFormControlContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { FormControlContext } from './FormControlContext';\n/**\n *\n * Demos:\n *\n * - [Form Control](https://mui.com/base-ui/react-form-control/#hook)\n *\n * API:\n *\n * - [useFormControlContext API](https://mui.com/base-ui/react-form-control/hooks-api/#use-form-control-context)\n */\nexport function useFormControlContext() {\n  return React.useContext(FormControlContext);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAAA,EAAG;EACtC,OAAOF,KAAK,CAACG,UAAU,CAACF,kBAAkB,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}