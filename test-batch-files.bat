@echo off
setlocal enabledelayedexpansion
color 0F
title Test Batch Files - Internal Complaints Portal

echo.
echo ===============================================
echo    TESTING UPDATED BATCH FILES
echo ===============================================
echo.

echo [94m[TEST][0m Testing stop.bat functionality...
echo [93m[INFO][0m Running stop.bat to clean up any existing processes...
call stop.bat

echo.
echo [94m[TEST][0m Testing start.bat functionality...
echo [93m[INFO][0m Running start.bat to start the application...
echo [93m[WARNING][0m This will build the React app and start the server...
echo.

pause

call start.bat

echo.
echo [92m[SUCCESS][0m Batch file testing completed!
echo.
echo [94m[INSTRUCTIONS][0m
echo 1. The stop.bat should have killed all processes
echo 2. The start.bat should have:
echo    - Built the React app for production
echo    - Started the backend server on port 1976
echo    - Automatically opened the browser to http://localhost:1976
echo.
echo [93m[VERIFY][0m Check that:
echo - Browser opened automatically to http://localhost:1976
echo - Application loads correctly
echo - Login works with EMP-M / qwerty
echo - Dashboard shows proper icons (no emojis)
echo - Response time shows correct percentage
echo - Attachments have both View and Download buttons
echo.

pause
