const express = require('express');
const cors = require('cors');
const sql = require('mssql');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const http = require('http');
const socketIo = require('socket.io');

dotenv.config();

// JWT secret key
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Initialize express app
const app = express();

// Configure CORS with mobile-friendly settings
app.use(cors({
    origin: function (origin, callback) {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true);

        // Allow all origins for development
        return callback(null, true);
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
    credentials: true,
    optionsSuccessStatus: 200,
    maxAge: 86400 // Cache preflight for 24 hours
}));

// Add security headers
app.use((req, res, next) => {
    // Allow requests from any origin
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Credentials', 'true');
    next();
});

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(__dirname));

// Constants
const UPLOAD_PATH = '\\\\***************\\IcSoft Server Setup Ver 4.0 D\\Attachments\\COMPLAINT ATTACHMENT';

// SQL Server configuration
const config = {
    user: process.env.DB_USER || 'sa',
    password: process.env.DB_PASSWORD || 'PRK@1234$',
    server: process.env.DB_SERVER || 'WIN-PRK-SRV-01',
    database: process.env.DB_NAME || 'ICsoft',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        connectionTimeout: 30000,
        requestTimeout: 30000,
        pool: {
            max: 25,
            min: 5,
            idleTimeoutMillis: 300000,
            acquireTimeoutMillis: 30000
        },
        enableArithAbort: true,
        keepAlive: true,
        maxRetriesOnTransientErrors: 3,
        connectionRetryInterval: 1000
    }
};

// Create a connection pool
let pool;
async function getConnection() {
    try {
        if (!pool) {
            pool = await sql.connect(config);
            console.log('New database connection pool created');
            
            // Handle pool errors
            pool.on('error', err => {
                console.error('Database pool error:', err);
                pool = null;
            });
        }
        return pool;
    } catch (err) {
        console.error('Error creating connection pool:', err);
        pool = null;
        throw err;
    }
}

// Ensure upload directory exists
try {
    if (!fs.existsSync(UPLOAD_PATH)) {
        fs.mkdirSync(UPLOAD_PATH, { recursive: true });
    }
    console.log('Successfully connected to network share');
} catch (error) {
    console.error('Error accessing network share:', error);
}

// Multer storage configuration
const storage = multer.diskStorage({
    destination: function(req, file, cb) {
        cb(null, UPLOAD_PATH);
    },
    filename: function(req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'COMP-' + uniqueSuffix + path.extname(file.originalname));
    }
});

// Multer upload configuration
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB
    }
}).array('attachments');

// Middleware to handle file upload
const handleUpload = (req, res, next) => {
    upload(req, res, function(err) {
        if (err instanceof multer.MulterError) {
            // A Multer error occurred when uploading
            console.error('Multer error:', err);
            return res.status(400).json({
                error: true,
                message: `File upload error: ${err.message}`
            });
        } else if (err) {
            // An unknown error occurred
            console.error('Unknown upload error:', err);
            return res.status(500).json({
                error: true,
                message: 'Unknown error occurred during file upload'
            });
        }
        // Everything went fine
        next();
    });
};

// Initialize authorized departments
async function initializeAuthorizedDepartments(pool) {
    try {
        // First clear existing authorized departments
        await pool.request().query('DELETE FROM AuthorizedDepartments');
        
        // Insert new authorized departments
        await pool.request().query(`
            INSERT INTO AuthorizedDepartments (DeptId, CanChangeStatus, CanAssignComplaints) 
            VALUES 
                (20063, 1, 1),
                (20064, 1, 1),
                (20083, 1, 1),
                (20084, 1, 1);
        `);
        
        console.log('Authorized departments initialized successfully');
    } catch (error) {
        console.error('Error initializing authorized departments:', error);
        throw error;
    }
}

// Initialize database and verify structure
async function initializeDatabase() {
    try {
        const pool = await getConnection();
        
        // Test the connection
        const result = await pool.request().query('SELECT 1');
        console.log('Database connection test successful');

        // Force add CanViewDashboard column if it doesn't exist
        try {
            await pool.request().query(`
                IF NOT EXISTS (
                    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'EmployeePermissions' 
                    AND COLUMN_NAME = 'CanViewDashboard'
                )
                BEGIN
                    ALTER TABLE EmployeePermissions
                    ADD CanViewDashboard bit NOT NULL DEFAULT(0)
                END
            `);
            console.log('Ensured CanViewDashboard column exists');
        } catch (err) {
            console.error('Error adding CanViewDashboard column:', err);
        }

        // Get admin users and ensure they have permissions
        const adminUsers = await pool.request().query(`
            SELECT EmpCode 
            FROM Complaints_Employee 
            WHERE DeptName LIKE 'Admin%' OR DeptName LIKE 'Info%'
        `);

        for (const user of adminUsers.recordset) {
            await pool.request()
                .input('empCode', sql.NVarChar, user.EmpCode)
                .query(`
                    MERGE EmployeePermissions AS target
                    USING (SELECT @empCode as EmpCode) AS source
                    ON (target.EmpCode = source.EmpCode)
                    WHEN MATCHED THEN
                        UPDATE SET 
                            CanAssign = 1,
                            CanUpdateStatus = 1,
                            CanViewDashboard = 1
                    WHEN NOT MATCHED THEN
                        INSERT (EmpCode, CanAssign, CanUpdateStatus, CanViewDashboard)
                        VALUES (@empCode, 1, 1, 1);
                `);
        }
        console.log('Updated permissions for admin users');

        // Log current permissions
        const permissions = await pool.request().query(`
            SELECT e.EmpCode, e.EmpName, e.DeptName, 
                   p.CanAssign, p.CanUpdateStatus, p.CanViewDashboard
            FROM Complaints_Employee e
            LEFT JOIN EmployeePermissions p ON e.EmpCode = p.EmpCode
            WHERE e.DeptName LIKE 'Admin%' OR e.DeptName LIKE 'Info%'
        `);
        console.log('Current admin permissions:', permissions.recordset);

    } catch (err) {
        console.error('Failed to initialize database:', err);
        throw err;
    }
}

// Add this test query after database initialization
async function testEmployeeTable() {
    try {
        const pool = await getConnection();
        const result = await pool.request().query('SELECT TOP 1 * FROM Complaints_Employee');
        // Log employee data without sensitive information
        const safeEmployeeData = { ...result.recordset[0] };
        delete safeEmployeeData.Password; // Remove password from logs
        console.log('Successfully queried Complaints_Employee table:', safeEmployeeData);
    } catch (err) {
        console.error('Error querying Complaints_Employee table:', err);
    }
}

// Call this after database initialization
(async () => {
    try {
        await initializeDatabase();
        await testEmployeeTable();
    } catch (err) {
        console.error('Startup error:', err);
    }
})();

// Create HTTP server and Socket.IO
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// Store connected users for permission updates
const connectedUsers = new Map();

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log('User connected:', socket.id);

    // Handle user authentication for socket
    socket.on('authenticate', (token) => {
        try {
            const decoded = jwt.verify(token, JWT_SECRET);
            const empCode = decoded.empCode;

            // Store user connection
            connectedUsers.set(empCode, socket.id);
            socket.empCode = empCode;

            console.log(`User ${empCode} authenticated on socket ${socket.id}`);
        } catch (error) {
            console.error('Socket authentication failed:', error);
            socket.emit('auth_error', 'Invalid token');
        }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
        if (socket.empCode) {
            connectedUsers.delete(socket.empCode);
            console.log(`User ${socket.empCode} disconnected`);
        }
    });
});

// Function to notify user of permission changes
function notifyPermissionUpdate(empCode) {
    const socketId = connectedUsers.get(empCode);
    if (socketId) {
        io.to(socketId).emit('permission_updated', {
            message: 'Your permissions have been updated',
            timestamp: new Date().toISOString()
        });
        console.log(`Notified user ${empCode} of permission update`);
    }
}

// Make notifyPermissionUpdate available globally
global.notifyPermissionUpdate = notifyPermissionUpdate;

// Start the server
const PORT = process.env.PORT || 1976;
const HOST = process.env.HOST || '0.0.0.0';

server.listen(PORT, HOST, () => {
    const interfaces = require('os').networkInterfaces();
    console.log('\nServer running on:');

    // Log all network interfaces
    Object.keys(interfaces).forEach((iface) => {
        interfaces[iface].forEach((details) => {
            if (details.family === 'IPv4') {
                console.log(`- http://${details.address}:${PORT}`);
            }
        });
    });

    console.log('\nFrontend should be accessible at:');
    console.log('- http://localhost:3000');
    console.log('- http://***************:3000');
    console.log('- http://*************:3000');
    console.log('\nServer is ready to accept connections');
    console.log('Socket.IO server is running for real-time updates');
});

// Authorization middleware
const authMiddleware = async (req, res, next) => {
  try {
    console.log('Auth middleware - Headers:', req.headers);
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('No token provided');
      return res.status(401).json({
        error: true,
        message: 'No token provided'
      });
    }

    const token = authHeader.split(' ')[1];
    console.log('Verifying token:', token);
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('Decoded token:', decoded);
    
    // Add user info to request
    req.user = decoded;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(401).json({
      error: true,
      message: 'Invalid token'
    });
  }
};

// Apply auth middleware to protected routes
app.use('/api/complaints', authMiddleware);

// Submit complaint endpoint
app.post('/api/complaints', handleUpload, async (req, res) => {
    let pool;
    try {
        console.log('=== Starting complaint submission ===');
        // Log request body without sensitive information
        const safeRequestBody = { ...req.body };
        if (safeRequestBody.password) delete safeRequestBody.password;
        console.log('Request body:', JSON.stringify(safeRequestBody, null, 2));
        console.log('Request files:', req.files);

        // Validate request body
        if (!req.body) {
            console.error('No request body received');
            return res.status(400).json({
                error: 'No data received',
                message: 'Request body is empty'
            });
        }

        // Check if required fields are present
        const requiredFields = ['title', 'type', 'description', 'priority'];
        const missingFields = requiredFields.filter(field => !req.body[field]);
        
        if (missingFields.length > 0) {
            console.error('Missing required fields:', missingFields);
            return res.status(400).json({ 
                error: 'Missing required fields',
                missingFields: missingFields,
                receivedData: { ...req.body, password: req.body.password ? '***' : undefined }
            });
        }

        console.log('All required fields present, connecting to database...');

        // Connect to database
        try {
            pool = await getConnection();
            console.log('Successfully connected to database');
        } catch (dbError) {
            console.error('Database connection error:', dbError);
            return res.status(500).json({
                error: 'Database connection failed',
                details: dbError.message,
                stack: dbError.stack
            });
        }

        // Start transaction
        const transaction = new sql.Transaction(pool);
        try {
            console.log('Starting database transaction...');
            await transaction.begin();

            // Get current date in SQL format
            const currentDate = new Date().toISOString();

            // Prepare complaint data
            const complaintData = {
                ...req.body,
                submittedOn: currentDate,
                submittedByEmpCode: req.user.empCode,
                statusId: 1,
                priority: req.body.priority,
                category: req.body.type,
                submissionDate: currentDate
            };

            console.log('Inserting complaint with data:', complaintData);

            // Insert complaint with detailed error logging
            let complaintResult;
            try {
                const request = new sql.Request(transaction);
                
                // Set request timeout
                request.timeout = 30000;

                complaintResult = await request
                    .input('title', sql.NVarChar, complaintData.title)
                    .input('description', sql.NVarChar, complaintData.description)
                    .input('submittedByEmpCode', sql.NVarChar, complaintData.submittedByEmpCode)
                    .input('statusId', sql.Int, complaintData.statusId)
                    .input('priority', sql.NVarChar, complaintData.priority)
                    .input('category', sql.NVarChar, complaintData.category)
                    .input('submissionDate', sql.DateTime2, complaintData.submissionDate)
                    .query(`
                        DECLARE @OutputTable TABLE (
                            ComplaintId INT,
                            ComplaintNumber NVARCHAR(40)
                        );

                        -- Generate ComplaintNumber
                        DECLARE @NewComplaintNumber NVARCHAR(40);
                        DECLARE @CurrentDate DATE = GETDATE();
                        DECLARE @Year VARCHAR(4) = YEAR(@CurrentDate);
                        DECLARE @Month VARCHAR(2) = RIGHT('0' + CAST(MONTH(@CurrentDate) AS VARCHAR(2)), 2);
                        DECLARE @Sequence INT;

                        -- Get the next sequence number for the current month
                        SELECT @Sequence = ISNULL(MAX(CAST(RIGHT(ComplaintNumber, 4) AS INT)), 0) + 1
                        FROM Complaints
                        WHERE ComplaintNumber LIKE 'C' + @Year + @Month + '-%';

                        -- Format the complaint number
                        SET @NewComplaintNumber = 'C' + @Year + @Month + '-' + RIGHT('0000' + CAST(@Sequence AS VARCHAR(4)), 4);

                        INSERT INTO Complaints 
                        (Title, Description, SubmittedByEmpCode, 
                         StatusId, Priority, Category, SubmissionDate, ComplaintNumber, LastUpdateDate) 
                        OUTPUT 
                            INSERTED.ComplaintId,
                            INSERTED.ComplaintNumber
                        INTO @OutputTable
                        VALUES 
                        (@title, @description, @submittedByEmpCode,
                         @statusId, @priority, @category, @submissionDate, @NewComplaintNumber, @submissionDate);

                        -- Return the complaint ID and number
                        SELECT ComplaintId, ComplaintNumber FROM @OutputTable;
                    `);

                console.log('SQL Query Result:', complaintResult);

                if (!complaintResult.recordset || !complaintResult.recordset[0]) {
                    throw new Error('Failed to get ComplaintId after insert');
                }

                const { ComplaintId, ComplaintNumber } = complaintResult.recordset[0];
                console.log(`Complaint inserted successfully with ID: ${ComplaintId} Number: ${ComplaintNumber}`);

                // Handle file attachments if present
                if (req.files && req.files.length > 0) {
                    console.log('Processing file attachments:', req.files);
                    
                    for (const file of req.files) {
                        const networkPath = path.join(UPLOAD_PATH, file.filename);
                        console.log('Network path for file:', networkPath);

                        await new sql.Request(transaction)
                            .input('complaintId', sql.Int, ComplaintId)
                            .input('originalFileName', sql.NVarChar, file.originalname)
                            .input('storedFileName', sql.NVarChar, file.filename)
                            .input('filePath', sql.NVarChar, networkPath)
                            .input('fileSize', sql.BigInt, file.size)
                            .input('fileType', sql.NVarChar, file.mimetype)
                            .input('uploadDate', sql.DateTime2, currentDate)
                            .query(`
                                INSERT INTO ComplaintAttachments 
                                (ComplaintId, OriginalFileName, StoredFileName, FilePath, FileSize, FileType, UploadDate) 
                                VALUES 
                                (@complaintId, @originalFileName, @storedFileName, @filePath, @fileSize, @fileType, @uploadDate)
                            `);
                        console.log('Attachment record inserted successfully');
                    }
                }

                console.log('Committing transaction...');
                await transaction.commit();
                console.log('Transaction committed successfully');

                // Emit real-time update to all connected clients
                io.emit('complaint_created', {
                    complaintId: ComplaintId,
                    complaintNumber: ComplaintNumber,
                    submittedBy: req.user.empCode,
                    timestamp: new Date().toISOString()
                });
                console.log('Emitted complaint_created event to all clients');

                res.json({
                    success: true,
                    complaintId: ComplaintId,
                    complaintNumber: ComplaintNumber,
                    message: 'Complaint submitted successfully'
                });

                console.log('=== Complaint submission completed successfully ===');

            } catch (insertError) {
                console.error('Error during complaint insert:', insertError);
                throw insertError;
            }

        } catch (err) {
            console.error('Transaction error:', err);
            console.log('Rolling back transaction...');
            if (transaction) {
                await transaction.rollback();
            }
            throw err;
        }
    } catch (err) {
        console.error('Server error:', err);
        res.status(500).json({ 
            error: 'Failed to submit complaint',
            message: err.message,
            details: err.stack
        });
    }
});

// Get new complaint form data
app.get('/api/complaints/new', authMiddleware, async (req, res) => {
    try {
        const pool = await getConnection();
        
        // Get departments and employees in parallel
        const [departments, employees] = await Promise.all([
            pool.request().query(`
                SELECT DISTINCT
                    DeptID,
                    DeptName
                FROM Complaints_Employee
                ORDER BY DeptName
            `),
            pool.request().query(`
                SELECT DISTINCT 
                    EmpCode,
                    EmpName,
                    DeptName
                FROM complaints_employee
                WHERE EmpName IS NOT NULL 
                AND EmpName <> ''
                AND EmpName NOT LIKE '-%'
                ORDER BY EmpName
            `)
        ]);

        res.json({
            departments: departments.recordset,
            employees: employees.recordset
        });
    } catch (error) {
        console.error('Error fetching new complaint form data:', error);
        res.status(500).json({ 
            error: true,
            message: 'Failed to fetch form data',
            details: error.message
        });
    }
});

// Get complaints endpoint
app.get('/api/complaints', authMiddleware, async (req, res) => {
    try {
        const pool = await getConnection();
        const userEmpCode = req.user.empCode;

        // First check if user is admin (from Administration or IT department)
        const adminCheck = await pool.request()
            .input('empCode', sql.NVarChar, userEmpCode)
            .query(`
                SELECT 1 as IsAdmin
                FROM Complaints_Employee
                WHERE EmpCode = @empCode
                AND (DeptName LIKE 'Administration%' OR DeptName = 'Information Technology')
            `);

        const isAdmin = adminCheck.recordset.length > 0;
        
        // Build the query based on user role
        const query = `
            SELECT 
                c.ComplaintId,
                c.ComplaintNumber,
                c.Title,
                c.Description,
                c.SubmittedByEmpCode,
                CASE 
                    WHEN c.StatusId = 1 THEN 'New'
                    WHEN c.StatusId = 2 THEN 'Assigned'
                    WHEN c.StatusId = 3 THEN 'In Progress'
                    WHEN c.StatusId = 4 THEN 'Resolved'
                    WHEN c.StatusId = 5 THEN 'Rejected'
                    ELSE 'Unknown'
                END as Status,
                c.Priority,
                c.Category,
                c.SubmissionDate,
                c.LastUpdateDate,
                c.ResolutionNotes,
                c.IsConfidential,
                submitter.EmpName as SubmittedByName,
                submitter.DeptName as SubmittedByDepartment,
                assigned.EmpName as AssignedToName,
                assigned.DeptName as AssignedToDepartment,
                CASE 
                    WHEN c.SubmittedByEmpCode = @userEmpCode THEN 'My Complaint'
                    WHEN ca.AssignedToEmpCode = @userEmpCode THEN 'Assigned to Me'
                    ELSE 'Other'
                END as RelationType
            FROM Complaints c
            LEFT JOIN Complaints_Employee submitter ON c.SubmittedByEmpCode = submitter.EmpCode
            LEFT JOIN ComplaintAssignments ca ON c.ComplaintId = ca.ComplaintId
            LEFT JOIN Complaints_Employee assigned ON ca.AssignedToEmpCode = assigned.EmpCode
            WHERE 1=1
            ${isAdmin ? 
                '' : // Admin can see all complaints
                'AND (c.SubmittedByEmpCode = @userEmpCode OR ca.AssignedToEmpCode = @userEmpCode)' // Others can only see their own or assigned complaints
            }
            ORDER BY c.SubmissionDate DESC;
        `;

        const result = await pool.request()
            .input('userEmpCode', sql.NVarChar, userEmpCode)
            .query(query);

        // Add metadata about user's access level
        const response = {
            complaints: result.recordset,
            metadata: {
                isAdmin: isAdmin,
                userEmpCode: userEmpCode,
                totalComplaints: result.recordset.length,
                myComplaints: result.recordset.filter(c => c.SubmittedByEmpCode === userEmpCode).length,
                assignedToMe: result.recordset.filter(c => c.AssignedToName && c.AssignedToEmpCode === userEmpCode).length
            }
        };

        res.json(response);
    } catch (error) {
        console.error('Error fetching complaints:', error);
        res.status(500).json({ 
            error: true,
            message: 'Error fetching complaints', 
            details: error.message 
        });
    }
});

// Get complaint details with status updates
app.get('/api/complaints/:id', async (req, res) => {
  try {
    const pool = await getConnection();
    const result = await pool.request()
      .input('complaintId', sql.Int, req.params.id)
      .query(`
        SELECT 
          c.ComplaintId,
          c.ComplaintNumber as complaintNumber,
          c.Title as title,
          c.Description as description,
          c.Category as category,
          c.Priority as priority,
          c.SubmittedByEmpCode as submittedByCode,
          e1.EmpName as submittedByName,
          e1.DeptName as submittedByDepartment,
          CONVERT(varchar, c.SubmissionDate, 120) as submissionDate,
          CONVERT(varchar, c.LastUpdateDate, 120) as lastUpdateDate,
          CASE 
            WHEN c.StatusId = 1 THEN 'New'
            WHEN c.StatusId = 2 THEN 'Assigned'
            WHEN c.StatusId = 3 THEN 'In Progress'
            WHEN c.StatusId = 4 THEN 'Resolved'
            WHEN c.StatusId = 5 THEN 'Rejected'
            ELSE 'Unknown'
          END as status,
          ca.AssignedToEmpCode as assignedToCode,
          e2.EmpName as assignedToName,
          e2.DeptName as assignedToDepartment,
          CONVERT(varchar, ca.AssignmentDate, 120) as assignmentDate
        FROM Complaints c
        LEFT JOIN Complaints_Employee e1 ON c.SubmittedByEmpCode = e1.EmpCode
        LEFT JOIN ComplaintAssignments ca ON c.ComplaintId = ca.ComplaintId
        LEFT JOIN Complaints_Employee e2 ON ca.AssignedToEmpCode = e2.EmpCode
        WHERE c.ComplaintId = @complaintId
      `);

    // Get attachments
    const attachments = await pool.request()
      .input('complaintId', sql.Int, req.params.id)
      .query(`
        SELECT 
          AttachmentId as id,
          OriginalFileName as name,
          StoredFileName as storedName,
          FilePath as path,
          FileSize as size,
          FileType as type,
          FORMAT(UploadDate, 'yyyy-MM-ddTHH:mm:ss') as uploadDate
        FROM ComplaintAttachments
        WHERE ComplaintId = @complaintId
      `);

    // Get status history with proper formatting
    const statusHistory = await pool.request()
      .input('complaintId', sql.Int, req.params.id)
      .query(`
        SELECT 
          CONVERT(varchar, csh.ChangeDate, 120) as timestamp,
          csh.ChangedByEmpCode as updatedByCode,
          ce.EmpName as updatedBy,
          ce.DeptName as updatedByDepartment,
          CASE 
            WHEN csh.OldStatusId = 1 THEN 'New'
            WHEN csh.OldStatusId = 2 THEN 'Assigned'
            WHEN csh.OldStatusId = 3 THEN 'In Progress'
            WHEN csh.OldStatusId = 4 THEN 'Resolved'
            WHEN csh.OldStatusId = 5 THEN 'Rejected'
            ELSE 'Unknown'
          END as fromStatus,
          CASE 
            WHEN csh.NewStatusId = 1 THEN 'New'
            WHEN csh.NewStatusId = 2 THEN 'Assigned'
            WHEN csh.NewStatusId = 3 THEN 'In Progress'
            WHEN csh.NewStatusId = 4 THEN 'Resolved'
            WHEN csh.NewStatusId = 5 THEN 'Rejected'
            ELSE 'Unknown'
          END as toStatus,
          csh.Comments as comments
        FROM ComplaintStatusHistory csh
        LEFT JOIN Complaints_Employee ce ON csh.ChangedByEmpCode = ce.EmpCode
        WHERE csh.ComplaintId = @complaintId
        ORDER BY csh.ChangeDate DESC
      `);

    const complaint = result.recordset[0];
    if (complaint) {
      complaint.attachments = attachments.recordset;
      complaint.statusHistory = statusHistory.recordset;
    }

    res.json(complaint || null);
  } catch (error) {
    console.error('Error fetching complaint details:', error);
    res.status(500).json({ 
      error: true,
      message: 'Failed to fetch complaint details',
      details: error.message 
    });
  }
});

// GET an attachment
app.get('/api/complaints/attachments/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        console.log('Fetching attachment with ID:', id);

        const result = await pool.request()
            .input('attachmentId', sql.Int, id)
            .query(`
                SELECT 
                    FilePath,
                    OriginalFileName,
                    StoredFileName,
                    FileType
                FROM ComplaintAttachments 
                WHERE AttachmentId = @attachmentId
            `);

        if (result.recordset.length === 0) {
            console.log('Attachment not found in database');
            return res.status(404).json({ 
                error: true,
                message: 'Attachment not found' 
            });
        }

        const { FilePath, OriginalFileName, FileType } = result.recordset[0];
        console.log('Found attachment:', { FilePath, OriginalFileName, FileType });
        
        // Check if file exists
        try {
            await fs.promises.access(FilePath);
            console.log('File exists at path:', FilePath);
        } catch (error) {
            console.error('File not found at path:', FilePath);
            return res.status(404).json({ 
                error: true,
                message: 'Attachment file not found on server',
                path: FilePath
            });
        }

        // Set appropriate headers for file download
        res.setHeader('Content-Type', FileType || 'application/octet-stream');
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(OriginalFileName)}"`);

        // Stream the file
        const fileStream = fs.createReadStream(FilePath);
        fileStream.on('error', (error) => {
            console.error('Error streaming file:', error);
            if (!res.headersSent) {
                res.status(500).json({ 
                    error: true,
                    message: 'Error streaming file',
                    details: error.message
                });
            }
        });

        fileStream.pipe(res);

    } catch (error) {
        console.error('Error fetching attachment:', error);
        if (!res.headersSent) {
            res.status(500).json({ 
                error: true,
                message: 'Failed to fetch attachment',
                details: error.message
            });
        }
    }
});

// Login endpoint with mobile optimizations
app.post('/api/auth/login', async (req, res) => {
    const { empCode, password } = req.body;

    try {
        console.log('=== Login attempt ===');
        console.log('Login credentials:', { empCode, password: '***' }); // Hide password in logs
        console.log('User-Agent:', req.headers['user-agent']);
        console.log('Origin:', req.headers.origin);

        // Validate input
        if (!empCode || !password) {
            return res.status(400).json({
                error: true,
                message: 'Employee code and password are required'
            });
        }

        // Trim whitespace
        const trimmedEmpCode = empCode.trim();
        const trimmedPassword = password.trim();

        const pool = await getConnection();
        
        // First, get the user details including department and permissions
        const result = await pool.request()
            .input('empCode', sql.NVarChar, trimmedEmpCode)
            .input('password', sql.NVarChar, trimmedPassword)
            .query(`
                SELECT 
                    e.EmpId,
                    e.EmpCode,
                    e.EmpName,
                    e.DeptName,
                    p.CanAssign,
                    p.CanUpdateStatus,
                    p.CanViewDashboard,
                    CASE 
                        WHEN e.DeptName LIKE 'Admin%' OR e.DeptName LIKE 'Info%' THEN 1 
                        ELSE 0 
                    END as IsAdmin
                FROM Complaints_Employee e
                LEFT JOIN EmployeePermissions p ON e.EmpCode = p.EmpCode
                WHERE e.EmpCode = @empCode AND e.Password = @password
            `);

        console.log('Query result:', result.recordset);

        if (result.recordset.length === 0) {
            console.log('Login failed: Invalid credentials');
            return res.status(401).json({
                error: true,
                message: 'Invalid employee code or password'
            });
        }

        const user = result.recordset[0];
        console.log('User found:', user);
        
        // Create JWT token
        const tokenPayload = {
            empCode: user.EmpCode,
            empName: user.EmpName,
            deptName: user.DeptName,
            isAdmin: user.IsAdmin === 1,
            permissions: {
                canAssign: user.CanAssign === true || user.CanAssign === 1,
                canUpdateStatus: user.CanUpdateStatus === true || user.CanUpdateStatus === 1,
                canViewDashboard: user.CanViewDashboard === true || user.CanViewDashboard === 1
            }
        };
        console.log('Token payload:', tokenPayload);

        const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: '24h' });

        // Return user data and token
        const response = {
            token,
            user: {
                empCode: user.EmpCode,
                empName: user.EmpName,
                deptName: user.DeptName,
                isAdmin: user.IsAdmin === 1,
                permissions: {
                    canAssign: user.CanAssign === true || user.CanAssign === 1,
                    canUpdateStatus: user.CanUpdateStatus === true || user.CanUpdateStatus === 1,
                    canViewDashboard: user.CanViewDashboard === true || user.CanViewDashboard === 1
                }
            }
        };
        console.log('Login response:', response);

        res.json(response);
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            error: true,
            message: 'Internal server error',
            details: error.message
        });
    }
});

app.get('/api/auth/verify', async (req, res) => {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
            error: true,
            message: 'No token provided'
        });
    }

    const token = authHeader.split(' ')[1];

    try {
        const decoded = jwt.verify(token, JWT_SECRET);
        const pool = await getConnection();
        const result = await pool.request()
            .input('empCode', sql.NVarChar, decoded.empCode)
            .query(`
                SELECT 
                    e.EmpCode, 
                    e.EmpName, 
                    e.DeptName,
                    CASE 
                        WHEN e.DeptName LIKE 'Admin%' OR e.DeptName LIKE 'Info%' THEN 1 
                        ELSE 0 
                    END as IsAdmin,
                    p.CanAssign,
                    p.CanUpdateStatus,
                    p.CanViewDashboard
                FROM Complaints_Employee e
                LEFT JOIN EmployeePermissions p ON e.EmpCode = p.EmpCode
                WHERE e.EmpCode = @empCode
            `);

        const user = result.recordset[0];

        if (!user) {
            return res.status(401).json({
                error: true,
                message: 'User not found'
            });
        }

        return res.json({
            user: {
                empCode: user.EmpCode,
                name: user.EmpName,
                department: user.DeptName,
                isAdmin: user.IsAdmin === 1,
                permissions: {
                    canAssign: user.CanAssign === true || user.CanAssign === 1,
                    canUpdateStatus: user.CanUpdateStatus === true || user.CanUpdateStatus === 1,
                    canViewDashboard: user.CanViewDashboard === true || user.CanViewDashboard === 1
                }
            }
        });

    } catch (error) {
        console.error('Token verification error:', error);
        return res.status(401).json({
            error: true,
            message: 'Invalid token'
        });
    }
});

// Change password endpoint
app.post('/api/auth/change-password', authMiddleware, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const empCode = req.user.empCode; // From auth middleware

        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                error: true,
                message: 'Current password and new password are required'
            });
        }

        const pool = await getConnection();
        
        // Verify current password
        const verifyResult = await pool.request()
            .input('empCode', sql.NVarChar, empCode)
            .input('currentPassword', sql.NVarChar, currentPassword)
            .query(`
                SELECT COUNT(*) as count
                FROM Complaints_Employee
                WHERE EmpCode = @empCode AND Password = @currentPassword
            `);

        if (verifyResult.recordset[0].count === 0) {
            return res.status(400).json({
                error: true,
                message: 'Current password is incorrect'
            });
        }

        // Update password
        await pool.request()
            .input('empCode', sql.NVarChar, empCode)
            .input('newPassword', sql.NVarChar, newPassword)
            .query(`
                UPDATE Complaints_Employee
                SET Password = @newPassword,CreatedDate = GETDATE()
                WHERE EmpCode = @empCode
            `);

        res.json({ message: 'Password changed successfully' });
    } catch (error) {
        console.error('Error changing password:', error);
        res.status(500).json({ 
            error: true,
            message: 'Failed to change password'
        });
    }
});

// Get dashboard statistics
app.get('/api/dashboard/stats', authMiddleware, async (req, res) => {
    try {
        const pool = await getConnection();
        
        // Get overall stats
        const overallStats = await pool.request().query(`
            SELECT
                COUNT(*) as totalComplaints,
                SUM(CASE WHEN StatusId IN (4, 5) THEN 1 ELSE 0 END) as resolvedComplaints,
                SUM(CASE WHEN StatusId NOT IN (4, 5) THEN 1 ELSE 0 END) as pendingComplaints,
                SUM(CASE 
                    WHEN Priority IN ('High', 'Critical') AND StatusId NOT IN (4, 5) THEN 1 
                    ELSE 0 
                END) as highPriorityComplaints
            FROM Complaints
        `);

        // Get monthly stats
        const monthlyStats = await pool.request().query(`
            DECLARE @StartOfMonth DATETIME = DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0);
            
            -- Get monthly complaints count
            WITH MonthlyComplaints AS (
                SELECT 
                    COUNT(*) as totalMonthlyComplaints,
                    SUM(CASE WHEN StatusId IN (4, 5) THEN 1 ELSE 0 END) as resolvedComplaints
                FROM Complaints 
                WHERE SubmissionDate >= @StartOfMonth
            ),
            AssignmentStats AS (
                SELECT 
                    COUNT(*) as assignedWithin24h
                FROM Complaints c
                INNER JOIN ComplaintAssignments ca ON c.ComplaintId = ca.ComplaintId
                WHERE c.SubmissionDate >= @StartOfMonth
                AND DATEDIFF(HOUR, c.SubmissionDate, ca.AssignmentDate) <= 24
            ),
            ResolutionStats AS (
                SELECT 
                    AVG(CAST(DATEDIFF(HOUR, c.SubmissionDate, csh.ChangeDate) as DECIMAL(10,2))) as avgResolutionHours
                FROM Complaints c
                INNER JOIN ComplaintStatusHistory csh ON c.ComplaintId = csh.ComplaintId
                WHERE c.SubmissionDate >= @StartOfMonth
                AND csh.NewStatusId IN (4, 5)  -- Include both Resolved and Rejected status
            )
            SELECT
                mc.totalMonthlyComplaints,
                mc.resolvedComplaints,
                CASE 
                    WHEN mc.totalMonthlyComplaints > 0 
                    THEN CAST(mc.resolvedComplaints * 100.0 / mc.totalMonthlyComplaints as DECIMAL(5,2))
                    ELSE 0 
                END as resolutionRate,
                CASE 
                    WHEN mc.totalMonthlyComplaints > 0 
                    THEN CAST(ast.assignedWithin24h * 100.0 / mc.totalMonthlyComplaints as DECIMAL(5,2))
                    ELSE 0 
                END as responseEfficiency,
                ISNULL(rs.avgResolutionHours, 0) as avgResolutionHours
            FROM MonthlyComplaints mc
            CROSS JOIN AssignmentStats ast
            CROSS JOIN ResolutionStats rs;
        `);

        // Combine the results
        const response = {
            ...overallStats.recordset[0],
            monthlyStats: monthlyStats.recordset[0]
        };

        res.json(response);
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        res.status(500).json({ message: 'Error fetching dashboard stats' });
    }
});

// Get all departments with their authority status
app.get('/api/authorities', async (req, res) => {
    try {
        const pool = await getConnection();
        
        // Check if user's department starts with 'Administration'
        const userDeptCheck = await pool.request()
            .input('empCode', sql.NVarChar, req.user.empCode)
            .query(`
                SELECT DeptName
                FROM Complaints_Employee
                WHERE EmpCode = @empCode
                AND DeptName LIKE 'Administration%'
            `);

        if (!userDeptCheck.recordset[0]) {
            return res.status(403).json({
                error: true,
                message: 'Access denied. Only Administration department can manage authorities.'
            });
        }
        
        const result = await pool.request().query(`
            SELECT DISTINCT
                d.DeptID,
                d.DeptName,
                CASE WHEN ad.DeptId IS NOT NULL THEN 1 ELSE 0 END as HasAuthority,
                ISNULL(ad.CanChangeStatus, 0) as CanChangeStatus,
                ISNULL(ad.CanAssignComplaints, 0) as CanAssignComplaints
            FROM ICSOFT.dbo.Department d
            LEFT JOIN AuthorizedDepartments ad ON d.DeptID = ad.DeptId
            ORDER BY d.DeptName
        `);

        res.json(result.recordset);
    } catch (err) {
        console.error('Error fetching authorities:', err);
        res.status(500).json({ 
            error: 'Failed to fetch authorities',
            message: err.message
        });
    }
});

// Update department authorities
app.post('/api/authorities/update', async (req, res) => {
    try {
        const { departments } = req.body;
        if (!Array.isArray(departments)) {
            return res.status(400).json({
                error: 'Invalid request format',
                message: 'Expected departments array'
            });
        }

        const pool = await getConnection();
        
        // Check if user's department starts with 'Administration'
        const userDeptCheck = await pool.request()
            .input('empCode', sql.NVarChar, req.user.empCode)
            .query(`
                SELECT DeptName
                FROM Complaints_Employee
                WHERE EmpCode = @empCode
                AND DeptName LIKE 'Administration%'
            `);

        if (!userDeptCheck.recordset[0]) {
            return res.status(403).json({
                error: true,
                message: 'Access denied. Only Administration department can manage authorities.'
            });
        }

        const transaction = new sql.Transaction(pool);
        
        try {
            await transaction.begin();

            // First clear all existing authorities
            await new sql.Request(transaction).query('DELETE FROM AuthorizedDepartments');

            // Insert new authorities
            for (const dept of departments) {
                if (dept.HasAuthority) {
                    await new sql.Request(transaction)
                        .input('deptId', sql.Int, dept.DeptID)
                        .input('canChangeStatus', sql.Bit, dept.CanChangeStatus)
                        .input('canAssignComplaints', sql.Bit, dept.CanAssignComplaints)
                        .query(`
                            INSERT INTO AuthorizedDepartments (DeptId, CanChangeStatus, CanAssignComplaints)
                            VALUES (@deptId, @canChangeStatus, @canAssignComplaints)
                        `);
                }
            }

            await transaction.commit();
            res.json({ success: true, message: 'Authorities updated successfully' });
        } catch (err) {
            await transaction.rollback();
            throw err;
        }
    } catch (err) {
        console.error('Error updating authorities:', err);
        res.status(500).json({ 
            error: 'Failed to update authorities',
            message: err.message
        });
    }
});

// Get employees by department
app.get('/api/employees', authMiddleware, async (req, res) => {
    try {
        const pool = await getConnection();
        
        const result = await pool.request().query(`
            SELECT DISTINCT EmpCode, EmpName, DeptName
            FROM complaints_employee
            ORDER BY EmpName
        `);

        res.json(result.recordset);
    } catch (error) {
        console.error('Error fetching employees:', error);
        res.status(500).json({ message: 'Error fetching employees' });
    }
});

// Get all departments
app.get('/api/departments', authMiddleware, async (req, res) => {
    try {
        const pool = await getConnection();
        
        const result = await pool.request().query(`
            SELECT DISTINCT
                DeptID,
                DeptName
            FROM Complaints_Employee
            ORDER BY DeptName
        `);

        res.json(result.recordset);
    } catch (err) {
        console.error('Error fetching departments:', err);
        res.status(500).json({ 
            error: 'Failed to fetch departments',
            message: err.message
        });
    }
});

// Update complaint status endpoint
app.post('/api/complaints/:id/status', async (req, res) => {
    const pool = await getConnection();
    const transaction = new sql.Transaction(pool);

    try {
        const { id } = req.params;
        const { newStatusId, comments } = req.body;

        console.log('=== Status Update Request ===');
        console.log('Request details:', {
            complaintId: id,
            newStatusId,
            comments,
            userEmpCode: req.user.empCode,
            userDeptName: req.user.deptName
        });

        if (!newStatusId) {
            console.log('Status update rejected: No new status provided');
            return res.status(400).json({
                error: true,
                message: 'New status is required'
            });
        }

        await transaction.begin();
        console.log('Transaction begun');

        // First check if user is from Administration department
        const adminCheck = await new sql.Request(transaction)
            .input('empCode', sql.NVarChar, req.user.empCode)
            .query(`
                SELECT 1 as IsAdmin, DeptName
                FROM Complaints_Employee ce
                WHERE ce.EmpCode = @empCode
                AND ce.DeptName LIKE 'Administration%'
            `);

        console.log('Admin check result:', adminCheck.recordset);

        // Get current status and check if complaint exists
        const currentStatus = await new sql.Request(transaction)
            .input('complaintId', sql.Int, id)
            .query(`
                SELECT 
                    c.StatusId, 
                    c.SubmittedByEmpCode,
                    CASE 
                        WHEN ca.AssignedToEmpCode IS NOT NULL THEN ca.AssignedToEmpCode
                        ELSE NULL
                    END as AssignedToEmpCode
                FROM Complaints c
                LEFT JOIN (
                    SELECT ComplaintId, AssignedToEmpCode
                    FROM ComplaintAssignments ca1
                    WHERE AssignmentId = (
                        SELECT TOP 1 AssignmentId 
                        FROM ComplaintAssignments ca2 
                        WHERE ca2.ComplaintId = ca1.ComplaintId 
                        ORDER BY AssignmentDate DESC
                    )
                ) ca ON c.ComplaintId = ca.ComplaintId
                WHERE c.ComplaintId = @complaintId
            `);

        console.log('Current status query result:', currentStatus.recordset);

        if (!currentStatus.recordset[0]) {
            console.log('Status update rejected: Complaint not found');
            await transaction.rollback();
            throw new Error('Complaint not found');
        }

        const oldStatusId = currentStatus.recordset[0].StatusId;
        const isAdmin = !!adminCheck.recordset[0];
        const isAssigned = currentStatus.recordset[0].AssignedToEmpCode === req.user.empCode;

        console.log('Authorization check:', {
            oldStatusId,
            isAdmin,
            isAssigned,
            currentAssignee: currentStatus.recordset[0].AssignedToEmpCode,
            userEmpCode: req.user.empCode,
            userDeptName: req.user.deptName
        });

        // If user is admin, they can always update status
        // If not admin, they must be assigned to the complaint
        if (!isAdmin && !isAssigned) {
            console.log('Status update rejected: User is neither admin nor assigned');
            await transaction.rollback();
            return res.status(403).json({
                error: true,
                message: 'Only administrators and assigned users can update the complaint status'
            });
        }

        console.log('Access granted:', isAdmin ? 'Administrator access' : 'Assigned user access');

        // Map status text to ID if needed
        let statusId = newStatusId;
        if (typeof newStatusId === 'string') {
            const statusMap = {
                'New': 1,
                'Assigned': 2,
                'In Progress': 3,
                'Resolved': 4,
                'Rejected': 5  // Changed from Closed to Rejected
            };
            statusId = statusMap[newStatusId] || newStatusId;
        }

        console.log('Using status ID:', statusId);

        // Update complaint status
        const updateResult = await new sql.Request(transaction)
            .input('complaintId', sql.Int, id)
            .input('newStatusId', sql.Int, statusId)
            .input('oldStatusId', sql.Int, oldStatusId)
            .input('comments', sql.NVarChar, comments || '')
            .input('changedByEmpCode', sql.NVarChar, req.user.empCode)
            .query(`
                DECLARE @UpdateCount INT;
                DECLARE @HistoryCount INT;

                -- Update complaint status
                UPDATE Complaints
                SET StatusId = @newStatusId,
                    LastUpdateDate = GETDATE()
                WHERE ComplaintId = @complaintId;
                
                SET @UpdateCount = @@ROWCOUNT;

                -- Add status history entry
                INSERT INTO ComplaintStatusHistory (
                    ComplaintId,
                    OldStatusId,
                    NewStatusId,
                    ChangedByEmpCode,
                    ChangeDate,
                    Comments
                )
                VALUES (
                    @complaintId,
                    @oldStatusId,
                    @newStatusId,
                    @changedByEmpCode,
                    GETDATE(),
                    @comments
                );

                SET @HistoryCount = @@ROWCOUNT;

                -- Return counts for logging
                SELECT @UpdateCount as UpdateCount, @HistoryCount as HistoryCount;
            `);

        console.log('Update result:', updateResult.recordset);

        await transaction.commit();
        console.log('Transaction committed successfully');

        // Emit real-time update to all connected clients
        io.emit('status_updated', {
            complaintId: id,
            newStatus: statusId,
            updatedBy: req.user.empCode,
            timestamp: new Date().toISOString()
        });
        console.log('Emitted status_updated event to all clients');

        res.json({
            error: false,
            message: 'Complaint status updated successfully'
        });
    } catch (err) {
        console.error('Error updating complaint status:', err);
        if (transaction) {
            try {
                await transaction.rollback();
                console.log('Transaction rolled back due to error');
            } catch (rollbackErr) {
                console.error('Error rolling back transaction:', rollbackErr);
            }
        }
        res.status(500).json({
            error: true,
            message: 'Failed to update complaint status: ' + (err.message || 'Unknown error')
        });
    }
});

// Assign complaint endpoint
app.post('/api/complaints/:id/assign', async (req, res) => {
    const pool = await getConnection();
    const transaction = new sql.Transaction(pool);

    try {
        const { id } = req.params;
        const { empCode } = req.body;

        console.log('Assignment request:', {
            complaintId: id,
            assignToEmpCode: empCode,
            assignedByEmpCode: req.user.empCode
        });

        await transaction.begin();

        // Get employee details to assign to
        const employeeDetails = await new sql.Request(transaction)
            .input('empCode', sql.NVarChar, empCode)
            .query(`
                SELECT EmpCode, EmpName, DeptName
                FROM Complaints_Employee
                WHERE EmpCode = @empCode
            `);

        console.log('Employee details:', employeeDetails.recordset);

        if (!employeeDetails.recordset[0]) {
            await transaction.rollback();
            return res.status(400).json({
                error: true,
                message: 'Selected employee not found'
            });
        }

        // Update complaint assignment
        await new sql.Request(transaction)
            .input('complaintId', sql.Int, id)
            .input('assignToEmpCode', sql.NVarChar, employeeDetails.recordset[0].EmpCode)
            .input('assignedByEmpCode', sql.NVarChar, req.user.empCode)
            .query(`
                -- Create new assignment
                INSERT INTO ComplaintAssignments (
                    ComplaintId,
                    AssignedToEmpCode,
                    AssignedByEmpCode,
                    AssignmentDate
                )
                VALUES (
                    @complaintId,
                    @assignToEmpCode,
                    @assignedByEmpCode,
                    GETDATE()
                );

                -- Update complaint status to Assigned if it's in New status
                UPDATE Complaints
                SET StatusId = CASE WHEN StatusId = 1 THEN 2 ELSE StatusId END,
                    LastUpdateDate = GETDATE()
                WHERE ComplaintId = @complaintId;

                -- Add status history entry if status changed from New to Assigned
                INSERT INTO ComplaintStatusHistory (
                    ComplaintId,
                    OldStatusId,
                    NewStatusId,
                    ChangedByEmpCode,
                    ChangeDate,
                    Comments
                )
                SELECT
                    @complaintId,
                    1, -- New
                    2, -- Assigned
                    @assignedByEmpCode,
                    GETDATE(),
                    'Complaint assigned to ' + @assignToEmpCode + ' (' + 
                    (SELECT EmpName FROM Complaints_Employee WHERE EmpCode = @assignToEmpCode) + ')'
                FROM Complaints
                WHERE ComplaintId = @complaintId
                AND StatusId = 2;
            `);

        await transaction.commit();

        res.json({
            error: false,
            message: 'Complaint assigned successfully',
            assignedTo: employeeDetails.recordset[0]
        });

    } catch (err) {
        console.error('Error assigning complaint:', err);
        if (transaction) {
            try {
                await transaction.rollback();
            } catch (rollbackErr) {
                console.error('Error rolling back transaction:', rollbackErr);
            }
        }
        res.status(500).json({
            error: true,
            message: 'Failed to assign complaint: ' + (err.message || 'Unknown error')
        });
    }
});

// Get all users
app.get('/api/users', async (req, res) => {
  try {
    const pool = await getConnection();
    const result = await pool.request().query(`
      SELECT EmpCode, EmpName, DeptName
      FROM Complaints_Employee
      ORDER BY EmpName
    `);
    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get recent activities
app.get('/api/dashboard/recent-activities', authMiddleware, async (req, res) => {
    try {
        const pool = await getConnection();
        const result = await pool.request().query(`
            SELECT TOP 10
                c.ComplaintId,
                c.ComplaintNumber,
                c.Title as description,
                CASE
                    WHEN c.StatusId = 1 THEN 'New'
                    WHEN c.StatusId = 2 THEN 'Assigned'
                    WHEN c.StatusId = 3 THEN 'In Progress'
                    WHEN c.StatusId = 4 THEN 'Resolved'
                    WHEN c.StatusId = 5 THEN 'Rejected'
                    ELSE 'Unknown'
                END as Status,
                c.Priority,
                c.Category,
                c.SubmissionDate as submissionDate,
                c.LastUpdateDate as lastUpdateDate,
                submitter.EmpName as submittedBy,
                submitter.DeptName as submittedByDepartment,
                -- Get the most recent activity details
                CASE
                    WHEN c.StatusId = 1 THEN
                        'New complaint submitted by ' + submitter.EmpName + ' (' + submitter.DeptName + ')'
                    ELSE
                        ISNULL(
                            (SELECT TOP 1
                                'Status changed to ' +
                                CASE
                                    WHEN csh.NewStatusId = 1 THEN 'New'
                                    WHEN csh.NewStatusId = 2 THEN 'Assigned'
                                    WHEN csh.NewStatusId = 3 THEN 'In Progress'
                                    WHEN csh.NewStatusId = 4 THEN 'Resolved'
                                    WHEN csh.NewStatusId = 5 THEN 'Rejected'
                                    ELSE 'Unknown'
                                END +
                                CASE
                                    WHEN csh.Comments IS NOT NULL AND csh.Comments <> ''
                                    THEN ' - ' + csh.Comments
                                    ELSE ''
                                END +
                                ' by ' + ISNULL(updater.EmpName, 'System')
                            FROM ComplaintStatusHistory csh
                            LEFT JOIN Complaints_Employee updater ON csh.ChangedByEmpCode = updater.EmpCode
                            WHERE csh.ComplaintId = c.ComplaintId
                            ORDER BY csh.ChangeDate DESC),
                            'Last updated by ' + submitter.EmpName
                        )
                END as activityDetails,
                -- Use appropriate timestamp based on status
                CASE
                    WHEN c.StatusId = 1 THEN c.SubmissionDate
                    ELSE ISNULL(c.LastUpdateDate, c.SubmissionDate)
                END as activityTimestamp
            FROM Complaints c
            LEFT JOIN Complaints_Employee submitter ON c.SubmittedByEmpCode = submitter.EmpCode
            ORDER BY
                CASE
                    WHEN c.StatusId = 1 THEN c.SubmissionDate
                    ELSE ISNULL(c.LastUpdateDate, c.SubmissionDate)
                END DESC
        `);

        res.json(result.recordset);
    } catch (error) {
        console.error('Error fetching recent activities:', error);
        res.status(500).json({
            error: true,
            message: 'Failed to fetch recent activities'
        });
    }
});

// Add this endpoint to your server.js
app.get('/api/complaints/:id/status-history', async (req, res) => {
  try {
    const pool = await getConnection();
    const result = await pool.request()
      .input('complaintId', sql.Int, req.params.id)
      .query(`
        SELECT 
          ch.StatusId,
          ISNULL(ch.Status, 'Unknown') as Status,
          ch.UpdatedByEmpCode,
          CONVERT(varchar, ch.UpdatedDate, 120) as timestamp,
          ISNULL(ch.Comments, '') as comments,
          ISNULL(e.EmpName, 'System') as updatedBy
        FROM ComplaintStatusHistory ch
        LEFT JOIN Complaints_Employee e ON ch.UpdatedByEmpCode = e.EmpCode
        WHERE ch.ComplaintId = @complaintId
        ORDER BY ch.UpdatedDate DESC
      `);

    // Ensure we always return an array
    res.json(result.recordset || []);
  } catch (error) {
    console.error('Error fetching status history:', error);
    res.status(500).json({ 
      error: 'Failed to fetch status history',
      details: error.message 
    });
  }
});

// Update the getStatusColor function to be more robust
const getStatusColor = (status) => {
  if (!status) return 'default';
  
  const normalizedStatus = status.toString().toLowerCase().trim();
  
  const statusColors = {
    'new': 'info',
    'assigned': 'warning',
    'in progress': 'warning',
    'resolved': 'success'
  };

  return statusColors[normalizedStatus] || 'default';
};

// Debug endpoint to check user status
app.get('/api/user/status', async (req, res) => {
    try {
        const pool = await getConnection();
        const empCode = req.user.empCode; // From JWT token

        const result = await pool.request()
            .input('empCode', sql.NVarChar, empCode)
            .query(`
                SELECT 
                    EmpCode,
                    EmpName,
                    DeptName
                FROM complaints_employee
                WHERE EmpCode = @empCode
            `);

        res.json({
            user: result.recordset[0],
            isAdmin: result.recordset[0]?.DeptName.startsWith('Admin') || 
                    result.recordset[0]?.DeptName.startsWith('Info')
        });
    } catch (error) {
        console.error('Status check error:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Get employee permissions
app.get('/api/employees/permissions', authMiddleware, async (req, res) => {
    try {
        const pool = await getConnection();
        
        // Get all employees with their permissions
        const query = `
            SELECT 
                e.EmpCode,
                e.EmpName,
                e.DeptName,
                ISNULL(ep.CanAssign, 0) as CanAssign,
                ISNULL(ep.CanUpdateStatus, 0) as CanUpdateStatus,
                ISNULL(ep.CanViewDashboard, 0) as CanViewDashboard
            FROM Complaints_Employee e
            LEFT JOIN EmployeePermissions ep ON e.EmpCode = ep.EmpCode
            WHERE e.EmpName IS NOT NULL 
            AND e.EmpName <> ''
            AND e.EmpName NOT LIKE '-%'
            ORDER BY e.DeptName, e.EmpName;
        `;

        const result = await pool.request().query(query);
        res.json(result.recordset);
    } catch (error) {
        console.error('Error fetching employee permissions:', error);
        res.status(500).json({ message: 'Error fetching employee permissions' });
    }
});

// Update employee permissions
app.post('/api/employees/permissions', authMiddleware, async (req, res) => {
    try {
        const pool = await getConnection();
        const { permissions } = req.body;

        // Start a transaction
        const transaction = new sql.Transaction(pool);
        await transaction.begin();

        try {
            // Store affected users for notification
            const affectedUsers = [];

            // Handle each permission using MERGE to handle both INSERT and UPDATE
            for (const emp of permissions) {
                await transaction.request()
                    .input('empCode', sql.NVarChar, emp.EmpCode)
                    .input('canAssign', sql.Bit, emp.CanAssign ? 1 : 0)
                    .input('canUpdateStatus', sql.Bit, emp.CanUpdateStatus ? 1 : 0)
                    .input('canViewDashboard', sql.Bit, emp.CanViewDashboard ? 1 : 0)
                    .query(`
                        MERGE EmployeePermissions AS target
                        USING (SELECT @empCode as EmpCode) AS source
                        ON (target.EmpCode = source.EmpCode)
                        WHEN MATCHED THEN
                            UPDATE SET
                                CanAssign = @canAssign,
                                CanUpdateStatus = @canUpdateStatus,
                                CanViewDashboard = @canViewDashboard
                        WHEN NOT MATCHED THEN
                            INSERT (EmpCode, CanAssign, CanUpdateStatus, CanViewDashboard)
                            VALUES (@empCode, @canAssign, @canUpdateStatus, @canViewDashboard);
                    `);

                // Add to affected users list
                affectedUsers.push(emp.EmpCode);
            }

            await transaction.commit();

            // Notify affected users of permission changes
            affectedUsers.forEach(empCode => {
                if (global.notifyPermissionUpdate) {
                    global.notifyPermissionUpdate(empCode);
                }
            });

            console.log(`Permission updates completed for users: ${affectedUsers.join(', ')}`);
            res.json({ message: 'Permissions updated successfully' });
        } catch (error) {
            console.error('Transaction error:', error);
            await transaction.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error updating permissions:', error);
        res.status(500).json({ message: 'Error updating permissions', details: error.message });
    }
});

// Debug endpoint to check table structure
app.get('/api/debug/table-info', authMiddleware, async (req, res) => {
    try {
        const pool = await getConnection();
        
        // Get Complaints table info
        const complaintsInfo = await pool.request().query(`
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'Complaints'
            ORDER BY ORDINAL_POSITION;
        `);

        // Get ComplaintAssignments table info
        const assignmentsInfo = await pool.request().query(`
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'ComplaintAssignments'
            ORDER BY ORDINAL_POSITION;
        `);

        // Get sample data
        const sampleComplaints = await pool.request().query(`
            SELECT TOP 1 * FROM Complaints ORDER BY ComplaintId DESC;
        `);

        const sampleAssignments = await pool.request().query(`
            SELECT TOP 1 * FROM ComplaintAssignments ORDER BY AssignmentId DESC;
        `);

        res.json({
            complaintsColumns: complaintsInfo.recordset,
            assignmentsColumns: assignmentsInfo.recordset,
            sampleComplaint: sampleComplaints.recordset[0],
            sampleAssignment: sampleAssignments.recordset[0]
        });
    } catch (error) {
        console.error('Error getting table info:', error);
        res.status(500).json({ error: error.message });
    }
});

// Add this endpoint to check user permissions
app.get('/api/user/permissions', authMiddleware, async (req, res) => {
    try {
        const pool = await getConnection();
        const empCode = req.user.empCode;

        const result = await pool.request()
            .input('empCode', sql.NVarChar, empCode)
            .query(`
                SELECT 
                    ce.EmpCode,
                    ce.EmpName,
                    ce.DeptName,
                    CASE 
                        WHEN ce.DeptName LIKE 'Admin%' THEN 1
                        WHEN ep.CanViewDashboard = 1 THEN 1
                        ELSE 0
                    END as CanViewDashboard,
                    CASE 
                        WHEN ce.DeptName LIKE 'Admin%' THEN 1
                        WHEN ep.CanAssign = 1 THEN 1
                        ELSE 0
                    END as CanAssign,
                    CASE 
                        WHEN ce.DeptName LIKE 'Admin%' THEN 1
                        WHEN ep.CanUpdateStatus = 1 THEN 1
                        ELSE 0
                    END as CanUpdateStatus
                FROM Complaints_Employee ce
                LEFT JOIN EmployeePermissions ep ON ce.EmpCode = ep.EmpCode
                WHERE ce.EmpCode = @empCode
            `);

        if (!result.recordset[0]) {
            return res.status(404).json({
                error: true,
                message: 'User not found'
            });
        }

        res.json(result.recordset[0]);
    } catch (error) {
        console.error('Error fetching user permissions:', error);
        res.status(500).json({ message: 'Error fetching user permissions' });
    }
});

// Add auth verification endpoint
app.get('/api/auth/me', authMiddleware, async (req, res) => {
    try {
        console.log('=== Auth verification request ===');
        console.log('User from token:', req.user);
        
        const pool = await getConnection();
        const result = await pool.request()
            .input('empCode', sql.NVarChar, req.user.empCode)
            .query(`
                SELECT 
                    e.EmpCode,
                    e.EmpName,
                    e.DeptName,
                    CASE 
                        WHEN e.DeptName LIKE 'Admin%' OR e.DeptName LIKE 'Info%' THEN 1 
                        ELSE 0 
                    END as IsAdmin,
                    ISNULL(p.CanViewDashboard, 0) as CanViewDashboard,
                    ISNULL(p.CanAssign, 0) as CanAssign,
                    ISNULL(p.CanUpdateStatus, 0) as CanUpdateStatus
                FROM Complaints_Employee e
                LEFT JOIN EmployeePermissions p ON e.EmpCode = p.EmpCode
                WHERE e.EmpCode = @empCode
            `);

        console.log('Database query result:', result.recordset);

        if (!result.recordset[0]) {
            console.log('User not found:', req.user.empCode);
            return res.status(404).json({
                error: true,
                message: 'User not found'
            });
        }

        const user = result.recordset[0];
        const response = {
            user: {
                empCode: user.EmpCode,
                name: user.EmpName,
                department: user.DeptName,
                isAdmin: user.IsAdmin === 1,
                permissions: {
                    canViewDashboard: user.CanViewDashboard === 1 || user.IsAdmin === 1,
                    canAssign: user.CanAssign === 1 || user.IsAdmin === 1,
                    canUpdateStatus: user.CanUpdateStatus === 1 || user.IsAdmin === 1
                }
            }
        };

        console.log('Auth verification response:', response);
        res.json(response);
    } catch (error) {
        console.error('Auth verification error:', error);
        res.status(500).json({
            error: true,
            message: 'Failed to fetch user details',
            details: error.message
        });
    }
});

// Debug endpoint to check database structure and permissions
app.get('/api/debug/database-check', async (req, res) => {
    try {
        const pool = await getConnection();
        
        // Check table structure
        const tableStructure = await pool.request().query(`
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'EmployeePermissions'
            ORDER BY ORDINAL_POSITION;
        `);

        // Check admin user permissions
        const adminPermissions = await pool.request().query(`
            SELECT 
                e.EmpCode,
                e.EmpName,
                e.DeptName,
                p.CanAssign,
                p.CanUpdateStatus,
                p.CanViewDashboard
            FROM Complaints_Employee e
            LEFT JOIN EmployeePermissions p ON e.EmpCode = p.EmpCode
            WHERE e.DeptName LIKE 'Admin%' OR e.DeptName LIKE 'Info%'
        `);

        // Check if table exists
        const tableExists = await pool.request().query(`
            SELECT COUNT(*) as exists
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME = 'EmployeePermissions'
        `);

        res.json({
            tableExists: tableExists.recordset[0].exists > 0,
            tableStructure: tableStructure.recordset,
            adminPermissions: adminPermissions.recordset
        });
    } catch (error) {
        console.error('Database check error:', error);
        res.status(500).json({ error: error.message });
    }
});
