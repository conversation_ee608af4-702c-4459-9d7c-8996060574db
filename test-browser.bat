@echo off
echo Testing browser opening methods...
echo.

echo Method 1: Using start command
start "" "http://localhost:3000"
timeout /t 2 /nobreak >nul

echo Method 2: Using PowerShell
powershell -Command "Start-Process 'http://localhost:3000'"
timeout /t 2 /nobreak >nul

echo Method 3: Using rundll32
rundll32 url.dll,FileProtocolHandler "http://localhost:3000"
timeout /t 2 /nobreak >nul

echo.
echo All browser opening methods attempted!
echo If none worked, manually open browser and go to: http://localhost:3000
echo.
pause
