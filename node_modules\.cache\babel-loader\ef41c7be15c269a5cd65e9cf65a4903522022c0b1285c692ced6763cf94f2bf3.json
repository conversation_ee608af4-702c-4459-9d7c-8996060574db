{"ast": null, "code": "/* Use it instead of .includes method for IE support */\nexport function arrayIncludes(array, itemOrItems) {\n  if (Array.isArray(itemOrItems)) {\n    return itemOrItems.every(item => array.indexOf(item) !== -1);\n  }\n  return array.indexOf(itemOrItems) !== -1;\n}\nexport const onSpaceOrEnter = (innerFn, onFocus) => event => {\n  if (event.key === 'Enter' || event.key === ' ') {\n    innerFn(event); // prevent any side effects\n\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  if (onFocus) {\n    onFocus(event);\n  }\n};\nexport const executeInTheNextEventLoopTick = fn => {\n  setTimeout(fn, 0);\n};\nexport const doNothing = () => {}; // https://www.abeautifulsite.net/posts/finding-the-active-element-in-a-shadow-root/\n\nexport const getActiveElement = function () {\n  let root = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  const activeEl = root.activeElement;\n  if (!activeEl) {\n    return null;\n  }\n  if (activeEl.shadowRoot) {\n    return getActiveElement(activeEl.shadowRoot);\n  }\n  return activeEl;\n};", "map": {"version": 3, "names": ["arrayIncludes", "array", "itemOrItems", "Array", "isArray", "every", "item", "indexOf", "onSpaceOrEnter", "innerFn", "onFocus", "event", "key", "preventDefault", "stopPropagation", "executeInTheNextEventLoopTick", "fn", "setTimeout", "doNothing", "getActiveElement", "root", "arguments", "length", "undefined", "document", "activeEl", "activeElement", "shadowRoot"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/utils/utils.js"], "sourcesContent": ["/* Use it instead of .includes method for IE support */\nexport function arrayIncludes(array, itemOrItems) {\n  if (Array.isArray(itemOrItems)) {\n    return itemOrItems.every(item => array.indexOf(item) !== -1);\n  }\n\n  return array.indexOf(itemOrItems) !== -1;\n}\nexport const onSpaceOrEnter = (innerFn, onFocus) => event => {\n  if (event.key === 'Enter' || event.key === ' ') {\n    innerFn(event); // prevent any side effects\n\n    event.preventDefault();\n    event.stopPropagation();\n  }\n\n  if (onFocus) {\n    onFocus(event);\n  }\n};\nexport const executeInTheNextEventLoopTick = fn => {\n  setTimeout(fn, 0);\n};\nexport const doNothing = () => {}; // https://www.abeautifulsite.net/posts/finding-the-active-element-in-a-shadow-root/\n\nexport const getActiveElement = (root = document) => {\n  const activeEl = root.activeElement;\n\n  if (!activeEl) {\n    return null;\n  }\n\n  if (activeEl.shadowRoot) {\n    return getActiveElement(activeEl.shadowRoot);\n  }\n\n  return activeEl;\n};"], "mappings": "AAAA;AACA,OAAO,SAASA,aAAaA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAChD,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;IAC9B,OAAOA,WAAW,CAACG,KAAK,CAACC,IAAI,IAAIL,KAAK,CAACM,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9D;EAEA,OAAOL,KAAK,CAACM,OAAO,CAACL,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1C;AACA,OAAO,MAAMM,cAAc,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAKC,KAAK,IAAI;EAC3D,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAID,KAAK,CAACC,GAAG,KAAK,GAAG,EAAE;IAC9CH,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC;;IAEhBA,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;EACzB;EAEA,IAAIJ,OAAO,EAAE;IACXA,OAAO,CAACC,KAAK,CAAC;EAChB;AACF,CAAC;AACD,OAAO,MAAMI,6BAA6B,GAAGC,EAAE,IAAI;EACjDC,UAAU,CAACD,EAAE,EAAE,CAAC,CAAC;AACnB,CAAC;AACD,OAAO,MAAME,SAAS,GAAGA,CAAA,KAAM,CAAC,CAAC,CAAC,CAAC;;AAEnC,OAAO,MAAMC,gBAAgB,GAAG,SAAAA,CAAA,EAAqB;EAAA,IAApBC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,QAAQ;EAC9C,MAAMC,QAAQ,GAAGL,IAAI,CAACM,aAAa;EAEnC,IAAI,CAACD,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,IAAIA,QAAQ,CAACE,UAAU,EAAE;IACvB,OAAOR,gBAAgB,CAACM,QAAQ,CAACE,UAAU,CAAC;EAC9C;EAEA,OAAOF,QAAQ;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}