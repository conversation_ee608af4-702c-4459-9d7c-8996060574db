{"ast": null, "code": "import axios from'axios';// Get base URL from environment or use window.location.origin as fallback\nconst getBaseUrl=()=>{if(process.env.REACT_APP_API_URL)return process.env.REACT_APP_API_URL;// In development, use the proxy setup (React dev server will proxy /api to backend)\nif(process.env.NODE_ENV==='development'){// Use relative URLs so the proxy can handle routing\nreturn'';}// In production, determine backend URL based on current location\nconst currentHost=window.location.hostname;const currentPort=window.location.port;// If accessing via localhost, use localhost backend\nif(currentHost==='localhost'||currentHost==='127.0.0.1'){return'http://localhost:1976';}// Otherwise, use the same hostname with backend port\nreturn\"http://\".concat(currentHost,\":1976\");};const instance=axios.create({baseURL:getBaseUrl(),timeout:45000,// Increased timeout for mobile networks\nheaders:{'Content-Type':'application/json','Accept':'application/json'},// Better mobile network handling\nvalidateStatus:function(status){return status>=200&&status<300;// default\n},// Retry configuration for mobile networks\nretry:3,retryDelay:1000});// Request interceptor\ninstance.interceptors.request.use(config=>{var _config$method;const token=localStorage.getItem('token');if(token){config.headers.Authorization=\"Bearer \".concat(token);}console.log(\"API Request: \".concat((_config$method=config.method)===null||_config$method===void 0?void 0:_config$method.toUpperCase(),\" \").concat(config.url));return config;},error=>{console.error('Request error:',error);return Promise.reject(error);});// Response interceptor with retry logic\ninstance.interceptors.response.use(response=>{console.log(\"API Response: \".concat(response.status,\" for \").concat(response.config.url));return response;},async error=>{var _error$response;const config=error.config;console.error('Response error:',error.response||error);// Retry logic for network errors (especially useful for mobile)\nif((!error.response||error.response.status>=500)&&config&&!config.__isRetryRequest){config.__retryCount=config.__retryCount||0;if(config.__retryCount<(config.retry||3)){config.__retryCount++;config.__isRetryRequest=true;console.log(\"Retrying request (\".concat(config.__retryCount,\"/\").concat(config.retry||3,\"): \").concat(config.url));// Wait before retrying\nawait new Promise(resolve=>setTimeout(resolve,config.retryDelay||1000));return instance(config);}}if(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===401){console.log('Authentication error, redirecting to login');// Clear token and redirect to login on unauthorized\nlocalStorage.removeItem('token');delete instance.defaults.headers.common['Authorization'];// Use a slight delay to ensure console logs are visible\nsetTimeout(()=>{window.location.href='/login';},100);}return Promise.reject(error);});// Expose the current baseURL for debugging\nconsole.log(\"API base URL: \".concat(instance.defaults.baseURL));export default instance;", "map": {"version": 3, "names": ["axios", "getBaseUrl", "process", "env", "REACT_APP_API_URL", "NODE_ENV", "currentHost", "window", "location", "hostname", "currentPort", "port", "concat", "instance", "create", "baseURL", "timeout", "headers", "validateStatus", "status", "retry", "retry<PERSON><PERSON><PERSON>", "interceptors", "request", "use", "config", "_config$method", "token", "localStorage", "getItem", "Authorization", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "_error$response", "__isRetryRequest", "__retryCount", "resolve", "setTimeout", "removeItem", "defaults", "common", "href"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/utils/axiosConfig.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Get base URL from environment or use window.location.origin as fallback\r\nconst getBaseUrl = () => {\r\n    if (process.env.REACT_APP_API_URL) return process.env.REACT_APP_API_URL;\r\n\r\n    // In development, use the proxy setup (React dev server will proxy /api to backend)\r\n    if (process.env.NODE_ENV === 'development') {\r\n        // Use relative URLs so the proxy can handle routing\r\n        return '';\r\n    }\r\n\r\n    // In production, determine backend URL based on current location\r\n    const currentHost = window.location.hostname;\r\n    const currentPort = window.location.port;\r\n\r\n    // If accessing via localhost, use localhost backend\r\n    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {\r\n        return 'http://localhost:1976';\r\n    }\r\n\r\n    // Otherwise, use the same hostname with backend port\r\n    return `http://${currentHost}:1976`;\r\n};\r\n\r\nconst instance = axios.create({\r\n    baseURL: getBaseUrl(),\r\n    timeout: 45000, // Increased timeout for mobile networks\r\n    headers: {\r\n        'Content-Type': 'application/json',\r\n        'Accept': 'application/json',\r\n    },\r\n    // Better mobile network handling\r\n    validateStatus: function (status) {\r\n        return status >= 200 && status < 300; // default\r\n    },\r\n    // Retry configuration for mobile networks\r\n    retry: 3,\r\n    retryDelay: 1000,\r\n});\r\n\r\n// Request interceptor\r\ninstance.interceptors.request.use(\r\n    (config) => {\r\n        const token = localStorage.getItem('token');\r\n        if (token) {\r\n            config.headers.Authorization = `Bearer ${token}`;\r\n        }\r\n\r\n        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\r\n        return config;\r\n    },\r\n    (error) => {\r\n        console.error('Request error:', error);\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Response interceptor with retry logic\r\ninstance.interceptors.response.use(\r\n    (response) => {\r\n        console.log(`API Response: ${response.status} for ${response.config.url}`);\r\n        return response;\r\n    },\r\n    async (error) => {\r\n        const config = error.config;\r\n\r\n        console.error('Response error:', error.response || error);\r\n\r\n        // Retry logic for network errors (especially useful for mobile)\r\n        if ((!error.response || error.response.status >= 500) && config && !config.__isRetryRequest) {\r\n            config.__retryCount = config.__retryCount || 0;\r\n\r\n            if (config.__retryCount < (config.retry || 3)) {\r\n                config.__retryCount++;\r\n                config.__isRetryRequest = true;\r\n\r\n                console.log(`Retrying request (${config.__retryCount}/${config.retry || 3}): ${config.url}`);\r\n\r\n                // Wait before retrying\r\n                await new Promise(resolve => setTimeout(resolve, config.retryDelay || 1000));\r\n\r\n                return instance(config);\r\n            }\r\n        }\r\n\r\n        if (error.response?.status === 401) {\r\n            console.log('Authentication error, redirecting to login');\r\n            // Clear token and redirect to login on unauthorized\r\n            localStorage.removeItem('token');\r\n            delete instance.defaults.headers.common['Authorization'];\r\n\r\n            // Use a slight delay to ensure console logs are visible\r\n            setTimeout(() => {\r\n                window.location.href = '/login';\r\n            }, 100);\r\n        }\r\n\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Expose the current baseURL for debugging\r\nconsole.log(`API base URL: ${instance.defaults.baseURL}`);\r\n\r\nexport default instance; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB;AACA,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACrB,GAAIC,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAE,MAAO,CAAAF,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAEvE;AACA,GAAIF,OAAO,CAACC,GAAG,CAACE,QAAQ,GAAK,aAAa,CAAE,CACxC;AACA,MAAO,EAAE,CACb,CAEA;AACA,KAAM,CAAAC,WAAW,CAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAC5C,KAAM,CAAAC,WAAW,CAAGH,MAAM,CAACC,QAAQ,CAACG,IAAI,CAExC;AACA,GAAIL,WAAW,GAAK,WAAW,EAAIA,WAAW,GAAK,WAAW,CAAE,CAC5D,MAAO,uBAAuB,CAClC,CAEA;AACA,gBAAAM,MAAA,CAAiBN,WAAW,UAChC,CAAC,CAED,KAAM,CAAAO,QAAQ,CAAGb,KAAK,CAACc,MAAM,CAAC,CAC1BC,OAAO,CAAEd,UAAU,CAAC,CAAC,CACrBe,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,CACL,cAAc,CAAE,kBAAkB,CAClC,QAAQ,CAAE,kBACd,CAAC,CACD;AACAC,cAAc,CAAE,QAAAA,CAAUC,MAAM,CAAE,CAC9B,MAAO,CAAAA,MAAM,EAAI,GAAG,EAAIA,MAAM,CAAG,GAAG,CAAE;AAC1C,CAAC,CACD;AACAC,KAAK,CAAE,CAAC,CACRC,UAAU,CAAE,IAChB,CAAC,CAAC,CAEF;AACAR,QAAQ,CAACS,YAAY,CAACC,OAAO,CAACC,GAAG,CAC5BC,MAAM,EAAK,KAAAC,cAAA,CACR,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,CAAE,CACPF,MAAM,CAACR,OAAO,CAACa,aAAa,WAAAlB,MAAA,CAAae,KAAK,CAAE,CACpD,CAEAI,OAAO,CAACC,GAAG,iBAAApB,MAAA,EAAAc,cAAA,CAAiBD,MAAM,CAACQ,MAAM,UAAAP,cAAA,iBAAbA,cAAA,CAAeQ,WAAW,CAAC,CAAC,MAAAtB,MAAA,CAAIa,MAAM,CAACU,GAAG,CAAE,CAAC,CACzE,MAAO,CAAAV,MAAM,CACjB,CAAC,CACAW,KAAK,EAAK,CACPL,OAAO,CAACK,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACtC,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAChC,CACJ,CAAC,CAED;AACAvB,QAAQ,CAACS,YAAY,CAACiB,QAAQ,CAACf,GAAG,CAC7Be,QAAQ,EAAK,CACVR,OAAO,CAACC,GAAG,kBAAApB,MAAA,CAAkB2B,QAAQ,CAACpB,MAAM,UAAAP,MAAA,CAAQ2B,QAAQ,CAACd,MAAM,CAACU,GAAG,CAAE,CAAC,CAC1E,MAAO,CAAAI,QAAQ,CACnB,CAAC,CACD,KAAO,CAAAH,KAAK,EAAK,KAAAI,eAAA,CACb,KAAM,CAAAf,MAAM,CAAGW,KAAK,CAACX,MAAM,CAE3BM,OAAO,CAACK,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAACG,QAAQ,EAAIH,KAAK,CAAC,CAEzD;AACA,GAAI,CAAC,CAACA,KAAK,CAACG,QAAQ,EAAIH,KAAK,CAACG,QAAQ,CAACpB,MAAM,EAAI,GAAG,GAAKM,MAAM,EAAI,CAACA,MAAM,CAACgB,gBAAgB,CAAE,CACzFhB,MAAM,CAACiB,YAAY,CAAGjB,MAAM,CAACiB,YAAY,EAAI,CAAC,CAE9C,GAAIjB,MAAM,CAACiB,YAAY,EAAIjB,MAAM,CAACL,KAAK,EAAI,CAAC,CAAC,CAAE,CAC3CK,MAAM,CAACiB,YAAY,EAAE,CACrBjB,MAAM,CAACgB,gBAAgB,CAAG,IAAI,CAE9BV,OAAO,CAACC,GAAG,sBAAApB,MAAA,CAAsBa,MAAM,CAACiB,YAAY,MAAA9B,MAAA,CAAIa,MAAM,CAACL,KAAK,EAAI,CAAC,QAAAR,MAAA,CAAMa,MAAM,CAACU,GAAG,CAAE,CAAC,CAE5F;AACA,KAAM,IAAI,CAAAE,OAAO,CAACM,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAElB,MAAM,CAACJ,UAAU,EAAI,IAAI,CAAC,CAAC,CAE5E,MAAO,CAAAR,QAAQ,CAACY,MAAM,CAAC,CAC3B,CACJ,CAEA,GAAI,EAAAe,eAAA,CAAAJ,KAAK,CAACG,QAAQ,UAAAC,eAAA,iBAAdA,eAAA,CAAgBrB,MAAM,IAAK,GAAG,CAAE,CAChCY,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC,CACzD;AACAJ,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC,CAChC,MAAO,CAAAhC,QAAQ,CAACiC,QAAQ,CAAC7B,OAAO,CAAC8B,MAAM,CAAC,eAAe,CAAC,CAExD;AACAH,UAAU,CAAC,IAAM,CACbrC,MAAM,CAACC,QAAQ,CAACwC,IAAI,CAAG,QAAQ,CACnC,CAAC,CAAE,GAAG,CAAC,CACX,CAEA,MAAO,CAAAX,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAChC,CACJ,CAAC,CAED;AACAL,OAAO,CAACC,GAAG,kBAAApB,MAAA,CAAkBC,QAAQ,CAACiC,QAAQ,CAAC/B,OAAO,CAAE,CAAC,CAEzD,cAAe,CAAAF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}