{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getPickersMonthUtilityClass(slot) {\n  // TODO v6 Rename 'PrivatePickersMonth' to 'MuiPickersMonth' to follow convention\n  return generateUtilityClass('PrivatePickersMonth', slot);\n}\nexport const pickersMonthClasses = generateUtilityClasses(\n// TODO v6 Rename 'PrivatePickersMonth' to 'MuiPickersMonth' to follow convention\n'PrivatePickersMonth', ['root', 'selected']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getPickersMonthUtilityClass", "slot", "pickersMonthClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/MonthPicker/pickersMonthClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getPickersMonthUtilityClass(slot) {\n  // TODO v6 Rename 'PrivatePickersMonth' to 'MuiPickersMonth' to follow convention\n  return generateUtilityClass('PrivatePickersMonth', slot);\n}\nexport const pickersMonthClasses = generateUtilityClasses( // TODO v6 Rename 'PrivatePickersMonth' to 'MuiPickersMonth' to follow convention\n'PrivatePickersMonth', ['root', 'selected']);"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,eAAe;AAC5E,OAAO,SAASC,2BAA2BA,CAACC,IAAI,EAAE;EAChD;EACA,OAAOH,oBAAoB,CAAC,qBAAqB,EAAEG,IAAI,CAAC;AAC1D;AACA,OAAO,MAAMC,mBAAmB,GAAGH,sBAAsB;AAAE;AAC3D,qBAAqB,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}