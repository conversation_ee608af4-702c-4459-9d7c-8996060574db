{"ast": null, "code": "'use client';\n\nexport * from './Option';\nexport * from './Option.types';\nexport * from './optionClasses';", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Option/index.js"], "sourcesContent": ["'use client';\n\nexport * from './Option';\nexport * from './Option.types';\nexport * from './optionClasses';"], "mappings": "AAAA,YAAY;;AAEZ,cAAc,UAAU;AACxB,cAAc,gBAAgB;AAC9B,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}