{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getClockUtilityClass(slot) {\n  return generateUtilityClass('MuiClock', slot);\n}\nexport const clockClasses = generateUtilityClasses('MuiClock', ['root', 'clock', 'wrapper', 'squareMask', 'pin', 'amButton', 'pmButton']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getClockUtilityClass", "slot", "clockClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/ClockPicker/clockClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getClockUtilityClass(slot) {\n  return generateUtilityClass('MuiClock', slot);\n}\nexport const clockClasses = generateUtilityClasses('MuiClock', ['root', 'clock', 'wrapper', 'squareMask', 'pin', 'amButton', 'pmButton']);"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,eAAe;AAC5E,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOH,oBAAoB,CAAC,UAAU,EAAEG,IAAI,CAAC;AAC/C;AACA,OAAO,MAAMC,YAAY,GAAGH,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}