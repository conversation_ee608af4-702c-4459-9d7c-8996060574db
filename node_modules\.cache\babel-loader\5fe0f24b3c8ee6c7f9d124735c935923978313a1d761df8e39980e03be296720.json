{"ast": null, "code": "import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst TabsContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  TabsContext.displayName = 'TabsContext';\n}\nexport function useTabsContext() {\n  const context = React.useContext(TabsContext);\n  if (context == null) {\n    throw new Error('No TabsContext provided');\n  }\n  return context;\n}\nexport { TabsContext };", "map": {"version": 3, "names": ["React", "TabsContext", "createContext", "process", "env", "NODE_ENV", "displayName", "useTabsContext", "context", "useContext", "Error"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Tabs/TabsContext.js"], "sourcesContent": ["import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst TabsContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  TabsContext.displayName = 'TabsContext';\n}\nexport function useTabsContext() {\n  const context = React.useContext(TabsContext);\n  if (context == null) {\n    throw new Error('No TabsContext provided');\n  }\n  return context;\n}\nexport { TabsContext };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,MAAMC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC1D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,WAAW,CAACK,WAAW,GAAG,aAAa;AACzC;AACA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,MAAMC,OAAO,GAAGR,KAAK,CAACS,UAAU,CAACR,WAAW,CAAC;EAC7C,IAAIO,OAAO,IAAI,IAAI,EAAE;IACnB,MAAM,IAAIE,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EACA,OAAOF,OAAO;AAChB;AACA,SAASP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}