{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getTabUtilityClass(slot) {\n  return generateUtilityClass('MuiTab', slot);\n}\nexport const tabClasses = generateUtilityClasses('MuiTab', ['root', 'selected', 'disabled']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabUtilityClass", "slot", "tabClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Tab/tabClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getTabUtilityClass(slot) {\n  return generateUtilityClass('MuiTab', slot);\n}\nexport const tabClasses = generateUtilityClasses('MuiTab', ['root', 'selected', 'disabled']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EACvC,OAAOH,oBAAoB,CAAC,QAAQ,EAAEG,IAAI,CAAC;AAC7C;AACA,OAAO,MAAMC,UAAU,GAAGH,sBAAsB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}