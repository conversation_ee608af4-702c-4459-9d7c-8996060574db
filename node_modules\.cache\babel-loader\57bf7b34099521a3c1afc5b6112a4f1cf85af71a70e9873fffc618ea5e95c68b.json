{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getYearPickerUtilityClass(slot) {\n  return generateUtilityClass('MuiYearPicker', slot);\n}\nexport const yearPickerClasses = generateUtilityClasses('MuiYearPicker', ['root']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getYearPickerUtilityClass", "slot", "yearPickerClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/YearPicker/yearPickerClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getYearPickerUtilityClass(slot) {\n  return generateUtilityClass('MuiYearPicker', slot);\n}\nexport const yearPickerClasses = generateUtilityClasses('MuiYearPicker', ['root']);"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,eAAe;AAC5E,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOH,oBAAoB,CAAC,eAAe,EAAEG,IAAI,CAAC;AACpD;AACA,OAAO,MAAMC,iBAAiB,GAAGH,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}