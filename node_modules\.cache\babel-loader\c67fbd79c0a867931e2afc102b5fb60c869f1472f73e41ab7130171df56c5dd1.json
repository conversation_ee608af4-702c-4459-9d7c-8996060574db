{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getPickersArrowSwitcherUtilityClass(slot) {\n  return generateUtilityClass('MuiPickersArrowSwitcher', slot);\n}\nexport const pickersArrowSwitcherClasses = generateUtilityClasses('MuiPickersArrowSwitcher', ['root', 'spacer', 'button']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getPickersArrowSwitcherUtilityClass", "slot", "pickersArrowSwitcherClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/components/pickersArrowSwitcherClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getPickersArrowSwitcherUtilityClass(slot) {\n  return generateUtilityClass('MuiPickersArrowSwitcher', slot);\n}\nexport const pickersArrowSwitcherClasses = generateUtilityClasses('MuiPickersArrowSwitcher', ['root', 'spacer', 'button']);"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,eAAe;AAC5E,OAAO,SAASC,mCAAmCA,CAACC,IAAI,EAAE;EACxD,OAAOH,oBAAoB,CAAC,yBAAyB,EAAEG,IAAI,CAAC;AAC9D;AACA,OAAO,MAAMC,2BAA2B,GAAGH,sBAAsB,CAAC,yBAAyB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}