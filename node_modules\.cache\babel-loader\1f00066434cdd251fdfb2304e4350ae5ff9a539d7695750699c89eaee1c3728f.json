{"ast": null, "code": "export const findClosestEnabledDate = _ref => {\n  let {\n    date,\n    disableFuture,\n    disablePast,\n    maxDate,\n    minDate,\n    isDateDisabled,\n    utils\n  } = _ref;\n  const today = utils.startOfDay(utils.date());\n  if (disablePast && utils.isBefore(minDate, today)) {\n    minDate = today;\n  }\n  if (disableFuture && utils.isAfter(maxDate, today)) {\n    maxDate = today;\n  }\n  let forward = date;\n  let backward = date;\n  if (utils.isBefore(date, minDate)) {\n    forward = utils.date(minDate);\n    backward = null;\n  }\n  if (utils.isAfter(date, maxDate)) {\n    if (backward) {\n      backward = utils.date(maxDate);\n    }\n    forward = null;\n  }\n  while (forward || backward) {\n    if (forward && utils.isAfter(forward, maxDate)) {\n      forward = null;\n    }\n    if (backward && utils.isBefore(backward, minDate)) {\n      backward = null;\n    }\n    if (forward) {\n      if (!isDateDisabled(forward)) {\n        return forward;\n      }\n      forward = utils.addDays(forward, 1);\n    }\n    if (backward) {\n      if (!isDateDisabled(backward)) {\n        return backward;\n      }\n      backward = utils.addDays(backward, -1);\n    }\n  }\n  return null;\n};\nexport const parsePickerInputValue = (utils, value) => {\n  const parsedValue = utils.date(value);\n  return utils.isValid(parsedValue) ? parsedValue : null;\n};\nexport const parseNonNullablePickerDate = (utils, value, defaultValue) => {\n  if (value == null) {\n    return defaultValue;\n  }\n  const parsedValue = utils.date(value);\n  const isDateValid = utils.isValid(parsedValue);\n  if (isDateValid) {\n    return parsedValue;\n  }\n  return defaultValue;\n};", "map": {"version": 3, "names": ["findClosestEnabledDate", "_ref", "date", "disableFuture", "disablePast", "maxDate", "minDate", "isDateDisabled", "utils", "today", "startOfDay", "isBefore", "isAfter", "forward", "backward", "addDays", "parsePickerInputValue", "value", "parsedValue", "<PERSON><PERSON><PERSON><PERSON>", "parseNonNullablePickerDate", "defaultValue", "isDateValid"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/utils/date-utils.js"], "sourcesContent": ["export const findClosestEnabledDate = ({\n  date,\n  disableFuture,\n  disablePast,\n  maxDate,\n  minDate,\n  isDateDisabled,\n  utils\n}) => {\n  const today = utils.startOfDay(utils.date());\n\n  if (disablePast && utils.isBefore(minDate, today)) {\n    minDate = today;\n  }\n\n  if (disableFuture && utils.isAfter(maxDate, today)) {\n    maxDate = today;\n  }\n\n  let forward = date;\n  let backward = date;\n\n  if (utils.isBefore(date, minDate)) {\n    forward = utils.date(minDate);\n    backward = null;\n  }\n\n  if (utils.isAfter(date, maxDate)) {\n    if (backward) {\n      backward = utils.date(maxDate);\n    }\n\n    forward = null;\n  }\n\n  while (forward || backward) {\n    if (forward && utils.isAfter(forward, maxDate)) {\n      forward = null;\n    }\n\n    if (backward && utils.isBefore(backward, minDate)) {\n      backward = null;\n    }\n\n    if (forward) {\n      if (!isDateDisabled(forward)) {\n        return forward;\n      }\n\n      forward = utils.addDays(forward, 1);\n    }\n\n    if (backward) {\n      if (!isDateDisabled(backward)) {\n        return backward;\n      }\n\n      backward = utils.addDays(backward, -1);\n    }\n  }\n\n  return null;\n};\nexport const parsePickerInputValue = (utils, value) => {\n  const parsedValue = utils.date(value);\n  return utils.isValid(parsedValue) ? parsedValue : null;\n};\nexport const parseNonNullablePickerDate = (utils, value, defaultValue) => {\n  if (value == null) {\n    return defaultValue;\n  }\n\n  const parsedValue = utils.date(value);\n  const isDateValid = utils.isValid(parsedValue);\n\n  if (isDateValid) {\n    return parsedValue;\n  }\n\n  return defaultValue;\n};"], "mappings": "AAAA,OAAO,MAAMA,sBAAsB,GAAGC,IAAA,IAQhC;EAAA,IARiC;IACrCC,IAAI;IACJC,aAAa;IACbC,WAAW;IACXC,OAAO;IACPC,OAAO;IACPC,cAAc;IACdC;EACF,CAAC,GAAAP,IAAA;EACC,MAAMQ,KAAK,GAAGD,KAAK,CAACE,UAAU,CAACF,KAAK,CAACN,IAAI,CAAC,CAAC,CAAC;EAE5C,IAAIE,WAAW,IAAII,KAAK,CAACG,QAAQ,CAACL,OAAO,EAAEG,KAAK,CAAC,EAAE;IACjDH,OAAO,GAAGG,KAAK;EACjB;EAEA,IAAIN,aAAa,IAAIK,KAAK,CAACI,OAAO,CAACP,OAAO,EAAEI,KAAK,CAAC,EAAE;IAClDJ,OAAO,GAAGI,KAAK;EACjB;EAEA,IAAII,OAAO,GAAGX,IAAI;EAClB,IAAIY,QAAQ,GAAGZ,IAAI;EAEnB,IAAIM,KAAK,CAACG,QAAQ,CAACT,IAAI,EAAEI,OAAO,CAAC,EAAE;IACjCO,OAAO,GAAGL,KAAK,CAACN,IAAI,CAACI,OAAO,CAAC;IAC7BQ,QAAQ,GAAG,IAAI;EACjB;EAEA,IAAIN,KAAK,CAACI,OAAO,CAACV,IAAI,EAAEG,OAAO,CAAC,EAAE;IAChC,IAAIS,QAAQ,EAAE;MACZA,QAAQ,GAAGN,KAAK,CAACN,IAAI,CAACG,OAAO,CAAC;IAChC;IAEAQ,OAAO,GAAG,IAAI;EAChB;EAEA,OAAOA,OAAO,IAAIC,QAAQ,EAAE;IAC1B,IAAID,OAAO,IAAIL,KAAK,CAACI,OAAO,CAACC,OAAO,EAAER,OAAO,CAAC,EAAE;MAC9CQ,OAAO,GAAG,IAAI;IAChB;IAEA,IAAIC,QAAQ,IAAIN,KAAK,CAACG,QAAQ,CAACG,QAAQ,EAAER,OAAO,CAAC,EAAE;MACjDQ,QAAQ,GAAG,IAAI;IACjB;IAEA,IAAID,OAAO,EAAE;MACX,IAAI,CAACN,cAAc,CAACM,OAAO,CAAC,EAAE;QAC5B,OAAOA,OAAO;MAChB;MAEAA,OAAO,GAAGL,KAAK,CAACO,OAAO,CAACF,OAAO,EAAE,CAAC,CAAC;IACrC;IAEA,IAAIC,QAAQ,EAAE;MACZ,IAAI,CAACP,cAAc,CAACO,QAAQ,CAAC,EAAE;QAC7B,OAAOA,QAAQ;MACjB;MAEAA,QAAQ,GAAGN,KAAK,CAACO,OAAO,CAACD,QAAQ,EAAE,CAAC,CAAC,CAAC;IACxC;EACF;EAEA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,MAAME,qBAAqB,GAAGA,CAACR,KAAK,EAAES,KAAK,KAAK;EACrD,MAAMC,WAAW,GAAGV,KAAK,CAACN,IAAI,CAACe,KAAK,CAAC;EACrC,OAAOT,KAAK,CAACW,OAAO,CAACD,WAAW,CAAC,GAAGA,WAAW,GAAG,IAAI;AACxD,CAAC;AACD,OAAO,MAAME,0BAA0B,GAAGA,CAACZ,KAAK,EAAES,KAAK,EAAEI,YAAY,KAAK;EACxE,IAAIJ,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOI,YAAY;EACrB;EAEA,MAAMH,WAAW,GAAGV,KAAK,CAACN,IAAI,CAACe,KAAK,CAAC;EACrC,MAAMK,WAAW,GAAGd,KAAK,CAACW,OAAO,CAACD,WAAW,CAAC;EAE9C,IAAII,WAAW,EAAE;IACf,OAAOJ,WAAW;EACpB;EAEA,OAAOG,YAAY;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}