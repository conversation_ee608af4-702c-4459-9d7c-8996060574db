{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getSnackbarUtilityClass(slot) {\n  return generateUtilityClass('MuiSnackbar', slot);\n}\nexport const snackbarClasses = generateUtilityClasses('MuiSnackbar', ['root']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getSnackbarUtilityClass", "slot", "snackbarClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Snackbar/snackbarClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getSnackbarUtilityClass(slot) {\n  return generateUtilityClass('MuiSnackbar', slot);\n}\nexport const snackbarClasses = generateUtilityClasses('MuiSnackbar', ['root']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,OAAO,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}