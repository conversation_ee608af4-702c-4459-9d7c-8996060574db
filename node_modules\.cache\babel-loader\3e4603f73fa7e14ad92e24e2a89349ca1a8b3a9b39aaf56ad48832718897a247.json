{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useId as useId, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useButton } from '../useButton';\nimport { useListItem } from '../useList';\nimport { DropdownActionTypes } from '../useDropdown';\nimport { DropdownContext } from '../useDropdown/DropdownContext';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\nimport { useCompoundItem } from '../useCompound';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nfunction idGenerator(existingKeys) {\n  return \"menu-item-\".concat(existingKeys.size);\n}\nconst FALLBACK_MENU_CONTEXT = {\n  dispatch: () => {},\n  popupId: '',\n  registerPopup: () => {},\n  registerTrigger: () => {},\n  state: {\n    open: true\n  },\n  triggerElement: null\n};\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useMenuItem API](https://mui.com/base-ui/react-menu/hooks-api/#use-menu-item)\n */\nexport function useMenuItem(params) {\n  var _React$useContext;\n  const {\n    disabled = false,\n    id: idParam,\n    rootRef: externalRef,\n    label\n  } = params;\n  const id = useId(idParam);\n  const itemRef = React.useRef(null);\n  const itemMetadata = React.useMemo(() => ({\n    disabled,\n    id: id != null ? id : '',\n    label,\n    ref: itemRef\n  }), [disabled, id, label]);\n  const {\n    dispatch\n  } = (_React$useContext = React.useContext(DropdownContext)) != null ? _React$useContext : FALLBACK_MENU_CONTEXT;\n  const {\n    getRootProps: getListRootProps,\n    highlighted\n  } = useListItem({\n    item: id\n  });\n  const {\n    index,\n    totalItemCount\n  } = useCompoundItem(id != null ? id : idGenerator, itemMetadata);\n  const {\n    getRootProps: getButtonProps,\n    focusVisible,\n    rootRef: buttonRefHandler\n  } = useButton({\n    disabled,\n    focusableWhenDisabled: true\n  });\n  const handleRef = useForkRef(buttonRefHandler, externalRef, itemRef);\n  React.useDebugValue({\n    id,\n    highlighted,\n    disabled,\n    label\n  });\n  const createHandleClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    (_otherHandlers$onClic = otherHandlers.onClick) == null || _otherHandlers$onClic.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    dispatch({\n      type: DropdownActionTypes.close,\n      event\n    });\n  };\n  const getOwnHandlers = function () {\n    let otherHandlers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return _extends({}, otherHandlers, {\n      onClick: createHandleClick(otherHandlers)\n    });\n  };\n  function getRootProps() {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const getCombinedRootProps = combineHooksSlotProps(getOwnHandlers, combineHooksSlotProps(getButtonProps, getListRootProps));\n    return _extends({}, externalProps, externalEventHandlers, getCombinedRootProps(externalEventHandlers), {\n      id,\n      ref: handleRef,\n      role: 'menuitem'\n    });\n  }\n\n  // If `id` is undefined (during SSR in React < 18), we fall back to rendering a simplified menu item\n  // which does not have access to infortmation about its position or highlighted state.\n  if (id === undefined) {\n    return {\n      getRootProps,\n      disabled: false,\n      focusVisible,\n      highlighted: false,\n      index: -1,\n      totalItemCount: 0,\n      rootRef: handleRef\n    };\n  }\n  return {\n    getRootProps,\n    disabled,\n    focusVisible,\n    highlighted,\n    index,\n    totalItemCount,\n    rootRef: handleRef\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useId", "useId", "unstable_useForkRef", "useForkRef", "useButton", "useListItem", "DropdownActionTypes", "DropdownContext", "combineHooksSlotProps", "useCompoundItem", "extractEventHandlers", "idGenerator", "existingKeys", "concat", "size", "FALLBACK_MENU_CONTEXT", "dispatch", "popupId", "registerPopup", "registerTrigger", "state", "open", "triggerElement", "useMenuItem", "params", "_React$useContext", "disabled", "id", "idParam", "rootRef", "externalRef", "label", "itemRef", "useRef", "itemMetadata", "useMemo", "ref", "useContext", "getRootProps", "getListRootProps", "highlighted", "item", "index", "totalItemCount", "getButtonProps", "focusVisible", "buttonRefHandler", "focusableWhenDisabled", "handleRef", "useDebugValue", "createHandleClick", "otherHandlers", "event", "_otherHandlers$onClic", "onClick", "call", "defaultMuiPrevented", "type", "close", "getOwnHandlers", "arguments", "length", "undefined", "externalProps", "externalEventHandlers", "getCombinedRootProps", "role"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/useMenuItem/useMenuItem.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useId as useId, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useButton } from '../useButton';\nimport { useListItem } from '../useList';\nimport { DropdownActionTypes } from '../useDropdown';\nimport { DropdownContext } from '../useDropdown/DropdownContext';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\nimport { useCompoundItem } from '../useCompound';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nfunction idGenerator(existingKeys) {\n  return `menu-item-${existingKeys.size}`;\n}\nconst FALLBACK_MENU_CONTEXT = {\n  dispatch: () => {},\n  popupId: '',\n  registerPopup: () => {},\n  registerTrigger: () => {},\n  state: {\n    open: true\n  },\n  triggerElement: null\n};\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useMenuItem API](https://mui.com/base-ui/react-menu/hooks-api/#use-menu-item)\n */\nexport function useMenuItem(params) {\n  var _React$useContext;\n  const {\n    disabled = false,\n    id: idParam,\n    rootRef: externalRef,\n    label\n  } = params;\n  const id = useId(idParam);\n  const itemRef = React.useRef(null);\n  const itemMetadata = React.useMemo(() => ({\n    disabled,\n    id: id != null ? id : '',\n    label,\n    ref: itemRef\n  }), [disabled, id, label]);\n  const {\n    dispatch\n  } = (_React$useContext = React.useContext(DropdownContext)) != null ? _React$useContext : FALLBACK_MENU_CONTEXT;\n  const {\n    getRootProps: getListRootProps,\n    highlighted\n  } = useListItem({\n    item: id\n  });\n  const {\n    index,\n    totalItemCount\n  } = useCompoundItem(id != null ? id : idGenerator, itemMetadata);\n  const {\n    getRootProps: getButtonProps,\n    focusVisible,\n    rootRef: buttonRefHandler\n  } = useButton({\n    disabled,\n    focusableWhenDisabled: true\n  });\n  const handleRef = useForkRef(buttonRefHandler, externalRef, itemRef);\n  React.useDebugValue({\n    id,\n    highlighted,\n    disabled,\n    label\n  });\n  const createHandleClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    (_otherHandlers$onClic = otherHandlers.onClick) == null || _otherHandlers$onClic.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    dispatch({\n      type: DropdownActionTypes.close,\n      event\n    });\n  };\n  const getOwnHandlers = (otherHandlers = {}) => _extends({}, otherHandlers, {\n    onClick: createHandleClick(otherHandlers)\n  });\n  function getRootProps(externalProps = {}) {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const getCombinedRootProps = combineHooksSlotProps(getOwnHandlers, combineHooksSlotProps(getButtonProps, getListRootProps));\n    return _extends({}, externalProps, externalEventHandlers, getCombinedRootProps(externalEventHandlers), {\n      id,\n      ref: handleRef,\n      role: 'menuitem'\n    });\n  }\n\n  // If `id` is undefined (during SSR in React < 18), we fall back to rendering a simplified menu item\n  // which does not have access to infortmation about its position or highlighted state.\n  if (id === undefined) {\n    return {\n      getRootProps,\n      disabled: false,\n      focusVisible,\n      highlighted: false,\n      index: -1,\n      totalItemCount: 0,\n      rootRef: handleRef\n    };\n  }\n  return {\n    getRootProps,\n    disabled,\n    focusVisible,\n    highlighted,\n    index,\n    totalItemCount,\n    rootRef: handleRef\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,IAAIC,KAAK,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AACvF,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,WAAW,QAAQ,YAAY;AACxC,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,WAAWA,CAACC,YAAY,EAAE;EACjC,oBAAAC,MAAA,CAAoBD,YAAY,CAACE,IAAI;AACvC;AACA,MAAMC,qBAAqB,GAAG;EAC5BC,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAC;EAClBC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;EACvBC,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAC;EACzBC,KAAK,EAAE;IACLC,IAAI,EAAE;EACR,CAAC;EACDC,cAAc,EAAE;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;EAClC,IAAIC,iBAAiB;EACrB,MAAM;IACJC,QAAQ,GAAG,KAAK;IAChBC,EAAE,EAAEC,OAAO;IACXC,OAAO,EAAEC,WAAW;IACpBC;EACF,CAAC,GAAGP,MAAM;EACV,MAAMG,EAAE,GAAG1B,KAAK,CAAC2B,OAAO,CAAC;EACzB,MAAMI,OAAO,GAAGjC,KAAK,CAACkC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,YAAY,GAAGnC,KAAK,CAACoC,OAAO,CAAC,OAAO;IACxCT,QAAQ;IACRC,EAAE,EAAEA,EAAE,IAAI,IAAI,GAAGA,EAAE,GAAG,EAAE;IACxBI,KAAK;IACLK,GAAG,EAAEJ;EACP,CAAC,CAAC,EAAE,CAACN,QAAQ,EAAEC,EAAE,EAAEI,KAAK,CAAC,CAAC;EAC1B,MAAM;IACJf;EACF,CAAC,GAAG,CAACS,iBAAiB,GAAG1B,KAAK,CAACsC,UAAU,CAAC9B,eAAe,CAAC,KAAK,IAAI,GAAGkB,iBAAiB,GAAGV,qBAAqB;EAC/G,MAAM;IACJuB,YAAY,EAAEC,gBAAgB;IAC9BC;EACF,CAAC,GAAGnC,WAAW,CAAC;IACdoC,IAAI,EAAEd;EACR,CAAC,CAAC;EACF,MAAM;IACJe,KAAK;IACLC;EACF,CAAC,GAAGlC,eAAe,CAACkB,EAAE,IAAI,IAAI,GAAGA,EAAE,GAAGhB,WAAW,EAAEuB,YAAY,CAAC;EAChE,MAAM;IACJI,YAAY,EAAEM,cAAc;IAC5BC,YAAY;IACZhB,OAAO,EAAEiB;EACX,CAAC,GAAG1C,SAAS,CAAC;IACZsB,QAAQ;IACRqB,qBAAqB,EAAE;EACzB,CAAC,CAAC;EACF,MAAMC,SAAS,GAAG7C,UAAU,CAAC2C,gBAAgB,EAAEhB,WAAW,EAAEE,OAAO,CAAC;EACpEjC,KAAK,CAACkD,aAAa,CAAC;IAClBtB,EAAE;IACFa,WAAW;IACXd,QAAQ;IACRK;EACF,CAAC,CAAC;EACF,MAAMmB,iBAAiB,GAAGC,aAAa,IAAIC,KAAK,IAAI;IAClD,IAAIC,qBAAqB;IACzB,CAACA,qBAAqB,GAAGF,aAAa,CAACG,OAAO,KAAK,IAAI,IAAID,qBAAqB,CAACE,IAAI,CAACJ,aAAa,EAAEC,KAAK,CAAC;IAC3G,IAAIA,KAAK,CAACI,mBAAmB,EAAE;MAC7B;IACF;IACAxC,QAAQ,CAAC;MACPyC,IAAI,EAAEnD,mBAAmB,CAACoD,KAAK;MAC/BN;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAMO,cAAc,GAAG,SAAAA,CAAA;IAAA,IAACR,aAAa,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,OAAK9D,QAAQ,CAAC,CAAC,CAAC,EAAEqD,aAAa,EAAE;MACzEG,OAAO,EAAEJ,iBAAiB,CAACC,aAAa;IAC1C,CAAC,CAAC;EAAA;EACF,SAASb,YAAYA,CAAA,EAAqB;IAAA,IAApByB,aAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACtC,MAAMI,qBAAqB,GAAGtD,oBAAoB,CAACqD,aAAa,CAAC;IACjE,MAAME,oBAAoB,GAAGzD,qBAAqB,CAACmD,cAAc,EAAEnD,qBAAqB,CAACoC,cAAc,EAAEL,gBAAgB,CAAC,CAAC;IAC3H,OAAOzC,QAAQ,CAAC,CAAC,CAAC,EAAEiE,aAAa,EAAEC,qBAAqB,EAAEC,oBAAoB,CAACD,qBAAqB,CAAC,EAAE;MACrGrC,EAAE;MACFS,GAAG,EAAEY,SAAS;MACdkB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;;EAEA;EACA;EACA,IAAIvC,EAAE,KAAKmC,SAAS,EAAE;IACpB,OAAO;MACLxB,YAAY;MACZZ,QAAQ,EAAE,KAAK;MACfmB,YAAY;MACZL,WAAW,EAAE,KAAK;MAClBE,KAAK,EAAE,CAAC,CAAC;MACTC,cAAc,EAAE,CAAC;MACjBd,OAAO,EAAEmB;IACX,CAAC;EACH;EACA,OAAO;IACLV,YAAY;IACZZ,QAAQ;IACRmB,YAAY;IACZL,WAAW;IACXE,KAAK;IACLC,cAAc;IACdd,OAAO,EAAEmB;EACX,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}