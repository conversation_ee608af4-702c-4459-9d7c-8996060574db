{"ast": null, "code": "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "map": {"version": 3, "names": ["getDocumentElement", "getComputedStyle", "getWindowScrollBarX", "getWindowScroll", "max", "getDocumentRect", "element", "_element$ownerDocumen", "html", "winScroll", "body", "ownerDocument", "width", "scrollWidth", "clientWidth", "height", "scrollHeight", "clientHeight", "x", "scrollLeft", "y", "scrollTop", "direction"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js"], "sourcesContent": ["import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,mBAAmB,MAAM,0BAA0B;AAC1D,OAAOC,eAAe,MAAM,sBAAsB;AAClD,SAASC,GAAG,QAAQ,kBAAkB,CAAC,CAAC;AACxC;;AAEA,eAAe,SAASC,eAAeA,CAACC,OAAO,EAAE;EAC/C,IAAIC,qBAAqB;EAEzB,IAAIC,IAAI,GAAGR,kBAAkB,CAACM,OAAO,CAAC;EACtC,IAAIG,SAAS,GAAGN,eAAe,CAACG,OAAO,CAAC;EACxC,IAAII,IAAI,GAAG,CAACH,qBAAqB,GAAGD,OAAO,CAACK,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,qBAAqB,CAACG,IAAI;EACxG,IAAIE,KAAK,GAAGR,GAAG,CAACI,IAAI,CAACK,WAAW,EAAEL,IAAI,CAACM,WAAW,EAAEJ,IAAI,GAAGA,IAAI,CAACG,WAAW,GAAG,CAAC,EAAEH,IAAI,GAAGA,IAAI,CAACI,WAAW,GAAG,CAAC,CAAC;EAC7G,IAAIC,MAAM,GAAGX,GAAG,CAACI,IAAI,CAACQ,YAAY,EAAER,IAAI,CAACS,YAAY,EAAEP,IAAI,GAAGA,IAAI,CAACM,YAAY,GAAG,CAAC,EAAEN,IAAI,GAAGA,IAAI,CAACO,YAAY,GAAG,CAAC,CAAC;EAClH,IAAIC,CAAC,GAAG,CAACT,SAAS,CAACU,UAAU,GAAGjB,mBAAmB,CAACI,OAAO,CAAC;EAC5D,IAAIc,CAAC,GAAG,CAACX,SAAS,CAACY,SAAS;EAE5B,IAAIpB,gBAAgB,CAACS,IAAI,IAAIF,IAAI,CAAC,CAACc,SAAS,KAAK,KAAK,EAAE;IACtDJ,CAAC,IAAId,GAAG,CAACI,IAAI,CAACM,WAAW,EAAEJ,IAAI,GAAGA,IAAI,CAACI,WAAW,GAAG,CAAC,CAAC,GAAGF,KAAK;EACjE;EAEA,OAAO;IACLA,KAAK,EAAEA,KAAK;IACZG,MAAM,EAAEA,MAAM;IACdG,CAAC,EAAEA,CAAC;IACJE,CAAC,EAAEA;EACL,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}