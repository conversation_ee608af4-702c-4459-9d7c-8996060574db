{"ast": null, "code": "import { useReducer, useRef, useLayoutEffect, useEffect } from 'react';\nconst useRifm = props => {\n  const [, refresh] = useReducer(c => c + 1, 0);\n  const valueRef = useRef(null);\n  const {\n    replace,\n    append\n  } = props;\n  const userValue = replace ? replace(props.format(props.value)) : props.format(props.value); // state of delete button see comments below about inputType support\n\n  const isDeleleteButtonDownRef = useRef(false);\n  const onChange = evt => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (evt.target.type === 'number') {\n        console.error('Rifm does not support input type=number, use type=tel instead.');\n        return;\n      }\n      if (evt.target.type === 'date') {\n        console.error('Rifm does not support input type=date.');\n        return;\n      }\n    }\n    const eventValue = evt.target.value;\n    valueRef.current = [eventValue,\n    // eventValue\n    evt.target,\n    // input\n    eventValue.length > userValue.length,\n    // isSizeIncreaseOperation\n    isDeleleteButtonDownRef.current,\n    // isDeleleteButtonDown\n    userValue === props.format(eventValue) // isNoOperation\n    ];\n    if (process.env.NODE_ENV !== 'production') {\n      const formattedEventValue = props.format(eventValue);\n      if (eventValue !== formattedEventValue && eventValue.toLowerCase() === formattedEventValue.toLowerCase()) {\n        console.warn('Case enforcement does not work with format. Please use replace={value => value.toLowerCase()} instead');\n      }\n    } // The main trick is to update underlying input with non formatted value (= eventValue)\n    // that allows us to calculate right cursor position after formatting (see getCursorPosition)\n    // then we format new value and call props.onChange with masked/formatted value\n    // and finally we are able to set cursor position into right place\n\n    refresh();\n  }; // React prints warn on server in non production mode about useLayoutEffect usage\n  // in both cases it's noop\n\n  if (process.env.NODE_ENV === 'production' || typeof window !== 'undefined') {\n    useLayoutEffect(() => {\n      if (valueRef.current == null) return;\n      let [eventValue, input, isSizeIncreaseOperation, isDeleleteButtonDown,\n      // No operation means that value itself hasn't been changed, BTW cursor, selection etc can be changed\n      isNoOperation] = valueRef.current;\n      valueRef.current = null; // this usually occurs on deleting special symbols like ' here 123'123.00\n      // in case of isDeleleteButtonDown cursor should move differently vs backspace\n\n      const deleteWasNoOp = isDeleleteButtonDown && isNoOperation;\n      const valueAfterSelectionStart = eventValue.slice(input.selectionStart);\n      const acceptedCharIndexAfterDelete = valueAfterSelectionStart.search(props.accept || /\\d/g);\n      const charsToSkipAfterDelete = acceptedCharIndexAfterDelete !== -1 ? acceptedCharIndexAfterDelete : 0; // Create string from only accepted symbols\n\n      const clean = str => (str.match(props.accept || /\\d/g) || []).join('');\n      const valueBeforeSelectionStart = clean(eventValue.substr(0, input.selectionStart)); // trying to find cursor position in formatted value having knowledge about valueBeforeSelectionStart\n      // This works because we assume that format doesn't change the order of accepted symbols.\n      // Imagine we have formatter which adds ' symbol between numbers, and by default we refuse all non numeric symbols\n      // for example we had input = 1'2|'4 (| means cursor position) then user entered '3' symbol\n      // inputValue = 1'23'|4 so valueBeforeSelectionStart = 123 and formatted value = 1'2'3'4\n      // calling getCursorPosition(\"1'2'3'4\") will give us position after 3, 1'2'3|'4\n      // so for formatting just this function to determine cursor position after formatting is enough\n      // with masking we need to do some additional checks see `mask` below\n\n      const getCursorPosition = val => {\n        let start = 0;\n        let cleanPos = 0;\n        for (let i = 0; i !== valueBeforeSelectionStart.length; ++i) {\n          let newPos = val.indexOf(valueBeforeSelectionStart[i], start) + 1;\n          let newCleanPos = clean(val).indexOf(valueBeforeSelectionStart[i], cleanPos) + 1; // this skips position change if accepted symbols order was broken\n          // For example fixes edge case with fixed point numbers:\n          // You have '0|.00', then press 1, it becomes 01|.00 and after format 1.00, this breaks our assumption\n          // that order of accepted symbols is not changed after format,\n          // so here we don't update start position if other accepted symbols was inbetween current and new position\n\n          if (newCleanPos - cleanPos > 1) {\n            newPos = start;\n            newCleanPos = cleanPos;\n          }\n          cleanPos = Math.max(newCleanPos, cleanPos);\n          start = Math.max(start, newPos);\n        }\n        return start;\n      }; // Masking part, for masks if size of mask is above some value\n      // we need to replace symbols instead of do nothing as like in format\n\n      if (props.mask === true && isSizeIncreaseOperation && !isNoOperation) {\n        let start = getCursorPosition(eventValue);\n        const c = clean(eventValue.substr(start))[0];\n        start = eventValue.indexOf(c, start);\n        eventValue = \"\".concat(eventValue.substr(0, start)).concat(eventValue.substr(start + 1));\n      }\n      let formattedValue = props.format(eventValue);\n      if (append != null &&\n      // cursor at the end\n      input.selectionStart === eventValue.length && !isNoOperation) {\n        if (isSizeIncreaseOperation) {\n          formattedValue = append(formattedValue);\n        } else {\n          // If after delete last char is special character and we use append\n          // delete it too\n          // was: \"12-3|\" backspace pressed, then should be \"12|\"\n          if (clean(formattedValue.slice(-1)) === '') {\n            formattedValue = formattedValue.slice(0, -1);\n          }\n        }\n      }\n      const replacedValue = replace ? replace(formattedValue) : formattedValue;\n      if (userValue === replacedValue) {\n        // if nothing changed for formatted value, just refresh so userValue will be used at render\n        refresh();\n      } else {\n        props.onChange(replacedValue);\n      }\n      return () => {\n        let start = getCursorPosition(formattedValue); // Visually improves working with masked values,\n        // like cursor jumping over refused symbols\n        // as an example date mask: was \"5|1-24-3\" then user pressed \"6\"\n        // it becomes \"56-|12-43\" with this code, and \"56|-12-43\" without\n\n        if (props.mask != null && (isSizeIncreaseOperation || isDeleleteButtonDown && !deleteWasNoOp)) {\n          while (formattedValue[start] && clean(formattedValue[start]) === '') {\n            start += 1;\n          }\n        }\n        input.selectionStart = input.selectionEnd = start + (deleteWasNoOp ? 1 + charsToSkipAfterDelete : 0);\n      };\n    });\n  }\n  useEffect(() => {\n    // until https://developer.mozilla.org/en-US/docs/Web/API/InputEvent/inputType will be supported\n    // by all major browsers (now supported by: +chrome, +safari, ?edge, !firefox)\n    // there is no way I found to distinguish in onChange\n    // backspace or delete was called in some situations\n    // firefox track https://bugzilla.mozilla.org/show_bug.cgi?id=1447239\n    const handleKeyDown = evt => {\n      if (evt.code === 'Delete') {\n        isDeleleteButtonDownRef.current = true;\n      }\n    };\n    const handleKeyUp = evt => {\n      if (evt.code === 'Delete') {\n        isDeleleteButtonDownRef.current = false;\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('keyup', handleKeyUp);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('keyup', handleKeyUp);\n    };\n  }, []);\n  return {\n    value: valueRef.current != null ? valueRef.current[0] : userValue,\n    onChange\n  };\n};\nconst Rifm = props => {\n  const renderProps = useRifm(props);\n  return props.children(renderProps);\n};\nexport { Rifm, useRifm };", "map": {"version": 3, "names": ["useReducer", "useRef", "useLayoutEffect", "useEffect", "useRifm", "props", "refresh", "c", "valueRef", "replace", "append", "userValue", "format", "value", "isDeleleteButtonDownRef", "onChange", "evt", "process", "env", "NODE_ENV", "target", "type", "console", "error", "eventValue", "current", "length", "formattedEventValue", "toLowerCase", "warn", "window", "input", "isSizeIncreaseOperation", "isDeleleteButtonDown", "isNoOperation", "deleteWasNoOp", "valueAfterSelectionStart", "slice", "selectionStart", "acceptedCharIndexAfterDelete", "search", "accept", "charsToSkipAfterDelete", "clean", "str", "match", "join", "valueBeforeSelectionStart", "substr", "getCursorPosition", "val", "start", "cleanPos", "i", "newPos", "indexOf", "newCleanPos", "Math", "max", "mask", "concat", "formattedValue", "replacedV<PERSON>ue", "selectionEnd", "handleKeyDown", "code", "handleKeyUp", "document", "addEventListener", "removeEventListener", "Rifm", "renderProps", "children"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/rifm/dist/rifm.esm.js"], "sourcesContent": ["import { useReducer, useRef, useLayoutEffect, useEffect } from 'react';\n\nconst useRifm = props => {\n  const [, refresh] = useReducer(c => c + 1, 0);\n  const valueRef = useRef(null);\n  const {\n    replace,\n    append\n  } = props;\n  const userValue = replace ? replace(props.format(props.value)) : props.format(props.value); // state of delete button see comments below about inputType support\n\n  const isDeleleteButtonDownRef = useRef(false);\n\n  const onChange = evt => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (evt.target.type === 'number') {\n        console.error('Rifm does not support input type=number, use type=tel instead.');\n        return;\n      }\n\n      if (evt.target.type === 'date') {\n        console.error('Rifm does not support input type=date.');\n        return;\n      }\n    }\n\n    const eventValue = evt.target.value;\n    valueRef.current = [eventValue, // eventValue\n    evt.target, // input\n    eventValue.length > userValue.length, // isSizeIncreaseOperation\n    isDeleleteButtonDownRef.current, // isDeleleteButtonDown\n    userValue === props.format(eventValue) // isNoOperation\n    ];\n\n    if (process.env.NODE_ENV !== 'production') {\n      const formattedEventValue = props.format(eventValue);\n\n      if (eventValue !== formattedEventValue && eventValue.toLowerCase() === formattedEventValue.toLowerCase()) {\n        console.warn('Case enforcement does not work with format. Please use replace={value => value.toLowerCase()} instead');\n      }\n    } // The main trick is to update underlying input with non formatted value (= eventValue)\n    // that allows us to calculate right cursor position after formatting (see getCursorPosition)\n    // then we format new value and call props.onChange with masked/formatted value\n    // and finally we are able to set cursor position into right place\n\n\n    refresh();\n  }; // React prints warn on server in non production mode about useLayoutEffect usage\n  // in both cases it's noop\n\n\n  if (process.env.NODE_ENV === 'production' || typeof window !== 'undefined') {\n    useLayoutEffect(() => {\n      if (valueRef.current == null) return;\n      let [eventValue, input, isSizeIncreaseOperation, isDeleleteButtonDown, // No operation means that value itself hasn't been changed, BTW cursor, selection etc can be changed\n      isNoOperation] = valueRef.current;\n      valueRef.current = null; // this usually occurs on deleting special symbols like ' here 123'123.00\n      // in case of isDeleleteButtonDown cursor should move differently vs backspace\n\n      const deleteWasNoOp = isDeleleteButtonDown && isNoOperation;\n      const valueAfterSelectionStart = eventValue.slice(input.selectionStart);\n      const acceptedCharIndexAfterDelete = valueAfterSelectionStart.search(props.accept || /\\d/g);\n      const charsToSkipAfterDelete = acceptedCharIndexAfterDelete !== -1 ? acceptedCharIndexAfterDelete : 0; // Create string from only accepted symbols\n\n      const clean = str => (str.match(props.accept || /\\d/g) || []).join('');\n\n      const valueBeforeSelectionStart = clean(eventValue.substr(0, input.selectionStart)); // trying to find cursor position in formatted value having knowledge about valueBeforeSelectionStart\n      // This works because we assume that format doesn't change the order of accepted symbols.\n      // Imagine we have formatter which adds ' symbol between numbers, and by default we refuse all non numeric symbols\n      // for example we had input = 1'2|'4 (| means cursor position) then user entered '3' symbol\n      // inputValue = 1'23'|4 so valueBeforeSelectionStart = 123 and formatted value = 1'2'3'4\n      // calling getCursorPosition(\"1'2'3'4\") will give us position after 3, 1'2'3|'4\n      // so for formatting just this function to determine cursor position after formatting is enough\n      // with masking we need to do some additional checks see `mask` below\n\n      const getCursorPosition = val => {\n        let start = 0;\n        let cleanPos = 0;\n\n        for (let i = 0; i !== valueBeforeSelectionStart.length; ++i) {\n          let newPos = val.indexOf(valueBeforeSelectionStart[i], start) + 1;\n          let newCleanPos = clean(val).indexOf(valueBeforeSelectionStart[i], cleanPos) + 1; // this skips position change if accepted symbols order was broken\n          // For example fixes edge case with fixed point numbers:\n          // You have '0|.00', then press 1, it becomes 01|.00 and after format 1.00, this breaks our assumption\n          // that order of accepted symbols is not changed after format,\n          // so here we don't update start position if other accepted symbols was inbetween current and new position\n\n          if (newCleanPos - cleanPos > 1) {\n            newPos = start;\n            newCleanPos = cleanPos;\n          }\n\n          cleanPos = Math.max(newCleanPos, cleanPos);\n          start = Math.max(start, newPos);\n        }\n\n        return start;\n      }; // Masking part, for masks if size of mask is above some value\n      // we need to replace symbols instead of do nothing as like in format\n\n\n      if (props.mask === true && isSizeIncreaseOperation && !isNoOperation) {\n        let start = getCursorPosition(eventValue);\n        const c = clean(eventValue.substr(start))[0];\n        start = eventValue.indexOf(c, start);\n        eventValue = `${eventValue.substr(0, start)}${eventValue.substr(start + 1)}`;\n      }\n\n      let formattedValue = props.format(eventValue);\n\n      if (append != null && // cursor at the end\n      input.selectionStart === eventValue.length && !isNoOperation) {\n        if (isSizeIncreaseOperation) {\n          formattedValue = append(formattedValue);\n        } else {\n          // If after delete last char is special character and we use append\n          // delete it too\n          // was: \"12-3|\" backspace pressed, then should be \"12|\"\n          if (clean(formattedValue.slice(-1)) === '') {\n            formattedValue = formattedValue.slice(0, -1);\n          }\n        }\n      }\n\n      const replacedValue = replace ? replace(formattedValue) : formattedValue;\n\n      if (userValue === replacedValue) {\n        // if nothing changed for formatted value, just refresh so userValue will be used at render\n        refresh();\n      } else {\n        props.onChange(replacedValue);\n      }\n\n      return () => {\n        let start = getCursorPosition(formattedValue); // Visually improves working with masked values,\n        // like cursor jumping over refused symbols\n        // as an example date mask: was \"5|1-24-3\" then user pressed \"6\"\n        // it becomes \"56-|12-43\" with this code, and \"56|-12-43\" without\n\n        if (props.mask != null && (isSizeIncreaseOperation || isDeleleteButtonDown && !deleteWasNoOp)) {\n          while (formattedValue[start] && clean(formattedValue[start]) === '') {\n            start += 1;\n          }\n        }\n\n        input.selectionStart = input.selectionEnd = start + (deleteWasNoOp ? 1 + charsToSkipAfterDelete : 0);\n      };\n    });\n  }\n\n  useEffect(() => {\n    // until https://developer.mozilla.org/en-US/docs/Web/API/InputEvent/inputType will be supported\n    // by all major browsers (now supported by: +chrome, +safari, ?edge, !firefox)\n    // there is no way I found to distinguish in onChange\n    // backspace or delete was called in some situations\n    // firefox track https://bugzilla.mozilla.org/show_bug.cgi?id=1447239\n    const handleKeyDown = evt => {\n      if (evt.code === 'Delete') {\n        isDeleleteButtonDownRef.current = true;\n      }\n    };\n\n    const handleKeyUp = evt => {\n      if (evt.code === 'Delete') {\n        isDeleleteButtonDownRef.current = false;\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('keyup', handleKeyUp);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('keyup', handleKeyUp);\n    };\n  }, []);\n  return {\n    value: valueRef.current != null ? valueRef.current[0] : userValue,\n    onChange\n  };\n};\nconst Rifm = props => {\n  const renderProps = useRifm(props);\n  return props.children(renderProps);\n};\n\nexport { Rifm, useRifm };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,MAAM,EAAEC,eAAe,EAAEC,SAAS,QAAQ,OAAO;AAEtE,MAAMC,OAAO,GAAGC,KAAK,IAAI;EACvB,MAAM,GAAGC,OAAO,CAAC,GAAGN,UAAU,CAACO,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAMC,QAAQ,GAAGP,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM;IACJQ,OAAO;IACPC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,SAAS,GAAGF,OAAO,GAAGA,OAAO,CAACJ,KAAK,CAACO,MAAM,CAACP,KAAK,CAACQ,KAAK,CAAC,CAAC,GAAGR,KAAK,CAACO,MAAM,CAACP,KAAK,CAACQ,KAAK,CAAC,CAAC,CAAC;;EAE5F,MAAMC,uBAAuB,GAAGb,MAAM,CAAC,KAAK,CAAC;EAE7C,MAAMc,QAAQ,GAAGC,GAAG,IAAI;IACtB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIH,GAAG,CAACI,MAAM,CAACC,IAAI,KAAK,QAAQ,EAAE;QAChCC,OAAO,CAACC,KAAK,CAAC,gEAAgE,CAAC;QAC/E;MACF;MAEA,IAAIP,GAAG,CAACI,MAAM,CAACC,IAAI,KAAK,MAAM,EAAE;QAC9BC,OAAO,CAACC,KAAK,CAAC,wCAAwC,CAAC;QACvD;MACF;IACF;IAEA,MAAMC,UAAU,GAAGR,GAAG,CAACI,MAAM,CAACP,KAAK;IACnCL,QAAQ,CAACiB,OAAO,GAAG,CAACD,UAAU;IAAE;IAChCR,GAAG,CAACI,MAAM;IAAE;IACZI,UAAU,CAACE,MAAM,GAAGf,SAAS,CAACe,MAAM;IAAE;IACtCZ,uBAAuB,CAACW,OAAO;IAAE;IACjCd,SAAS,KAAKN,KAAK,CAACO,MAAM,CAACY,UAAU,CAAC,CAAC;IAAA,CACtC;IAED,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAMQ,mBAAmB,GAAGtB,KAAK,CAACO,MAAM,CAACY,UAAU,CAAC;MAEpD,IAAIA,UAAU,KAAKG,mBAAmB,IAAIH,UAAU,CAACI,WAAW,CAAC,CAAC,KAAKD,mBAAmB,CAACC,WAAW,CAAC,CAAC,EAAE;QACxGN,OAAO,CAACO,IAAI,CAAC,uGAAuG,CAAC;MACvH;IACF,CAAC,CAAC;IACF;IACA;IACA;;IAGAvB,OAAO,CAAC,CAAC;EACX,CAAC,CAAC,CAAC;EACH;;EAGA,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAOW,MAAM,KAAK,WAAW,EAAE;IAC1E5B,eAAe,CAAC,MAAM;MACpB,IAAIM,QAAQ,CAACiB,OAAO,IAAI,IAAI,EAAE;MAC9B,IAAI,CAACD,UAAU,EAAEO,KAAK,EAAEC,uBAAuB,EAAEC,oBAAoB;MAAE;MACvEC,aAAa,CAAC,GAAG1B,QAAQ,CAACiB,OAAO;MACjCjB,QAAQ,CAACiB,OAAO,GAAG,IAAI,CAAC,CAAC;MACzB;;MAEA,MAAMU,aAAa,GAAGF,oBAAoB,IAAIC,aAAa;MAC3D,MAAME,wBAAwB,GAAGZ,UAAU,CAACa,KAAK,CAACN,KAAK,CAACO,cAAc,CAAC;MACvE,MAAMC,4BAA4B,GAAGH,wBAAwB,CAACI,MAAM,CAACnC,KAAK,CAACoC,MAAM,IAAI,KAAK,CAAC;MAC3F,MAAMC,sBAAsB,GAAGH,4BAA4B,KAAK,CAAC,CAAC,GAAGA,4BAA4B,GAAG,CAAC,CAAC,CAAC;;MAEvG,MAAMI,KAAK,GAAGC,GAAG,IAAI,CAACA,GAAG,CAACC,KAAK,CAACxC,KAAK,CAACoC,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,EAAEK,IAAI,CAAC,EAAE,CAAC;MAEtE,MAAMC,yBAAyB,GAAGJ,KAAK,CAACnB,UAAU,CAACwB,MAAM,CAAC,CAAC,EAAEjB,KAAK,CAACO,cAAc,CAAC,CAAC,CAAC,CAAC;MACrF;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,MAAMW,iBAAiB,GAAGC,GAAG,IAAI;QAC/B,IAAIC,KAAK,GAAG,CAAC;QACb,IAAIC,QAAQ,GAAG,CAAC;QAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAKN,yBAAyB,CAACrB,MAAM,EAAE,EAAE2B,CAAC,EAAE;UAC3D,IAAIC,MAAM,GAAGJ,GAAG,CAACK,OAAO,CAACR,yBAAyB,CAACM,CAAC,CAAC,EAAEF,KAAK,CAAC,GAAG,CAAC;UACjE,IAAIK,WAAW,GAAGb,KAAK,CAACO,GAAG,CAAC,CAACK,OAAO,CAACR,yBAAyB,CAACM,CAAC,CAAC,EAAED,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;UAClF;UACA;UACA;UACA;;UAEA,IAAII,WAAW,GAAGJ,QAAQ,GAAG,CAAC,EAAE;YAC9BE,MAAM,GAAGH,KAAK;YACdK,WAAW,GAAGJ,QAAQ;UACxB;UAEAA,QAAQ,GAAGK,IAAI,CAACC,GAAG,CAACF,WAAW,EAAEJ,QAAQ,CAAC;UAC1CD,KAAK,GAAGM,IAAI,CAACC,GAAG,CAACP,KAAK,EAAEG,MAAM,CAAC;QACjC;QAEA,OAAOH,KAAK;MACd,CAAC,CAAC,CAAC;MACH;;MAGA,IAAI9C,KAAK,CAACsD,IAAI,KAAK,IAAI,IAAI3B,uBAAuB,IAAI,CAACE,aAAa,EAAE;QACpE,IAAIiB,KAAK,GAAGF,iBAAiB,CAACzB,UAAU,CAAC;QACzC,MAAMjB,CAAC,GAAGoC,KAAK,CAACnB,UAAU,CAACwB,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5CA,KAAK,GAAG3B,UAAU,CAAC+B,OAAO,CAAChD,CAAC,EAAE4C,KAAK,CAAC;QACpC3B,UAAU,MAAAoC,MAAA,CAAMpC,UAAU,CAACwB,MAAM,CAAC,CAAC,EAAEG,KAAK,CAAC,EAAAS,MAAA,CAAGpC,UAAU,CAACwB,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,CAAE;MAC9E;MAEA,IAAIU,cAAc,GAAGxD,KAAK,CAACO,MAAM,CAACY,UAAU,CAAC;MAE7C,IAAId,MAAM,IAAI,IAAI;MAAI;MACtBqB,KAAK,CAACO,cAAc,KAAKd,UAAU,CAACE,MAAM,IAAI,CAACQ,aAAa,EAAE;QAC5D,IAAIF,uBAAuB,EAAE;UAC3B6B,cAAc,GAAGnD,MAAM,CAACmD,cAAc,CAAC;QACzC,CAAC,MAAM;UACL;UACA;UACA;UACA,IAAIlB,KAAK,CAACkB,cAAc,CAACxB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1CwB,cAAc,GAAGA,cAAc,CAACxB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC9C;QACF;MACF;MAEA,MAAMyB,aAAa,GAAGrD,OAAO,GAAGA,OAAO,CAACoD,cAAc,CAAC,GAAGA,cAAc;MAExE,IAAIlD,SAAS,KAAKmD,aAAa,EAAE;QAC/B;QACAxD,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACLD,KAAK,CAACU,QAAQ,CAAC+C,aAAa,CAAC;MAC/B;MAEA,OAAO,MAAM;QACX,IAAIX,KAAK,GAAGF,iBAAiB,CAACY,cAAc,CAAC,CAAC,CAAC;QAC/C;QACA;QACA;;QAEA,IAAIxD,KAAK,CAACsD,IAAI,IAAI,IAAI,KAAK3B,uBAAuB,IAAIC,oBAAoB,IAAI,CAACE,aAAa,CAAC,EAAE;UAC7F,OAAO0B,cAAc,CAACV,KAAK,CAAC,IAAIR,KAAK,CAACkB,cAAc,CAACV,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACnEA,KAAK,IAAI,CAAC;UACZ;QACF;QAEApB,KAAK,CAACO,cAAc,GAAGP,KAAK,CAACgC,YAAY,GAAGZ,KAAK,IAAIhB,aAAa,GAAG,CAAC,GAAGO,sBAAsB,GAAG,CAAC,CAAC;MACtG,CAAC;IACH,CAAC,CAAC;EACJ;EAEAvC,SAAS,CAAC,MAAM;IACd;IACA;IACA;IACA;IACA;IACA,MAAM6D,aAAa,GAAGhD,GAAG,IAAI;MAC3B,IAAIA,GAAG,CAACiD,IAAI,KAAK,QAAQ,EAAE;QACzBnD,uBAAuB,CAACW,OAAO,GAAG,IAAI;MACxC;IACF,CAAC;IAED,MAAMyC,WAAW,GAAGlD,GAAG,IAAI;MACzB,IAAIA,GAAG,CAACiD,IAAI,KAAK,QAAQ,EAAE;QACzBnD,uBAAuB,CAACW,OAAO,GAAG,KAAK;MACzC;IACF,CAAC;IAED0C,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,aAAa,CAAC;IACnDG,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEF,WAAW,CAAC;IAC/C,OAAO,MAAM;MACXC,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEL,aAAa,CAAC;MACtDG,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEH,WAAW,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO;IACLrD,KAAK,EAAEL,QAAQ,CAACiB,OAAO,IAAI,IAAI,GAAGjB,QAAQ,CAACiB,OAAO,CAAC,CAAC,CAAC,GAAGd,SAAS;IACjEI;EACF,CAAC;AACH,CAAC;AACD,MAAMuD,IAAI,GAAGjE,KAAK,IAAI;EACpB,MAAMkE,WAAW,GAAGnE,OAAO,CAACC,KAAK,CAAC;EAClC,OAAOA,KAAK,CAACmE,QAAQ,CAACD,WAAW,CAAC;AACpC,CAAC;AAED,SAASD,IAAI,EAAElE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}