{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, CircularProgress, Alert, useTheme, List, ListItem, ListItemText, ListItemIcon, Divider, Paper, Chip, LinearProgress, useMediaQuery, Button } from '@mui/material';\nimport { Assignment as ComplaintsIcon, CheckCircle as ResolvedIcon, Pending as PendingIcon, Error as HighPriorityIcon, FiberManualRecord as StatusIcon, Schedule as TimeIcon, Speed as EfficiencyIcon, Timeline as TrendIcon, AccessTime as ResolutionIcon } from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport axios from '../utils/axiosConfig';\nimport { format, formatDistanceToNow } from 'date-fns';\nimport { useSocket } from '../contexts/SocketContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MonthlyStatCard = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  title,\n  value,\n  icon,\n  color,\n  loading,\n  subtitle,\n  index\n}) => {\n  _s();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        height: 120,\n        background: 'rgba(255,255,255,0.15)',\n        backdropFilter: 'blur(10px)',\n        border: '1px solid rgba(255,255,255,0.2)',\n        color: 'white'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24,\n          sx: {\n            color: 'white'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      scale: 0.9\n    },\n    animate: {\n      opacity: 1,\n      scale: 1\n    },\n    transition: {\n      delay: index * 0.1,\n      duration: 0.3,\n      ease: \"easeOut\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        height: 120,\n        background: 'rgba(255,255,255,0.15)',\n        backdropFilter: 'blur(10px)',\n        border: '1px solid rgba(255,255,255,0.2)',\n        color: 'white',\n        cursor: 'pointer',\n        transition: 'all 0.2s ease',\n        '&:hover': {\n          background: 'rgba(255,255,255,0.25)',\n          transform: 'translateY(-4px)',\n          boxShadow: '0 8px 25px rgba(0,0,0,0.3)'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'space-between',\n          height: '100%',\n          p: 2,\n          '&:last-child': {\n            pb: 2\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"body2\" : \"body1\",\n              sx: {\n                opacity: 0.9,\n                fontWeight: 500,\n                fontSize: '0.875rem'\n              },\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                opacity: 0.7,\n                fontSize: '0.75rem',\n                display: 'block'\n              },\n              children: subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              opacity: 0.8,\n              fontSize: isMobile ? '1.2rem' : '1.5rem'\n            },\n            children: icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: isMobile ? \"h6\" : \"h5\",\n          sx: {\n            fontWeight: 700,\n            fontSize: isMobile ? '1.25rem' : '1.5rem',\n            textShadow: '0px 2px 4px rgba(0,0,0,0.3)'\n          },\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n}, \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n  return [useTheme, useMediaQuery];\n})), \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c2 = MonthlyStatCard;\nconst StatCard = ({\n  title,\n  value,\n  icon,\n  color,\n  loading,\n  subtitle\n}) => {\n  _s2();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      type: \"spring\",\n      stiffness: 100,\n      damping: 15,\n      duration: 0.6\n    },\n    whileHover: {\n      scale: 1.02,\n      transition: {\n        duration: 0.2\n      }\n    },\n    whileTap: {\n      scale: 0.98\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        height: '100%',\n        background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\n        color: 'white',\n        position: 'relative',\n        overflow: 'hidden',\n        boxShadow: theme.shadows[loading ? 0 : 2],\n        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n        '&:hover': {\n          boxShadow: theme.shadows[4],\n          transform: 'translateY(-4px)'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: isMobile ? 2 : 3,\n          height: '100%',\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'relative',\n            zIndex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              delay: 0.2,\n              type: \"spring\",\n              stiffness: 120\n            },\n            children: /*#__PURE__*/React.cloneElement(icon, {\n              sx: {\n                fontSize: isMobile ? 32 : 48,\n                opacity: 0.9,\n                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"h5\" : \"h4\",\n              component: \"div\",\n              sx: {\n                fontWeight: 700,\n                lineHeight: 1.2,\n                mb: 0.5,\n                textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\n              },\n              children: loading ? /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0\n                },\n                animate: {\n                  opacity: 1\n                },\n                transition: {\n                  duration: 0.5\n                },\n                children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: isMobile ? 20 : 24,\n                  color: \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 10\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3,\n                  duration: 0.5\n                },\n                children: value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"body2\" : \"body1\",\n              sx: {\n                opacity: 0.9,\n                fontWeight: 500,\n                letterSpacing: '0.5px',\n                textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\n              },\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                opacity: 0.8,\n                fontWeight: 400,\n                textShadow: '0px 1px 2px rgba(0,0,0,0.2)',\n                display: 'block'\n              },\n              children: subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            scale: 0.5\n          },\n          animate: {\n            opacity: 0.15,\n            scale: 2\n          },\n          transition: {\n            delay: 0.4,\n            duration: 0.8\n          },\n          sx: {\n            position: 'absolute',\n            right: -20,\n            bottom: -20,\n            filter: 'blur(2px)'\n          },\n          children: /*#__PURE__*/React.cloneElement(icon, {\n            sx: {\n              fontSize: isMobile ? 100 : 140\n            }\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n\n// Memoized color functions to prevent recalculation\n_s2(StatCard, \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c3 = StatCard;\nconst getStatusColor = (status, theme) => {\n  const statusColors = {\n    'New': theme.palette.info.main,\n    'Assigned': theme.palette.warning.main,\n    'In Progress': theme.palette.warning.dark,\n    'Resolved': theme.palette.success.main,\n    'Rejected': theme.palette.error.main\n  };\n  return statusColors[status] || theme.palette.grey[500];\n};\nconst getPriorityColor = (priority, theme) => {\n  const priorityColors = {\n    'Low': theme.palette.success.main,\n    'Medium': theme.palette.warning.main,\n    'High': theme.palette.error.main,\n    'Critical': theme.palette.error.dark\n  };\n  return priorityColors[priority] || theme.palette.grey[500];\n};\n\n// Optimized timestamp formatting function - consistent with complaint details\nconst formatTimestamp = timestamp => {\n  if (!timestamp) return 'N/A';\n  try {\n    const date = new Date(timestamp);\n    if (isNaN(date.getTime())) {\n      console.warn('Invalid timestamp:', timestamp);\n      return 'N/A';\n    }\n\n    // Use the same format as complaint details - PPpp gives \"Jun 9, 2025, 11:27:36 AM\"\n    return format(date, 'PPpp');\n  } catch (error) {\n    console.error('Error formatting timestamp:', error);\n    return 'Invalid date';\n  }\n};\nconst ActivityItem = /*#__PURE__*/_s3(/*#__PURE__*/React.memo(_c4 = _s3(({\n  activity,\n  index\n}) => {\n  _s3();\n  const theme = useTheme();\n\n  // Memoize colors\n  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);\n  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      x: -20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    transition: {\n      delay: Math.min(index * 0.05, 0.3),\n      // Reduced delay for better performance\n      duration: 0.3,\n      // Reduced duration\n      ease: \"easeOut\"\n    },\n    children: /*#__PURE__*/_jsxDEV(ListItem, {\n      sx: {\n        bgcolor: 'background.paper',\n        borderRadius: 2,\n        mb: 1,\n        boxShadow: 1,\n        '&:hover': {\n          bgcolor: 'action.hover',\n          transform: 'translateX(4px)',\n          transition: 'transform 0.15s ease' // Faster transition\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(StatusIcon, {\n          sx: {\n            color: statusColor\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            mb: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 500,\n              flex: 1\n            },\n            children: [\"#\", activity.ComplaintNumber, \" - \", activity.description]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this), activity.Priority && /*#__PURE__*/_jsxDEV(Chip, {\n            label: activity.Priority,\n            size: \"small\",\n            sx: {\n              bgcolor: `${priorityColor}15`,\n              color: priorityColor,\n              fontWeight: 500,\n              fontSize: '0.7rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this),\n        secondary: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 0.5\n          },\n          children: [activity.activityDetails && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 0.5\n            },\n            children: activity.activityDetails\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              label: activity.Status,\n              size: \"small\",\n              sx: {\n                bgcolor: `${statusColor}15`,\n                color: statusColor,\n                fontWeight: 500\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this), activity.Category && /*#__PURE__*/_jsxDEV(Chip, {\n              label: activity.Category,\n              size: \"small\",\n              variant: \"outlined\",\n              sx: {\n                fontSize: '0.7rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 337,\n    columnNumber: 5\n  }, this);\n}, \"o0BaSXnzBaXh/W+F64SAqGC2Z0M=\", false, function () {\n  return [useTheme];\n})), \"o0BaSXnzBaXh/W+F64SAqGC2Z0M=\", false, function () {\n  return [useTheme];\n});\n_c5 = ActivityItem;\nfunction Dashboard() {\n  _s4();\n  const [stats, setStats] = useState(null);\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const {\n    socket\n  } = useSocket();\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n\n      // Fetch data with optimized timeout and caching\n      const [statsResponse, activitiesResponse] = await Promise.all([axios.get('/api/dashboard/stats', {\n        timeout: 10000,\n        // 10 second timeout\n        headers: {\n          'Cache-Control': 'max-age=60' // Cache for 1 minute\n        }\n      }), axios.get('/api/dashboard/recent-activities', {\n        timeout: 10000,\n        // 10 second timeout\n        headers: {\n          'Cache-Control': 'no-cache' // Always fetch fresh data for activities\n        }\n      })]);\n      setStats(statsResponse.data);\n      setActivities(activitiesResponse.data);\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching dashboard data:', err);\n      setError('Failed to load dashboard data. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchDashboardData();\n\n    // Set up auto-refresh every 30 seconds\n    const interval = setInterval(fetchDashboardData, 30000);\n\n    // Listen for real-time updates via Socket.IO\n    if (socket) {\n      // Listen for status updates to refresh dashboard\n      socket.on('status_updated', () => {\n        console.log('Status update received, refreshing dashboard...');\n        fetchDashboardData();\n      });\n\n      // Listen for new complaints to refresh dashboard\n      socket.on('complaint_created', () => {\n        console.log('New complaint received, refreshing dashboard...');\n        fetchDashboardData();\n      });\n    }\n    return () => {\n      clearInterval(interval);\n      if (socket) {\n        socket.off('status_updated');\n        socket.off('complaint_created');\n      }\n    };\n  }, [fetchDashboardData, socket]);\n  const statCards = useMemo(() => [{\n    title: 'Total Complaints',\n    value: (stats === null || stats === void 0 ? void 0 : stats.totalComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(ComplaintsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 13\n    }, this),\n    color: 'primary'\n  }, {\n    title: 'Resolved',\n    value: (stats === null || stats === void 0 ? void 0 : stats.resolvedComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(ResolvedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 13\n    }, this),\n    color: 'success'\n  }, {\n    title: 'Pending',\n    value: (stats === null || stats === void 0 ? void 0 : stats.pendingComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(PendingIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 13\n    }, this),\n    color: 'warning'\n  }, {\n    title: 'High Priority',\n    value: (stats === null || stats === void 0 ? void 0 : stats.highPriorityComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(HighPriorityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 13\n    }, this),\n    color: 'error'\n  }], [stats]);\n  const monthlyStatCards = useMemo(() => {\n    if (!(stats !== null && stats !== void 0 && stats.monthlyStats)) return [];\n    return [{\n      title: 'This Month',\n      value: stats.monthlyStats.totalMonthlyComplaints || 0,\n      icon: /*#__PURE__*/_jsxDEV(ComplaintsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 15\n      }, this),\n      color: 'info',\n      subtitle: 'New complaints'\n    }, {\n      title: 'Resolution Rate',\n      value: `${stats.monthlyStats.resolutionRate || 0}%`,\n      icon: /*#__PURE__*/_jsxDEV(ResolvedIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 15\n      }, this),\n      color: 'success',\n      subtitle: 'Monthly average'\n    }, {\n      title: 'Response Time',\n      value: `${stats.monthlyStats.responseEfficiency || 0}%`,\n      icon: /*#__PURE__*/_jsxDEV(TimeIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 15\n      }, this),\n      color: 'warning',\n      subtitle: 'Within 24h'\n    }, {\n      title: 'Avg Resolution',\n      value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,\n      icon: /*#__PURE__*/_jsxDEV(ResolutionIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 15\n      }, this),\n      color: 'primary',\n      subtitle: 'Hours to resolve'\n    }];\n  }, [stats]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: {\n        xs: 2,\n        sm: 3\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      sx: {\n        mb: 4,\n        fontWeight: 600\n      },\n      children: \"Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      action: /*#__PURE__*/_jsxDEV(motion.div, {\n        whileHover: {\n          scale: 1.05\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          size: \"small\",\n          onClick: fetchDashboardData,\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 13\n      }, this),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [statCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          ...card,\n          loading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 13\n        }, this)\n      }, card.title, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 11\n      }, this)), monthlyStatCards.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.2,\n            duration: 0.3\n          },\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            overflow: 'visible',\n            position: 'relative',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(255,255,255,0.1)',\n              backdropFilter: 'blur(10px)',\n              borderRadius: 'inherit',\n              zIndex: 0\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              position: 'relative',\n              zIndex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 3,\n                fontWeight: 600,\n                color: 'white'\n              },\n              children: \"\\uD83D\\uDCCA Monthly Performance Insights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: monthlyStatCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(MonthlyStatCard, {\n                  ...card,\n                  loading: loading,\n                  index: index\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 23\n                }, this)\n              }, card.title, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.4\n          },\n          sx: {\n            overflow: 'visible',\n            height: '100%',\n            minHeight: 400\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600\n              },\n              children: \"Recent Activities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                p: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 17\n            }, this) : activities.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n              sx: {\n                p: 0\n              },\n              children: activities.slice(0, 8).map((activity, index) => /*#__PURE__*/_jsxDEV(ActivityItem, {\n                activity: activity,\n                index: index\n              }, `${activity.ComplaintId}-${activity.Status}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                py: 4,\n                color: 'text.secondary'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                children: \"No recent activities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 550,\n    columnNumber: 5\n  }, this);\n}\n_s4(Dashboard, \"+5xHOEl+CAhlePZKwdRbLSaYLPs=\", false, function () {\n  return [useTheme, useMediaQuery, useSocket];\n});\n_c6 = Dashboard;\nexport default Dashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"MonthlyStatCard$React.memo\");\n$RefreshReg$(_c2, \"MonthlyStatCard\");\n$RefreshReg$(_c3, \"StatCard\");\n$RefreshReg$(_c4, \"ActivityItem$React.memo\");\n$RefreshReg$(_c5, \"ActivityItem\");\n$RefreshReg$(_c6, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "CircularProgress", "<PERSON><PERSON>", "useTheme", "List", "ListItem", "ListItemText", "ListItemIcon", "Divider", "Paper", "Chip", "LinearProgress", "useMediaQuery", "<PERSON><PERSON>", "Assignment", "ComplaintsIcon", "CheckCircle", "ResolvedIcon", "Pending", "PendingIcon", "Error", "HighPriorityIcon", "FiberManualRecord", "StatusIcon", "Schedule", "TimeIcon", "Speed", "EfficiencyIcon", "Timeline", "TrendIcon", "AccessTime", "ResolutionIcon", "motion", "AnimatePresence", "axios", "format", "formatDistanceToNow", "useSocket", "jsxDEV", "_jsxDEV", "MonthlyStatCard", "_s", "memo", "_c", "title", "value", "icon", "color", "loading", "subtitle", "index", "theme", "isMobile", "breakpoints", "down", "sx", "height", "background", "<PERSON><PERSON>ilter", "border", "children", "display", "alignItems", "justifyContent", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "scale", "animate", "transition", "delay", "duration", "ease", "cursor", "transform", "boxShadow", "flexDirection", "p", "pb", "variant", "fontWeight", "fontSize", "textShadow", "_c2", "StatCard", "_s2", "y", "type", "stiffness", "damping", "whileHover", "whileTap", "palette", "main", "dark", "position", "overflow", "shadows", "zIndex", "gap", "cloneElement", "filter", "component", "lineHeight", "mb", "letterSpacing", "right", "bottom", "_c3", "getStatusColor", "status", "statusColors", "info", "warning", "success", "error", "grey", "getPriorityColor", "priority", "priorityColors", "formatTimestamp", "timestamp", "date", "Date", "isNaN", "getTime", "console", "warn", "ActivityItem", "_s3", "_c4", "activity", "statusColor", "Status", "priorityColor", "Priority", "x", "Math", "min", "bgcolor", "borderRadius", "primary", "flex", "ComplaintNumber", "description", "label", "secondary", "mt", "activityDetails", "flexWrap", "Category", "_c5", "Dashboard", "_s4", "stats", "setStats", "activities", "setActivities", "setLoading", "setError", "socket", "fetchDashboardData", "statsResponse", "activitiesResponse", "Promise", "all", "get", "timeout", "headers", "data", "err", "interval", "setInterval", "on", "log", "clearInterval", "off", "statCards", "totalComplaints", "resolvedComplaints", "pendingComplaints", "highPriorityComplaints", "monthlyStatCards", "monthlyStats", "totalMonthlyComplaints", "resolutionRate", "responseEfficiency", "round", "avgResolutionHours", "xs", "sm", "severity", "action", "onClick", "container", "spacing", "map", "card", "item", "md", "length", "content", "top", "left", "minHeight", "slice", "ComplaintId", "textAlign", "py", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport {\r\n  Box,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  CircularProgress,\r\n  Alert,\r\n  useTheme,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Divider,\r\n  Paper,\r\n  Chip,\r\n  LinearProgress,\r\n  useMediaQuery,\r\n  Button,\r\n} from '@mui/material';\r\nimport {\r\n  Assignment as ComplaintsIcon,\r\n  CheckCircle as ResolvedIcon,\r\n  Pending as PendingIcon,\r\n  Error as HighPriorityIcon,\r\n  FiberManualRecord as StatusIcon,\r\n  Schedule as TimeIcon,\r\n  Speed as EfficiencyIcon,\r\n  Timeline as TrendIcon,\r\n  AccessTime as ResolutionIcon,\r\n} from '@mui/icons-material';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport axios from '../utils/axiosConfig';\r\nimport { format, formatDistanceToNow } from 'date-fns';\r\nimport { useSocket } from '../contexts/SocketContext';\r\n\r\nconst MonthlyStatCard = React.memo(({ title, value, icon, color, loading, subtitle, index }) => {\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\n  if (loading) {\r\n    return (\r\n      <Card sx={{\r\n        height: 120,\r\n        background: 'rgba(255,255,255,0.15)',\r\n        backdropFilter: 'blur(10px)',\r\n        border: '1px solid rgba(255,255,255,0.2)',\r\n        color: 'white'\r\n      }}>\r\n        <CardContent sx={{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          height: '100%'\r\n        }}>\r\n          <CircularProgress size={24} sx={{ color: 'white' }} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, scale: 0.9 }}\r\n      animate={{ opacity: 1, scale: 1 }}\r\n      transition={{\r\n        delay: index * 0.1,\r\n        duration: 0.3,\r\n        ease: \"easeOut\"\r\n      }}\r\n    >\r\n      <Card\r\n        sx={{\r\n          height: 120,\r\n          background: 'rgba(255,255,255,0.15)',\r\n          backdropFilter: 'blur(10px)',\r\n          border: '1px solid rgba(255,255,255,0.2)',\r\n          color: 'white',\r\n          cursor: 'pointer',\r\n          transition: 'all 0.2s ease',\r\n          '&:hover': {\r\n            background: 'rgba(255,255,255,0.25)',\r\n            transform: 'translateY(-4px)',\r\n            boxShadow: '0 8px 25px rgba(0,0,0,0.3)',\r\n          },\r\n        }}\r\n      >\r\n        <CardContent sx={{\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'space-between',\r\n          height: '100%',\r\n          p: 2,\r\n          '&:last-child': { pb: 2 }\r\n        }}>\r\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\r\n            <Box>\r\n              <Typography\r\n                variant={isMobile ? \"body2\" : \"body1\"}\r\n                sx={{\r\n                  opacity: 0.9,\r\n                  fontWeight: 500,\r\n                  fontSize: '0.875rem'\r\n                }}\r\n              >\r\n                {title}\r\n              </Typography>\r\n              {subtitle && (\r\n                <Typography\r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    opacity: 0.7,\r\n                    fontSize: '0.75rem',\r\n                    display: 'block'\r\n                  }}\r\n                >\r\n                  {subtitle}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n            <Box sx={{\r\n              opacity: 0.8,\r\n              fontSize: isMobile ? '1.2rem' : '1.5rem'\r\n            }}>\r\n              {icon}\r\n            </Box>\r\n          </Box>\r\n\r\n          <Typography\r\n            variant={isMobile ? \"h6\" : \"h5\"}\r\n            sx={{\r\n              fontWeight: 700,\r\n              fontSize: isMobile ? '1.25rem' : '1.5rem',\r\n              textShadow: '0px 2px 4px rgba(0,0,0,0.3)'\r\n            }}\r\n          >\r\n            {value}\r\n          </Typography>\r\n        </CardContent>\r\n      </Card>\r\n    </motion.div>\r\n  );\r\n});\r\n\r\nconst StatCard = ({ title, value, icon, color, loading, subtitle }) => {\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ \r\n        type: \"spring\",\r\n        stiffness: 100,\r\n        damping: 15,\r\n        duration: 0.6 \r\n      }}\r\n      whileHover={{ \r\n        scale: 1.02,\r\n        transition: { duration: 0.2 }\r\n      }}\r\n      whileTap={{ scale: 0.98 }}\r\n    >\r\n      <Card\r\n        sx={{\r\n          height: '100%',\r\n          background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\r\n          color: 'white',\r\n          position: 'relative',\r\n          overflow: 'hidden',\r\n          boxShadow: theme.shadows[loading ? 0 : 2],\r\n          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\r\n          '&:hover': {\r\n            boxShadow: theme.shadows[4],\r\n            transform: 'translateY(-4px)',\r\n          },\r\n        }}\r\n      >\r\n        <CardContent sx={{ \r\n          p: isMobile ? 2 : 3,\r\n          height: '100%',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'space-between'\r\n        }}>\r\n          <Box sx={{ \r\n            position: 'relative', \r\n            zIndex: 1,\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 2\r\n          }}>\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 120 }}\r\n            >\r\n              {React.cloneElement(icon, { \r\n                sx: { \r\n                  fontSize: isMobile ? 32 : 48,\r\n                  opacity: 0.9,\r\n                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\r\n                } \r\n              })}\r\n            </motion.div>\r\n            <Box>\r\n              <Typography \r\n                variant={isMobile ? \"h5\" : \"h4\"} \r\n                component=\"div\" \r\n                sx={{ \r\n                  fontWeight: 700,\r\n                  lineHeight: 1.2,\r\n                  mb: 0.5,\r\n                  textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {loading ? (\r\n                  <motion.div\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    transition={{ duration: 0.5 }}\r\n                  >\r\n                    <CircularProgress size={isMobile ? 20 : 24} color=\"inherit\" />\r\n                  </motion.div>\r\n                ) : (\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.3, duration: 0.5 }}\r\n                  >\r\n                    {value}\r\n                  </motion.div>\r\n                )}\r\n              </Typography>\r\n              <Typography\r\n                variant={isMobile ? \"body2\" : \"body1\"}\r\n                sx={{\r\n                  opacity: 0.9,\r\n                  fontWeight: 500,\r\n                  letterSpacing: '0.5px',\r\n                  textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {title}\r\n              </Typography>\r\n              {subtitle && (\r\n                <Typography\r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    opacity: 0.8,\r\n                    fontWeight: 400,\r\n                    textShadow: '0px 1px 2px rgba(0,0,0,0.2)',\r\n                    display: 'block'\r\n                  }}\r\n                >\r\n                  {subtitle}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n          </Box>\r\n          <Box\r\n            component={motion.div}\r\n            initial={{ opacity: 0, scale: 0.5 }}\r\n            animate={{ opacity: 0.15, scale: 2 }}\r\n            transition={{ delay: 0.4, duration: 0.8 }}\r\n            sx={{\r\n              position: 'absolute',\r\n              right: -20,\r\n              bottom: -20,\r\n              filter: 'blur(2px)'\r\n            }}\r\n          >\r\n            {React.cloneElement(icon, { \r\n              sx: { fontSize: isMobile ? 100 : 140 }\r\n            })}\r\n          </Box>\r\n        </CardContent>\r\n      </Card>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\n// Memoized color functions to prevent recalculation\r\nconst getStatusColor = (status, theme) => {\r\n  const statusColors = {\r\n    'New': theme.palette.info.main,\r\n    'Assigned': theme.palette.warning.main,\r\n    'In Progress': theme.palette.warning.dark,\r\n    'Resolved': theme.palette.success.main,\r\n    'Rejected': theme.palette.error.main,\r\n  };\r\n  return statusColors[status] || theme.palette.grey[500];\r\n};\r\n\r\nconst getPriorityColor = (priority, theme) => {\r\n  const priorityColors = {\r\n    'Low': theme.palette.success.main,\r\n    'Medium': theme.palette.warning.main,\r\n    'High': theme.palette.error.main,\r\n    'Critical': theme.palette.error.dark,\r\n  };\r\n  return priorityColors[priority] || theme.palette.grey[500];\r\n};\r\n\r\n// Optimized timestamp formatting function - consistent with complaint details\r\nconst formatTimestamp = (timestamp) => {\r\n  if (!timestamp) return 'N/A';\r\n\r\n  try {\r\n    const date = new Date(timestamp);\r\n\r\n    if (isNaN(date.getTime())) {\r\n      console.warn('Invalid timestamp:', timestamp);\r\n      return 'N/A';\r\n    }\r\n\r\n    // Use the same format as complaint details - PPpp gives \"Jun 9, 2025, 11:27:36 AM\"\r\n    return format(date, 'PPpp');\r\n  } catch (error) {\r\n    console.error('Error formatting timestamp:', error);\r\n    return 'Invalid date';\r\n  }\r\n};\r\n\r\nconst ActivityItem = React.memo(({ activity, index }) => {\r\n  const theme = useTheme();\r\n\r\n\r\n\r\n  // Memoize colors\r\n  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);\r\n  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, x: -20 }}\r\n      animate={{ opacity: 1, x: 0 }}\r\n      transition={{\r\n        delay: Math.min(index * 0.05, 0.3), // Reduced delay for better performance\r\n        duration: 0.3, // Reduced duration\r\n        ease: \"easeOut\"\r\n      }}\r\n    >\r\n      <ListItem\r\n        sx={{\r\n          bgcolor: 'background.paper',\r\n          borderRadius: 2,\r\n          mb: 1,\r\n          boxShadow: 1,\r\n          '&:hover': {\r\n            bgcolor: 'action.hover',\r\n            transform: 'translateX(4px)',\r\n            transition: 'transform 0.15s ease', // Faster transition\r\n          },\r\n        }}\r\n      >\r\n        <ListItemIcon>\r\n          <StatusIcon sx={{ color: statusColor }} />\r\n        </ListItemIcon>\r\n        <ListItemText\r\n          primary={\r\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>\r\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, flex: 1 }}>\r\n                #{activity.ComplaintNumber} - {activity.description}\r\n              </Typography>\r\n              {activity.Priority && (\r\n                <Chip\r\n                  label={activity.Priority}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${priorityColor}15`,\r\n                    color: priorityColor,\r\n                    fontWeight: 500,\r\n                    fontSize: '0.7rem'\r\n                  }}\r\n                />\r\n              )}\r\n            </Box>\r\n          }\r\n          secondary={\r\n            <Box sx={{ mt: 0.5 }}>\r\n              {activity.activityDetails && (\r\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 0.5 }}>\r\n                  {activity.activityDetails}\r\n                </Typography>\r\n              )}\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>\r\n                <Chip\r\n                  label={activity.Status}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${statusColor}15`,\r\n                    color: statusColor,\r\n                    fontWeight: 500\r\n                  }}\r\n                />\r\n                {activity.Category && (\r\n                  <Chip\r\n                    label={activity.Category}\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ fontSize: '0.7rem' }}\r\n                  />\r\n                )}\r\n\r\n              </Box>\r\n            </Box>\r\n          }\r\n        />\r\n      </ListItem>\r\n    </motion.div>\r\n  );\r\n});\r\n\r\nfunction Dashboard() {\r\n  const [stats, setStats] = useState(null);\r\n  const [activities, setActivities] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n  const { socket } = useSocket();\r\n\r\n  const fetchDashboardData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Fetch data with optimized timeout and caching\r\n      const [statsResponse, activitiesResponse] = await Promise.all([\r\n        axios.get('/api/dashboard/stats', {\r\n          timeout: 10000, // 10 second timeout\r\n          headers: {\r\n            'Cache-Control': 'max-age=60' // Cache for 1 minute\r\n          }\r\n        }),\r\n        axios.get('/api/dashboard/recent-activities', {\r\n          timeout: 10000, // 10 second timeout\r\n          headers: {\r\n            'Cache-Control': 'no-cache' // Always fetch fresh data for activities\r\n          }\r\n        })\r\n      ]);\r\n\r\n      setStats(statsResponse.data);\r\n      setActivities(activitiesResponse.data);\r\n      setError(null);\r\n    } catch (err) {\r\n      console.error('Error fetching dashboard data:', err);\r\n      setError('Failed to load dashboard data. Please try again later.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n\r\n    // Set up auto-refresh every 30 seconds\r\n    const interval = setInterval(fetchDashboardData, 30000);\r\n\r\n    // Listen for real-time updates via Socket.IO\r\n    if (socket) {\r\n      // Listen for status updates to refresh dashboard\r\n      socket.on('status_updated', () => {\r\n        console.log('Status update received, refreshing dashboard...');\r\n        fetchDashboardData();\r\n      });\r\n\r\n      // Listen for new complaints to refresh dashboard\r\n      socket.on('complaint_created', () => {\r\n        console.log('New complaint received, refreshing dashboard...');\r\n        fetchDashboardData();\r\n      });\r\n    }\r\n\r\n    return () => {\r\n      clearInterval(interval);\r\n      if (socket) {\r\n        socket.off('status_updated');\r\n        socket.off('complaint_created');\r\n      }\r\n    };\r\n  }, [fetchDashboardData, socket]);\r\n\r\n  const statCards = useMemo(() => [\r\n    {\r\n      title: 'Total Complaints',\r\n      value: stats?.totalComplaints || 0,\r\n      icon: <ComplaintsIcon />,\r\n      color: 'primary'\r\n    },\r\n    {\r\n      title: 'Resolved',\r\n      value: stats?.resolvedComplaints || 0,\r\n      icon: <ResolvedIcon />,\r\n      color: 'success'\r\n    },\r\n    {\r\n      title: 'Pending',\r\n      value: stats?.pendingComplaints || 0,\r\n      icon: <PendingIcon />,\r\n      color: 'warning'\r\n    },\r\n    {\r\n      title: 'High Priority',\r\n      value: stats?.highPriorityComplaints || 0,\r\n      icon: <HighPriorityIcon />,\r\n      color: 'error'\r\n    }\r\n  ], [stats]);\r\n\r\n  const monthlyStatCards = useMemo(() => {\r\n    if (!stats?.monthlyStats) return [];\r\n\r\n    return [\r\n      {\r\n        title: 'This Month',\r\n        value: stats.monthlyStats.totalMonthlyComplaints || 0,\r\n        icon: <ComplaintsIcon />,\r\n        color: 'info',\r\n        subtitle: 'New complaints'\r\n      },\r\n      {\r\n        title: 'Resolution Rate',\r\n        value: `${stats.monthlyStats.resolutionRate || 0}%`,\r\n        icon: <ResolvedIcon />,\r\n        color: 'success',\r\n        subtitle: 'Monthly average'\r\n      },\r\n      {\r\n        title: 'Response Time',\r\n        value: `${stats.monthlyStats.responseEfficiency || 0}%`,\r\n        icon: <TimeIcon />,\r\n        color: 'warning',\r\n        subtitle: 'Within 24h'\r\n      },\r\n      {\r\n        title: 'Avg Resolution',\r\n        value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,\r\n        icon: <ResolutionIcon />,\r\n        color: 'primary',\r\n        subtitle: 'Hours to resolve'\r\n      }\r\n    ];\r\n  }, [stats]);\r\n\r\n  return (\r\n    <Box sx={{ p: { xs: 2, sm: 3 } }}>\r\n      <Typography variant=\"h4\" sx={{ mb: 4, fontWeight: 600 }}>\r\n        Dashboard\r\n      </Typography>\r\n\r\n      {error && (\r\n        <Alert \r\n          severity=\"error\" \r\n          sx={{ mb: 3 }}\r\n          action={\r\n            <motion.div whileHover={{ scale: 1.05 }}>\r\n              <Button color=\"inherit\" size=\"small\" onClick={fetchDashboardData}>\r\n                Retry\r\n              </Button>\r\n            </motion.div>\r\n          }\r\n        >\r\n          {error}\r\n        </Alert>\r\n      )}\r\n\r\n      <Grid container spacing={3}>\r\n        {statCards.map((card, index) => (\r\n          <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n            <StatCard {...card} loading={loading} />\r\n          </Grid>\r\n        ))}\r\n\r\n        {/* Monthly Statistics Section */}\r\n        {monthlyStatCards.length > 0 && (\r\n          <Grid item xs={12}>\r\n            <Card\r\n              component={motion.div}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.2, duration: 0.3 }}\r\n              sx={{\r\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n                color: 'white',\r\n                overflow: 'visible',\r\n                position: 'relative',\r\n                '&::before': {\r\n                  content: '\"\"',\r\n                  position: 'absolute',\r\n                  top: 0,\r\n                  left: 0,\r\n                  right: 0,\r\n                  bottom: 0,\r\n                  background: 'rgba(255,255,255,0.1)',\r\n                  backdropFilter: 'blur(10px)',\r\n                  borderRadius: 'inherit',\r\n                  zIndex: 0\r\n                }\r\n              }}\r\n            >\r\n              <CardContent sx={{ position: 'relative', zIndex: 1 }}>\r\n                <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600, color: 'white' }}>\r\n                  📊 Monthly Performance Insights\r\n                </Typography>\r\n                <Grid container spacing={3}>\r\n                  {monthlyStatCards.map((card, index) => (\r\n                    <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n                      <MonthlyStatCard {...card} loading={loading} index={index} />\r\n                    </Grid>\r\n                  ))}\r\n                </Grid>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n        )}\r\n\r\n        <Grid item xs={12}>\r\n          <Card\r\n            component={motion.div}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4 }}\r\n            sx={{ \r\n              overflow: 'visible',\r\n              height: '100%',\r\n              minHeight: 400\r\n            }}\r\n          >\r\n            <CardContent>\r\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\r\n                Recent Activities\r\n              </Typography>\r\n              {loading ? (\r\n                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>\r\n                  <CircularProgress />\r\n                </Box>\r\n              ) : activities.length > 0 ? (\r\n                <List sx={{ p: 0 }}>\r\n                  {activities.slice(0, 8).map((activity, index) => (\r\n                    <ActivityItem\r\n                      key={`${activity.ComplaintId}-${activity.Status}`}\r\n                      activity={activity}\r\n                      index={index}\r\n                    />\r\n                  ))}\r\n                </List>\r\n              ) : (\r\n                <Box \r\n                  sx={{ \r\n                    textAlign: 'center', \r\n                    py: 4,\r\n                    color: 'text.secondary'\r\n                  }}\r\n                >\r\n                  <Typography>No recent activities</Typography>\r\n                </Box>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default Dashboard; "], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,cAAc,EACdC,aAAa,EACbC,MAAM,QACD,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,YAAY,EAC3BC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,gBAAgB,EACzBC,iBAAiB,IAAIC,UAAU,EAC/BC,QAAQ,IAAIC,QAAQ,EACpBC,KAAK,IAAIC,cAAc,EACvBC,QAAQ,IAAIC,SAAS,EACrBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AACtD,SAASC,SAAS,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,eAAe,gBAAAC,EAAA,cAAGlD,KAAK,CAACmD,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,KAAK;EAAEC,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC,QAAQ;EAAEC;AAAM,CAAC,KAAK;EAAAT,EAAA;EAC9F,MAAMU,KAAK,GAAGhD,QAAQ,CAAC,CAAC;EACxB,MAAMiD,QAAQ,GAAGxC,aAAa,CAACuC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,IAAIN,OAAO,EAAE;IACX,oBACET,OAAA,CAACzC,IAAI;MAACyD,EAAE,EAAE;QACRC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,wBAAwB;QACpCC,cAAc,EAAE,YAAY;QAC5BC,MAAM,EAAE,iCAAiC;QACzCZ,KAAK,EAAE;MACT,CAAE;MAAAa,QAAA,eACArB,OAAA,CAACxC,WAAW;QAACwD,EAAE,EAAE;UACfM,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBP,MAAM,EAAE;QACV,CAAE;QAAAI,QAAA,eACArB,OAAA,CAACtC,gBAAgB;UAAC+D,IAAI,EAAE,EAAG;UAACT,EAAE,EAAE;YAAER,KAAK,EAAE;UAAQ;QAAE;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX;EAEA,oBACE7B,OAAA,CAACP,MAAM,CAACqC,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE;IACpCC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAE;IAClCE,UAAU,EAAE;MACVC,KAAK,EAAEzB,KAAK,GAAG,GAAG;MAClB0B,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAE;IACR,CAAE;IAAAjB,QAAA,eAEFrB,OAAA,CAACzC,IAAI;MACHyD,EAAE,EAAE;QACFC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,wBAAwB;QACpCC,cAAc,EAAE,YAAY;QAC5BC,MAAM,EAAE,iCAAiC;QACzCZ,KAAK,EAAE,OAAO;QACd+B,MAAM,EAAE,SAAS;QACjBJ,UAAU,EAAE,eAAe;QAC3B,SAAS,EAAE;UACTjB,UAAU,EAAE,wBAAwB;UACpCsB,SAAS,EAAE,kBAAkB;UAC7BC,SAAS,EAAE;QACb;MACF,CAAE;MAAApB,QAAA,eAEFrB,OAAA,CAACxC,WAAW;QAACwD,EAAE,EAAE;UACfM,OAAO,EAAE,MAAM;UACfoB,aAAa,EAAE,QAAQ;UACvBlB,cAAc,EAAE,eAAe;UAC/BP,MAAM,EAAE,MAAM;UACd0B,CAAC,EAAE,CAAC;UACJ,cAAc,EAAE;YAAEC,EAAE,EAAE;UAAE;QAC1B,CAAE;QAAAvB,QAAA,gBACArB,OAAA,CAAC3C,GAAG;UAAC2D,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE;UAAa,CAAE;UAAAF,QAAA,gBACtFrB,OAAA,CAAC3C,GAAG;YAAAgE,QAAA,gBACFrB,OAAA,CAACvC,UAAU;cACToF,OAAO,EAAEhC,QAAQ,GAAG,OAAO,GAAG,OAAQ;cACtCG,EAAE,EAAE;gBACFgB,OAAO,EAAE,GAAG;gBACZc,UAAU,EAAE,GAAG;gBACfC,QAAQ,EAAE;cACZ,CAAE;cAAA1B,QAAA,EAEDhB;YAAK;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACZnB,QAAQ,iBACPV,OAAA,CAACvC,UAAU;cACToF,OAAO,EAAC,SAAS;cACjB7B,EAAE,EAAE;gBACFgB,OAAO,EAAE,GAAG;gBACZe,QAAQ,EAAE,SAAS;gBACnBzB,OAAO,EAAE;cACX,CAAE;cAAAD,QAAA,EAEDX;YAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN7B,OAAA,CAAC3C,GAAG;YAAC2D,EAAE,EAAE;cACPgB,OAAO,EAAE,GAAG;cACZe,QAAQ,EAAElC,QAAQ,GAAG,QAAQ,GAAG;YAClC,CAAE;YAAAQ,QAAA,EACCd;UAAI;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7B,OAAA,CAACvC,UAAU;UACToF,OAAO,EAAEhC,QAAQ,GAAG,IAAI,GAAG,IAAK;UAChCG,EAAE,EAAE;YACF8B,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAElC,QAAQ,GAAG,SAAS,GAAG,QAAQ;YACzCmC,UAAU,EAAE;UACd,CAAE;UAAA3B,QAAA,EAEDf;QAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEjB,CAAC;EAAA,QAzGejE,QAAQ,EACLS,aAAa;AAAA,EAwG/B,CAAC;EAAA,QAzGcT,QAAQ,EACLS,aAAa;AAAA,EAwG9B;AAAC4E,GAAA,GA1GGhD,eAAe;AA4GrB,MAAMiD,QAAQ,GAAGA,CAAC;EAAE7C,KAAK;EAAEC,KAAK;EAAEC,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAyC,GAAA;EACrE,MAAMvC,KAAK,GAAGhD,QAAQ,CAAC,CAAC;EACxB,MAAMiD,QAAQ,GAAGxC,aAAa,CAACuC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,oBACEf,OAAA,CAACP,MAAM,CAACqC,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEoB,CAAC,EAAE;IAAG,CAAE;IAC/BlB,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEoB,CAAC,EAAE;IAAE,CAAE;IAC9BjB,UAAU,EAAE;MACVkB,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAE,GAAG;MACdC,OAAO,EAAE,EAAE;MACXlB,QAAQ,EAAE;IACZ,CAAE;IACFmB,UAAU,EAAE;MACVvB,KAAK,EAAE,IAAI;MACXE,UAAU,EAAE;QAAEE,QAAQ,EAAE;MAAI;IAC9B,CAAE;IACFoB,QAAQ,EAAE;MAAExB,KAAK,EAAE;IAAK,CAAE;IAAAZ,QAAA,eAE1BrB,OAAA,CAACzC,IAAI;MACHyD,EAAE,EAAE;QACFC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,2BAA2BN,KAAK,CAAC8C,OAAO,CAAClD,KAAK,CAAC,CAACmD,IAAI,QAAQ/C,KAAK,CAAC8C,OAAO,CAAClD,KAAK,CAAC,CAACoD,IAAI,QAAQ;QACzGpD,KAAK,EAAE,OAAO;QACdqD,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClBrB,SAAS,EAAE7B,KAAK,CAACmD,OAAO,CAACtD,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;QACzC0B,UAAU,EAAE,uCAAuC;QACnD,SAAS,EAAE;UACTM,SAAS,EAAE7B,KAAK,CAACmD,OAAO,CAAC,CAAC,CAAC;UAC3BvB,SAAS,EAAE;QACb;MACF,CAAE;MAAAnB,QAAA,eAEFrB,OAAA,CAACxC,WAAW;QAACwD,EAAE,EAAE;UACf2B,CAAC,EAAE9B,QAAQ,GAAG,CAAC,GAAG,CAAC;UACnBI,MAAM,EAAE,MAAM;UACdK,OAAO,EAAE,MAAM;UACfoB,aAAa,EAAE,QAAQ;UACvBlB,cAAc,EAAE;QAClB,CAAE;QAAAH,QAAA,gBACArB,OAAA,CAAC3C,GAAG;UAAC2D,EAAE,EAAE;YACP6C,QAAQ,EAAE,UAAU;YACpBG,MAAM,EAAE,CAAC;YACT1C,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB0C,GAAG,EAAE;UACP,CAAE;UAAA5C,QAAA,gBACArB,OAAA,CAACP,MAAM,CAACqC,GAAG;YACTC,OAAO,EAAE;cAAEE,KAAK,EAAE;YAAE,CAAE;YACtBC,OAAO,EAAE;cAAED,KAAK,EAAE;YAAE,CAAE;YACtBE,UAAU,EAAE;cAAEC,KAAK,EAAE,GAAG;cAAEiB,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAAAjC,QAAA,eAE1DrE,KAAK,CAACkH,YAAY,CAAC3D,IAAI,EAAE;cACxBS,EAAE,EAAE;gBACF+B,QAAQ,EAAElC,QAAQ,GAAG,EAAE,GAAG,EAAE;gBAC5BmB,OAAO,EAAE,GAAG;gBACZmC,MAAM,EAAE;cACV;YACF,CAAC;UAAC;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACb7B,OAAA,CAAC3C,GAAG;YAAAgE,QAAA,gBACFrB,OAAA,CAACvC,UAAU;cACToF,OAAO,EAAEhC,QAAQ,GAAG,IAAI,GAAG,IAAK;cAChCuD,SAAS,EAAC,KAAK;cACfpD,EAAE,EAAE;gBACF8B,UAAU,EAAE,GAAG;gBACfuB,UAAU,EAAE,GAAG;gBACfC,EAAE,EAAE,GAAG;gBACPtB,UAAU,EAAE;cACd,CAAE;cAAA3B,QAAA,EAEDZ,OAAO,gBACNT,OAAA,CAACP,MAAM,CAACqC,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE;gBAAE,CAAE;gBACxBE,OAAO,EAAE;kBAAEF,OAAO,EAAE;gBAAE,CAAE;gBACxBG,UAAU,EAAE;kBAAEE,QAAQ,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,eAE9BrB,OAAA,CAACtC,gBAAgB;kBAAC+D,IAAI,EAAEZ,QAAQ,GAAG,EAAE,GAAG,EAAG;kBAACL,KAAK,EAAC;gBAAS;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,gBAEb7B,OAAA,CAACP,MAAM,CAACqC,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEoB,CAAC,EAAE;gBAAG,CAAE;gBAC/BlB,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEoB,CAAC,EAAE;gBAAE,CAAE;gBAC9BjB,UAAU,EAAE;kBAAEC,KAAK,EAAE,GAAG;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAEzCf;cAAK;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eACb7B,OAAA,CAACvC,UAAU;cACToF,OAAO,EAAEhC,QAAQ,GAAG,OAAO,GAAG,OAAQ;cACtCG,EAAE,EAAE;gBACFgB,OAAO,EAAE,GAAG;gBACZc,UAAU,EAAE,GAAG;gBACfyB,aAAa,EAAE,OAAO;gBACtBvB,UAAU,EAAE;cACd,CAAE;cAAA3B,QAAA,EAEDhB;YAAK;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACZnB,QAAQ,iBACPV,OAAA,CAACvC,UAAU;cACToF,OAAO,EAAC,SAAS;cACjB7B,EAAE,EAAE;gBACFgB,OAAO,EAAE,GAAG;gBACZc,UAAU,EAAE,GAAG;gBACfE,UAAU,EAAE,6BAA6B;gBACzC1B,OAAO,EAAE;cACX,CAAE;cAAAD,QAAA,EAEDX;YAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7B,OAAA,CAAC3C,GAAG;UACF+G,SAAS,EAAE3E,MAAM,CAACqC,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAE,CAAE;UACrCE,UAAU,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC1CrB,EAAE,EAAE;YACF6C,QAAQ,EAAE,UAAU;YACpBW,KAAK,EAAE,CAAC,EAAE;YACVC,MAAM,EAAE,CAAC,EAAE;YACXN,MAAM,EAAE;UACV,CAAE;UAAA9C,QAAA,eAEDrE,KAAK,CAACkH,YAAY,CAAC3D,IAAI,EAAE;YACxBS,EAAE,EAAE;cAAE+B,QAAQ,EAAElC,QAAQ,GAAG,GAAG,GAAG;YAAI;UACvC,CAAC;QAAC;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEjB,CAAC;;AAED;AAAAsB,GAAA,CA3IMD,QAAQ;EAAA,QACEtF,QAAQ,EACLS,aAAa;AAAA;AAAAqG,GAAA,GAF1BxB,QAAQ;AA4Id,MAAMyB,cAAc,GAAGA,CAACC,MAAM,EAAEhE,KAAK,KAAK;EACxC,MAAMiE,YAAY,GAAG;IACnB,KAAK,EAAEjE,KAAK,CAAC8C,OAAO,CAACoB,IAAI,CAACnB,IAAI;IAC9B,UAAU,EAAE/C,KAAK,CAAC8C,OAAO,CAACqB,OAAO,CAACpB,IAAI;IACtC,aAAa,EAAE/C,KAAK,CAAC8C,OAAO,CAACqB,OAAO,CAACnB,IAAI;IACzC,UAAU,EAAEhD,KAAK,CAAC8C,OAAO,CAACsB,OAAO,CAACrB,IAAI;IACtC,UAAU,EAAE/C,KAAK,CAAC8C,OAAO,CAACuB,KAAK,CAACtB;EAClC,CAAC;EACD,OAAOkB,YAAY,CAACD,MAAM,CAAC,IAAIhE,KAAK,CAAC8C,OAAO,CAACwB,IAAI,CAAC,GAAG,CAAC;AACxD,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAACC,QAAQ,EAAExE,KAAK,KAAK;EAC5C,MAAMyE,cAAc,GAAG;IACrB,KAAK,EAAEzE,KAAK,CAAC8C,OAAO,CAACsB,OAAO,CAACrB,IAAI;IACjC,QAAQ,EAAE/C,KAAK,CAAC8C,OAAO,CAACqB,OAAO,CAACpB,IAAI;IACpC,MAAM,EAAE/C,KAAK,CAAC8C,OAAO,CAACuB,KAAK,CAACtB,IAAI;IAChC,UAAU,EAAE/C,KAAK,CAAC8C,OAAO,CAACuB,KAAK,CAACrB;EAClC,CAAC;EACD,OAAOyB,cAAc,CAACD,QAAQ,CAAC,IAAIxE,KAAK,CAAC8C,OAAO,CAACwB,IAAI,CAAC,GAAG,CAAC;AAC5D,CAAC;;AAED;AACA,MAAMI,eAAe,GAAIC,SAAS,IAAK;EACrC,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;EAE5B,IAAI;IACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAEhC,IAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MACzBC,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEN,SAAS,CAAC;MAC7C,OAAO,KAAK;IACd;;IAEA;IACA,OAAO3F,MAAM,CAAC4F,IAAI,EAAE,MAAM,CAAC;EAC7B,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdW,OAAO,CAACX,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,cAAc;EACvB;AACF,CAAC;AAED,MAAMa,YAAY,gBAAAC,GAAA,cAAG/I,KAAK,CAACmD,IAAI,CAAA6F,GAAA,GAAAD,GAAA,CAAC,CAAC;EAAEE,QAAQ;EAAEtF;AAAM,CAAC,KAAK;EAAAoF,GAAA;EACvD,MAAMnF,KAAK,GAAGhD,QAAQ,CAAC,CAAC;;EAIxB;EACA,MAAMsI,WAAW,GAAG9I,OAAO,CAAC,MAAMuH,cAAc,CAACsB,QAAQ,CAACE,MAAM,EAAEvF,KAAK,CAAC,EAAE,CAACqF,QAAQ,CAACE,MAAM,EAAEvF,KAAK,CAAC,CAAC;EACnG,MAAMwF,aAAa,GAAGhJ,OAAO,CAAC,MAAM+H,gBAAgB,CAACc,QAAQ,CAACI,QAAQ,EAAEzF,KAAK,CAAC,EAAE,CAACqF,QAAQ,CAACI,QAAQ,EAAEzF,KAAK,CAAC,CAAC;EAE3G,oBACEZ,OAAA,CAACP,MAAM,CAACqC,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEsE,CAAC,EAAE,CAAC;IAAG,CAAE;IAChCpE,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEsE,CAAC,EAAE;IAAE,CAAE;IAC9BnE,UAAU,EAAE;MACVC,KAAK,EAAEmE,IAAI,CAACC,GAAG,CAAC7F,KAAK,GAAG,IAAI,EAAE,GAAG,CAAC;MAAE;MACpC0B,QAAQ,EAAE,GAAG;MAAE;MACfC,IAAI,EAAE;IACR,CAAE;IAAAjB,QAAA,eAEFrB,OAAA,CAAClC,QAAQ;MACPkD,EAAE,EAAE;QACFyF,OAAO,EAAE,kBAAkB;QAC3BC,YAAY,EAAE,CAAC;QACfpC,EAAE,EAAE,CAAC;QACL7B,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE;UACTgE,OAAO,EAAE,cAAc;UACvBjE,SAAS,EAAE,iBAAiB;UAC5BL,UAAU,EAAE,sBAAsB,CAAE;QACtC;MACF,CAAE;MAAAd,QAAA,gBAEFrB,OAAA,CAAChC,YAAY;QAAAqD,QAAA,eACXrB,OAAA,CAAChB,UAAU;UAACgC,EAAE,EAAE;YAAER,KAAK,EAAE0F;UAAY;QAAE;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACf7B,OAAA,CAACjC,YAAY;QACX4I,OAAO,eACL3G,OAAA,CAAC3C,GAAG;UAAC2D,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE0C,GAAG,EAAE,CAAC;YAAEK,EAAE,EAAE;UAAI,CAAE;UAAAjD,QAAA,gBAClErB,OAAA,CAACvC,UAAU;YAACoF,OAAO,EAAC,WAAW;YAAC7B,EAAE,EAAE;cAAE8B,UAAU,EAAE,GAAG;cAAE8D,IAAI,EAAE;YAAE,CAAE;YAAAvF,QAAA,GAAC,GAC/D,EAAC4E,QAAQ,CAACY,eAAe,EAAC,KAAG,EAACZ,QAAQ,CAACa,WAAW;UAAA;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EACZoE,QAAQ,CAACI,QAAQ,iBAChBrG,OAAA,CAAC7B,IAAI;YACH4I,KAAK,EAAEd,QAAQ,CAACI,QAAS;YACzB5E,IAAI,EAAC,OAAO;YACZT,EAAE,EAAE;cACFyF,OAAO,EAAE,GAAGL,aAAa,IAAI;cAC7B5F,KAAK,EAAE4F,aAAa;cACpBtD,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE;YACZ;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACDmF,SAAS,eACPhH,OAAA,CAAC3C,GAAG;UAAC2D,EAAE,EAAE;YAAEiG,EAAE,EAAE;UAAI,CAAE;UAAA5F,QAAA,GAClB4E,QAAQ,CAACiB,eAAe,iBACvBlH,OAAA,CAACvC,UAAU;YAACoF,OAAO,EAAC,OAAO;YAACrC,KAAK,EAAC,gBAAgB;YAACQ,EAAE,EAAE;cAAEsD,EAAE,EAAE;YAAI,CAAE;YAAAjD,QAAA,EAChE4E,QAAQ,CAACiB;UAAe;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACb,eACD7B,OAAA,CAAC3C,GAAG;YAAC2D,EAAE,EAAE;cAAEM,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAE0C,GAAG,EAAE,CAAC;cAAEkD,QAAQ,EAAE;YAAO,CAAE;YAAA9F,QAAA,gBAC3ErB,OAAA,CAAC7B,IAAI;cACH4I,KAAK,EAAEd,QAAQ,CAACE,MAAO;cACvB1E,IAAI,EAAC,OAAO;cACZT,EAAE,EAAE;gBACFyF,OAAO,EAAE,GAAGP,WAAW,IAAI;gBAC3B1F,KAAK,EAAE0F,WAAW;gBAClBpD,UAAU,EAAE;cACd;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACDoE,QAAQ,CAACmB,QAAQ,iBAChBpH,OAAA,CAAC7B,IAAI;cACH4I,KAAK,EAAEd,QAAQ,CAACmB,QAAS;cACzB3F,IAAI,EAAC,OAAO;cACZoB,OAAO,EAAC,UAAU;cAClB7B,EAAE,EAAE;gBAAE+B,QAAQ,EAAE;cAAS;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEjB,CAAC;EAAA,QAvFejE,QAAQ;AAAA,EAuFvB,CAAC;EAAA,QAvFcA,QAAQ;AAAA,EAuFtB;AAACyJ,GAAA,GAxFGvB,YAAY;AA0FlB,SAASwB,SAASA,CAAA,EAAG;EAAAC,GAAA;EACnB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxK,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyK,UAAU,EAAEC,aAAa,CAAC,GAAG1K,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwD,OAAO,EAAEmH,UAAU,CAAC,GAAG3K,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgI,KAAK,EAAE4C,QAAQ,CAAC,GAAG5K,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM2D,KAAK,GAAGhD,QAAQ,CAAC,CAAC;EACxB,MAAMiD,QAAQ,GAAGxC,aAAa,CAACuC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAM;IAAE+G;EAAO,CAAC,GAAGhI,SAAS,CAAC,CAAC;EAE9B,MAAMiI,kBAAkB,GAAG5K,WAAW,CAAC,YAAY;IACjD,IAAI;MACFyK,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACI,aAAa,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5DxI,KAAK,CAACyI,GAAG,CAAC,sBAAsB,EAAE;QAChCC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;UACP,eAAe,EAAE,YAAY,CAAC;QAChC;MACF,CAAC,CAAC,EACF3I,KAAK,CAACyI,GAAG,CAAC,kCAAkC,EAAE;QAC5CC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU,CAAC;QAC9B;MACF,CAAC,CAAC,CACH,CAAC;MAEFb,QAAQ,CAACO,aAAa,CAACO,IAAI,CAAC;MAC5BZ,aAAa,CAACM,kBAAkB,CAACM,IAAI,CAAC;MACtCV,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZ5C,OAAO,CAACX,KAAK,CAAC,gCAAgC,EAAEuD,GAAG,CAAC;MACpDX,QAAQ,CAAC,wDAAwD,CAAC;IACpE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN1K,SAAS,CAAC,MAAM;IACd6K,kBAAkB,CAAC,CAAC;;IAEpB;IACA,MAAMU,QAAQ,GAAGC,WAAW,CAACX,kBAAkB,EAAE,KAAK,CAAC;;IAEvD;IACA,IAAID,MAAM,EAAE;MACV;MACAA,MAAM,CAACa,EAAE,CAAC,gBAAgB,EAAE,MAAM;QAChC/C,OAAO,CAACgD,GAAG,CAAC,iDAAiD,CAAC;QAC9Db,kBAAkB,CAAC,CAAC;MACtB,CAAC,CAAC;;MAEF;MACAD,MAAM,CAACa,EAAE,CAAC,mBAAmB,EAAE,MAAM;QACnC/C,OAAO,CAACgD,GAAG,CAAC,iDAAiD,CAAC;QAC9Db,kBAAkB,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXc,aAAa,CAACJ,QAAQ,CAAC;MACvB,IAAIX,MAAM,EAAE;QACVA,MAAM,CAACgB,GAAG,CAAC,gBAAgB,CAAC;QAC5BhB,MAAM,CAACgB,GAAG,CAAC,mBAAmB,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACf,kBAAkB,EAAED,MAAM,CAAC,CAAC;EAEhC,MAAMiB,SAAS,GAAG3L,OAAO,CAAC,MAAM,CAC9B;IACEiD,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,CAAAkH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwB,eAAe,KAAI,CAAC;IAClCzI,IAAI,eAAEP,OAAA,CAACxB,cAAc;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBrB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CAAAkH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyB,kBAAkB,KAAI,CAAC;IACrC1I,IAAI,eAAEP,OAAA,CAACtB,YAAY;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBrB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAAkH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0B,iBAAiB,KAAI,CAAC;IACpC3I,IAAI,eAAEP,OAAA,CAACpB,WAAW;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBrB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,CAAAkH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2B,sBAAsB,KAAI,CAAC;IACzC5I,IAAI,eAAEP,OAAA,CAAClB,gBAAgB;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BrB,KAAK,EAAE;EACT,CAAC,CACF,EAAE,CAACgH,KAAK,CAAC,CAAC;EAEX,MAAM4B,gBAAgB,GAAGhM,OAAO,CAAC,MAAM;IACrC,IAAI,EAACoK,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE6B,YAAY,GAAE,OAAO,EAAE;IAEnC,OAAO,CACL;MACEhJ,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAEkH,KAAK,CAAC6B,YAAY,CAACC,sBAAsB,IAAI,CAAC;MACrD/I,IAAI,eAAEP,OAAA,CAACxB,cAAc;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxBrB,KAAK,EAAE,MAAM;MACbE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,GAAGkH,KAAK,CAAC6B,YAAY,CAACE,cAAc,IAAI,CAAC,GAAG;MACnDhJ,IAAI,eAAEP,OAAA,CAACtB,YAAY;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBrB,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,GAAGkH,KAAK,CAAC6B,YAAY,CAACG,kBAAkB,IAAI,CAAC,GAAG;MACvDjJ,IAAI,eAAEP,OAAA,CAACd,QAAQ;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClBrB,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,GAAGiG,IAAI,CAACkD,KAAK,CAACjC,KAAK,CAAC6B,YAAY,CAACK,kBAAkB,IAAI,CAAC,CAAC,GAAG;MACnEnJ,IAAI,eAAEP,OAAA,CAACR,cAAc;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxBrB,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,CACF;EACH,CAAC,EAAE,CAAC8G,KAAK,CAAC,CAAC;EAEX,oBACExH,OAAA,CAAC3C,GAAG;IAAC2D,EAAE,EAAE;MAAE2B,CAAC,EAAE;QAAEgH,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAAE,CAAE;IAAAvI,QAAA,gBAC/BrB,OAAA,CAACvC,UAAU;MAACoF,OAAO,EAAC,IAAI;MAAC7B,EAAE,EAAE;QAAEsD,EAAE,EAAE,CAAC;QAAExB,UAAU,EAAE;MAAI,CAAE;MAAAzB,QAAA,EAAC;IAEzD;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZoD,KAAK,iBACJjF,OAAA,CAACrC,KAAK;MACJkM,QAAQ,EAAC,OAAO;MAChB7I,EAAE,EAAE;QAAEsD,EAAE,EAAE;MAAE,CAAE;MACdwF,MAAM,eACJ9J,OAAA,CAACP,MAAM,CAACqC,GAAG;QAAC0B,UAAU,EAAE;UAAEvB,KAAK,EAAE;QAAK,CAAE;QAAAZ,QAAA,eACtCrB,OAAA,CAAC1B,MAAM;UAACkC,KAAK,EAAC,SAAS;UAACiB,IAAI,EAAC,OAAO;UAACsI,OAAO,EAAEhC,kBAAmB;UAAA1G,QAAA,EAAC;QAElE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb;MAAAR,QAAA,EAEA4D;IAAK;MAAAvD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAED7B,OAAA,CAAC1C,IAAI;MAAC0M,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA5I,QAAA,GACxB0H,SAAS,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAExJ,KAAK,kBACzBX,OAAA,CAAC1C,IAAI;QAAC8M,IAAI;QAACT,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACS,EAAE,EAAE,CAAE;QAAAhJ,QAAA,eAC9BrB,OAAA,CAACkD,QAAQ;UAAA,GAAKiH,IAAI;UAAE1J,OAAO,EAAEA;QAAQ;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GADJsI,IAAI,CAAC9J,KAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE1C,CACP,CAAC,EAGDuH,gBAAgB,CAACkB,MAAM,GAAG,CAAC,iBAC1BtK,OAAA,CAAC1C,IAAI;QAAC8M,IAAI;QAACT,EAAE,EAAE,EAAG;QAAAtI,QAAA,eAChBrB,OAAA,CAACzC,IAAI;UACH6G,SAAS,EAAE3E,MAAM,CAACqC,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEoB,CAAC,EAAE;UAAG,CAAE;UAC/BlB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEoB,CAAC,EAAE;UAAE,CAAE;UAC9BjB,UAAU,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC1CrB,EAAE,EAAE;YACFE,UAAU,EAAE,mDAAmD;YAC/DV,KAAK,EAAE,OAAO;YACdsD,QAAQ,EAAE,SAAS;YACnBD,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE;cACX0G,OAAO,EAAE,IAAI;cACb1G,QAAQ,EAAE,UAAU;cACpB2G,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPjG,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTvD,UAAU,EAAE,uBAAuB;cACnCC,cAAc,EAAE,YAAY;cAC5BuF,YAAY,EAAE,SAAS;cACvB1C,MAAM,EAAE;YACV;UACF,CAAE;UAAA3C,QAAA,eAEFrB,OAAA,CAACxC,WAAW;YAACwD,EAAE,EAAE;cAAE6C,QAAQ,EAAE,UAAU;cAAEG,MAAM,EAAE;YAAE,CAAE;YAAA3C,QAAA,gBACnDrB,OAAA,CAACvC,UAAU;cAACoF,OAAO,EAAC,IAAI;cAAC7B,EAAE,EAAE;gBAAEsD,EAAE,EAAE,CAAC;gBAAExB,UAAU,EAAE,GAAG;gBAAEtC,KAAK,EAAE;cAAQ,CAAE;cAAAa,QAAA,EAAC;YAEzE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7B,OAAA,CAAC1C,IAAI;cAAC0M,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA5I,QAAA,EACxB+H,gBAAgB,CAACc,GAAG,CAAC,CAACC,IAAI,EAAExJ,KAAK,kBAChCX,OAAA,CAAC1C,IAAI;gBAAC8M,IAAI;gBAACT,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAACS,EAAE,EAAE,CAAE;gBAAAhJ,QAAA,eAC9BrB,OAAA,CAACC,eAAe;kBAAA,GAAKkK,IAAI;kBAAE1J,OAAO,EAAEA,OAAQ;kBAACE,KAAK,EAAEA;gBAAM;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC,GADzBsI,IAAI,CAAC9J,KAAK;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE1C,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,eAED7B,OAAA,CAAC1C,IAAI;QAAC8M,IAAI;QAACT,EAAE,EAAE,EAAG;QAAAtI,QAAA,eAChBrB,OAAA,CAACzC,IAAI;UACH6G,SAAS,EAAE3E,MAAM,CAACqC,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEoB,CAAC,EAAE;UAAG,CAAE;UAC/BlB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEoB,CAAC,EAAE;UAAE,CAAE;UAC9BjB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BpB,EAAE,EAAE;YACF8C,QAAQ,EAAE,SAAS;YACnB7C,MAAM,EAAE,MAAM;YACdyJ,SAAS,EAAE;UACb,CAAE;UAAArJ,QAAA,eAEFrB,OAAA,CAACxC,WAAW;YAAA6D,QAAA,gBACVrB,OAAA,CAACvC,UAAU;cAACoF,OAAO,EAAC,IAAI;cAAC7B,EAAE,EAAE;gBAAEsD,EAAE,EAAE,CAAC;gBAAExB,UAAU,EAAE;cAAI,CAAE;cAAAzB,QAAA,EAAC;YAEzD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZpB,OAAO,gBACNT,OAAA,CAAC3C,GAAG;cAAC2D,EAAE,EAAE;gBAAEM,OAAO,EAAE,MAAM;gBAAEE,cAAc,EAAE,QAAQ;gBAAEmB,CAAC,EAAE;cAAE,CAAE;cAAAtB,QAAA,eAC3DrB,OAAA,CAACtC,gBAAgB;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,GACJ6F,UAAU,CAAC4C,MAAM,GAAG,CAAC,gBACvBtK,OAAA,CAACnC,IAAI;cAACmD,EAAE,EAAE;gBAAE2B,CAAC,EAAE;cAAE,CAAE;cAAAtB,QAAA,EAChBqG,UAAU,CAACiD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACT,GAAG,CAAC,CAACjE,QAAQ,EAAEtF,KAAK,kBAC1CX,OAAA,CAAC8F,YAAY;gBAEXG,QAAQ,EAAEA,QAAS;gBACnBtF,KAAK,EAAEA;cAAM,GAFR,GAAGsF,QAAQ,CAAC2E,WAAW,IAAI3E,QAAQ,CAACE,MAAM,EAAE;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGlD,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAEP7B,OAAA,CAAC3C,GAAG;cACF2D,EAAE,EAAE;gBACF6J,SAAS,EAAE,QAAQ;gBACnBC,EAAE,EAAE,CAAC;gBACLtK,KAAK,EAAE;cACT,CAAE;cAAAa,QAAA,eAEFrB,OAAA,CAACvC,UAAU;gBAAA4D,QAAA,EAAC;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC0F,GAAA,CA3PQD,SAAS;EAAA,QAKF1J,QAAQ,EACLS,aAAa,EACXyB,SAAS;AAAA;AAAAiL,GAAA,GAPrBzD,SAAS;AA6PlB,eAAeA,SAAS;AAAC,IAAAlH,EAAA,EAAA6C,GAAA,EAAAyB,GAAA,EAAAsB,GAAA,EAAAqB,GAAA,EAAA0D,GAAA;AAAAC,YAAA,CAAA5K,EAAA;AAAA4K,YAAA,CAAA/H,GAAA;AAAA+H,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA3D,GAAA;AAAA2D,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}