{"ast": null, "code": "import React,{createContext,useContext,useState,useEffect}from'react';import axios from'../utils/axiosConfig';import{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext(null);export const useAuth=()=>{const context=useContext(AuthContext);if(!context){throw new Error('useAuth must be used within an AuthProvider');}return context;};export const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[loading,setLoading]=useState(true);const[initialized,setInitialized]=useState(false);// Initialize auth state on mount\nuseEffect(()=>{const initializeAuth=async()=>{try{var _response$data;const token=localStorage.getItem('token');console.log('Initializing auth with token:',token?'exists':'none');if(!token){setUser(null);setLoading(false);setInitialized(true);return;}// Set token in axios defaults\naxios.defaults.headers.common['Authorization']=\"Bearer \".concat(token);// Verify token and get user data\nconsole.log('Verifying token...');const response=await axios.get('/api/auth/verify-token');console.log('Verification response:',response.data);if((_response$data=response.data)!==null&&_response$data!==void 0&&_response$data.user){// Ensure admin users always have dashboard permission\nconst userData=response.data.user;if(userData.isAdmin){if(!userData.permissions)userData.permissions={};userData.permissions.canViewDashboard=true;}setUser({empCode:userData.empCode,name:userData.name,department:userData.department,isAdmin:userData.isAdmin,permissions:userData.permissions});console.log('User state set:',userData);}else{console.log('No user data in response, clearing auth state');localStorage.removeItem('token');delete axios.defaults.headers.common['Authorization'];setUser(null);}}catch(error){console.error('Auth initialization error:',error);localStorage.removeItem('token');delete axios.defaults.headers.common['Authorization'];setUser(null);}finally{setLoading(false);setInitialized(true);}};initializeAuth();},[]);const login=async(empCode,password)=>{try{setLoading(true);console.log('AuthContext: Starting login process');const response=await axios.post('/api/auth/login',{empCode:empCode.trim(),password:password.trim()});console.log('AuthContext: Login response received',response.data);const{token,user:userData}=response.data;if(!token||!userData){throw new Error('Invalid response from server');}// Store token\nlocalStorage.setItem('token',token);console.log('AuthContext: Token stored');// Set token in axios headers\naxios.defaults.headers.common['Authorization']=\"Bearer \".concat(token);// Ensure admin users always have dashboard permission\nif(userData.isAdmin){if(!userData.permissions)userData.permissions={};userData.permissions.canViewDashboard=true;}// Update user state with proper structure\nconst userState={empCode:userData.empCode,name:userData.empName,department:userData.deptName,isAdmin:userData.isAdmin,permissions:userData.permissions};setUser(userState);console.log('AuthContext: User state updated',userState);return{success:true};}catch(error){var _error$response,_error$response2,_error$response3,_error$response3$data;console.error('AuthContext: Login error:',error);// Clear any partial state\nlocalStorage.removeItem('token');delete axios.defaults.headers.common['Authorization'];setUser(null);// Better error messages for mobile\nlet errorMessage='Login failed';if(error.code==='NETWORK_ERROR'||!error.response){errorMessage='Network connection error. Please check your internet connection and try again.';}else if(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===401){var _error$response$data;errorMessage=((_error$response$data=error.response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||'Invalid employee code or password';}else if(((_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.status)>=500){errorMessage='Server error. Please try again in a few moments.';}else if((_error$response3=error.response)!==null&&_error$response3!==void 0&&(_error$response3$data=_error$response3.data)!==null&&_error$response3$data!==void 0&&_error$response3$data.message){errorMessage=error.response.data.message;}return{success:false,message:errorMessage};}finally{setLoading(false);}};const logout=async()=>{try{// Call logout endpoint to clean up server session\nawait axios.post('/api/auth/logout');}catch(error){console.error('Error during logout:',error);// Continue with local cleanup even if server call fails\n}// Clear token\nlocalStorage.removeItem('token');delete axios.defaults.headers.common['Authorization'];// Clear user state\nsetUser(null);};const refreshUserPermissions=async()=>{try{var _response$data2;const token=localStorage.getItem('token');if(!token)return;console.log('Refreshing user permissions...');const response=await axios.get('/api/auth/me');console.log('Permission refresh response:',response.data);if((_response$data2=response.data)!==null&&_response$data2!==void 0&&_response$data2.user){const userData=response.data.user;if(userData.isAdmin){if(!userData.permissions)userData.permissions={};userData.permissions.canViewDashboard=true;}setUser({empCode:userData.empCode,name:userData.name,department:userData.department,isAdmin:userData.isAdmin,permissions:userData.permissions});console.log('User permissions refreshed:',userData);return true;}return false;}catch(error){console.error('Error refreshing user permissions:',error);return false;}};const value={user,loading,initialized,login,logout,refreshUserPermissions};// Don't render children until auth is initialized\nif(!initialized){return null;}return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};export default AuthContext;", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsx", "_jsx", "AuthContext", "useAuth", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "loading", "setLoading", "initialized", "setInitialized", "initializeAuth", "_response$data", "token", "localStorage", "getItem", "console", "log", "defaults", "headers", "common", "concat", "response", "get", "data", "userData", "isAdmin", "permissions", "canViewDashboard", "empCode", "name", "department", "removeItem", "error", "login", "password", "post", "trim", "setItem", "userState", "empName", "deptName", "success", "_error$response", "_error$response2", "_error$response3", "_error$response3$data", "errorMessage", "code", "status", "_error$response$data", "message", "logout", "refreshUserPermissions", "_response$data2", "value", "Provider"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport axios from '../utils/axiosConfig';\r\n\r\nconst AuthContext = createContext(null);\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (!context) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const AuthProvider = ({ children }) => {\r\n  const [user, setUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [initialized, setInitialized] = useState(false);\r\n\r\n  // Initialize auth state on mount\r\n  useEffect(() => {\r\n    const initializeAuth = async () => {\r\n      try {\r\n        const token = localStorage.getItem('token');\r\n        console.log('Initializing auth with token:', token ? 'exists' : 'none');\r\n        \r\n        if (!token) {\r\n          setUser(null);\r\n          setLoading(false);\r\n          setInitialized(true);\r\n          return;\r\n        }\r\n\r\n        // Set token in axios defaults\r\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n        \r\n        // Verify token and get user data\r\n        console.log('Verifying token...');\r\n        const response = await axios.get('/api/auth/verify-token');\r\n        console.log('Verification response:', response.data);\r\n        \r\n        if (response.data?.user) {\r\n          // Ensure admin users always have dashboard permission\r\n          const userData = response.data.user;\r\n          if (userData.isAdmin) {\r\n            if (!userData.permissions) userData.permissions = {};\r\n            userData.permissions.canViewDashboard = true;\r\n          }\r\n\r\n          setUser({\r\n            empCode: userData.empCode,\r\n            name: userData.name,\r\n            department: userData.department,\r\n            isAdmin: userData.isAdmin,\r\n            permissions: userData.permissions\r\n          });\r\n          console.log('User state set:', userData);\r\n        } else {\r\n          console.log('No user data in response, clearing auth state');\r\n          localStorage.removeItem('token');\r\n          delete axios.defaults.headers.common['Authorization'];\r\n          setUser(null);\r\n        }\r\n      } catch (error) {\r\n        console.error('Auth initialization error:', error);\r\n        localStorage.removeItem('token');\r\n        delete axios.defaults.headers.common['Authorization'];\r\n        setUser(null);\r\n      } finally {\r\n        setLoading(false);\r\n        setInitialized(true);\r\n      }\r\n    };\r\n\r\n    initializeAuth();\r\n  }, []);\r\n\r\n  const login = async (empCode, password) => {\r\n    try {\r\n      setLoading(true);\r\n      console.log('AuthContext: Starting login process');\r\n\r\n      const response = await axios.post('/api/auth/login', {\r\n        empCode: empCode.trim(),\r\n        password: password.trim()\r\n      });\r\n\r\n      console.log('AuthContext: Login response received', response.data);\r\n\r\n      const { token, user: userData } = response.data;\r\n      if (!token || !userData) {\r\n        throw new Error('Invalid response from server');\r\n      }\r\n\r\n      // Store token\r\n      localStorage.setItem('token', token);\r\n      console.log('AuthContext: Token stored');\r\n\r\n      // Set token in axios headers\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n\r\n      // Ensure admin users always have dashboard permission\r\n      if (userData.isAdmin) {\r\n        if (!userData.permissions) userData.permissions = {};\r\n        userData.permissions.canViewDashboard = true;\r\n      }\r\n\r\n      // Update user state with proper structure\r\n      const userState = {\r\n        empCode: userData.empCode,\r\n        name: userData.empName,\r\n        department: userData.deptName,\r\n        isAdmin: userData.isAdmin,\r\n        permissions: userData.permissions\r\n      };\r\n\r\n      setUser(userState);\r\n      console.log('AuthContext: User state updated', userState);\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('AuthContext: Login error:', error);\r\n\r\n      // Clear any partial state\r\n      localStorage.removeItem('token');\r\n      delete axios.defaults.headers.common['Authorization'];\r\n      setUser(null);\r\n\r\n      // Better error messages for mobile\r\n      let errorMessage = 'Login failed';\r\n\r\n      if (error.code === 'NETWORK_ERROR' || !error.response) {\r\n        errorMessage = 'Network connection error. Please check your internet connection and try again.';\r\n      } else if (error.response?.status === 401) {\r\n        errorMessage = error.response.data?.message || 'Invalid employee code or password';\r\n      } else if (error.response?.status >= 500) {\r\n        errorMessage = 'Server error. Please try again in a few moments.';\r\n      } else if (error.response?.data?.message) {\r\n        errorMessage = error.response.data.message;\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: errorMessage\r\n      };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      // Call logout endpoint to clean up server session\r\n      await axios.post('/api/auth/logout');\r\n    } catch (error) {\r\n      console.error('Error during logout:', error);\r\n      // Continue with local cleanup even if server call fails\r\n    }\r\n\r\n    // Clear token\r\n    localStorage.removeItem('token');\r\n    delete axios.defaults.headers.common['Authorization'];\r\n\r\n    // Clear user state\r\n    setUser(null);\r\n  };\r\n\r\n  const refreshUserPermissions = async () => {\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      if (!token) return;\r\n\r\n      console.log('Refreshing user permissions...');\r\n      const response = await axios.get('/api/auth/me');\r\n      console.log('Permission refresh response:', response.data);\r\n\r\n      if (response.data?.user) {\r\n        const userData = response.data.user;\r\n        if (userData.isAdmin) {\r\n          if (!userData.permissions) userData.permissions = {};\r\n          userData.permissions.canViewDashboard = true;\r\n        }\r\n\r\n        setUser({\r\n          empCode: userData.empCode,\r\n          name: userData.name,\r\n          department: userData.department,\r\n          isAdmin: userData.isAdmin,\r\n          permissions: userData.permissions\r\n        });\r\n        console.log('User permissions refreshed:', userData);\r\n        return true;\r\n      }\r\n      return false;\r\n    } catch (error) {\r\n      console.error('Error refreshing user permissions:', error);\r\n      return false;\r\n    }\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    loading,\r\n    initialized,\r\n    login,\r\n    logout,\r\n    refreshUserPermissions\r\n  };\r\n\r\n  // Don't render children until auth is initialized\r\n  if (!initialized) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport default AuthContext; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC7E,MAAO,CAAAC,KAAK,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEzC,KAAM,CAAAC,WAAW,cAAGP,aAAa,CAAC,IAAI,CAAC,CAEvC,MAAO,MAAM,CAAAQ,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGR,UAAU,CAACM,WAAW,CAAC,CACvC,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED,MAAO,MAAM,CAAAE,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACc,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACgB,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAErD;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiB,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,KAAAC,cAAA,CACF,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3CC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAEJ,KAAK,CAAG,QAAQ,CAAG,MAAM,CAAC,CAEvE,GAAI,CAACA,KAAK,CAAE,CACVP,OAAO,CAAC,IAAI,CAAC,CACbE,UAAU,CAAC,KAAK,CAAC,CACjBE,cAAc,CAAC,IAAI,CAAC,CACpB,OACF,CAEA;AACAf,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,WAAAC,MAAA,CAAaR,KAAK,CAAE,CAElE;AACAG,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAA3B,KAAK,CAAC4B,GAAG,CAAC,wBAAwB,CAAC,CAC1DP,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEK,QAAQ,CAACE,IAAI,CAAC,CAEpD,IAAAZ,cAAA,CAAIU,QAAQ,CAACE,IAAI,UAAAZ,cAAA,WAAbA,cAAA,CAAeP,IAAI,CAAE,CACvB;AACA,KAAM,CAAAoB,QAAQ,CAAGH,QAAQ,CAACE,IAAI,CAACnB,IAAI,CACnC,GAAIoB,QAAQ,CAACC,OAAO,CAAE,CACpB,GAAI,CAACD,QAAQ,CAACE,WAAW,CAAEF,QAAQ,CAACE,WAAW,CAAG,CAAC,CAAC,CACpDF,QAAQ,CAACE,WAAW,CAACC,gBAAgB,CAAG,IAAI,CAC9C,CAEAtB,OAAO,CAAC,CACNuB,OAAO,CAAEJ,QAAQ,CAACI,OAAO,CACzBC,IAAI,CAAEL,QAAQ,CAACK,IAAI,CACnBC,UAAU,CAAEN,QAAQ,CAACM,UAAU,CAC/BL,OAAO,CAAED,QAAQ,CAACC,OAAO,CACzBC,WAAW,CAAEF,QAAQ,CAACE,WACxB,CAAC,CAAC,CACFX,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEQ,QAAQ,CAAC,CAC1C,CAAC,IAAM,CACLT,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAC5DH,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC,CAChC,MAAO,CAAArC,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CACrDd,OAAO,CAAC,IAAI,CAAC,CACf,CACF,CAAE,MAAO2B,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDnB,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC,CAChC,MAAO,CAAArC,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CACrDd,OAAO,CAAC,IAAI,CAAC,CACf,CAAC,OAAS,CACRE,UAAU,CAAC,KAAK,CAAC,CACjBE,cAAc,CAAC,IAAI,CAAC,CACtB,CACF,CAAC,CAEDC,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAuB,KAAK,CAAG,KAAAA,CAAOL,OAAO,CAAEM,QAAQ,GAAK,CACzC,GAAI,CACF3B,UAAU,CAAC,IAAI,CAAC,CAChBQ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC,CAElD,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAA3B,KAAK,CAACyC,IAAI,CAAC,iBAAiB,CAAE,CACnDP,OAAO,CAAEA,OAAO,CAACQ,IAAI,CAAC,CAAC,CACvBF,QAAQ,CAAEA,QAAQ,CAACE,IAAI,CAAC,CAC1B,CAAC,CAAC,CAEFrB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAEK,QAAQ,CAACE,IAAI,CAAC,CAElE,KAAM,CAAEX,KAAK,CAAER,IAAI,CAAEoB,QAAS,CAAC,CAAGH,QAAQ,CAACE,IAAI,CAC/C,GAAI,CAACX,KAAK,EAAI,CAACY,QAAQ,CAAE,CACvB,KAAM,IAAI,CAAAxB,KAAK,CAAC,8BAA8B,CAAC,CACjD,CAEA;AACAa,YAAY,CAACwB,OAAO,CAAC,OAAO,CAAEzB,KAAK,CAAC,CACpCG,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACAtB,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,WAAAC,MAAA,CAAaR,KAAK,CAAE,CAElE;AACA,GAAIY,QAAQ,CAACC,OAAO,CAAE,CACpB,GAAI,CAACD,QAAQ,CAACE,WAAW,CAAEF,QAAQ,CAACE,WAAW,CAAG,CAAC,CAAC,CACpDF,QAAQ,CAACE,WAAW,CAACC,gBAAgB,CAAG,IAAI,CAC9C,CAEA;AACA,KAAM,CAAAW,SAAS,CAAG,CAChBV,OAAO,CAAEJ,QAAQ,CAACI,OAAO,CACzBC,IAAI,CAAEL,QAAQ,CAACe,OAAO,CACtBT,UAAU,CAAEN,QAAQ,CAACgB,QAAQ,CAC7Bf,OAAO,CAAED,QAAQ,CAACC,OAAO,CACzBC,WAAW,CAAEF,QAAQ,CAACE,WACxB,CAAC,CAEDrB,OAAO,CAACiC,SAAS,CAAC,CAClBvB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAEsB,SAAS,CAAC,CAEzD,MAAO,CAAEG,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOT,KAAK,CAAE,KAAAU,eAAA,CAAAC,gBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACd9B,OAAO,CAACiB,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CAEjD;AACAnB,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC,CAChC,MAAO,CAAArC,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CACrDd,OAAO,CAAC,IAAI,CAAC,CAEb;AACA,GAAI,CAAAyC,YAAY,CAAG,cAAc,CAEjC,GAAId,KAAK,CAACe,IAAI,GAAK,eAAe,EAAI,CAACf,KAAK,CAACX,QAAQ,CAAE,CACrDyB,YAAY,CAAG,gFAAgF,CACjG,CAAC,IAAM,IAAI,EAAAJ,eAAA,CAAAV,KAAK,CAACX,QAAQ,UAAAqB,eAAA,iBAAdA,eAAA,CAAgBM,MAAM,IAAK,GAAG,CAAE,KAAAC,oBAAA,CACzCH,YAAY,CAAG,EAAAG,oBAAA,CAAAjB,KAAK,CAACX,QAAQ,CAACE,IAAI,UAAA0B,oBAAA,iBAAnBA,oBAAA,CAAqBC,OAAO,GAAI,mCAAmC,CACpF,CAAC,IAAM,IAAI,EAAAP,gBAAA,CAAAX,KAAK,CAACX,QAAQ,UAAAsB,gBAAA,iBAAdA,gBAAA,CAAgBK,MAAM,GAAI,GAAG,CAAE,CACxCF,YAAY,CAAG,kDAAkD,CACnE,CAAC,IAAM,KAAAF,gBAAA,CAAIZ,KAAK,CAACX,QAAQ,UAAAuB,gBAAA,YAAAC,qBAAA,CAAdD,gBAAA,CAAgBrB,IAAI,UAAAsB,qBAAA,WAApBA,qBAAA,CAAsBK,OAAO,CAAE,CACxCJ,YAAY,CAAGd,KAAK,CAACX,QAAQ,CAACE,IAAI,CAAC2B,OAAO,CAC5C,CAEA,MAAO,CACLT,OAAO,CAAE,KAAK,CACdS,OAAO,CAAEJ,YACX,CAAC,CACH,CAAC,OAAS,CACRvC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA4C,MAAM,CAAG,KAAAA,CAAA,GAAY,CACzB,GAAI,CACF;AACA,KAAM,CAAAzD,KAAK,CAACyC,IAAI,CAAC,kBAAkB,CAAC,CACtC,CAAE,MAAOH,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C;AACF,CAEA;AACAnB,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC,CAChC,MAAO,CAAArC,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAErD;AACAd,OAAO,CAAC,IAAI,CAAC,CACf,CAAC,CAED,KAAM,CAAA+C,sBAAsB,CAAG,KAAAA,CAAA,GAAY,CACzC,GAAI,KAAAC,eAAA,CACF,KAAM,CAAAzC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAI,CAACF,KAAK,CAAE,OAEZG,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAC7C,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAA3B,KAAK,CAAC4B,GAAG,CAAC,cAAc,CAAC,CAChDP,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEK,QAAQ,CAACE,IAAI,CAAC,CAE1D,IAAA8B,eAAA,CAAIhC,QAAQ,CAACE,IAAI,UAAA8B,eAAA,WAAbA,eAAA,CAAejD,IAAI,CAAE,CACvB,KAAM,CAAAoB,QAAQ,CAAGH,QAAQ,CAACE,IAAI,CAACnB,IAAI,CACnC,GAAIoB,QAAQ,CAACC,OAAO,CAAE,CACpB,GAAI,CAACD,QAAQ,CAACE,WAAW,CAAEF,QAAQ,CAACE,WAAW,CAAG,CAAC,CAAC,CACpDF,QAAQ,CAACE,WAAW,CAACC,gBAAgB,CAAG,IAAI,CAC9C,CAEAtB,OAAO,CAAC,CACNuB,OAAO,CAAEJ,QAAQ,CAACI,OAAO,CACzBC,IAAI,CAAEL,QAAQ,CAACK,IAAI,CACnBC,UAAU,CAAEN,QAAQ,CAACM,UAAU,CAC/BL,OAAO,CAAED,QAAQ,CAACC,OAAO,CACzBC,WAAW,CAAEF,QAAQ,CAACE,WACxB,CAAC,CAAC,CACFX,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAEQ,QAAQ,CAAC,CACpD,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAE,MAAOQ,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D,MAAO,MAAK,CACd,CACF,CAAC,CAED,KAAM,CAAAsB,KAAK,CAAG,CACZlD,IAAI,CACJE,OAAO,CACPE,WAAW,CACXyB,KAAK,CACLkB,MAAM,CACNC,sBACF,CAAC,CAED;AACA,GAAI,CAAC5C,WAAW,CAAE,CAChB,MAAO,KAAI,CACb,CAEA,mBACEZ,IAAA,CAACC,WAAW,CAAC0D,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAnD,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC,CAED,cAAe,CAAAN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}