{"ast": null, "code": "function simpleClamp(val) {\n  let min = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.MIN_SAFE_INTEGER;\n  let max = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.MAX_SAFE_INTEGER;\n  return Math.max(min, Math.min(val, max));\n}\nexport function clamp(val) {\n  let min = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.MIN_SAFE_INTEGER;\n  let max = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.MAX_SAFE_INTEGER;\n  let stepProp = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : NaN;\n  if (Number.isNaN(stepProp)) {\n    return simpleClamp(val, min, max);\n  }\n  const step = stepProp || 1;\n  const remainder = val % step;\n  const positivity = Math.sign(remainder);\n  if (Math.abs(remainder) > step / 2) {\n    return simpleClamp(val + positivity * (step - Math.abs(remainder)), min, max);\n  }\n  return simpleClamp(val - positivity * Math.abs(remainder), min, max);\n}\nexport function isNumber(val) {\n  return typeof val === 'number' && !Number.isNaN(val) && Number.isFinite(val);\n}", "map": {"version": 3, "names": ["simpleClamp", "val", "min", "arguments", "length", "undefined", "Number", "MIN_SAFE_INTEGER", "max", "MAX_SAFE_INTEGER", "Math", "clamp", "stepProp", "NaN", "isNaN", "step", "remainder", "positivity", "sign", "abs", "isNumber", "isFinite"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/unstable_useNumberInput/utils.js"], "sourcesContent": ["function simpleClamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER) {\n  return Math.max(min, Math.min(val, max));\n}\nexport function clamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER, stepProp = NaN) {\n  if (Number.isNaN(stepProp)) {\n    return simpleClamp(val, min, max);\n  }\n  const step = stepProp || 1;\n  const remainder = val % step;\n  const positivity = Math.sign(remainder);\n  if (Math.abs(remainder) > step / 2) {\n    return simpleClamp(val + positivity * (step - Math.abs(remainder)), min, max);\n  }\n  return simpleClamp(val - positivity * Math.abs(remainder), min, max);\n}\nexport function isNumber(val) {\n  return typeof val === 'number' && !Number.isNaN(val) && Number.isFinite(val);\n}"], "mappings": "AAAA,SAASA,WAAWA,CAACC,GAAG,EAAgE;EAAA,IAA9DC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,MAAM,CAACC,gBAAgB;EAAA,IAAEC,GAAG,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,MAAM,CAACG,gBAAgB;EACpF,OAAOC,IAAI,CAACF,GAAG,CAACN,GAAG,EAAEQ,IAAI,CAACR,GAAG,CAACD,GAAG,EAAEO,GAAG,CAAC,CAAC;AAC1C;AACA,OAAO,SAASG,KAAKA,CAACV,GAAG,EAAgF;EAAA,IAA9EC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,MAAM,CAACC,gBAAgB;EAAA,IAAEC,GAAG,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,MAAM,CAACG,gBAAgB;EAAA,IAAEG,QAAQ,GAAAT,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGU,GAAG;EACrG,IAAIP,MAAM,CAACQ,KAAK,CAACF,QAAQ,CAAC,EAAE;IAC1B,OAAOZ,WAAW,CAACC,GAAG,EAAEC,GAAG,EAAEM,GAAG,CAAC;EACnC;EACA,MAAMO,IAAI,GAAGH,QAAQ,IAAI,CAAC;EAC1B,MAAMI,SAAS,GAAGf,GAAG,GAAGc,IAAI;EAC5B,MAAME,UAAU,GAAGP,IAAI,CAACQ,IAAI,CAACF,SAAS,CAAC;EACvC,IAAIN,IAAI,CAACS,GAAG,CAACH,SAAS,CAAC,GAAGD,IAAI,GAAG,CAAC,EAAE;IAClC,OAAOf,WAAW,CAACC,GAAG,GAAGgB,UAAU,IAAIF,IAAI,GAAGL,IAAI,CAACS,GAAG,CAACH,SAAS,CAAC,CAAC,EAAEd,GAAG,EAAEM,GAAG,CAAC;EAC/E;EACA,OAAOR,WAAW,CAACC,GAAG,GAAGgB,UAAU,GAAGP,IAAI,CAACS,GAAG,CAACH,SAAS,CAAC,EAAEd,GAAG,EAAEM,GAAG,CAAC;AACtE;AACA,OAAO,SAASY,QAAQA,CAACnB,GAAG,EAAE;EAC5B,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACK,MAAM,CAACQ,KAAK,CAACb,GAAG,CAAC,IAAIK,MAAM,CAACe,QAAQ,CAACpB,GAAG,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}