{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { DropdownContext } from '../useDropdown/DropdownContext';\nimport { DropdownActionTypes } from '../useDropdown/useDropdown.types';\nimport { useButton } from '../useButton/useButton';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\nimport { extractEventHandlers } from '../utils';\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useMenuButton API](https://mui.com/base-ui/react-menu/hooks-api/#use-menu-button)\n */\nexport function useMenuButton() {\n  let parameters = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    disabled = false,\n    focusableWhenDisabled,\n    rootRef: externalRef\n  } = parameters;\n  const menuContext = React.useContext(DropdownContext);\n  if (menuContext === null) {\n    throw new Error('useMenuButton: no menu context available.');\n  }\n  const {\n    state,\n    dispatch,\n    registerTrigger,\n    popupId\n  } = menuContext;\n  const {\n    getRootProps: getButtonRootProps,\n    rootRef: buttonRootRef,\n    active\n  } = useButton({\n    disabled,\n    focusableWhenDisabled,\n    rootRef: externalRef\n  });\n  const handleRef = useForkRef(buttonRootRef, registerTrigger);\n  const createHandleClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    (_otherHandlers$onClic = otherHandlers.onClick) == null || _otherHandlers$onClic.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    dispatch({\n      type: DropdownActionTypes.toggle,\n      event\n    });\n  };\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {\n      event.preventDefault();\n      dispatch({\n        type: DropdownActionTypes.open,\n        event\n      });\n    }\n  };\n  const getOwnRootProps = function () {\n    let otherHandlers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return {\n      onClick: createHandleClick(otherHandlers),\n      onKeyDown: createHandleKeyDown(otherHandlers)\n    };\n  };\n  const getRootProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const getCombinedProps = combineHooksSlotProps(getButtonRootProps, getOwnRootProps);\n    return _extends({\n      'aria-haspopup': 'menu',\n      'aria-expanded': state.open,\n      'aria-controls': popupId\n    }, externalProps, externalEventHandlers, getCombinedProps(externalEventHandlers), {\n      tabIndex: 0,\n      // this is needed to make the button focused after click in Safari\n      ref: handleRef\n    });\n  };\n  return {\n    active,\n    getRootProps,\n    open: state.open,\n    rootRef: handleRef\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useForkRef", "useForkRef", "DropdownContext", "DropdownActionTypes", "useButton", "combineHooksSlotProps", "extractEventHandlers", "useMenuButton", "parameters", "arguments", "length", "undefined", "disabled", "focusableWhenDisabled", "rootRef", "externalRef", "menuContext", "useContext", "Error", "state", "dispatch", "registerTrigger", "popupId", "getRootProps", "getButtonRootProps", "buttonRootRef", "active", "handleRef", "createHandleClick", "otherHandlers", "event", "_otherHandlers$onClic", "onClick", "call", "defaultMuiPrevented", "type", "toggle", "createHandleKeyDown", "_otherHandlers$onKeyD", "onKeyDown", "key", "preventDefault", "open", "getOwnRootProps", "externalProps", "externalEventHandlers", "getCombinedProps", "tabIndex", "ref"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/useMenuButton/useMenuButton.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { DropdownContext } from '../useDropdown/DropdownContext';\nimport { DropdownActionTypes } from '../useDropdown/useDropdown.types';\nimport { useButton } from '../useButton/useButton';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\nimport { extractEventHandlers } from '../utils';\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useMenuButton API](https://mui.com/base-ui/react-menu/hooks-api/#use-menu-button)\n */\nexport function useMenuButton(parameters = {}) {\n  const {\n    disabled = false,\n    focusableWhenDisabled,\n    rootRef: externalRef\n  } = parameters;\n  const menuContext = React.useContext(DropdownContext);\n  if (menuContext === null) {\n    throw new Error('useMenuButton: no menu context available.');\n  }\n  const {\n    state,\n    dispatch,\n    registerTrigger,\n    popupId\n  } = menuContext;\n  const {\n    getRootProps: getButtonRootProps,\n    rootRef: buttonRootRef,\n    active\n  } = useButton({\n    disabled,\n    focusableWhenDisabled,\n    rootRef: externalRef\n  });\n  const handleRef = useForkRef(buttonRootRef, registerTrigger);\n  const createHandleClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    (_otherHandlers$onClic = otherHandlers.onClick) == null || _otherHandlers$onClic.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    dispatch({\n      type: DropdownActionTypes.toggle,\n      event\n    });\n  };\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {\n      event.preventDefault();\n      dispatch({\n        type: DropdownActionTypes.open,\n        event\n      });\n    }\n  };\n  const getOwnRootProps = (otherHandlers = {}) => ({\n    onClick: createHandleClick(otherHandlers),\n    onKeyDown: createHandleKeyDown(otherHandlers)\n  });\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const getCombinedProps = combineHooksSlotProps(getButtonRootProps, getOwnRootProps);\n    return _extends({\n      'aria-haspopup': 'menu',\n      'aria-expanded': state.open,\n      'aria-controls': popupId\n    }, externalProps, externalEventHandlers, getCombinedProps(externalEventHandlers), {\n      tabIndex: 0,\n      // this is needed to make the button focused after click in Safari\n      ref: handleRef\n    });\n  };\n  return {\n    active,\n    getRootProps,\n    open: state.open,\n    rootRef: handleRef\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,oBAAoB,QAAQ,UAAU;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAAA,EAAkB;EAAA,IAAjBC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC3C,MAAM;IACJG,QAAQ,GAAG,KAAK;IAChBC,qBAAqB;IACrBC,OAAO,EAAEC;EACX,CAAC,GAAGP,UAAU;EACd,MAAMQ,WAAW,GAAGjB,KAAK,CAACkB,UAAU,CAACf,eAAe,CAAC;EACrD,IAAIc,WAAW,KAAK,IAAI,EAAE;IACxB,MAAM,IAAIE,KAAK,CAAC,2CAA2C,CAAC;EAC9D;EACA,MAAM;IACJC,KAAK;IACLC,QAAQ;IACRC,eAAe;IACfC;EACF,CAAC,GAAGN,WAAW;EACf,MAAM;IACJO,YAAY,EAAEC,kBAAkB;IAChCV,OAAO,EAAEW,aAAa;IACtBC;EACF,CAAC,GAAGtB,SAAS,CAAC;IACZQ,QAAQ;IACRC,qBAAqB;IACrBC,OAAO,EAAEC;EACX,CAAC,CAAC;EACF,MAAMY,SAAS,GAAG1B,UAAU,CAACwB,aAAa,EAAEJ,eAAe,CAAC;EAC5D,MAAMO,iBAAiB,GAAGC,aAAa,IAAIC,KAAK,IAAI;IAClD,IAAIC,qBAAqB;IACzB,CAACA,qBAAqB,GAAGF,aAAa,CAACG,OAAO,KAAK,IAAI,IAAID,qBAAqB,CAACE,IAAI,CAACJ,aAAa,EAAEC,KAAK,CAAC;IAC3G,IAAIA,KAAK,CAACI,mBAAmB,EAAE;MAC7B;IACF;IACAd,QAAQ,CAAC;MACPe,IAAI,EAAEhC,mBAAmB,CAACiC,MAAM;MAChCN;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAMO,mBAAmB,GAAGR,aAAa,IAAIC,KAAK,IAAI;IACpD,IAAIQ,qBAAqB;IACzB,CAACA,qBAAqB,GAAGT,aAAa,CAACU,SAAS,KAAK,IAAI,IAAID,qBAAqB,CAACL,IAAI,CAACJ,aAAa,EAAEC,KAAK,CAAC;IAC7G,IAAIA,KAAK,CAACI,mBAAmB,EAAE;MAC7B;IACF;IACA,IAAIJ,KAAK,CAACU,GAAG,KAAK,WAAW,IAAIV,KAAK,CAACU,GAAG,KAAK,SAAS,EAAE;MACxDV,KAAK,CAACW,cAAc,CAAC,CAAC;MACtBrB,QAAQ,CAAC;QACPe,IAAI,EAAEhC,mBAAmB,CAACuC,IAAI;QAC9BZ;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAMa,eAAe,GAAG,SAAAA,CAAA;IAAA,IAACd,aAAa,GAAApB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,OAAM;MAC/CuB,OAAO,EAAEJ,iBAAiB,CAACC,aAAa,CAAC;MACzCU,SAAS,EAAEF,mBAAmB,CAACR,aAAa;IAC9C,CAAC;EAAA,CAAC;EACF,MAAMN,YAAY,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBqB,aAAa,GAAAnC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACtC,MAAMoC,qBAAqB,GAAGvC,oBAAoB,CAACsC,aAAa,CAAC;IACjE,MAAME,gBAAgB,GAAGzC,qBAAqB,CAACmB,kBAAkB,EAAEmB,eAAe,CAAC;IACnF,OAAO7C,QAAQ,CAAC;MACd,eAAe,EAAE,MAAM;MACvB,eAAe,EAAEqB,KAAK,CAACuB,IAAI;MAC3B,eAAe,EAAEpB;IACnB,CAAC,EAAEsB,aAAa,EAAEC,qBAAqB,EAAEC,gBAAgB,CAACD,qBAAqB,CAAC,EAAE;MAChFE,QAAQ,EAAE,CAAC;MACX;MACAC,GAAG,EAAErB;IACP,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLD,MAAM;IACNH,YAAY;IACZmB,IAAI,EAAEvB,KAAK,CAACuB,IAAI;IAChB5B,OAAO,EAAEa;EACX,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}