{"ast": null, "code": "import { createIsAfterIgnoreDatePart } from '../../utils/time-utils';\nimport { useValidation } from './useValidation';\nexport const validateTime = _ref => {\n  let {\n    adapter,\n    value,\n    props\n  } = _ref;\n  const {\n    minTime,\n    maxTime,\n    minutesStep,\n    shouldDisableTime,\n    disableIgnoringDatePartForTimeValidation\n  } = props;\n  const date = adapter.utils.date(value);\n  const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, adapter.utils);\n  if (value === null) {\n    return null;\n  }\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(minTime && isAfter(minTime, date)):\n      return 'minTime';\n    case Boolean(maxTime && isAfter(date, maxTime)):\n      return 'maxTime';\n    case Boolean(shouldDisableTime && shouldDisableTime(adapter.utils.getHours(date), 'hours')):\n      return 'shouldDisableTime-hours';\n    case Boolean(shouldDisableTime && shouldDisableTime(adapter.utils.getMinutes(date), 'minutes')):\n      return 'shouldDisableTime-minutes';\n    case Boolean(shouldDisableTime && shouldDisableTime(adapter.utils.getSeconds(date), 'seconds')):\n      return 'shouldDisableTime-seconds';\n    case Boolean(minutesStep && adapter.utils.getMinutes(date) % minutesStep !== 0):\n      return 'minutesStep';\n    default:\n      return null;\n  }\n};\nconst isSameTimeError = (a, b) => a === b;\nexport const useTimeValidation = props => useValidation(props, validateTime, isSameTimeError);", "map": {"version": 3, "names": ["createIsAfterIgnoreDatePart", "useValidation", "validateTime", "_ref", "adapter", "value", "props", "minTime", "maxTime", "minutesStep", "shouldDisableTime", "disableIgnoringDatePartForTimeValidation", "date", "utils", "isAfter", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "getHours", "getMinutes", "getSeconds", "isSameTimeError", "a", "b", "useTimeValidation"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/hooks/validation/useTimeValidation.js"], "sourcesContent": ["import { createIsAfterIgnoreDatePart } from '../../utils/time-utils';\nimport { useValidation } from './useValidation';\nexport const validateTime = ({\n  adapter,\n  value,\n  props\n}) => {\n  const {\n    minTime,\n    maxTime,\n    minutesStep,\n    shouldDisableTime,\n    disableIgnoringDatePartForTimeValidation\n  } = props;\n  const date = adapter.utils.date(value);\n  const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, adapter.utils);\n\n  if (value === null) {\n    return null;\n  }\n\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n\n    case Boolean(minTime && isAfter(minTime, date)):\n      return 'minTime';\n\n    case Boolean(maxTime && isAfter(date, maxTime)):\n      return 'maxTime';\n\n    case Boolean(shouldDisableTime && shouldDisableTime(adapter.utils.getHours(date), 'hours')):\n      return 'shouldDisableTime-hours';\n\n    case Boolean(shouldDisableTime && shouldDisableTime(adapter.utils.getMinutes(date), 'minutes')):\n      return 'shouldDisableTime-minutes';\n\n    case Boolean(shouldDisableTime && shouldDisableTime(adapter.utils.getSeconds(date), 'seconds')):\n      return 'shouldDisableTime-seconds';\n\n    case Boolean(minutesStep && adapter.utils.getMinutes(date) % minutesStep !== 0):\n      return 'minutesStep';\n\n    default:\n      return null;\n  }\n};\n\nconst isSameTimeError = (a, b) => a === b;\n\nexport const useTimeValidation = props => useValidation(props, validateTime, isSameTimeError);"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,OAAO,MAAMC,YAAY,GAAGC,IAAA,IAItB;EAAA,IAJuB;IAC3BC,OAAO;IACPC,KAAK;IACLC;EACF,CAAC,GAAAH,IAAA;EACC,MAAM;IACJI,OAAO;IACPC,OAAO;IACPC,WAAW;IACXC,iBAAiB;IACjBC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,IAAI,GAAGR,OAAO,CAACS,KAAK,CAACD,IAAI,CAACP,KAAK,CAAC;EACtC,MAAMS,OAAO,GAAGd,2BAA2B,CAACW,wCAAwC,EAAEP,OAAO,CAACS,KAAK,CAAC;EAEpG,IAAIR,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,IAAI;EACb;EAEA,QAAQ,IAAI;IACV,KAAK,CAACD,OAAO,CAACS,KAAK,CAACE,OAAO,CAACV,KAAK,CAAC;MAChC,OAAO,aAAa;IAEtB,KAAKW,OAAO,CAACT,OAAO,IAAIO,OAAO,CAACP,OAAO,EAAEK,IAAI,CAAC,CAAC;MAC7C,OAAO,SAAS;IAElB,KAAKI,OAAO,CAACR,OAAO,IAAIM,OAAO,CAACF,IAAI,EAAEJ,OAAO,CAAC,CAAC;MAC7C,OAAO,SAAS;IAElB,KAAKQ,OAAO,CAACN,iBAAiB,IAAIA,iBAAiB,CAACN,OAAO,CAACS,KAAK,CAACI,QAAQ,CAACL,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;MACzF,OAAO,yBAAyB;IAElC,KAAKI,OAAO,CAACN,iBAAiB,IAAIA,iBAAiB,CAACN,OAAO,CAACS,KAAK,CAACK,UAAU,CAACN,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC;MAC7F,OAAO,2BAA2B;IAEpC,KAAKI,OAAO,CAACN,iBAAiB,IAAIA,iBAAiB,CAACN,OAAO,CAACS,KAAK,CAACM,UAAU,CAACP,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC;MAC7F,OAAO,2BAA2B;IAEpC,KAAKI,OAAO,CAACP,WAAW,IAAIL,OAAO,CAACS,KAAK,CAACK,UAAU,CAACN,IAAI,CAAC,GAAGH,WAAW,KAAK,CAAC,CAAC;MAC7E,OAAO,aAAa;IAEtB;MACE,OAAO,IAAI;EACf;AACF,CAAC;AAED,MAAMW,eAAe,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC;AAEzC,OAAO,MAAMC,iBAAiB,GAAGjB,KAAK,IAAIL,aAAa,CAACK,KAAK,EAAEJ,YAAY,EAAEkB,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}