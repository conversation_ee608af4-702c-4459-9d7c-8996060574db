{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\pages\\\\AuthorityManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Box, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button, Typography, Alert, Snackbar, CircularProgress, TextField, InputAdornment, Paper, Chip, FormControl, InputLabel, Select, MenuItem, Grid, Switch } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport axios from '../utils/axiosConfig';\nimport config from '../config';\nimport { useAuth } from '../contexts/AuthContext';\n\n// Memoized Employee Row Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EmployeeRow = /*#__PURE__*/React.memo(_c = ({\n  employee,\n  onPermissionChange,\n  isAdmin\n}) => {\n  var _employee$DeptName, _employee$DeptName2, _employee$DeptName3;\n  return /*#__PURE__*/_jsxDEV(TableRow, {\n    hover: true,\n    sx: {\n      '&:nth-of-type(odd)': {\n        bgcolor: 'action.hover'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n      children: /*#__PURE__*/_jsxDEV(Chip, {\n        label: employee.EmpCode,\n        size: \"small\",\n        color: \"primary\",\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n      children: employee.EmpName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n      children: /*#__PURE__*/_jsxDEV(Chip, {\n        label: employee.DeptName,\n        size: \"small\",\n        color: \"secondary\",\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n      align: \"center\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-start',\n          gap: 3,\n          flexWrap: 'nowrap',\n          minWidth: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            minWidth: '100px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Assign:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            size: \"small\",\n            checked: employee.CanAssign || false,\n            onChange: () => onPermissionChange(employee.EmpCode, 'CanAssign'),\n            disabled: isAdmin || ((_employee$DeptName = employee.DeptName) === null || _employee$DeptName === void 0 ? void 0 : _employee$DeptName.toLowerCase().includes('admin')),\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            minWidth: '100px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Update:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            size: \"small\",\n            checked: employee.CanUpdateStatus || false,\n            onChange: () => onPermissionChange(employee.EmpCode, 'CanUpdateStatus'),\n            disabled: isAdmin || ((_employee$DeptName2 = employee.DeptName) === null || _employee$DeptName2 === void 0 ? void 0 : _employee$DeptName2.toLowerCase().includes('admin')),\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            minWidth: '120px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Dashboard:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            size: \"small\",\n            checked: employee.CanViewDashboard || false,\n            onChange: () => onPermissionChange(employee.EmpCode, 'CanViewDashboard'),\n            disabled: isAdmin || ((_employee$DeptName3 = employee.DeptName) === null || _employee$DeptName3 === void 0 ? void 0 : _employee$DeptName3.toLowerCase().includes('admin')),\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n});\n_c2 = EmployeeRow;\nconst AuthorityManagement = () => {\n  _s();\n  const {\n    refreshUserPermissions\n  } = useAuth();\n  const [employees, setEmployees] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [message, setMessage] = useState({\n    text: '',\n    type: 'info'\n  });\n  const [showMessage, setShowMessage] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [hasChanges, setHasChanges] = useState(false);\n  const [departmentFilter, setDepartmentFilter] = useState('all');\n\n  // Memoize departments list\n  const departments = useMemo(() => {\n    const uniqueDepartments = [...new Set(employees.map(emp => emp.DeptName))];\n    return uniqueDepartments.sort();\n  }, [employees]);\n\n  // Memoize filtered employees\n  const filteredEmployees = useMemo(() => {\n    return employees.filter(emp => {\n      var _emp$EmpName, _emp$EmpCode, _emp$DeptName;\n      const matchesSearch = (((_emp$EmpName = emp.EmpName) === null || _emp$EmpName === void 0 ? void 0 : _emp$EmpName.toLowerCase()) || '').includes(searchTerm.toLowerCase()) || (((_emp$EmpCode = emp.EmpCode) === null || _emp$EmpCode === void 0 ? void 0 : _emp$EmpCode.toLowerCase()) || '').includes(searchTerm.toLowerCase()) || (((_emp$DeptName = emp.DeptName) === null || _emp$DeptName === void 0 ? void 0 : _emp$DeptName.toLowerCase()) || '').includes(searchTerm.toLowerCase());\n      const matchesDepartment = departmentFilter === 'all' || emp.DeptName === departmentFilter;\n      return matchesSearch && matchesDepartment;\n    }).sort((a, b) => {\n      if (a.DeptName !== b.DeptName) {\n        return a.DeptName.localeCompare(b.DeptName);\n      }\n      return a.EmpName.localeCompare(b.EmpName);\n    });\n  }, [employees, searchTerm, departmentFilter]);\n  const fetchEmployees = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/employees/permissions');\n\n      // Filter out invalid employees\n      const validEmployees = response.data.filter(emp => {\n        var _emp$EmpName2;\n        const cleanName = ((_emp$EmpName2 = emp.EmpName) === null || _emp$EmpName2 === void 0 ? void 0 : _emp$EmpName2.trim()) || '';\n        return cleanName !== '' && !/^[-]+$/.test(cleanName);\n      });\n      setEmployees(validEmployees);\n      setHasChanges(false);\n    } catch (error) {\n      console.error('Error fetching employees:', error);\n      showNotification('Failed to fetch employees', 'error');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchEmployees();\n  }, [fetchEmployees]);\n  const handlePermissionChange = useCallback((empCode, permission) => {\n    setEmployees(prevEmployees => prevEmployees.map(emp => emp.EmpCode === empCode ? {\n      ...emp,\n      [permission]: !emp[permission]\n    } : emp));\n    setHasChanges(true);\n  }, []);\n  const handleSave = useCallback(async () => {\n    try {\n      setLoading(true);\n      await axios.post('/api/employees/permissions', {\n        permissions: employees\n      }, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      // Refresh user permissions for all logged-in users\n      await refreshUserPermissions();\n      showNotification('Permissions updated successfully. Users may need to refresh their page to see changes.', 'success');\n      setHasChanges(false);\n    } catch (error) {\n      console.error('Error updating permissions:', error);\n      showNotification('Failed to update permissions', 'error');\n    } finally {\n      setLoading(false);\n    }\n  }, [employees, refreshUserPermissions]);\n  const showNotification = useCallback((text, type) => {\n    setMessage({\n      text,\n      type\n    });\n    setShowMessage(true);\n  }, []);\n  const handleSearchChange = useCallback(e => {\n    setSearchTerm(e.target.value);\n  }, []);\n  const handleDepartmentChange = useCallback(e => {\n    setDepartmentFilter(e.target.value);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      elevation: 3,\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"h2\",\n              sx: {\n                color: 'primary.main',\n                fontWeight: 'bold'\n              },\n              children: \"Authority Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 48\n                }, this),\n                onClick: fetchEmployees,\n                sx: {\n                  mr: 2\n                },\n                disabled: loading,\n                children: \"Refresh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 48\n                }, this),\n                onClick: handleSave,\n                disabled: !hasChanges || loading,\n                color: \"primary\",\n                children: \"Save Changes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                variant: \"outlined\",\n                placeholder: \"Search by employee code, name or department...\",\n                value: searchTerm,\n                onChange: handleSearchChange,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                      color: \"action\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 45\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Department\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: departmentFilter,\n                  onChange: handleDepartmentChange,\n                  label: \"Department\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"all\",\n                    children: \"All Departments\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 41\n                  }, this), departments.map(dept => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: dept,\n                    children: dept\n                  }, dept, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 45\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 21\n        }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            p: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          elevation: 0,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: 'primary.light'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Employee Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Department\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: \"Permissions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: filteredEmployees.map(employee => {\n                var _employee$DeptName4;\n                return /*#__PURE__*/_jsxDEV(EmployeeRow, {\n                  employee: employee,\n                  onPermissionChange: handlePermissionChange,\n                  isAdmin: (_employee$DeptName4 = employee.DeptName) === null || _employee$DeptName4 === void 0 ? void 0 : _employee$DeptName4.toLowerCase().includes('admin')\n                }, employee.EmpCode, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 41\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showMessage,\n      autoHideDuration: 6000,\n      onClose: () => setShowMessage(false),\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: message.type,\n        onClose: () => setShowMessage(false),\n        sx: {\n          width: '100%'\n        },\n        variant: \"filled\",\n        children: message.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 9\n  }, this);\n};\n_s(AuthorityManagement, \"1zWk8y1Xu5l3DxriFiyW8rgqYIs=\", false, function () {\n  return [useAuth];\n});\n_c3 = AuthorityManagement;\nexport default AuthorityManagement;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"EmployeeRow$React.memo\");\n$RefreshReg$(_c2, \"EmployeeRow\");\n$RefreshReg$(_c3, \"AuthorityManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Snackbar", "CircularProgress", "TextField", "InputAdornment", "Paper", "Chip", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "Switch", "Search", "SearchIcon", "Save", "SaveIcon", "Refresh", "RefreshIcon", "axios", "config", "useAuth", "jsxDEV", "_jsxDEV", "EmployeeRow", "memo", "_c", "employee", "onPermissionChange", "isAdmin", "_employee$DeptName", "_employee$DeptName2", "_employee$DeptName3", "hover", "sx", "bgcolor", "children", "label", "EmpCode", "size", "color", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "EmpName", "DeptName", "align", "display", "justifyContent", "gap", "flexWrap", "min<PERSON><PERSON><PERSON>", "alignItems", "checked", "CanAssign", "onChange", "disabled", "toLowerCase", "includes", "CanUpdateStatus", "CanViewDashboard", "_c2", "AuthorityManagement", "_s", "refreshUserPermissions", "employees", "setEmployees", "searchTerm", "setSearchTerm", "message", "setMessage", "text", "type", "showMessage", "setShowMessage", "loading", "setLoading", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "setDepartmentFilter", "departments", "uniqueDepartments", "Set", "map", "emp", "sort", "filteredEmployees", "filter", "_emp$EmpName", "_emp$EmpCode", "_emp$DeptName", "matchesSearch", "matchesDepartment", "a", "b", "localeCompare", "fetchEmployees", "response", "get", "validEmployees", "data", "_emp$EmpName2", "cleanName", "trim", "test", "error", "console", "showNotification", "handlePermissionChange", "empCode", "permission", "prevEmployees", "handleSave", "post", "permissions", "headers", "localStorage", "getItem", "handleSearchChange", "e", "target", "value", "handleDepartmentChange", "p", "elevation", "mb", "component", "fontWeight", "startIcon", "onClick", "mr", "container", "spacing", "item", "xs", "md", "fullWidth", "placeholder", "InputProps", "startAdornment", "position", "dept", "_employee$DeptName4", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/pages/AuthorityManagement.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport {\r\n    Box,\r\n    Card,\r\n    CardContent,\r\n    Table,\r\n    TableBody,\r\n    TableCell,\r\n    TableContainer,\r\n    TableHead,\r\n    TableRow,\r\n    Button,\r\n    Typography,\r\n    Alert,\r\n    Snackbar,\r\n    CircularProgress,\r\n    TextField,\r\n    InputAdornment,\r\n    Paper,\r\n    Chip,\r\n    FormControl,\r\n    InputLabel,\r\n    Select,\r\n    MenuItem,\r\n    Grid,\r\n    Switch\r\n} from '@mui/material';\r\nimport {\r\n    Search as SearchIcon,\r\n    Save as SaveIcon,\r\n    Refresh as RefreshIcon\r\n} from '@mui/icons-material';\r\nimport axios from '../utils/axiosConfig';\r\nimport config from '../config';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\n// Memoized Employee Row Component\r\nconst EmployeeRow = React.memo(({ employee, onPermissionChange, isAdmin }) => (\r\n    <TableRow\r\n        hover\r\n        sx={{\r\n            '&:nth-of-type(odd)': {\r\n                bgcolor: 'action.hover',\r\n            }\r\n        }}\r\n    >\r\n        <TableCell>\r\n            <Chip \r\n                label={employee.EmpCode}\r\n                size=\"small\"\r\n                color=\"primary\"\r\n                variant=\"outlined\"\r\n            />\r\n        </TableCell>\r\n        <TableCell>{employee.EmpName}</TableCell>\r\n        <TableCell>\r\n            <Chip \r\n                label={employee.DeptName}\r\n                size=\"small\"\r\n                color=\"secondary\"\r\n                variant=\"outlined\"\r\n            />\r\n        </TableCell>\r\n        <TableCell align=\"center\">\r\n            <Box sx={{ \r\n                display: 'flex', \r\n                justifyContent: 'flex-start', \r\n                gap: 3,\r\n                flexWrap: 'nowrap',\r\n                minWidth: '400px'\r\n            }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: '100px' }}>\r\n                    <Typography variant=\"body2\">Assign:</Typography>\r\n                    <Switch\r\n                        size=\"small\"\r\n                        checked={employee.CanAssign || false}\r\n                        onChange={() => onPermissionChange(employee.EmpCode, 'CanAssign')}\r\n                        disabled={isAdmin || employee.DeptName?.toLowerCase().includes('admin')}\r\n                        color=\"primary\"\r\n                    />\r\n                </Box>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: '100px' }}>\r\n                    <Typography variant=\"body2\">Update:</Typography>\r\n                    <Switch\r\n                        size=\"small\"\r\n                        checked={employee.CanUpdateStatus || false}\r\n                        onChange={() => onPermissionChange(employee.EmpCode, 'CanUpdateStatus')}\r\n                        disabled={isAdmin || employee.DeptName?.toLowerCase().includes('admin')}\r\n                        color=\"primary\"\r\n                    />\r\n                </Box>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: '120px' }}>\r\n                    <Typography variant=\"body2\">Dashboard:</Typography>\r\n                    <Switch\r\n                        size=\"small\"\r\n                        checked={employee.CanViewDashboard || false}\r\n                        onChange={() => onPermissionChange(employee.EmpCode, 'CanViewDashboard')}\r\n                        disabled={isAdmin || employee.DeptName?.toLowerCase().includes('admin')}\r\n                        color=\"primary\"\r\n                    />\r\n                </Box>\r\n            </Box>\r\n        </TableCell>\r\n    </TableRow>\r\n));\r\n\r\nconst AuthorityManagement = () => {\r\n    const { refreshUserPermissions } = useAuth();\r\n    const [employees, setEmployees] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [message, setMessage] = useState({ text: '', type: 'info' });\r\n    const [showMessage, setShowMessage] = useState(false);\r\n    const [loading, setLoading] = useState(true);\r\n    const [hasChanges, setHasChanges] = useState(false);\r\n    const [departmentFilter, setDepartmentFilter] = useState('all');\r\n\r\n    // Memoize departments list\r\n    const departments = useMemo(() => {\r\n        const uniqueDepartments = [...new Set(employees.map(emp => emp.DeptName))];\r\n        return uniqueDepartments.sort();\r\n    }, [employees]);\r\n\r\n    // Memoize filtered employees\r\n    const filteredEmployees = useMemo(() => {\r\n        return employees.filter(emp => {\r\n            const matchesSearch = \r\n                (emp.EmpName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||\r\n                (emp.EmpCode?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||\r\n                (emp.DeptName?.toLowerCase() || '').includes(searchTerm.toLowerCase());\r\n            \r\n            const matchesDepartment = \r\n                departmentFilter === 'all' || emp.DeptName === departmentFilter;\r\n\r\n            return matchesSearch && matchesDepartment;\r\n        }).sort((a, b) => {\r\n            if (a.DeptName !== b.DeptName) {\r\n                return a.DeptName.localeCompare(b.DeptName);\r\n            }\r\n            return a.EmpName.localeCompare(b.EmpName);\r\n        });\r\n    }, [employees, searchTerm, departmentFilter]);\r\n\r\n    const fetchEmployees = useCallback(async () => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await axios.get('/api/employees/permissions');\r\n            \r\n            // Filter out invalid employees\r\n            const validEmployees = response.data.filter(emp => {\r\n                const cleanName = emp.EmpName?.trim() || '';\r\n                return cleanName !== '' && !/^[-]+$/.test(cleanName);\r\n            });\r\n            \r\n            setEmployees(validEmployees);\r\n            setHasChanges(false);\r\n        } catch (error) {\r\n            console.error('Error fetching employees:', error);\r\n            showNotification('Failed to fetch employees', 'error');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        fetchEmployees();\r\n    }, [fetchEmployees]);\r\n\r\n    const handlePermissionChange = useCallback((empCode, permission) => {\r\n        setEmployees(prevEmployees => \r\n            prevEmployees.map(emp => \r\n                emp.EmpCode === empCode \r\n                    ? { ...emp, [permission]: !emp[permission] }\r\n                    : emp\r\n            )\r\n        );\r\n        setHasChanges(true);\r\n    }, []);\r\n\r\n    const handleSave = useCallback(async () => {\r\n        try {\r\n            setLoading(true);\r\n            await axios.post('/api/employees/permissions',\r\n                { permissions: employees },\r\n                {\r\n                    headers: {\r\n                        'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n                    }\r\n                }\r\n            );\r\n\r\n            // Refresh user permissions for all logged-in users\r\n            await refreshUserPermissions();\r\n\r\n            showNotification('Permissions updated successfully. Users may need to refresh their page to see changes.', 'success');\r\n            setHasChanges(false);\r\n        } catch (error) {\r\n            console.error('Error updating permissions:', error);\r\n            showNotification('Failed to update permissions', 'error');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [employees, refreshUserPermissions]);\r\n\r\n    const showNotification = useCallback((text, type) => {\r\n        setMessage({ text, type });\r\n        setShowMessage(true);\r\n    }, []);\r\n\r\n    const handleSearchChange = useCallback((e) => {\r\n        setSearchTerm(e.target.value);\r\n    }, []);\r\n\r\n    const handleDepartmentChange = useCallback((e) => {\r\n        setDepartmentFilter(e.target.value);\r\n    }, []);\r\n\r\n    return (\r\n        <Box sx={{ p: 3 }}>\r\n            <Card elevation={3}>\r\n                <CardContent>\r\n                    <Box sx={{ mb: 4 }}>\r\n                        <Box sx={{ \r\n                            display: 'flex', \r\n                            justifyContent: 'space-between', \r\n                            alignItems: 'center',\r\n                            mb: 3 \r\n                        }}>\r\n                            <Typography variant=\"h5\" component=\"h2\" \r\n                                sx={{ \r\n                                    color: 'primary.main',\r\n                                    fontWeight: 'bold'\r\n                                }}\r\n                            >\r\n                                Authority Management\r\n                            </Typography>\r\n                            <Box>\r\n                                <Button\r\n                                    startIcon={<RefreshIcon />}\r\n                                    onClick={fetchEmployees}\r\n                                    sx={{ mr: 2 }}\r\n                                    disabled={loading}\r\n                                >\r\n                                    Refresh\r\n                                </Button>\r\n                                <Button\r\n                                    variant=\"contained\"\r\n                                    startIcon={<SaveIcon />}\r\n                                    onClick={handleSave}\r\n                                    disabled={!hasChanges || loading}\r\n                                    color=\"primary\"\r\n                                >\r\n                                    Save Changes\r\n                                </Button>\r\n                            </Box>\r\n                        </Box>\r\n\r\n                        <Grid container spacing={2}>\r\n                            <Grid item xs={12} md={6}>\r\n                                <TextField\r\n                                    fullWidth\r\n                                    variant=\"outlined\"\r\n                                    placeholder=\"Search by employee code, name or department...\"\r\n                                    value={searchTerm}\r\n                                    onChange={handleSearchChange}\r\n                                    InputProps={{\r\n                                        startAdornment: (\r\n                                            <InputAdornment position=\"start\">\r\n                                                <SearchIcon color=\"action\" />\r\n                                            </InputAdornment>\r\n                                        )\r\n                                    }}\r\n                                />\r\n                            </Grid>\r\n                            <Grid item xs={12} md={6}>\r\n                                <FormControl fullWidth>\r\n                                    <InputLabel>Department</InputLabel>\r\n                                    <Select\r\n                                        value={departmentFilter}\r\n                                        onChange={handleDepartmentChange}\r\n                                        label=\"Department\"\r\n                                    >\r\n                                        <MenuItem value=\"all\">All Departments</MenuItem>\r\n                                        {departments.map(dept => (\r\n                                            <MenuItem key={dept} value={dept}>\r\n                                                {dept}\r\n                                            </MenuItem>\r\n                                        ))}\r\n                                    </Select>\r\n                                </FormControl>\r\n                            </Grid>\r\n                        </Grid>\r\n                    </Box>\r\n\r\n                    {loading ? (\r\n                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>\r\n                            <CircularProgress />\r\n                        </Box>\r\n                    ) : (\r\n                        <TableContainer component={Paper} elevation={0}>\r\n                            <Table>\r\n                                <TableHead>\r\n                                    <TableRow sx={{ bgcolor: 'primary.light' }}>\r\n                                        <TableCell>Employee Code</TableCell>\r\n                                        <TableCell>Name</TableCell>\r\n                                        <TableCell>Department</TableCell>\r\n                                        <TableCell align=\"center\">Permissions</TableCell>\r\n                                    </TableRow>\r\n                                </TableHead>\r\n                                <TableBody>\r\n                                    {filteredEmployees.map((employee) => (\r\n                                        <EmployeeRow\r\n                                            key={employee.EmpCode}\r\n                                            employee={employee}\r\n                                            onPermissionChange={handlePermissionChange}\r\n                                            isAdmin={employee.DeptName?.toLowerCase().includes('admin')}\r\n                                        />\r\n                                    ))}\r\n                                </TableBody>\r\n                            </Table>\r\n                        </TableContainer>\r\n                    )}\r\n                </CardContent>\r\n            </Card>\r\n\r\n            <Snackbar\r\n                open={showMessage}\r\n                autoHideDuration={6000}\r\n                onClose={() => setShowMessage(false)}\r\n                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\r\n            >\r\n                <Alert \r\n                    severity={message.type} \r\n                    onClose={() => setShowMessage(false)}\r\n                    sx={{ width: '100%' }}\r\n                    variant=\"filled\"\r\n                >\r\n                    {message.text}\r\n                </Alert>\r\n            </Snackbar>\r\n        </Box>\r\n    );\r\n};\r\n\r\nexport default AuthorityManagement; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SACIC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,gBAAgB,EAChBC,SAAS,EACTC,cAAc,EACdC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,MAAM,QACH,eAAe;AACtB,SACIC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACnB,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,OAAO,QAAQ,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGxC,KAAK,CAACyC,IAAI,CAAAC,EAAA,GAACA,CAAC;EAAEC,QAAQ;EAAEC,kBAAkB;EAAEC;AAAQ,CAAC;EAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;EAAA,oBACrET,OAAA,CAAC1B,QAAQ;IACLoC,KAAK;IACLC,EAAE,EAAE;MACA,oBAAoB,EAAE;QAClBC,OAAO,EAAE;MACb;IACJ,CAAE;IAAAC,QAAA,gBAEFb,OAAA,CAAC7B,SAAS;MAAA0C,QAAA,eACNb,OAAA,CAACjB,IAAI;QACD+B,KAAK,EAAEV,QAAQ,CAACW,OAAQ;QACxBC,IAAI,EAAC,OAAO;QACZC,KAAK,EAAC,SAAS;QACfC,OAAO,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACZtB,OAAA,CAAC7B,SAAS;MAAA0C,QAAA,EAAET,QAAQ,CAACmB;IAAO;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACzCtB,OAAA,CAAC7B,SAAS;MAAA0C,QAAA,eACNb,OAAA,CAACjB,IAAI;QACD+B,KAAK,EAAEV,QAAQ,CAACoB,QAAS;QACzBR,IAAI,EAAC,OAAO;QACZC,KAAK,EAAC,WAAW;QACjBC,OAAO,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACZtB,OAAA,CAAC7B,SAAS;MAACsD,KAAK,EAAC,QAAQ;MAAAZ,QAAA,eACrBb,OAAA,CAAClC,GAAG;QAAC6C,EAAE,EAAE;UACLe,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,YAAY;UAC5BC,GAAG,EAAE,CAAC;UACNC,QAAQ,EAAE,QAAQ;UAClBC,QAAQ,EAAE;QACd,CAAE;QAAAjB,QAAA,gBACEb,OAAA,CAAClC,GAAG;UAAC6C,EAAE,EAAE;YAAEe,OAAO,EAAE,MAAM;YAAEK,UAAU,EAAE,QAAQ;YAAEH,GAAG,EAAE,CAAC;YAAEE,QAAQ,EAAE;UAAQ,CAAE;UAAAjB,QAAA,gBAC1Eb,OAAA,CAACxB,UAAU;YAAC0C,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAO;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChDtB,OAAA,CAACX,MAAM;YACH2B,IAAI,EAAC,OAAO;YACZgB,OAAO,EAAE5B,QAAQ,CAAC6B,SAAS,IAAI,KAAM;YACrCC,QAAQ,EAAEA,CAAA,KAAM7B,kBAAkB,CAACD,QAAQ,CAACW,OAAO,EAAE,WAAW,CAAE;YAClEoB,QAAQ,EAAE7B,OAAO,MAAAC,kBAAA,GAAIH,QAAQ,CAACoB,QAAQ,cAAAjB,kBAAA,uBAAjBA,kBAAA,CAAmB6B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACxEpB,KAAK,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtB,OAAA,CAAClC,GAAG;UAAC6C,EAAE,EAAE;YAAEe,OAAO,EAAE,MAAM;YAAEK,UAAU,EAAE,QAAQ;YAAEH,GAAG,EAAE,CAAC;YAAEE,QAAQ,EAAE;UAAQ,CAAE;UAAAjB,QAAA,gBAC1Eb,OAAA,CAACxB,UAAU;YAAC0C,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAO;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChDtB,OAAA,CAACX,MAAM;YACH2B,IAAI,EAAC,OAAO;YACZgB,OAAO,EAAE5B,QAAQ,CAACkC,eAAe,IAAI,KAAM;YAC3CJ,QAAQ,EAAEA,CAAA,KAAM7B,kBAAkB,CAACD,QAAQ,CAACW,OAAO,EAAE,iBAAiB,CAAE;YACxEoB,QAAQ,EAAE7B,OAAO,MAAAE,mBAAA,GAAIJ,QAAQ,CAACoB,QAAQ,cAAAhB,mBAAA,uBAAjBA,mBAAA,CAAmB4B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACxEpB,KAAK,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtB,OAAA,CAAClC,GAAG;UAAC6C,EAAE,EAAE;YAAEe,OAAO,EAAE,MAAM;YAAEK,UAAU,EAAE,QAAQ;YAAEH,GAAG,EAAE,CAAC;YAAEE,QAAQ,EAAE;UAAQ,CAAE;UAAAjB,QAAA,gBAC1Eb,OAAA,CAACxB,UAAU;YAAC0C,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAU;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnDtB,OAAA,CAACX,MAAM;YACH2B,IAAI,EAAC,OAAO;YACZgB,OAAO,EAAE5B,QAAQ,CAACmC,gBAAgB,IAAI,KAAM;YAC5CL,QAAQ,EAAEA,CAAA,KAAM7B,kBAAkB,CAACD,QAAQ,CAACW,OAAO,EAAE,kBAAkB,CAAE;YACzEoB,QAAQ,EAAE7B,OAAO,MAAAG,mBAAA,GAAIL,QAAQ,CAACoB,QAAQ,cAAAf,mBAAA,uBAAjBA,mBAAA,CAAmB2B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACxEpB,KAAK,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAAA,CACd,CAAC;AAACkB,GAAA,GAnEGvC,WAAW;AAqEjB,MAAMwC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAuB,CAAC,GAAG7C,OAAO,CAAC,CAAC;EAC5C,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsF,OAAO,EAAEC,UAAU,CAAC,GAAGvF,QAAQ,CAAC;IAAEwF,IAAI,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAO,CAAC,CAAC;EAClE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4F,OAAO,EAAEC,UAAU,CAAC,GAAG7F,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8F,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMkG,WAAW,GAAG/F,OAAO,CAAC,MAAM;IAC9B,MAAMgG,iBAAiB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAClB,SAAS,CAACmB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACxC,QAAQ,CAAC,CAAC,CAAC;IAC1E,OAAOqC,iBAAiB,CAACI,IAAI,CAAC,CAAC;EACnC,CAAC,EAAE,CAACrB,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMsB,iBAAiB,GAAGrG,OAAO,CAAC,MAAM;IACpC,OAAO+E,SAAS,CAACuB,MAAM,CAACH,GAAG,IAAI;MAAA,IAAAI,YAAA,EAAAC,YAAA,EAAAC,aAAA;MAC3B,MAAMC,aAAa,GACf,CAAC,EAAAH,YAAA,GAAAJ,GAAG,CAACzC,OAAO,cAAA6C,YAAA,uBAAXA,YAAA,CAAahC,WAAW,CAAC,CAAC,KAAI,EAAE,EAAEC,QAAQ,CAACS,UAAU,CAACV,WAAW,CAAC,CAAC,CAAC,IACrE,CAAC,EAAAiC,YAAA,GAAAL,GAAG,CAACjD,OAAO,cAAAsD,YAAA,uBAAXA,YAAA,CAAajC,WAAW,CAAC,CAAC,KAAI,EAAE,EAAEC,QAAQ,CAACS,UAAU,CAACV,WAAW,CAAC,CAAC,CAAC,IACrE,CAAC,EAAAkC,aAAA,GAAAN,GAAG,CAACxC,QAAQ,cAAA8C,aAAA,uBAAZA,aAAA,CAAclC,WAAW,CAAC,CAAC,KAAI,EAAE,EAAEC,QAAQ,CAACS,UAAU,CAACV,WAAW,CAAC,CAAC,CAAC;MAE1E,MAAMoC,iBAAiB,GACnBd,gBAAgB,KAAK,KAAK,IAAIM,GAAG,CAACxC,QAAQ,KAAKkC,gBAAgB;MAEnE,OAAOa,aAAa,IAAIC,iBAAiB;IAC7C,CAAC,CAAC,CAACP,IAAI,CAAC,CAACQ,CAAC,EAAEC,CAAC,KAAK;MACd,IAAID,CAAC,CAACjD,QAAQ,KAAKkD,CAAC,CAAClD,QAAQ,EAAE;QAC3B,OAAOiD,CAAC,CAACjD,QAAQ,CAACmD,aAAa,CAACD,CAAC,CAAClD,QAAQ,CAAC;MAC/C;MACA,OAAOiD,CAAC,CAAClD,OAAO,CAACoD,aAAa,CAACD,CAAC,CAACnD,OAAO,CAAC;IAC7C,CAAC,CAAC;EACN,CAAC,EAAE,CAACqB,SAAS,EAAEE,UAAU,EAAEY,gBAAgB,CAAC,CAAC;EAE7C,MAAMkB,cAAc,GAAGhH,WAAW,CAAC,YAAY;IAC3C,IAAI;MACA2F,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsB,QAAQ,GAAG,MAAMjF,KAAK,CAACkF,GAAG,CAAC,4BAA4B,CAAC;;MAE9D;MACA,MAAMC,cAAc,GAAGF,QAAQ,CAACG,IAAI,CAACb,MAAM,CAACH,GAAG,IAAI;QAAA,IAAAiB,aAAA;QAC/C,MAAMC,SAAS,GAAG,EAAAD,aAAA,GAAAjB,GAAG,CAACzC,OAAO,cAAA0D,aAAA,uBAAXA,aAAA,CAAaE,IAAI,CAAC,CAAC,KAAI,EAAE;QAC3C,OAAOD,SAAS,KAAK,EAAE,IAAI,CAAC,QAAQ,CAACE,IAAI,CAACF,SAAS,CAAC;MACxD,CAAC,CAAC;MAEFrC,YAAY,CAACkC,cAAc,CAAC;MAC5BtB,aAAa,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDE,gBAAgB,CAAC,2BAA2B,EAAE,OAAO,CAAC;IAC1D,CAAC,SAAS;MACNhC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN5F,SAAS,CAAC,MAAM;IACZiH,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpB,MAAMY,sBAAsB,GAAG5H,WAAW,CAAC,CAAC6H,OAAO,EAAEC,UAAU,KAAK;IAChE7C,YAAY,CAAC8C,aAAa,IACtBA,aAAa,CAAC5B,GAAG,CAACC,GAAG,IACjBA,GAAG,CAACjD,OAAO,KAAK0E,OAAO,GACjB;MAAE,GAAGzB,GAAG;MAAE,CAAC0B,UAAU,GAAG,CAAC1B,GAAG,CAAC0B,UAAU;IAAE,CAAC,GAC1C1B,GACV,CACJ,CAAC;IACDP,aAAa,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmC,UAAU,GAAGhI,WAAW,CAAC,YAAY;IACvC,IAAI;MACA2F,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM3D,KAAK,CAACiG,IAAI,CAAC,4BAA4B,EACzC;QAAEC,WAAW,EAAElD;MAAU,CAAC,EAC1B;QACImD,OAAO,EAAE;UACL,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC5D;MACJ,CACJ,CAAC;;MAED;MACA,MAAMtD,sBAAsB,CAAC,CAAC;MAE9B4C,gBAAgB,CAAC,wFAAwF,EAAE,SAAS,CAAC;MACrH9B,aAAa,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDE,gBAAgB,CAAC,8BAA8B,EAAE,OAAO,CAAC;IAC7D,CAAC,SAAS;MACNhC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,CAACX,SAAS,EAAED,sBAAsB,CAAC,CAAC;EAEvC,MAAM4C,gBAAgB,GAAG3H,WAAW,CAAC,CAACsF,IAAI,EAAEC,IAAI,KAAK;IACjDF,UAAU,CAAC;MAAEC,IAAI;MAAEC;IAAK,CAAC,CAAC;IAC1BE,cAAc,CAAC,IAAI,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6C,kBAAkB,GAAGtI,WAAW,CAAEuI,CAAC,IAAK;IAC1CpD,aAAa,CAACoD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EACjC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,sBAAsB,GAAG1I,WAAW,CAAEuI,CAAC,IAAK;IAC9CxC,mBAAmB,CAACwC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIrG,OAAA,CAAClC,GAAG;IAAC6C,EAAE,EAAE;MAAE4F,CAAC,EAAE;IAAE,CAAE;IAAA1F,QAAA,gBACdb,OAAA,CAACjC,IAAI;MAACyI,SAAS,EAAE,CAAE;MAAA3F,QAAA,eACfb,OAAA,CAAChC,WAAW;QAAA6C,QAAA,gBACRb,OAAA,CAAClC,GAAG;UAAC6C,EAAE,EAAE;YAAE8F,EAAE,EAAE;UAAE,CAAE;UAAA5F,QAAA,gBACfb,OAAA,CAAClC,GAAG;YAAC6C,EAAE,EAAE;cACLe,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BI,UAAU,EAAE,QAAQ;cACpB0E,EAAE,EAAE;YACR,CAAE;YAAA5F,QAAA,gBACEb,OAAA,CAACxB,UAAU;cAAC0C,OAAO,EAAC,IAAI;cAACwF,SAAS,EAAC,IAAI;cACnC/F,EAAE,EAAE;gBACAM,KAAK,EAAE,cAAc;gBACrB0F,UAAU,EAAE;cAChB,CAAE;cAAA9F,QAAA,EACL;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtB,OAAA,CAAClC,GAAG;cAAA+C,QAAA,gBACAb,OAAA,CAACzB,MAAM;gBACHqI,SAAS,eAAE5G,OAAA,CAACL,WAAW;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3BuF,OAAO,EAAEjC,cAAe;gBACxBjE,EAAE,EAAE;kBAAEmG,EAAE,EAAE;gBAAE,CAAE;gBACd3E,QAAQ,EAAEmB,OAAQ;gBAAAzC,QAAA,EACrB;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtB,OAAA,CAACzB,MAAM;gBACH2C,OAAO,EAAC,WAAW;gBACnB0F,SAAS,eAAE5G,OAAA,CAACP,QAAQ;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACxBuF,OAAO,EAAEjB,UAAW;gBACpBzD,QAAQ,EAAE,CAACqB,UAAU,IAAIF,OAAQ;gBACjCrC,KAAK,EAAC,SAAS;gBAAAJ,QAAA,EAClB;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENtB,OAAA,CAACZ,IAAI;YAAC2H,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAnG,QAAA,gBACvBb,OAAA,CAACZ,IAAI;cAAC6H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtG,QAAA,eACrBb,OAAA,CAACpB,SAAS;gBACNwI,SAAS;gBACTlG,OAAO,EAAC,UAAU;gBAClBmG,WAAW,EAAC,gDAAgD;gBAC5DhB,KAAK,EAAEvD,UAAW;gBAClBZ,QAAQ,EAAEgE,kBAAmB;gBAC7BoB,UAAU,EAAE;kBACRC,cAAc,eACVvH,OAAA,CAACnB,cAAc;oBAAC2I,QAAQ,EAAC,OAAO;oBAAA3G,QAAA,eAC5Bb,OAAA,CAACT,UAAU;sBAAC0B,KAAK,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAExB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACPtB,OAAA,CAACZ,IAAI;cAAC6H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtG,QAAA,eACrBb,OAAA,CAAChB,WAAW;gBAACoI,SAAS;gBAAAvG,QAAA,gBAClBb,OAAA,CAACf,UAAU;kBAAA4B,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCtB,OAAA,CAACd,MAAM;kBACHmH,KAAK,EAAE3C,gBAAiB;kBACxBxB,QAAQ,EAAEoE,sBAAuB;kBACjCxF,KAAK,EAAC,YAAY;kBAAAD,QAAA,gBAElBb,OAAA,CAACb,QAAQ;oBAACkH,KAAK,EAAC,KAAK;oBAAAxF,QAAA,EAAC;kBAAe;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EAC/CsC,WAAW,CAACG,GAAG,CAAC0D,IAAI,iBACjBzH,OAAA,CAACb,QAAQ;oBAAYkH,KAAK,EAAEoB,IAAK;oBAAA5G,QAAA,EAC5B4G;kBAAI,GADMA,IAAI;oBAAAtG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAET,CACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELgC,OAAO,gBACJtD,OAAA,CAAClC,GAAG;UAAC6C,EAAE,EAAE;YAAEe,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAE4E,CAAC,EAAE;UAAE,CAAE;UAAA1F,QAAA,eACzDb,OAAA,CAACrB,gBAAgB;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,gBAENtB,OAAA,CAAC5B,cAAc;UAACsI,SAAS,EAAE5H,KAAM;UAAC0H,SAAS,EAAE,CAAE;UAAA3F,QAAA,eAC3Cb,OAAA,CAAC/B,KAAK;YAAA4C,QAAA,gBACFb,OAAA,CAAC3B,SAAS;cAAAwC,QAAA,eACNb,OAAA,CAAC1B,QAAQ;gBAACqC,EAAE,EAAE;kBAAEC,OAAO,EAAE;gBAAgB,CAAE;gBAAAC,QAAA,gBACvCb,OAAA,CAAC7B,SAAS;kBAAA0C,QAAA,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpCtB,OAAA,CAAC7B,SAAS;kBAAA0C,QAAA,EAAC;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC3BtB,OAAA,CAAC7B,SAAS;kBAAA0C,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjCtB,OAAA,CAAC7B,SAAS;kBAACsD,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZtB,OAAA,CAAC9B,SAAS;cAAA2C,QAAA,EACLqD,iBAAiB,CAACH,GAAG,CAAE3D,QAAQ;gBAAA,IAAAsH,mBAAA;gBAAA,oBAC5B1H,OAAA,CAACC,WAAW;kBAERG,QAAQ,EAAEA,QAAS;kBACnBC,kBAAkB,EAAEmF,sBAAuB;kBAC3ClF,OAAO,GAAAoH,mBAAA,GAAEtH,QAAQ,CAACoB,QAAQ,cAAAkG,mBAAA,uBAAjBA,mBAAA,CAAmBtF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO;gBAAE,GAHvDjC,QAAQ,CAACW,OAAO;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIxB,CAAC;cAAA,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACnB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAEPtB,OAAA,CAACtB,QAAQ;MACLiJ,IAAI,EAAEvE,WAAY;MAClBwE,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAMxE,cAAc,CAAC,KAAK,CAAE;MACrCyE,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAnH,QAAA,eAE1Db,OAAA,CAACvB,KAAK;QACFwJ,QAAQ,EAAEjF,OAAO,CAACG,IAAK;QACvB0E,OAAO,EAAEA,CAAA,KAAMxE,cAAc,CAAC,KAAK,CAAE;QACrC1C,EAAE,EAAE;UAAEuH,KAAK,EAAE;QAAO,CAAE;QACtBhH,OAAO,EAAC,QAAQ;QAAAL,QAAA,EAEfmC,OAAO,CAACE;MAAI;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEd,CAAC;AAACoB,EAAA,CA3OID,mBAAmB;EAAA,QACc3C,OAAO;AAAA;AAAAqI,GAAA,GADxC1F,mBAAmB;AA6OzB,eAAeA,mBAAmB;AAAC,IAAAtC,EAAA,EAAAqC,GAAA,EAAA2F,GAAA;AAAAC,YAAA,CAAAjI,EAAA;AAAAiI,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}