import React, { useState, useEffect } from 'react';
import {
    Snackbar,
    Alert,
    Slide
} from '@mui/material';

function SlideTransition(props) {
    return <Slide {...props} direction="down" />;
}

const PermissionNotification = () => {
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState('');

    useEffect(() => {
        // Create a global function to show permission update notifications
        window.showPermissionUpdateNotification = (msg) => {
            setMessage(msg);
            setOpen(true);
        };

        // Cleanup
        return () => {
            delete window.showPermissionUpdateNotification;
        };
    }, []);

    const handleClose = (event, reason) => {
        if (reason === 'clickaway') {
            return;
        }
        setOpen(false);
    };

    return (
        <Snackbar
            open={open}
            autoHideDuration={5000}
            onClose={handleClose}
            anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            TransitionComponent={SlideTransition}
            sx={{ mt: 8 }} // Add margin top to avoid overlapping with app bar
        >
            <Alert 
                onClose={handleClose} 
                severity="success" 
                variant="filled"
                sx={{ width: '100%' }}
            >
                {message}
            </Alert>
        </Snackbar>
    );
};

export default PermissionNotification;
