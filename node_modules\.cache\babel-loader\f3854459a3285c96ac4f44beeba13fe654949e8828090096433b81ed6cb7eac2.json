{"ast": null, "code": "import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nexport function getSliderUtilityClass(slot) {\n  return generateUtilityClass('MuiSlider', slot);\n}\nexport const sliderClasses = generateUtilityClasses('MuiSlider', ['root', 'active', 'focusVisible', 'disabled', 'dragging', 'marked', 'vertical', 'trackInverted', 'trackFalse', 'rail', 'track', 'mark', 'markActive', 'markLabel', 'markLabelActive', 'thumb']);", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getSliderUtilityClass", "slot", "sliderClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Slider/sliderClasses.js"], "sourcesContent": ["import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nexport function getSliderUtilityClass(slot) {\n  return generateUtilityClass('MuiSlider', slot);\n}\nexport const sliderClasses = generateUtilityClasses('MuiSlider', ['root', 'active', 'focusVisible', 'disabled', 'dragging', 'marked', 'vertical', 'trackInverted', 'trackFalse', 'rail', 'track', 'mark', 'markActive', 'markLabel', 'markLabelActive', 'thumb']);"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOF,oBAAoB,CAAC,WAAW,EAAEE,IAAI,CAAC;AAChD;AACA,OAAO,MAAMC,aAAa,GAAGJ,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}