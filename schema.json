{"success": true, "schema": {"tables": [{"TABLE_NAME": "AuthorizedDepartments", "TABLE_TYPE": "BASE TABLE", "TABLE_SCHEMA": "dbo"}, {"TABLE_NAME": "ComplaintAssignments", "TABLE_TYPE": "BASE TABLE", "TABLE_SCHEMA": "dbo"}, {"TABLE_NAME": "ComplaintAttachments", "TABLE_TYPE": "BASE TABLE", "TABLE_SCHEMA": "dbo"}, {"TABLE_NAME": "<PERSON><PERSON><PERSON><PERSON>", "TABLE_TYPE": "BASE TABLE", "TABLE_SCHEMA": "dbo"}, {"TABLE_NAME": "Complaints_Employee", "TABLE_TYPE": "BASE TABLE", "TABLE_SCHEMA": "dbo"}, {"TABLE_NAME": "ComplaintStatusHistory", "TABLE_TYPE": "BASE TABLE", "TABLE_SCHEMA": "dbo"}, {"TABLE_NAME": "Department", "TABLE_TYPE": "BASE TABLE", "TABLE_SCHEMA": "dbo"}, {"TABLE_NAME": "Employee", "TABLE_TYPE": "BASE TABLE", "TABLE_SCHEMA": "dbo"}, {"TABLE_NAME": "EmployeePermissions", "TABLE_TYPE": "BASE TABLE", "TABLE_SCHEMA": "dbo"}], "table_structures": {"AuthorizedDepartments": [{"COLUMN_NAME": "DeptId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 1}, {"COLUMN_NAME": "CanChangeStatus", "DATA_TYPE": "bit", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 2}, {"COLUMN_NAME": "CanAssignComplaints", "DATA_TYPE": "bit", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 3}], "ComplaintAssignments": [{"COLUMN_NAME": "AssignmentId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 1}, {"COLUMN_NAME": "ComplaintId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 2}, {"COLUMN_NAME": "AssignedToEmpCode", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 3}, {"COLUMN_NAME": "AssignedByEmpCode", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 4}, {"COLUMN_NAME": "AssignmentDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 5}, {"COLUMN_NAME": "DueDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 6}, {"COLUMN_NAME": "Notes", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": -1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 7}], "ComplaintAttachments": [{"COLUMN_NAME": "AttachmentId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 1}, {"COLUMN_NAME": "ComplaintId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 2}, {"COLUMN_NAME": "OriginalFileName", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 255, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 3}, {"COLUMN_NAME": "StoredFileName", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 255, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 4}, {"COLUMN_NAME": "FilePath", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 500, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 5}, {"COLUMN_NAME": "FileSize", "DATA_TYPE": "bigint", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 19, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 6}, {"COLUMN_NAME": "FileType", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 7}, {"COLUMN_NAME": "UploadDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "(getdate())", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 8}], "Complaints": [{"COLUMN_NAME": "ComplaintId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 1}, {"COLUMN_NAME": "ComplaintNumber", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 40, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 2}, {"COLUMN_NAME": "Title", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 200, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 3}, {"COLUMN_NAME": "Description", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": -1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 4}, {"COLUMN_NAME": "SubmittedByEmpCode", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 5}, {"COLUMN_NAME": "StatusId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 6}, {"COLUMN_NAME": "Priority", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 20, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 7}, {"COLUMN_NAME": "Category", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 8}, {"COLUMN_NAME": "SubmissionDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "(getdate())", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 9}, {"COLUMN_NAME": "LastUpdateDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 10}, {"COLUMN_NAME": "ResolutionDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 11}, {"COLUMN_NAME": "ResolutionNotes", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": -1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 12}, {"COLUMN_NAME": "IsConfidential", "DATA_TYPE": "bit", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 13}], "Complaints_Employee": [{"COLUMN_NAME": "EmpID", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 1}, {"COLUMN_NAME": "EmpCode", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 2}, {"COLUMN_NAME": "EmpName", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 3}, {"COLUMN_NAME": "Password", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 4}, {"COLUMN_NAME": "DeptID", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 5}, {"COLUMN_NAME": "DeptName", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 6}, {"COLUMN_NAME": "CreatedDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "(getdate())", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 7}], "ComplaintStatusHistory": [{"COLUMN_NAME": "HistoryId", "DATA_TYPE": "int", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 1}, {"COLUMN_NAME": "ComplaintId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 2}, {"COLUMN_NAME": "OldStatusId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 3}, {"COLUMN_NAME": "NewStatusId", "DATA_TYPE": "int", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 4}, {"COLUMN_NAME": "ChangedByEmpCode", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 5}, {"COLUMN_NAME": "ChangeDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "(getdate())", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 6}, {"COLUMN_NAME": "Comments", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": -1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 7}], "Department": [{"COLUMN_NAME": "DeptID", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 1}, {"COLUMN_NAME": "DeptName", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 2}, {"COLUMN_NAME": "DeptPosition", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 3}, {"COLUMN_NAME": "Editable", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('Y')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 4}, {"COLUMN_NAME": "ParentDeptId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 5}, {"COLUMN_NAME": "BrkTimeConsiderInPlan", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('Y')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 6}, {"COLUMN_NAME": "PlanReq", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 7}, {"COLUMN_NAME": "DeptGroupID", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 8}, {"COLUMN_NAME": "DeptGroupName", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 9}, {"COLUMN_NAME": "ConsiderStkRep", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('Y')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 10}, {"COLUMN_NAME": "DCode", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 10, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 11}, {"COLUMN_NAME": "MaintDept", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 12}, {"COLUMN_NAME": "LocationIDs", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 250, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 13}, {"COLUMN_NAME": "BrkTimeNotReqInProdn", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('Y')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 14}, {"COLUMN_NAME": "EntryEmpId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 15}, {"COLUMN_NAME": "EntryComputer", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 16}, {"COLUMN_NAME": "DeptLevel", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((1))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 17}, {"COLUMN_NAME": "SubDeptIds", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 250, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 18}, {"COLUMN_NAME": "active", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('Y')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 19}, {"COLUMN_NAME": "LocationId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((1))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 20}, {"COLUMN_NAME": "DeptCostType", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('Direct')", "CHARACTER_MAXIMUM_LENGTH": 500, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 21}, {"COLUMN_NAME": "DepartmentGroup", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 22}, {"COLUMN_NAME": "MaxNoOfUsers", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((-1))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 23}, {"COLUMN_NAME": "PlpCal", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('I')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 24}, {"COLUMN_NAME": "Plpper", "DATA_TYPE": "float", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((75))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 53, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 25}, {"COLUMN_NAME": "LoadedOnCompName", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 500, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 26}, {"COLUMN_NAME": "Line", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 27}, {"COLUMN_NAME": "WeeklyHolidays", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('Y')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 28}], "Employee": [{"COLUMN_NAME": "EmpId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 1}, {"COLUMN_NAME": "EmpCode", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 2}, {"COLUMN_NAME": "EmpName", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 3}, {"COLUMN_NAME": "<PERSON><PERSON><PERSON>", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 4}, {"COLUMN_NAME": "Address", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 250, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 5}, {"COLUMN_NAME": "PinCode", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 6}, {"COLUMN_NAME": "Phone", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 7}, {"COLUMN_NAME": "EMailId", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 8000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 8}, {"COLUMN_NAME": "BirthDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 9}, {"COLUMN_NAME": "JoinDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 10}, {"COLUMN_NAME": "DeptId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 11}, {"COLUMN_NAME": "CatId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 12}, {"COLUMN_NAME": "Attachment", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 8000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 13}, {"COLUMN_NAME": "AttachPhoto", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 8000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 14}, {"COLUMN_NAME": "BloodGroup", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 5, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 15}, {"COLUMN_NAME": "BasicPay", "DATA_TYPE": "float", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 53, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 16}, {"COLUMN_NAME": "Gender", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 17}, {"COLUMN_NAME": "BankNo", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 18}, {"COLUMN_NAME": "Wages", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 19}, {"COLUMN_NAME": "MaritalStatus", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 20}, {"COLUMN_NAME": "Resign", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 21}, {"COLUMN_NAME": "ResignDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 22}, {"COLUMN_NAME": "AccountID", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 23}, {"COLUMN_NAME": "AccountHeadId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 24}, {"COLUMN_NAME": "AccExpenseID", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 25}, {"COLUMN_NAME": "LoginName", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 26}, {"COLUMN_NAME": "Password", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 27}, {"COLUMN_NAME": "RunOnce", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 28}, {"COLUMN_NAME": "StockReportReminder", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 3, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 29}, {"COLUMN_NAME": "AdminAccess", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 30}, {"COLUMN_NAME": "CardId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 31}, {"COLUMN_NAME": "Description", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 32}, {"COLUMN_NAME": "MaximumPOValue", "DATA_TYPE": "float", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 53, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 33}, {"COLUMN_NAME": "AccBonusAcId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 34}, {"COLUMN_NAME": "IncrementedDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 35}, {"COLUMN_NAME": "DocCtrlPwd", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 36}, {"COLUMN_NAME": "StockReportReminderForMG", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 37}, {"COLUMN_NAME": "DId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 10, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 38}, {"COLUMN_NAME": "Grade", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 39}, {"COLUMN_NAME": "QID", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 40}, {"COLUMN_NAME": "ExpId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 41}, {"COLUMN_NAME": "CompanyHead", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 42}, {"COLUMN_NAME": "Authority", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 43}, {"COLUMN_NAME": "Interviewed", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 44}, {"COLUMN_NAME": "ReasonForLeaving", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "(' ')", "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 45}, {"COLUMN_NAME": "CostCenter", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 46}, {"COLUMN_NAME": "ConfirmationDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 47}, {"COLUMN_NAME": "ShiftRotate", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 48}, {"COLUMN_NAME": "PRApproval", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 49}, {"COLUMN_NAME": "PORelease", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 50}, {"COLUMN_NAME": "CardNo", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 51}, {"COLUMN_NAME": "Notes", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 1000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 52}, {"COLUMN_NAME": "<PERSON><PERSON><PERSON>", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 500, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 53}, {"COLUMN_NAME": "Block", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 54}, {"COLUMN_NAME": "Cases", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 55}, {"COLUMN_NAME": "OP_Permission", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 56}, {"COLUMN_NAME": "Nac_Code", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 57}, {"COLUMN_NAME": "Absconding", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 58}, {"COLUMN_NAME": "ETSEAdequate", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 59}, {"COLUMN_NAME": "ETSEComments", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 250, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 60}, {"COLUMN_NAME": "ShiftRotationPattern", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 1000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 61}, {"COLUMN_NAME": "ShiftRotationEndDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 62}, {"COLUMN_NAME": "RotationInterval", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 63}, {"COLUMN_NAME": "AutoAttReq", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 64}, {"COLUMN_NAME": "PanNo", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('PANNOTAVBL')", "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 65}, {"COLUMN_NAME": "Setters", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 2, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 66}, {"COLUMN_NAME": "EmpAttDesc", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 800, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 67}, {"COLUMN_NAME": "ppserv", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 1000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 68}, {"COLUMN_NAME": "MonthDayId", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('W')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 69}, {"COLUMN_NAME": "SalOnWeeklyOff", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 70}, {"COLUMN_NAME": "SalOnWeeklyOffGo", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 71}, {"COLUMN_NAME": "WeekOff_HourlyGo", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 72}, {"COLUMN_NAME": "WeekOff_Hourly", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 73}, {"COLUMN_NAME": "StandardHours", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('8,8,8,8,8,8,8')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 74}, {"COLUMN_NAME": "MonthDay", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 75}, {"COLUMN_NAME": "Form5Remarks", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 8000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 76}, {"COLUMN_NAME": "UnAvailGenHol_Present", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 77}, {"COLUMN_NAME": "UnAvailGenHol_Absent", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 78}, {"COLUMN_NAME": "ActualRent", "DATA_TYPE": "float", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 53, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 79}, {"COLUMN_NAME": "EntryEmpId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 80}, {"COLUMN_NAME": "EntryComputer", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 81}, {"COLUMN_NAME": "Exclusiveuser", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 82}, {"COLUMN_NAME": "WorkDaysBasedOnAbsent", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 83}, {"COLUMN_NAME": "IsHandicapped", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 84}, {"COLUMN_NAME": "AccExpenseSubID", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 85}, {"COLUMN_NAME": "AccBonusSubID", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 86}, {"COLUMN_NAME": "ReportUser", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 87}, {"COLUMN_NAME": "LocationId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((1))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 88}, {"COLUMN_NAME": "UserGroupId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('0')", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 9, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 89}, {"COLUMN_NAME": "PayrollLocationId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 90}, {"COLUMN_NAME": "ServiceID", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 91}, {"COLUMN_NAME": "IFSCCode", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 92}, {"COLUMN_NAME": "Bank_BranchName", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 93}, {"COLUMN_NAME": "ReportTo", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 94}, {"COLUMN_NAME": "EarlierEmployerName", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 95}, {"COLUMN_NAME": "EarlierEmployerLocation", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 96}, {"COLUMN_NAME": "EarlierDesignation", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 97}, {"COLUMN_NAME": "EarlierWorkedFrom", "DATA_TYPE": "date", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 98}, {"COLUMN_NAME": "EarlierWorkedTo", "DATA_TYPE": "date", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 99}, {"COLUMN_NAME": "EarlierSalaryDrawn", "DATA_TYPE": "float", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 53, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 100}, {"COLUMN_NAME": "EarlierReportingTo", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 101}, {"COLUMN_NAME": "AttachmentForEarlierEmprDetails", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 8000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 102}, {"COLUMN_NAME": "IsSrCitizen", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 103}, {"COLUMN_NAME": "BankName", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 104}, {"COLUMN_NAME": "SubIds", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 1000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 105}, {"COLUMN_NAME": "EmailPwd", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 106}, {"COLUMN_NAME": "Cityid", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 107}, {"COLUMN_NAME": "EffectiveDate", "DATA_TYPE": "date", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 108}, {"COLUMN_NAME": "Ctc", "DATA_TYPE": "float", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 53, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 109}, {"COLUMN_NAME": "ExFromPayRoll", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 5, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 110}, {"COLUMN_NAME": "SignatureImage", "DATA_TYPE": "image", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 2147483647, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 111}, {"COLUMN_NAME": "EmailUserName", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 112}, {"COLUMN_NAME": "IsContractEmp", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 113}, {"COLUMN_NAME": "RFID", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 1000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 114}, {"COLUMN_NAME": "RFIDEffFrom", "DATA_TYPE": "date", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 115}, {"COLUMN_NAME": "RFIDEffTo", "DATA_TYPE": "date", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 116}, {"COLUMN_NAME": "ContractorTypeId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 117}, {"COLUMN_NAME": "Cont_BadgeNo", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 1000, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 118}, {"COLUMN_NAME": "Status_EffectiveDate", "DATA_TYPE": "datetime", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 119}, {"COLUMN_NAME": "StatusId", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 120}, {"COLUMN_NAME": "Territory", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 100, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 121}, {"COLUMN_NAME": "MobileAppRegId", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "YES", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 500, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 122}, {"COLUMN_NAME": "MobileAppLocUpdateMins", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((15))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 18, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 123}, {"COLUMN_NAME": "Deptids", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('')", "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 124}, {"COLUMN_NAME": "DefaultESS", "DATA_TYPE": "<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "('N')", "CHARACTER_MAXIMUM_LENGTH": 1, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 125}, {"COLUMN_NAME": "Aadhar<PERSON><PERSON>", "DATA_TYPE": "numeric", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": 30, "NUMERIC_SCALE": 0, "ORDINAL_POSITION": 126}], "EmployeePermissions": [{"COLUMN_NAME": "EmpCode", "DATA_TYPE": "n<PERSON><PERSON><PERSON>", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": null, "CHARACTER_MAXIMUM_LENGTH": 50, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 1}, {"COLUMN_NAME": "CanAssign", "DATA_TYPE": "bit", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 2}, {"COLUMN_NAME": "CanUpdateStatus", "DATA_TYPE": "bit", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 3}, {"COLUMN_NAME": "CanViewDashboard", "DATA_TYPE": "bit", "IS_NULLABLE": "NO", "COLUMN_DEFAULT": "((0))", "CHARACTER_MAXIMUM_LENGTH": null, "NUMERIC_PRECISION": null, "NUMERIC_SCALE": null, "ORDINAL_POSITION": 4}]}, "triggers": [{"trigger_name": "Department_Add", "table_name": "Department", "trigger_type": "SQL_TRIGGER", "is_disabled": false, "create_date": "2020-03-28T23:19:07.787Z", "modify_date": "2020-03-28T23:19:07.787Z", "trigger_definition": "create trigger [Department_Add] on dbo.Department for Insert\r\nAs\r\ninsert into location_department (DeptID,LocationID) select Deptid,locationid  from inserted\r\n"}, {"trigger_name": "TR_Employee_AfterUpdate", "table_name": "Employee", "trigger_type": "SQL_TRIGGER", "is_disabled": false, "create_date": "2025-05-29T16:01:45.970Z", "modify_date": "2025-06-02T10:54:00.203Z", "trigger_definition": "\r\n-- Trigger to sync employee updates\r\nCREATE   TRIGGER TR_Employee_AfterUpdate\r\nON employee\r\nAFTER UPDATE\r\nAS\r\nBEGIN\r\n    SET NOCOUNT ON;\r\n    EXEC SyncEmployeesToComplaints;\r\nEND;\r\n"}, {"trigger_name": "TR_Employee_Insert", "table_name": "Employee", "trigger_type": "SQL_TRIGGER", "is_disabled": false, "create_date": "2025-06-09T16:47:55.857Z", "modify_date": "2025-06-09T16:47:55.857Z", "trigger_definition": "\n            CREATE TRIGGER TR_Employee_Insert\n            ON Employee\n            AFTER INSERT\n            AS\n            BEGIN\n                SET NOCOUNT ON;\n\n                INSERT INTO Complaints_Employee (EmpID, EmpCode, EmpName, Password, DeptID, DeptName)\n                SELECT\n                    i.EmpID,\n                    i.EmpCode,\n                    i.EmpName,\n                    '1234',\n                    i.DeptID,\n                    d.DeptName\n                FROM inserted i\n                INNER JOIN Department d ON i.DeptID = d.DeptID;\n            END\n        "}], "stored_procedures": [{"procedure_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_desc": "SQL_STORED_PROCEDURE", "create_date": "2025-05-29T09:47:57.320Z", "modify_date": "2025-05-29T16:08:27.410Z", "procedure_definition": "\r\n-- Procedure to assign a complaint to an employee\r\nCREATE   PROCEDURE AssignComplaint\r\n    @ComplaintId INT,\r\n    @AssignedToEmpCode NVARCHAR(20),\r\n    @AssignedByEmpCode NVARCHAR(20),\r\n    @DueDate DATETIME = NULL,\r\n    @Notes NVARCHAR(MAX) = NULL\r\nAS\r\nBEGIN\r\n    SET NOCOUNT ON;\r\n    \r\n    DECLARE @AssignedStatusId INT;\r\n    \r\n    -- Get the 'Assigned' status ID\r\n    SELECT @AssignedStatusId = StatusId \r\n    FROM ComplaintStatus \r\n    WHERE StatusName = 'Assigned' AND IsActive = 1;\r\n    \r\n    -- Verify assigner is from authorized department\r\n    IF NOT EXISTS (\r\n        SELECT 1 \r\n        FROM Complaints_Employee ce\r\n        INNER JOIN AuthorizedDepartments ad ON ce.DeptID = ad.DeptId\r\n        WHERE ce.EmpCode = @AssignedByEmpCode \r\n        AND ad.IsActive = 1\r\n    )\r\n    BEGIN\r\n        RAISERROR ('You are not authorized to assign complaints', 16, 1);\r\n        RETURN;\r\n    END\r\n    \r\n    BEGIN TRANSACTION;\r\n    \r\n    -- Update complaint status to Assigned\r\n    EXEC UpdateComplaintStatusWithWorkflow \r\n        @ComplaintId = @ComplaintId,\r\n        @NewStatus = 'Assigned',\r\n        @UpdatedByEmpCode = @AssignedByEmpCode,\r\n        @Comments = @Notes;\r\n    \r\n    -- Deactivate any existing active assignments\r\n    UPDATE ComplaintAssignments\r\n    SET IsActive = 0\r\n    WHERE ComplaintId = @ComplaintId AND IsActive = 1;\r\n    \r\n    -- Create new assignment\r\n    INSERT INTO ComplaintAssignments (\r\n        ComplaintId,\r\n        AssignedToEmpCode,\r\n        AssignedByEmpCode,\r\n        DueDate,\r\n        Notes,\r\n        AssignmentDate,\r\n        IsActive\r\n    )\r\n    VALUES (\r\n        @ComplaintId,\r\n        @AssignedToEmpCode,\r\n        @AssignedByEmpCode,\r\n        @DueDate,\r\n        @Notes,\r\n        GETDATE(),\r\n        1\r\n    );\r\n    \r\n    COMMIT;\r\nEND;\r\n"}, {"procedure_name": "ChangeComplaintPassword", "type_desc": "SQL_STORED_PROCEDURE", "create_date": "2025-06-02T10:54:00.203Z", "modify_date": "2025-06-02T10:54:00.203Z", "procedure_definition": "\r\n-- Procedure to change password\r\nCREATE   PROCEDURE ChangeComplaintPassword\r\n    @EmpCode NVARCHAR(50),\r\n    @OldPassword NVARCHAR(50),\r\n    @NewPassword NVARCHAR(50)\r\nAS\r\nBEGIN\r\n    SET NOCOUNT ON;\r\n\r\n    -- Check if old password matches\r\n    IF EXISTS (SELECT 1 FROM Complaints_Employee WHERE EmpCode = @EmpCode AND Password = @OldPassword)\r\n    BEGIN\r\n        -- Update password\r\n        UPDATE Complaints_Employee\r\n        SET Password = @NewPassword\r\n        WHERE EmpCode = @EmpCode;\r\n\r\n        SELECT 'Password changed successfully' AS Result;\r\n    END\r\n    ELSE\r\n    BEGIN\r\n        RAISERROR ('Invalid old password', 16, 1);\r\n        RETURN;\r\n    END\r\nEND;\r\n"}, {"procedure_name": "GetComplaintDetails", "type_desc": "SQL_STORED_PROCEDURE", "create_date": "2025-05-29T09:47:57.320Z", "modify_date": "2025-05-29T09:47:57.320Z", "procedure_definition": "\r\n-- Procedure to get complaint details\r\nCREATE PROCEDURE GetComplaintDetails\r\n    @ComplaintId INT\r\nAS\r\nBEGIN\r\n    SET NOCOUNT ON;\r\n    \r\n    -- Get main complaint details\r\n    SELECT \r\n        c.ComplaintId,\r\n        c.Complain<PERSON><PERSON><PERSON>ber,\r\n        c.Title,\r\n        c.Description,\r\n        c.SubmittedByEmpCode,\r\n        ce_sub.EmpName AS SubmittedByName,\r\n        ce_sub.DeptName AS SubmittedByDepartment,\r\n        cs.StatusName,\r\n        c.Priority,\r\n        c.Category,\r\n        c.SubmissionDate,\r\n        c.LastUpdateDate,\r\n        c.ResolutionDate,\r\n        c.ResolutionNotes,\r\n        c.IsConfidential\r\n    FROM Complaints c\r\n    INNER JOIN Complaints_Employee ce_sub ON ce_sub.EmpCode = c.SubmittedByEmpCode\r\n    INNER JOIN ComplaintStatus cs ON cs.StatusId = c.StatusId\r\n    WHERE c.ComplaintId = @ComplaintId;\r\n    \r\n    -- Get assignment history\r\n    SELECT \r\n        ca.AssignmentId,\r\n        ca.AssignedToEmpCode,\r\n        ce_to.EmpName AS AssignedToName,\r\n        ce_to.DeptName AS AssignedToDepartment,\r\n        ca.AssignedByEmpCode,\r\n        ce_by.EmpName AS AssignedByName,\r\n        ca.AssignmentDate,\r\n        ca.DueDate,\r\n        ca.Notes,\r\n        ca.IsActive\r\n    FROM ComplaintAssignments ca\r\n    INNER JOIN Complaints_Employee ce_to ON ce_to.EmpCode = ca.AssignedToEmpCode\r\n    INNER JOIN Complaints_Employee ce_by ON ce_by.EmpCode = ca.AssignedByEmpCode\r\n    WHERE ca.ComplaintId = @ComplaintId\r\n    ORDER BY ca.AssignmentDate DESC;\r\nEND;\r\n"}, {"procedure_name": "GetComplaintsList", "type_desc": "SQL_STORED_PROCEDURE", "create_date": "2025-05-29T09:47:57.323Z", "modify_date": "2025-05-29T09:47:57.323Z", "procedure_definition": "\r\n-- Procedure to get complaints list with filters\r\nCREATE PROCEDURE GetComplaintsList\r\n    @EmpCode NVARCHAR(20) = NULL,\r\n    @StatusName NVARCHAR(50) = NULL,\r\n    @Priority NVARCHAR(20) = NULL,\r\n    @StartDate DATETIME = NULL,\r\n    @EndDate DATETIME = NULL,\r\n    @AssignedToMe BIT = 0\r\nAS\r\nBEGIN\r\n    SET NOCOUNT ON;\r\n    \r\n    SELECT \r\n        c.ComplaintId,\r\n        c.ComplaintNumber,\r\n        c.Title,\r\n        c.SubmittedByEmpCode,\r\n        ce_sub.EmpName AS SubmittedByName,\r\n        ce_sub.DeptName AS SubmittedByDepartment,\r\n        cs.StatusName,\r\n        c.Priority,\r\n        c.Category,\r\n        c.SubmissionDate,\r\n        c.LastUpdateDate,\r\n        CASE \r\n            WHEN ca.AssignedToEmpCode IS NOT NULL THEN ca.AssignedToEmpCode\r\n            ELSE 'Unassigned'\r\n        END AS AssignedTo,\r\n        CASE \r\n            WHEN ca.AssignedToEmpCode IS NOT NULL THEN ce_assigned.EmpName\r\n            ELSE 'Unassigned'\r\n        END AS AssignedToName,\r\n        ca.DueDate\r\n    FROM Complaints c\r\n    INNER JOIN Complaints_Employee ce_sub ON ce_sub.EmpCode = c.SubmittedByEmpCode\r\n    INNER JOIN ComplaintStatus cs ON cs.StatusId = c.StatusId\r\n    LEFT JOIN ComplaintAssignments ca ON ca.ComplaintId = c.ComplaintId AND ca.IsActive = 1\r\n    LEFT JOIN Complaints_Employee ce_assigned ON ce_assigned.EmpCode = ca.AssignedToEmpCode\r\n    WHERE \r\n        (@EmpCode IS NULL OR c.SubmittedByEmpCode = @EmpCode)\r\n        AND (@StatusName IS NULL OR cs.StatusName = @StatusName)\r\n        AND (@Priority IS NULL OR c.Priority = @Priority)\r\n        AND (@StartDate IS NULL OR c.SubmissionDate >= @StartDate)\r\n        AND (@EndDate IS NULL OR c.SubmissionDate <= @EndDate)\r\n        AND (\r\n            @AssignedToMe = 0 \r\n            OR (@AssignedToMe = 1 AND ca.AssignedToEmpCode = @EmpCode)\r\n        )\r\n    ORDER BY c.SubmissionDate DESC;\r\nEND;\r\n"}, {"procedure_name": "PopulateComplaintsEmployee", "type_desc": "SQL_STORED_PROCEDURE", "create_date": "2025-05-29T16:01:41.377Z", "modify_date": "2025-05-29T16:01:41.377Z", "procedure_definition": "CREATE PROCEDURE PopulateComplaintsEmployee\r\nAS\r\nBEGIN\r\n    SET NOCOUNT ON;\r\n    \r\n    INSERT INTO Complaints_Employee (EmpCode, EmpName, EmpID, DeptID, DeptName)\r\n    SELECT \r\n        e.Empcode,\r\n        e.Empname,\r\n        e.<PERSON><PERSON>,\r\n        e.<PERSON><PERSON>,\r\n        d.Dept<PERSON>ame\r\n    FROM employee e\r\n    INNER JOIN Department d ON d.DeptID = e.DeptId\r\n    WHERE NOT EXISTS (\r\n        SELECT 1 \r\n        FROM Complaints_Employee ce \r\n        WHERE ce.EmpCode = e.Empcode\r\n    );\r\nEND;\r\n"}, {"procedure_name": "SubmitComplaint", "type_desc": "SQL_STORED_PROCEDURE", "create_date": "2025-05-29T09:47:57.317Z", "modify_date": "2025-05-29T16:08:27.407Z", "procedure_definition": "\r\n-- Procedure to submit a new complaint\r\nCREATE   PROCEDURE SubmitComplaint\r\n    @Title NVARCHAR(200),\r\n    @Description NVARCHAR(MAX),\r\n    @SubmittedByEmpCode NVARCHAR(20),\r\n    @Priority NVARCHAR(20),\r\n    @Category NVARCHAR(100) = NULL,\r\n    @IsConfidential BIT = 0\r\nAS\r\nBEGIN\r\n    SET NOCOUNT ON;\r\n    \r\n    DECLARE @NewStatusId INT;\r\n    \r\n    -- Get the 'New' status ID\r\n    SELECT @NewStatusId = StatusId \r\n    FROM ComplaintStatus \r\n    WHERE StatusName = 'New' AND IsActive = 1;\r\n    \r\n    IF @NewStatusId IS NULL\r\n    BEGIN\r\n        RAISERROR ('Status \"New\" not found or not active', 16, 1);\r\n        RETURN;\r\n    END\r\n    \r\n    INSERT INTO Complaints (\r\n        Title,\r\n        Description,\r\n        SubmittedByEmpCode,\r\n        StatusId,        -- Will be set to 'New' status\r\n        Priority,\r\n        Category,\r\n        IsConfidential,\r\n        SubmissionDate,\r\n        LastUpdateDate\r\n    )\r\n    VALUES (\r\n        @Title,\r\n        @Description,\r\n        @SubmittedByEmpCode,\r\n        @NewStatusId,    -- Using the 'New' status ID\r\n        @Priority,\r\n        @Category,\r\n        @IsConfidential,\r\n        GETDATE(),\r\n        GETDATE()\r\n    );\r\n    \r\n    DECLARE @ComplaintId INT = SCOPE_IDENTITY();\r\n    \r\n    -- Insert into status history\r\n    INSERT INTO ComplaintStatusHistory (\r\n        ComplaintId,\r\n        FromStatusId,\r\n        ToStatusId,\r\n        ChangedByEmpCode,\r\n        ChangeDate,\r\n        Comments\r\n    )\r\n    VALUES (\r\n        @ComplaintId,\r\n        NULL,           -- No previous status for new complaints\r\n        @NewStatusId,\r\n        @SubmittedByEmpCode,\r\n        GETDATE(),\r\n        'Complaint submitted'\r\n    );\r\n    \r\n    -- Return the new complaint ID\r\n    SELECT @ComplaintId AS ComplaintId;\r\nEND;\r\n"}, {"procedure_name": "SyncNewEmployeeWithDefaultPassword", "type_desc": "SQL_STORED_PROCEDURE", "create_date": "2025-06-02T12:49:10.993Z", "modify_date": "2025-06-02T13:00:12.657Z", "procedure_definition": "CREATE   PROCEDURE SyncNewEmployeeWithDefaultPassword AS BEGIN SET NOCOUNT ON; INSERT INTO Complaints_Employee (EmpID, EmpCode, EmpName, Password, DeptID, DeptName) SELECT e.EmpID, e.EmpCode, e.EmpName, '1234', e.DeptID, d.DeptName FROM Employee e JOIN Department d ON d.DeptID = e.DeptID WHERE NOT EXISTS (SELECT 1 FROM Complaints_Employee ce WHERE ce.EmpCode = e.EmpCode); END"}, {"procedure_name": "UpdateComplaintStatus", "type_desc": "SQL_STORED_PROCEDURE", "create_date": "2025-05-29T09:47:57.320Z", "modify_date": "2025-05-29T09:47:57.320Z", "procedure_definition": "\r\n-- Procedure to update complaint status\r\nCREATE PROCEDURE UpdateComplaintStatus\r\n    @ComplaintId INT,\r\n    @StatusName NVARCHAR(50),\r\n    @ResolutionNotes NVARCHAR(MAX) = NULL\r\nAS\r\nBEGIN\r\n    SET NOCOUNT ON;\r\n    \r\n    DECLARE @StatusId INT;\r\n    SELECT @StatusId = StatusId FROM ComplaintStatus WHERE StatusName = @StatusName;\r\n    \r\n    IF @StatusId IS NULL\r\n        RAISERROR ('Invalid status name', 16, 1);\r\n    \r\n    UPDATE Complaints\r\n    SET \r\n        StatusId = @StatusId,\r\n        ResolutionNotes = CASE \r\n            WHEN @StatusName IN ('Resolved', 'Closed') THEN @ResolutionNotes \r\n            ELSE ResolutionNotes \r\n        END,\r\n        ResolutionDate = CASE \r\n            WHEN @StatusName IN ('Resolved', 'Closed') THEN GETDATE() \r\n            ELSE ResolutionDate \r\n        END\r\n    WHERE ComplaintId = @ComplaintId;\r\nEND;\r\n"}, {"procedure_name": "UpdateComplaintStatusWithWorkflow", "type_desc": "SQL_STORED_PROCEDURE", "create_date": "2025-05-29T16:05:13.297Z", "modify_date": "2025-05-29T16:06:51.670Z", "procedure_definition": "\r\n-- Create a new procedure for updating complaint status with workflow rules\r\nCREATE   PROCEDURE UpdateComplaintStatusWithWorkflow\r\n    @ComplaintId INT,\r\n    @NewStatus NVARCHAR(50),\r\n    @UpdatedByEmpCode NVARCHAR(20),\r\n    @Comments NVARCHAR(MAX) = NULL\r\nAS\r\nBEGIN\r\n    SET NOCOUNT ON;\r\n    \r\n    DECLARE @CurrentStatusId INT,\r\n            @NewStatusId INT,\r\n            @AssignedToEmpCode NVARCHAR(20),\r\n            @AllowedRole NVARCHAR(20),\r\n            @RequiresComment BIT,\r\n            @ErrorMessage NVARCHAR(200),\r\n            @UpdaterDeptId INT;\r\n\r\n    -- Get current status and assignment\r\n    SELECT \r\n        @CurrentStatusId = c.StatusId,\r\n        @AssignedToEmpCode = ca.AssignedToEmpCode\r\n    FROM Complaints c\r\n    LEFT JOIN ComplaintAssignments ca ON c.ComplaintId = ca.ComplaintId AND ca.IsActive = 1\r\n    WHERE c.ComplaintId = @ComplaintId;\r\n\r\n    -- Get new status ID\r\n    SELECT @NewStatusId = StatusId \r\n    FROM ComplaintStatus \r\n    WHERE StatusName = @NewStatus AND IsActive = 1;\r\n\r\n    -- Get updater's department\r\n    SELECT @UpdaterDeptId = DeptID\r\n    FROM Complaints_Employee\r\n    WHERE EmpCode = @UpdatedByEmpCode;\r\n\r\n    -- Validate status exists\r\n    IF @NewStatusId IS NULL\r\n    BEGIN\r\n        RAISERROR ('Invalid status specified', 16, 1);\r\n        RETURN;\r\n    END\r\n\r\n    -- Get transition requirements\r\n    SELECT \r\n        @AllowedRole = AllowedRole,\r\n        @RequiresComment = RequiresComment\r\n    FROM ComplaintStatusTransition\r\n    WHERE FromStatusId = @CurrentStatusId\r\n    AND ToStatusId = @NewStatusId;\r\n\r\n    -- Validate transition is allowed\r\n    IF @AllowedRole IS NULL\r\n    BEGIN\r\n        SET @ErrorMessage = 'Cannot transition from ' + \r\n            (SELECT StatusName FROM ComplaintStatus WHERE StatusId = @CurrentStatusId) +\r\n            ' to ' + @NewStatus;\r\n        RAISERROR (@ErrorMessage, 16, 1);\r\n        RETURN;\r\n    END\r\n\r\n    -- Check if user has permission\r\n    IF (\r\n        (@AllowedRole = 'Assignee' AND @UpdatedByEmpCode != @AssignedToEmpCode)\r\n        OR \r\n        (@AllowedRole = 'AuthorizedDept' AND NOT EXISTS (\r\n            SELECT 1 \r\n            FROM AuthorizedDepartments \r\n            WHERE DeptId = @UpdaterDeptId \r\n            AND IsActive = 1\r\n        ))\r\n    )\r\n    BEGIN\r\n        RAISERROR ('You do not have permission to make this status change', 16, 1);\r\n        RETURN;\r\n    END\r\n\r\n    -- Check if comments are required\r\n    IF @RequiresComment = 1 AND (@Comments IS NULL OR LEN(TRIM(@Comments)) = 0)\r\n    BEGIN\r\n        RAISERROR ('Comments are required for this status change', 16, 1);\r\n        RETURN;\r\n    END\r\n\r\n    -- Update the complaint status\r\n    BEGIN TRANSACTION;\r\n    \r\n    UPDATE Complaints\r\n    SET \r\n        StatusId = @NewStatusId,\r\n        LastUpdateDate = GETDATE(),\r\n        ResolutionDate = CASE \r\n            WHEN @NewStatus IN ('Resolved', 'Closed') THEN GETDATE()\r\n            ELSE ResolutionDate\r\n        END,\r\n        ResolutionNotes = CASE\r\n            WHEN @NewStatus IN ('Resolved', 'Closed') THEN @Comments\r\n            ELSE ResolutionNotes\r\n        END\r\n    WHERE ComplaintId = @ComplaintId;\r\n\r\n    -- Log the status change\r\n    INSERT INTO ComplaintStatusHistory (\r\n        ComplaintId,\r\n        FromStatusId,\r\n        ToStatusId,\r\n        ChangedByEmpCode,\r\n        ChangeDate,\r\n        Comments\r\n    )\r\n    VALUES (\r\n        @ComplaintId,\r\n        @CurrentStatusId,\r\n        @NewStatusId,\r\n        @UpdatedByEmpCode,\r\n        GETDATE(),\r\n        @Comments\r\n    );\r\n\r\n    COMMIT;\r\nEND;\r\n"}], "foreign_keys": [{"foreign_key_name": "FK_ComplaintAttachments_Complaints", "parent_table": "ComplaintAttachments", "parent_column": "ComplaintId", "referenced_table": "<PERSON><PERSON><PERSON><PERSON>", "referenced_column": "ComplaintId"}, {"foreign_key_name": "FK_Complaints_Employee", "parent_table": "<PERSON><PERSON><PERSON><PERSON>", "parent_column": "SubmittedByEmpCode", "referenced_table": "Complaints_Employee", "referenced_column": "EmpCode"}, {"foreign_key_name": "FK_ComplaintStatusHistory_ChangedBy", "parent_table": "ComplaintStatusHistory", "parent_column": "ChangedByEmpCode", "referenced_table": "Complaints_Employee", "referenced_column": "EmpCode"}, {"foreign_key_name": "FK_ComplaintStatusHistory_Complaints", "parent_table": "ComplaintStatusHistory", "parent_column": "ComplaintId", "referenced_table": "<PERSON><PERSON><PERSON><PERSON>", "referenced_column": "ComplaintId"}, {"foreign_key_name": "FK_EmployeePermissions_Employee", "parent_table": "EmployeePermissions", "parent_column": "EmpCode", "referenced_table": "Complaints_Employee", "referenced_column": "EmpCode"}]}}