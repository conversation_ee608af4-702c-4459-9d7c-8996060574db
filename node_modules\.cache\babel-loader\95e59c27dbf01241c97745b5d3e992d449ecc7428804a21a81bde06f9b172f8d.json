{"ast": null, "code": "import React,{createContext,useContext,useEffect,useState}from'react';import{io}from'socket.io-client';import{useAuth}from'./AuthContext';import{jsx as _jsx}from\"react/jsx-runtime\";const SocketContext=/*#__PURE__*/createContext(null);export const useSocket=()=>{const context=useContext(SocketContext);if(!context){throw new Error('useSocket must be used within a SocketProvider');}return context;};export const SocketProvider=_ref=>{let{children}=_ref;const[socket,setSocket]=useState(null);const[connected,setConnected]=useState(false);const{user,refreshUserPermissions,logout}=useAuth();useEffect(()=>{// Only connect if user is authenticated\nif(user&&user.empCode){console.log('Connecting to Socket.IO server...');// Create socket connection to the backend server\nconst getSocketUrl=()=>{// In development, connect to the backend server directly\nif(process.env.NODE_ENV==='development'){const currentHost=window.location.hostname;if(currentHost==='localhost'||currentHost==='127.0.0.1'){return'http://localhost:1976';}return\"http://\".concat(currentHost,\":1976\");}// In production, use the same origin\nreturn window.location.origin;};const newSocket=io(getSocketUrl(),{transports:['websocket','polling']});// Handle connection\nnewSocket.on('connect',()=>{console.log('Connected to Socket.IO server');setConnected(true);// Authenticate with the server\nconst token=localStorage.getItem('token');if(token){newSocket.emit('authenticate',token);}});// Handle disconnection\nnewSocket.on('disconnect',()=>{console.log('Disconnected from Socket.IO server');setConnected(false);});// Handle authentication errors\nnewSocket.on('auth_error',error=>{console.error('Socket authentication error:',error);});// Handle permission updates\nnewSocket.on('permission_updated',async data=>{console.log('Permission update received:',data);// Automatically refresh user permissions\nconst success=await refreshUserPermissions();if(success){console.log('User permissions refreshed automatically');// Show a notification to the user\nif(window.showPermissionUpdateNotification){window.showPermissionUpdateNotification('Your permissions have been updated automatically!');}// Force a page reload to update the UI immediately\nsetTimeout(()=>{window.location.reload();},1000);}});// Handle session termination\nnewSocket.on('session_terminated',data=>{console.log('Session terminated:',data);// Show alert to user\nalert(data.message||'Your session has been terminated because you logged in from another device.');// Logout the user\nlogout();// Redirect to login page\nwindow.location.href='/login';});setSocket(newSocket);// Cleanup on unmount\nreturn()=>{console.log('Cleaning up socket connection');newSocket.disconnect();};}else{// Disconnect if user is not authenticated\nif(socket){socket.disconnect();setSocket(null);setConnected(false);}}},[user,refreshUserPermissions]);const value={socket,connected};return/*#__PURE__*/_jsx(SocketContext.Provider,{value:value,children:children});};export default SocketContext;", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "io", "useAuth", "jsx", "_jsx", "SocketContext", "useSocket", "context", "Error", "SocketProvider", "_ref", "children", "socket", "setSocket", "connected", "setConnected", "user", "refreshUserPermissions", "logout", "empCode", "console", "log", "getSocketUrl", "process", "env", "NODE_ENV", "currentHost", "window", "location", "hostname", "concat", "origin", "newSocket", "transports", "on", "token", "localStorage", "getItem", "emit", "error", "data", "success", "showPermissionUpdateNotification", "setTimeout", "reload", "alert", "message", "href", "disconnect", "value", "Provider"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/contexts/SocketContext.js"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { io } from 'socket.io-client';\nimport { useAuth } from './AuthContext';\n\nconst SocketContext = createContext(null);\n\nexport const useSocket = () => {\n  const context = useContext(SocketContext);\n  if (!context) {\n    throw new Error('useSocket must be used within a SocketProvider');\n  }\n  return context;\n};\n\nexport const SocketProvider = ({ children }) => {\n  const [socket, setSocket] = useState(null);\n  const [connected, setConnected] = useState(false);\n  const { user, refreshUserPermissions, logout } = useAuth();\n\n  useEffect(() => {\n    // Only connect if user is authenticated\n    if (user && user.empCode) {\n      console.log('Connecting to Socket.IO server...');\n      \n      // Create socket connection to the backend server\n      const getSocketUrl = () => {\n        // In development, connect to the backend server directly\n        if (process.env.NODE_ENV === 'development') {\n          const currentHost = window.location.hostname;\n          if (currentHost === 'localhost' || currentHost === '127.0.0.1') {\n            return 'http://localhost:1976';\n          }\n          return `http://${currentHost}:1976`;\n        }\n        // In production, use the same origin\n        return window.location.origin;\n      };\n\n      const newSocket = io(getSocketUrl(), {\n        transports: ['websocket', 'polling']\n      });\n\n      // Handle connection\n      newSocket.on('connect', () => {\n        console.log('Connected to Socket.IO server');\n        setConnected(true);\n        \n        // Authenticate with the server\n        const token = localStorage.getItem('token');\n        if (token) {\n          newSocket.emit('authenticate', token);\n        }\n      });\n\n      // Handle disconnection\n      newSocket.on('disconnect', () => {\n        console.log('Disconnected from Socket.IO server');\n        setConnected(false);\n      });\n\n      // Handle authentication errors\n      newSocket.on('auth_error', (error) => {\n        console.error('Socket authentication error:', error);\n      });\n\n      // Handle permission updates\n      newSocket.on('permission_updated', async (data) => {\n        console.log('Permission update received:', data);\n\n        // Automatically refresh user permissions\n        const success = await refreshUserPermissions();\n        if (success) {\n          console.log('User permissions refreshed automatically');\n\n          // Show a notification to the user\n          if (window.showPermissionUpdateNotification) {\n            window.showPermissionUpdateNotification('Your permissions have been updated automatically!');\n          }\n\n          // Force a page reload to update the UI immediately\n          setTimeout(() => {\n            window.location.reload();\n          }, 1000);\n        }\n      });\n\n      // Handle session termination\n      newSocket.on('session_terminated', (data) => {\n        console.log('Session terminated:', data);\n\n        // Show alert to user\n        alert(data.message || 'Your session has been terminated because you logged in from another device.');\n\n        // Logout the user\n        logout();\n\n        // Redirect to login page\n        window.location.href = '/login';\n      });\n\n      setSocket(newSocket);\n\n      // Cleanup on unmount\n      return () => {\n        console.log('Cleaning up socket connection');\n        newSocket.disconnect();\n      };\n    } else {\n      // Disconnect if user is not authenticated\n      if (socket) {\n        socket.disconnect();\n        setSocket(null);\n        setConnected(false);\n      }\n    }\n  }, [user, refreshUserPermissions]);\n\n  const value = {\n    socket,\n    connected\n  };\n\n  return (\n    <SocketContext.Provider value={value}>\n      {children}\n    </SocketContext.Provider>\n  );\n};\n\nexport default SocketContext;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC7E,OAASC,EAAE,KAAQ,kBAAkB,CACrC,OAASC,OAAO,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAExC,KAAM,CAAAC,aAAa,cAAGR,aAAa,CAAC,IAAI,CAAC,CAEzC,MAAO,MAAM,CAAAS,SAAS,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,OAAO,CAAGT,UAAU,CAACO,aAAa,CAAC,CACzC,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,gDAAgD,CAAC,CACnE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED,MAAO,MAAM,CAAAE,cAAc,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACzC,KAAM,CAACE,MAAM,CAAEC,SAAS,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAEgB,IAAI,CAAEC,sBAAsB,CAAEC,MAAO,CAAC,CAAGhB,OAAO,CAAC,CAAC,CAE1DH,SAAS,CAAC,IAAM,CACd;AACA,GAAIiB,IAAI,EAAIA,IAAI,CAACG,OAAO,CAAE,CACxBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAEhD;AACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB;AACA,GAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1C,KAAM,CAAAC,WAAW,CAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAC5C,GAAIH,WAAW,GAAK,WAAW,EAAIA,WAAW,GAAK,WAAW,CAAE,CAC9D,MAAO,uBAAuB,CAChC,CACA,gBAAAI,MAAA,CAAiBJ,WAAW,UAC9B,CACA;AACA,MAAO,CAAAC,MAAM,CAACC,QAAQ,CAACG,MAAM,CAC/B,CAAC,CAED,KAAM,CAAAC,SAAS,CAAG/B,EAAE,CAACqB,YAAY,CAAC,CAAC,CAAE,CACnCW,UAAU,CAAE,CAAC,WAAW,CAAE,SAAS,CACrC,CAAC,CAAC,CAEF;AACAD,SAAS,CAACE,EAAE,CAAC,SAAS,CAAE,IAAM,CAC5Bd,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC,CAC5CN,YAAY,CAAC,IAAI,CAAC,CAElB;AACA,KAAM,CAAAoB,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,CAAE,CACTH,SAAS,CAACM,IAAI,CAAC,cAAc,CAAEH,KAAK,CAAC,CACvC,CACF,CAAC,CAAC,CAEF;AACAH,SAAS,CAACE,EAAE,CAAC,YAAY,CAAE,IAAM,CAC/Bd,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CACjDN,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAC,CAEF;AACAiB,SAAS,CAACE,EAAE,CAAC,YAAY,CAAGK,KAAK,EAAK,CACpCnB,OAAO,CAACmB,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CAAC,CAAC,CAEF;AACAP,SAAS,CAACE,EAAE,CAAC,oBAAoB,CAAE,KAAO,CAAAM,IAAI,EAAK,CACjDpB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAEmB,IAAI,CAAC,CAEhD;AACA,KAAM,CAAAC,OAAO,CAAG,KAAM,CAAAxB,sBAAsB,CAAC,CAAC,CAC9C,GAAIwB,OAAO,CAAE,CACXrB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CAEvD;AACA,GAAIM,MAAM,CAACe,gCAAgC,CAAE,CAC3Cf,MAAM,CAACe,gCAAgC,CAAC,mDAAmD,CAAC,CAC9F,CAEA;AACAC,UAAU,CAAC,IAAM,CACfhB,MAAM,CAACC,QAAQ,CAACgB,MAAM,CAAC,CAAC,CAC1B,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CAAC,CAAC,CAEF;AACAZ,SAAS,CAACE,EAAE,CAAC,oBAAoB,CAAGM,IAAI,EAAK,CAC3CpB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEmB,IAAI,CAAC,CAExC;AACAK,KAAK,CAACL,IAAI,CAACM,OAAO,EAAI,6EAA6E,CAAC,CAEpG;AACA5B,MAAM,CAAC,CAAC,CAER;AACAS,MAAM,CAACC,QAAQ,CAACmB,IAAI,CAAG,QAAQ,CACjC,CAAC,CAAC,CAEFlC,SAAS,CAACmB,SAAS,CAAC,CAEpB;AACA,MAAO,IAAM,CACXZ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC,CAC5CW,SAAS,CAACgB,UAAU,CAAC,CAAC,CACxB,CAAC,CACH,CAAC,IAAM,CACL;AACA,GAAIpC,MAAM,CAAE,CACVA,MAAM,CAACoC,UAAU,CAAC,CAAC,CACnBnC,SAAS,CAAC,IAAI,CAAC,CACfE,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CACF,CAAC,CAAE,CAACC,IAAI,CAAEC,sBAAsB,CAAC,CAAC,CAElC,KAAM,CAAAgC,KAAK,CAAG,CACZrC,MAAM,CACNE,SACF,CAAC,CAED,mBACEV,IAAA,CAACC,aAAa,CAAC6C,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAtC,QAAA,CAClCA,QAAQ,CACa,CAAC,CAE7B,CAAC,CAED,cAAe,CAAAN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}