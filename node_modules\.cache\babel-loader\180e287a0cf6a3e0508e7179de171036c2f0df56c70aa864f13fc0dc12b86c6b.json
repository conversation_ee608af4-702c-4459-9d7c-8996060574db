{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useForkRef } from '@mui/material/utils';\nimport { WrapperVariantContext } from './WrapperVariantContext';\nimport { PickersPopper } from '../PickersPopper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function DesktopWrapper(props) {\n  const {\n    children,\n    DateInputProps,\n    KeyboardDateInputComponent,\n    onClear,\n    onDismiss,\n    onCancel,\n    onAccept,\n    onSetToday,\n    open,\n    PopperProps,\n    PaperProps,\n    TransitionComponent,\n    components,\n    componentsProps\n  } = props;\n  const ownInputRef = React.useRef(null);\n  const inputRef = useForkRef(DateInputProps.inputRef, ownInputRef);\n  return /*#__PURE__*/_jsxs(WrapperVariantContext.Provider, {\n    value: \"desktop\",\n    children: [/*#__PURE__*/_jsx(KeyboardDateInputComponent, _extends({}, DateInputProps, {\n      inputRef: inputRef\n    })), /*#__PURE__*/_jsx(PickersPopper, {\n      role: \"dialog\",\n      open: open,\n      anchorEl: ownInputRef.current,\n      TransitionComponent: TransitionComponent,\n      PopperProps: PopperProps,\n      PaperProps: PaperProps,\n      onClose: onDismiss,\n      onCancel: onCancel,\n      onClear: onClear,\n      onAccept: onAccept,\n      onSetToday: onSetToday,\n      components: components,\n      componentsProps: componentsProps,\n      children: children\n    })]\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "useForkRef", "WrapperVariantContext", "PickersPopper", "jsx", "_jsx", "jsxs", "_jsxs", "DesktopWrapper", "props", "children", "DateInputProps", "KeyboardDateInputComponent", "onClear", "on<PERSON><PERSON><PERSON>", "onCancel", "onAccept", "onSetToday", "open", "PopperProps", "PaperProps", "TransitionComponent", "components", "componentsProps", "ownInputRef", "useRef", "inputRef", "Provider", "value", "role", "anchorEl", "current", "onClose"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/components/wrappers/DesktopWrapper.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useForkRef } from '@mui/material/utils';\nimport { WrapperVariantContext } from './WrapperVariantContext';\nimport { PickersPopper } from '../PickersPopper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function DesktopWrapper(props) {\n  const {\n    children,\n    DateInputProps,\n    KeyboardDateInputComponent,\n    onClear,\n    onDismiss,\n    onCancel,\n    onAccept,\n    onSetToday,\n    open,\n    PopperProps,\n    PaperProps,\n    TransitionComponent,\n    components,\n    componentsProps\n  } = props;\n  const ownInputRef = React.useRef(null);\n  const inputRef = useForkRef(DateInputProps.inputRef, ownInputRef);\n  return /*#__PURE__*/_jsxs(WrapperVariantContext.Provider, {\n    value: \"desktop\",\n    children: [/*#__PURE__*/_jsx(KeyboardDateInputComponent, _extends({}, DateInputProps, {\n      inputRef: inputRef\n    })), /*#__PURE__*/_jsx(PickersPopper, {\n      role: \"dialog\",\n      open: open,\n      anchorEl: ownInputRef.current,\n      TransitionComponent: TransitionComponent,\n      PopperProps: PopperProps,\n      PaperProps: PaperProps,\n      onClose: onDismiss,\n      onCancel: onCancel,\n      onClear: onClear,\n      onAccept: onAccept,\n      onSetToday: onSetToday,\n      components: components,\n      componentsProps: componentsProps,\n      children: children\n    })]\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,MAAM;IACJC,QAAQ;IACRC,cAAc;IACdC,0BAA0B;IAC1BC,OAAO;IACPC,SAAS;IACTC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,IAAI;IACJC,WAAW;IACXC,UAAU;IACVC,mBAAmB;IACnBC,UAAU;IACVC;EACF,CAAC,GAAGd,KAAK;EACT,MAAMe,WAAW,GAAGxB,KAAK,CAACyB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMC,QAAQ,GAAGzB,UAAU,CAACU,cAAc,CAACe,QAAQ,EAAEF,WAAW,CAAC;EACjE,OAAO,aAAajB,KAAK,CAACL,qBAAqB,CAACyB,QAAQ,EAAE;IACxDC,KAAK,EAAE,SAAS;IAChBlB,QAAQ,EAAE,CAAC,aAAaL,IAAI,CAACO,0BAA0B,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEY,cAAc,EAAE;MACpFe,QAAQ,EAAEA;IACZ,CAAC,CAAC,CAAC,EAAE,aAAarB,IAAI,CAACF,aAAa,EAAE;MACpC0B,IAAI,EAAE,QAAQ;MACdX,IAAI,EAAEA,IAAI;MACVY,QAAQ,EAAEN,WAAW,CAACO,OAAO;MAC7BV,mBAAmB,EAAEA,mBAAmB;MACxCF,WAAW,EAAEA,WAAW;MACxBC,UAAU,EAAEA,UAAU;MACtBY,OAAO,EAAElB,SAAS;MAClBC,QAAQ,EAAEA,QAAQ;MAClBF,OAAO,EAAEA,OAAO;MAChBG,QAAQ,EAAEA,QAAQ;MAClBC,UAAU,EAAEA,UAAU;MACtBK,UAAU,EAAEA,UAAU;MACtBC,eAAe,EAAEA,eAAe;MAChCb,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}