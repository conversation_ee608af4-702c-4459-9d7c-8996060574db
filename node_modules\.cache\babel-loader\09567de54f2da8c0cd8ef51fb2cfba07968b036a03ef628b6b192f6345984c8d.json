{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\pages\\\\Dashboard.js\",\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, CircularProgress, Alert, useTheme, List, ListItem, ListItemText, ListItemIcon, Divider, Paper, Chip, LinearProgress, useMediaQuery, Button } from '@mui/material';\nimport { Assignment as ComplaintsIcon, CheckCircle as ResolvedIcon, Pending as PendingIcon, Error as HighPriorityIcon, FiberManualRecord as StatusIcon, Schedule as TimeIcon, Speed as EfficiencyIcon, Timeline as TrendIcon, AccessTime as ResolutionIcon } from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport axios from '../utils/axiosConfig';\nimport { format, formatDistanceToNow } from 'date-fns';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatCard = ({\n  title,\n  value,\n  icon,\n  color,\n  loading,\n  subtitle\n}) => {\n  _s2();\n  var _s = $RefreshSig$();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const MonthlyStatCard = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_s(({\n    title,\n    value,\n    icon,\n    color,\n    loading,\n    subtitle,\n    index\n  }) => {\n    _s();\n    const theme = useTheme();\n    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          height: 120,\n          background: 'rgba(255,255,255,0.15)',\n          backdropFilter: 'blur(10px)',\n          border: '1px solid rgba(255,255,255,0.2)',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            height: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24,\n            sx: {\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 7\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        scale: 0.9\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      transition: {\n        delay: index * 0.1,\n        duration: 0.3,\n        ease: \"easeOut\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          height: 120,\n          background: 'rgba(255,255,255,0.15)',\n          backdropFilter: 'blur(10px)',\n          border: '1px solid rgba(255,255,255,0.2)',\n          color: 'white',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease',\n          '&:hover': {\n            background: 'rgba(255,255,255,0.25)',\n            transform: 'translateY(-4px)',\n            boxShadow: '0 8px 25px rgba(0,0,0,0.3)'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'space-between',\n            height: '100%',\n            p: 2,\n            '&:last-child': {\n              pb: 2\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'flex-start'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: isMobile ? \"body2\" : \"body1\",\n                sx: {\n                  opacity: 0.9,\n                  fontWeight: 500,\n                  fontSize: '0.875rem'\n                },\n                children: title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 15\n              }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  opacity: 0.7,\n                  fontSize: '0.75rem',\n                  display: 'block'\n                },\n                children: subtitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                opacity: 0.8,\n                fontSize: isMobile ? '1.2rem' : '1.5rem'\n              },\n              children: icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: isMobile ? \"h6\" : \"h5\",\n            sx: {\n              fontWeight: 700,\n              fontSize: isMobile ? '1.25rem' : '1.5rem',\n              textShadow: '0px 2px 4px rgba(0,0,0,0.3)'\n            },\n            children: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 5\n    }, this);\n  }, \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n    return [useTheme, useMediaQuery];\n  })), \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n    return [useTheme, useMediaQuery];\n  });\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      type: \"spring\",\n      stiffness: 100,\n      damping: 15,\n      duration: 0.6\n    },\n    whileHover: {\n      scale: 1.02,\n      transition: {\n        duration: 0.2\n      }\n    },\n    whileTap: {\n      scale: 0.98\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        height: '100%',\n        background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\n        color: 'white',\n        position: 'relative',\n        overflow: 'hidden',\n        boxShadow: theme.shadows[loading ? 0 : 2],\n        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n        '&:hover': {\n          boxShadow: theme.shadows[4],\n          transform: 'translateY(-4px)'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: isMobile ? 2 : 3,\n          height: '100%',\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'relative',\n            zIndex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              delay: 0.2,\n              type: \"spring\",\n              stiffness: 120\n            },\n            children: /*#__PURE__*/React.cloneElement(icon, {\n              sx: {\n                fontSize: isMobile ? 32 : 48,\n                opacity: 0.9,\n                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"h5\" : \"h4\",\n              component: \"div\",\n              sx: {\n                fontWeight: 700,\n                lineHeight: 1.2,\n                mb: 0.5,\n                textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\n              },\n              children: loading ? /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0\n                },\n                animate: {\n                  opacity: 1\n                },\n                transition: {\n                  duration: 0.5\n                },\n                children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: isMobile ? 20 : 24,\n                  color: \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 10\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3,\n                  duration: 0.5\n                },\n                children: value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"body2\" : \"body1\",\n              sx: {\n                opacity: 0.9,\n                fontWeight: 500,\n                letterSpacing: '0.5px',\n                textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\n              },\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                opacity: 0.8,\n                fontWeight: 400,\n                textShadow: '0px 1px 2px rgba(0,0,0,0.2)',\n                display: 'block'\n              },\n              children: subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            scale: 0.5\n          },\n          animate: {\n            opacity: 0.15,\n            scale: 2\n          },\n          transition: {\n            delay: 0.4,\n            duration: 0.8\n          },\n          sx: {\n            position: 'absolute',\n            right: -20,\n            bottom: -20,\n            filter: 'blur(2px)'\n          },\n          children: /*#__PURE__*/React.cloneElement(icon, {\n            sx: {\n              fontSize: isMobile ? 100 : 140\n            }\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n\n// Memoized color functions to prevent recalculation\n_s2(StatCard, \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = StatCard;\nconst getStatusColor = (status, theme) => {\n  const statusColors = {\n    'New': theme.palette.info.main,\n    'Assigned': theme.palette.warning.main,\n    'In Progress': theme.palette.warning.dark,\n    'Resolved': theme.palette.success.main,\n    'Rejected': theme.palette.error.main\n  };\n  return statusColors[status] || theme.palette.grey[500];\n};\nconst getPriorityColor = (priority, theme) => {\n  const priorityColors = {\n    'Low': theme.palette.success.main,\n    'Medium': theme.palette.warning.main,\n    'High': theme.palette.error.main,\n    'Critical': theme.palette.error.dark\n  };\n  return priorityColors[priority] || theme.palette.grey[500];\n};\n\n// Optimized timestamp formatting function\nconst formatTimestamp = timestamp => {\n  if (!timestamp) return 'N/A';\n  try {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = Math.abs(now - date) / (1000 * 60 * 60);\n\n    // If less than 24 hours, show time\n    if (diffInHours < 24) {\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    }\n    // If less than 7 days, show day and time\n    else if (diffInHours < 168) {\n      return date.toLocaleDateString('en-US', {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    }\n    // Otherwise show full date and time\n    else {\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    }\n  } catch (error) {\n    console.error('Error formatting timestamp:', error);\n    return 'Invalid date';\n  }\n};\nconst ActivityItem = /*#__PURE__*/_s3(/*#__PURE__*/React.memo(_c2 = _s3(({\n  activity,\n  index\n}) => {\n  _s3();\n  const theme = useTheme();\n\n  // Use activityTimestamp if available, otherwise fall back to submissionDate\n  // The backend already handles this logic correctly\n  const displayTimestamp = activity.activityTimestamp || activity.submissionDate;\n\n  // Memoize formatted timestamp\n  const formattedTimestamp = useMemo(() => formatTimestamp(displayTimestamp), [displayTimestamp]);\n\n  // Memoize colors\n  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);\n  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      x: -20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    transition: {\n      delay: Math.min(index * 0.05, 0.3),\n      // Reduced delay for better performance\n      duration: 0.3,\n      // Reduced duration\n      ease: \"easeOut\"\n    },\n    children: /*#__PURE__*/_jsxDEV(ListItem, {\n      sx: {\n        bgcolor: 'background.paper',\n        borderRadius: 2,\n        mb: 1,\n        boxShadow: 1,\n        '&:hover': {\n          bgcolor: 'action.hover',\n          transform: 'translateX(4px)',\n          transition: 'transform 0.15s ease' // Faster transition\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(StatusIcon, {\n          sx: {\n            color: statusColor\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            mb: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 500,\n              flex: 1\n            },\n            children: [\"#\", activity.ComplaintNumber, \" - \", activity.description]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this), activity.Priority && /*#__PURE__*/_jsxDEV(Chip, {\n            label: activity.Priority,\n            size: \"small\",\n            sx: {\n              bgcolor: `${priorityColor}15`,\n              color: priorityColor,\n              fontWeight: 500,\n              fontSize: '0.7rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this),\n        secondary: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 0.5\n          },\n          children: [activity.activityDetails && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 0.5\n            },\n            children: activity.activityDetails\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              label: activity.Status,\n              size: \"small\",\n              sx: {\n                bgcolor: `${statusColor}15`,\n                color: statusColor,\n                fontWeight: 500\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this), activity.Category && /*#__PURE__*/_jsxDEV(Chip, {\n              label: activity.Category,\n              size: \"small\",\n              variant: \"outlined\",\n              sx: {\n                fontSize: '0.7rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: formattedTimestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 364,\n    columnNumber: 5\n  }, this);\n}, \"0XhO5jXSGOZ751H0Lnx1g3U0Ar4=\", false, function () {\n  return [useTheme];\n})), \"0XhO5jXSGOZ751H0Lnx1g3U0Ar4=\", false, function () {\n  return [useTheme];\n});\n_c3 = ActivityItem;\nfunction Dashboard() {\n  _s4();\n  const [stats, setStats] = useState(null);\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n\n      // Fetch data with optimized timeout and caching\n      const [statsResponse, activitiesResponse] = await Promise.all([axios.get('/api/dashboard/stats', {\n        timeout: 10000,\n        // 10 second timeout\n        headers: {\n          'Cache-Control': 'max-age=60' // Cache for 1 minute\n        }\n      }), axios.get('/api/dashboard/recent-activities', {\n        timeout: 10000,\n        // 10 second timeout\n        headers: {\n          'Cache-Control': 'max-age=30' // Cache for 30 seconds\n        }\n      })]);\n      setStats(statsResponse.data);\n      setActivities(activitiesResponse.data);\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching dashboard data:', err);\n      setError('Failed to load dashboard data. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n  const statCards = useMemo(() => [{\n    title: 'Total Complaints',\n    value: (stats === null || stats === void 0 ? void 0 : stats.totalComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(ComplaintsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 13\n    }, this),\n    color: 'primary'\n  }, {\n    title: 'Resolved',\n    value: (stats === null || stats === void 0 ? void 0 : stats.resolvedComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(ResolvedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 13\n    }, this),\n    color: 'success'\n  }, {\n    title: 'Pending',\n    value: (stats === null || stats === void 0 ? void 0 : stats.pendingComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(PendingIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 13\n    }, this),\n    color: 'warning'\n  }, {\n    title: 'High Priority',\n    value: (stats === null || stats === void 0 ? void 0 : stats.highPriorityComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(HighPriorityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 13\n    }, this),\n    color: 'error'\n  }], [stats]);\n  const monthlyStatCards = useMemo(() => {\n    if (!(stats !== null && stats !== void 0 && stats.monthlyStats)) return [];\n    return [{\n      title: 'This Month',\n      value: stats.monthlyStats.totalMonthlyComplaints || 0,\n      icon: /*#__PURE__*/_jsxDEV(ComplaintsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 15\n      }, this),\n      color: 'info',\n      subtitle: 'New complaints'\n    }, {\n      title: 'Resolution Rate',\n      value: `${stats.monthlyStats.resolutionRate || 0}%`,\n      icon: /*#__PURE__*/_jsxDEV(ResolvedIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 15\n      }, this),\n      color: 'success',\n      subtitle: 'Monthly average'\n    }, {\n      title: 'Response Time',\n      value: `${stats.monthlyStats.responseEfficiency || 0}%`,\n      icon: /*#__PURE__*/_jsxDEV(TimeIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 15\n      }, this),\n      color: 'warning',\n      subtitle: 'Within 24h'\n    }, {\n      title: 'Avg Resolution',\n      value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,\n      icon: /*#__PURE__*/_jsxDEV(ResolutionIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 15\n      }, this),\n      color: 'primary',\n      subtitle: 'Hours to resolve'\n    }];\n  }, [stats]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: {\n        xs: 2,\n        sm: 3\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      sx: {\n        mb: 4,\n        fontWeight: 600\n      },\n      children: \"Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      action: /*#__PURE__*/_jsxDEV(motion.div, {\n        whileHover: {\n          scale: 1.05\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          size: \"small\",\n          onClick: fetchDashboardData,\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 13\n      }, this),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [statCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          ...card,\n          loading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this)\n      }, card.title, false, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 11\n      }, this)), monthlyStatCards.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.2,\n            duration: 0.3\n          },\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            overflow: 'visible',\n            position: 'relative',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(255,255,255,0.1)',\n              backdropFilter: 'blur(10px)',\n              borderRadius: 'inherit',\n              zIndex: 0\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              position: 'relative',\n              zIndex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 3,\n                fontWeight: 600,\n                color: 'white'\n              },\n              children: \"\\uD83D\\uDCCA Monthly Performance Insights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: monthlyStatCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(MonthlyStatCard, {\n                  ...card,\n                  loading: loading,\n                  index: index\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 23\n                }, this)\n              }, card.title, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.4\n          },\n          sx: {\n            overflow: 'visible',\n            height: '100%',\n            minHeight: 400\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600\n              },\n              children: \"Recent Activities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                p: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 17\n            }, this) : activities.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n              sx: {\n                p: 0\n              },\n              children: activities.slice(0, 8).map((activity, index) => /*#__PURE__*/_jsxDEV(ActivityItem, {\n                activity: activity,\n                index: index\n              }, `${activity.ComplaintId}-${activity.Status}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                py: 4,\n                color: 'text.secondary'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                children: \"No recent activities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 552,\n    columnNumber: 5\n  }, this);\n}\n_s4(Dashboard, \"NT4e200PVgwxttbR+vOjVkpb2PA=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c4 = Dashboard;\nexport default Dashboard;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"StatCard\");\n$RefreshReg$(_c2, \"ActivityItem$React.memo\");\n$RefreshReg$(_c3, \"ActivityItem\");\n$RefreshReg$(_c4, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "CircularProgress", "<PERSON><PERSON>", "useTheme", "List", "ListItem", "ListItemText", "ListItemIcon", "Divider", "Paper", "Chip", "LinearProgress", "useMediaQuery", "<PERSON><PERSON>", "Assignment", "ComplaintsIcon", "CheckCircle", "ResolvedIcon", "Pending", "PendingIcon", "Error", "HighPriorityIcon", "FiberManualRecord", "StatusIcon", "Schedule", "TimeIcon", "Speed", "EfficiencyIcon", "Timeline", "TrendIcon", "AccessTime", "ResolutionIcon", "motion", "AnimatePresence", "axios", "format", "formatDistanceToNow", "jsxDEV", "_jsxDEV", "StatCard", "title", "value", "icon", "color", "loading", "subtitle", "_s2", "_s", "$RefreshSig$", "theme", "isMobile", "breakpoints", "down", "MonthlyStatCard", "memo", "index", "sx", "height", "background", "<PERSON><PERSON>ilter", "border", "children", "display", "alignItems", "justifyContent", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "scale", "animate", "transition", "delay", "duration", "ease", "cursor", "transform", "boxShadow", "flexDirection", "p", "pb", "variant", "fontWeight", "fontSize", "textShadow", "y", "type", "stiffness", "damping", "whileHover", "whileTap", "palette", "main", "dark", "position", "overflow", "shadows", "zIndex", "gap", "cloneElement", "filter", "component", "lineHeight", "mb", "letterSpacing", "right", "bottom", "_c", "getStatusColor", "status", "statusColors", "info", "warning", "success", "error", "grey", "getPriorityColor", "priority", "priorityColors", "formatTimestamp", "timestamp", "date", "Date", "now", "diffInHours", "Math", "abs", "toLocaleTimeString", "hour", "minute", "hour12", "toLocaleDateString", "weekday", "month", "day", "year", "console", "ActivityItem", "_s3", "_c2", "activity", "displayTimestamp", "activityTimestamp", "submissionDate", "formattedTimestamp", "statusColor", "Status", "priorityColor", "Priority", "x", "min", "bgcolor", "borderRadius", "primary", "flex", "ComplaintNumber", "description", "label", "secondary", "mt", "activityDetails", "flexWrap", "Category", "_c3", "Dashboard", "_s4", "stats", "setStats", "activities", "setActivities", "setLoading", "setError", "fetchDashboardData", "statsResponse", "activitiesResponse", "Promise", "all", "get", "timeout", "headers", "data", "err", "statCards", "totalComplaints", "resolvedComplaints", "pendingComplaints", "highPriorityComplaints", "monthlyStatCards", "monthlyStats", "totalMonthlyComplaints", "resolutionRate", "responseEfficiency", "round", "avgResolutionHours", "xs", "sm", "severity", "action", "onClick", "container", "spacing", "map", "card", "item", "md", "length", "content", "top", "left", "minHeight", "slice", "ComplaintId", "textAlign", "py", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport {\r\n  Box,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  CircularProgress,\r\n  Alert,\r\n  useTheme,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Divider,\r\n  Paper,\r\n  Chip,\r\n  LinearProgress,\r\n  useMediaQuery,\r\n  Button,\r\n} from '@mui/material';\r\nimport {\r\n  Assignment as ComplaintsIcon,\r\n  CheckCircle as ResolvedIcon,\r\n  Pending as PendingIcon,\r\n  Error as HighPriorityIcon,\r\n  FiberManualRecord as StatusIcon,\r\n  Schedule as TimeIcon,\r\n  Speed as EfficiencyIcon,\r\n  Timeline as TrendIcon,\r\n  AccessTime as ResolutionIcon,\r\n} from '@mui/icons-material';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport axios from '../utils/axiosConfig';\r\nimport { format, formatDistanceToNow } from 'date-fns';\r\n\r\nconst StatCard = ({ title, value, icon, color, loading, subtitle }) => {\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\nconst MonthlyStatCard = React.memo(({ title, value, icon, color, loading, subtitle, index }) => {\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\n  if (loading) {\r\n    return (\r\n      <Card sx={{\r\n        height: 120,\r\n        background: 'rgba(255,255,255,0.15)',\r\n        backdropFilter: 'blur(10px)',\r\n        border: '1px solid rgba(255,255,255,0.2)',\r\n        color: 'white'\r\n      }}>\r\n        <CardContent sx={{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          height: '100%'\r\n        }}>\r\n          <CircularProgress size={24} sx={{ color: 'white' }} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, scale: 0.9 }}\r\n      animate={{ opacity: 1, scale: 1 }}\r\n      transition={{\r\n        delay: index * 0.1,\r\n        duration: 0.3,\r\n        ease: \"easeOut\"\r\n      }}\r\n    >\r\n      <Card\r\n        sx={{\r\n          height: 120,\r\n          background: 'rgba(255,255,255,0.15)',\r\n          backdropFilter: 'blur(10px)',\r\n          border: '1px solid rgba(255,255,255,0.2)',\r\n          color: 'white',\r\n          cursor: 'pointer',\r\n          transition: 'all 0.2s ease',\r\n          '&:hover': {\r\n            background: 'rgba(255,255,255,0.25)',\r\n            transform: 'translateY(-4px)',\r\n            boxShadow: '0 8px 25px rgba(0,0,0,0.3)',\r\n          },\r\n        }}\r\n      >\r\n        <CardContent sx={{\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'space-between',\r\n          height: '100%',\r\n          p: 2,\r\n          '&:last-child': { pb: 2 }\r\n        }}>\r\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\r\n            <Box>\r\n              <Typography\r\n                variant={isMobile ? \"body2\" : \"body1\"}\r\n                sx={{\r\n                  opacity: 0.9,\r\n                  fontWeight: 500,\r\n                  fontSize: '0.875rem'\r\n                }}\r\n              >\r\n                {title}\r\n              </Typography>\r\n              {subtitle && (\r\n                <Typography\r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    opacity: 0.7,\r\n                    fontSize: '0.75rem',\r\n                    display: 'block'\r\n                  }}\r\n                >\r\n                  {subtitle}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n            <Box sx={{\r\n              opacity: 0.8,\r\n              fontSize: isMobile ? '1.2rem' : '1.5rem'\r\n            }}>\r\n              {icon}\r\n            </Box>\r\n          </Box>\r\n\r\n          <Typography\r\n            variant={isMobile ? \"h6\" : \"h5\"}\r\n            sx={{\r\n              fontWeight: 700,\r\n              fontSize: isMobile ? '1.25rem' : '1.5rem',\r\n              textShadow: '0px 2px 4px rgba(0,0,0,0.3)'\r\n            }}\r\n          >\r\n            {value}\r\n          </Typography>\r\n        </CardContent>\r\n      </Card>\r\n    </motion.div>\r\n  );\r\n});\r\n  \r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ \r\n        type: \"spring\",\r\n        stiffness: 100,\r\n        damping: 15,\r\n        duration: 0.6 \r\n      }}\r\n      whileHover={{ \r\n        scale: 1.02,\r\n        transition: { duration: 0.2 }\r\n      }}\r\n      whileTap={{ scale: 0.98 }}\r\n    >\r\n      <Card\r\n        sx={{\r\n          height: '100%',\r\n          background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\r\n          color: 'white',\r\n          position: 'relative',\r\n          overflow: 'hidden',\r\n          boxShadow: theme.shadows[loading ? 0 : 2],\r\n          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\r\n          '&:hover': {\r\n            boxShadow: theme.shadows[4],\r\n            transform: 'translateY(-4px)',\r\n          },\r\n        }}\r\n      >\r\n        <CardContent sx={{ \r\n          p: isMobile ? 2 : 3,\r\n          height: '100%',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'space-between'\r\n        }}>\r\n          <Box sx={{ \r\n            position: 'relative', \r\n            zIndex: 1,\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 2\r\n          }}>\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 120 }}\r\n            >\r\n              {React.cloneElement(icon, { \r\n                sx: { \r\n                  fontSize: isMobile ? 32 : 48,\r\n                  opacity: 0.9,\r\n                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\r\n                } \r\n              })}\r\n            </motion.div>\r\n            <Box>\r\n              <Typography \r\n                variant={isMobile ? \"h5\" : \"h4\"} \r\n                component=\"div\" \r\n                sx={{ \r\n                  fontWeight: 700,\r\n                  lineHeight: 1.2,\r\n                  mb: 0.5,\r\n                  textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {loading ? (\r\n                  <motion.div\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    transition={{ duration: 0.5 }}\r\n                  >\r\n                    <CircularProgress size={isMobile ? 20 : 24} color=\"inherit\" />\r\n                  </motion.div>\r\n                ) : (\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.3, duration: 0.5 }}\r\n                  >\r\n                    {value}\r\n                  </motion.div>\r\n                )}\r\n              </Typography>\r\n              <Typography\r\n                variant={isMobile ? \"body2\" : \"body1\"}\r\n                sx={{\r\n                  opacity: 0.9,\r\n                  fontWeight: 500,\r\n                  letterSpacing: '0.5px',\r\n                  textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {title}\r\n              </Typography>\r\n              {subtitle && (\r\n                <Typography\r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    opacity: 0.8,\r\n                    fontWeight: 400,\r\n                    textShadow: '0px 1px 2px rgba(0,0,0,0.2)',\r\n                    display: 'block'\r\n                  }}\r\n                >\r\n                  {subtitle}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n          </Box>\r\n          <Box\r\n            component={motion.div}\r\n            initial={{ opacity: 0, scale: 0.5 }}\r\n            animate={{ opacity: 0.15, scale: 2 }}\r\n            transition={{ delay: 0.4, duration: 0.8 }}\r\n            sx={{\r\n              position: 'absolute',\r\n              right: -20,\r\n              bottom: -20,\r\n              filter: 'blur(2px)'\r\n            }}\r\n          >\r\n            {React.cloneElement(icon, { \r\n              sx: { fontSize: isMobile ? 100 : 140 }\r\n            })}\r\n          </Box>\r\n        </CardContent>\r\n      </Card>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\n// Memoized color functions to prevent recalculation\r\nconst getStatusColor = (status, theme) => {\r\n  const statusColors = {\r\n    'New': theme.palette.info.main,\r\n    'Assigned': theme.palette.warning.main,\r\n    'In Progress': theme.palette.warning.dark,\r\n    'Resolved': theme.palette.success.main,\r\n    'Rejected': theme.palette.error.main,\r\n  };\r\n  return statusColors[status] || theme.palette.grey[500];\r\n};\r\n\r\nconst getPriorityColor = (priority, theme) => {\r\n  const priorityColors = {\r\n    'Low': theme.palette.success.main,\r\n    'Medium': theme.palette.warning.main,\r\n    'High': theme.palette.error.main,\r\n    'Critical': theme.palette.error.dark,\r\n  };\r\n  return priorityColors[priority] || theme.palette.grey[500];\r\n};\r\n\r\n// Optimized timestamp formatting function\r\nconst formatTimestamp = (timestamp) => {\r\n  if (!timestamp) return 'N/A';\r\n\r\n  try {\r\n    const date = new Date(timestamp);\r\n    const now = new Date();\r\n    const diffInHours = Math.abs(now - date) / (1000 * 60 * 60);\r\n\r\n    // If less than 24 hours, show time\r\n    if (diffInHours < 24) {\r\n      return date.toLocaleTimeString('en-US', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: true\r\n      });\r\n    }\r\n    // If less than 7 days, show day and time\r\n    else if (diffInHours < 168) {\r\n      return date.toLocaleDateString('en-US', {\r\n        weekday: 'short',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: true\r\n      });\r\n    }\r\n    // Otherwise show full date and time\r\n    else {\r\n      return date.toLocaleDateString('en-US', {\r\n        month: 'short',\r\n        day: 'numeric',\r\n        year: 'numeric',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: true\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error('Error formatting timestamp:', error);\r\n    return 'Invalid date';\r\n  }\r\n};\r\n\r\nconst ActivityItem = React.memo(({ activity, index }) => {\r\n  const theme = useTheme();\r\n\r\n  // Use activityTimestamp if available, otherwise fall back to submissionDate\r\n  // The backend already handles this logic correctly\r\n  const displayTimestamp = activity.activityTimestamp || activity.submissionDate;\r\n\r\n  // Memoize formatted timestamp\r\n  const formattedTimestamp = useMemo(() => formatTimestamp(displayTimestamp), [displayTimestamp]);\r\n\r\n  // Memoize colors\r\n  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);\r\n  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, x: -20 }}\r\n      animate={{ opacity: 1, x: 0 }}\r\n      transition={{\r\n        delay: Math.min(index * 0.05, 0.3), // Reduced delay for better performance\r\n        duration: 0.3, // Reduced duration\r\n        ease: \"easeOut\"\r\n      }}\r\n    >\r\n      <ListItem\r\n        sx={{\r\n          bgcolor: 'background.paper',\r\n          borderRadius: 2,\r\n          mb: 1,\r\n          boxShadow: 1,\r\n          '&:hover': {\r\n            bgcolor: 'action.hover',\r\n            transform: 'translateX(4px)',\r\n            transition: 'transform 0.15s ease', // Faster transition\r\n          },\r\n        }}\r\n      >\r\n        <ListItemIcon>\r\n          <StatusIcon sx={{ color: statusColor }} />\r\n        </ListItemIcon>\r\n        <ListItemText\r\n          primary={\r\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>\r\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, flex: 1 }}>\r\n                #{activity.ComplaintNumber} - {activity.description}\r\n              </Typography>\r\n              {activity.Priority && (\r\n                <Chip\r\n                  label={activity.Priority}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${priorityColor}15`,\r\n                    color: priorityColor,\r\n                    fontWeight: 500,\r\n                    fontSize: '0.7rem'\r\n                  }}\r\n                />\r\n              )}\r\n            </Box>\r\n          }\r\n          secondary={\r\n            <Box sx={{ mt: 0.5 }}>\r\n              {activity.activityDetails && (\r\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 0.5 }}>\r\n                  {activity.activityDetails}\r\n                </Typography>\r\n              )}\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>\r\n                <Chip\r\n                  label={activity.Status}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${statusColor}15`,\r\n                    color: statusColor,\r\n                    fontWeight: 500\r\n                  }}\r\n                />\r\n                {activity.Category && (\r\n                  <Chip\r\n                    label={activity.Category}\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ fontSize: '0.7rem' }}\r\n                  />\r\n                )}\r\n                <Typography variant=\"caption\" color=\"text.secondary\">\r\n                  {formattedTimestamp}\r\n                </Typography>\r\n              </Box>\r\n            </Box>\r\n          }\r\n        />\r\n      </ListItem>\r\n    </motion.div>\r\n  );\r\n});\r\n\r\nfunction Dashboard() {\r\n  const [stats, setStats] = useState(null);\r\n  const [activities, setActivities] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\n  const fetchDashboardData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Fetch data with optimized timeout and caching\r\n      const [statsResponse, activitiesResponse] = await Promise.all([\r\n        axios.get('/api/dashboard/stats', {\r\n          timeout: 10000, // 10 second timeout\r\n          headers: {\r\n            'Cache-Control': 'max-age=60' // Cache for 1 minute\r\n          }\r\n        }),\r\n        axios.get('/api/dashboard/recent-activities', {\r\n          timeout: 10000, // 10 second timeout\r\n          headers: {\r\n            'Cache-Control': 'max-age=30' // Cache for 30 seconds\r\n          }\r\n        })\r\n      ]);\r\n\r\n      setStats(statsResponse.data);\r\n      setActivities(activitiesResponse.data);\r\n      setError(null);\r\n    } catch (err) {\r\n      console.error('Error fetching dashboard data:', err);\r\n      setError('Failed to load dashboard data. Please try again later.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n  }, [fetchDashboardData]);\r\n\r\n  const statCards = useMemo(() => [\r\n    {\r\n      title: 'Total Complaints',\r\n      value: stats?.totalComplaints || 0,\r\n      icon: <ComplaintsIcon />,\r\n      color: 'primary'\r\n    },\r\n    {\r\n      title: 'Resolved',\r\n      value: stats?.resolvedComplaints || 0,\r\n      icon: <ResolvedIcon />,\r\n      color: 'success'\r\n    },\r\n    {\r\n      title: 'Pending',\r\n      value: stats?.pendingComplaints || 0,\r\n      icon: <PendingIcon />,\r\n      color: 'warning'\r\n    },\r\n    {\r\n      title: 'High Priority',\r\n      value: stats?.highPriorityComplaints || 0,\r\n      icon: <HighPriorityIcon />,\r\n      color: 'error'\r\n    }\r\n  ], [stats]);\r\n\r\n  const monthlyStatCards = useMemo(() => {\r\n    if (!stats?.monthlyStats) return [];\r\n\r\n    return [\r\n      {\r\n        title: 'This Month',\r\n        value: stats.monthlyStats.totalMonthlyComplaints || 0,\r\n        icon: <ComplaintsIcon />,\r\n        color: 'info',\r\n        subtitle: 'New complaints'\r\n      },\r\n      {\r\n        title: 'Resolution Rate',\r\n        value: `${stats.monthlyStats.resolutionRate || 0}%`,\r\n        icon: <ResolvedIcon />,\r\n        color: 'success',\r\n        subtitle: 'Monthly average'\r\n      },\r\n      {\r\n        title: 'Response Time',\r\n        value: `${stats.monthlyStats.responseEfficiency || 0}%`,\r\n        icon: <TimeIcon />,\r\n        color: 'warning',\r\n        subtitle: 'Within 24h'\r\n      },\r\n      {\r\n        title: 'Avg Resolution',\r\n        value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,\r\n        icon: <ResolutionIcon />,\r\n        color: 'primary',\r\n        subtitle: 'Hours to resolve'\r\n      }\r\n    ];\r\n  }, [stats]);\r\n\r\n  return (\r\n    <Box sx={{ p: { xs: 2, sm: 3 } }}>\r\n      <Typography variant=\"h4\" sx={{ mb: 4, fontWeight: 600 }}>\r\n        Dashboard\r\n      </Typography>\r\n\r\n      {error && (\r\n        <Alert \r\n          severity=\"error\" \r\n          sx={{ mb: 3 }}\r\n          action={\r\n            <motion.div whileHover={{ scale: 1.05 }}>\r\n              <Button color=\"inherit\" size=\"small\" onClick={fetchDashboardData}>\r\n                Retry\r\n              </Button>\r\n            </motion.div>\r\n          }\r\n        >\r\n          {error}\r\n        </Alert>\r\n      )}\r\n\r\n      <Grid container spacing={3}>\r\n        {statCards.map((card, index) => (\r\n          <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n            <StatCard {...card} loading={loading} />\r\n          </Grid>\r\n        ))}\r\n\r\n        {/* Monthly Statistics Section */}\r\n        {monthlyStatCards.length > 0 && (\r\n          <Grid item xs={12}>\r\n            <Card\r\n              component={motion.div}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.2, duration: 0.3 }}\r\n              sx={{\r\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n                color: 'white',\r\n                overflow: 'visible',\r\n                position: 'relative',\r\n                '&::before': {\r\n                  content: '\"\"',\r\n                  position: 'absolute',\r\n                  top: 0,\r\n                  left: 0,\r\n                  right: 0,\r\n                  bottom: 0,\r\n                  background: 'rgba(255,255,255,0.1)',\r\n                  backdropFilter: 'blur(10px)',\r\n                  borderRadius: 'inherit',\r\n                  zIndex: 0\r\n                }\r\n              }}\r\n            >\r\n              <CardContent sx={{ position: 'relative', zIndex: 1 }}>\r\n                <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600, color: 'white' }}>\r\n                  📊 Monthly Performance Insights\r\n                </Typography>\r\n                <Grid container spacing={3}>\r\n                  {monthlyStatCards.map((card, index) => (\r\n                    <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n                      <MonthlyStatCard {...card} loading={loading} index={index} />\r\n                    </Grid>\r\n                  ))}\r\n                </Grid>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n        )}\r\n\r\n        <Grid item xs={12}>\r\n          <Card\r\n            component={motion.div}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4 }}\r\n            sx={{ \r\n              overflow: 'visible',\r\n              height: '100%',\r\n              minHeight: 400\r\n            }}\r\n          >\r\n            <CardContent>\r\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\r\n                Recent Activities\r\n              </Typography>\r\n              {loading ? (\r\n                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>\r\n                  <CircularProgress />\r\n                </Box>\r\n              ) : activities.length > 0 ? (\r\n                <List sx={{ p: 0 }}>\r\n                  {activities.slice(0, 8).map((activity, index) => (\r\n                    <ActivityItem\r\n                      key={`${activity.ComplaintId}-${activity.Status}`}\r\n                      activity={activity}\r\n                      index={index}\r\n                    />\r\n                  ))}\r\n                </List>\r\n              ) : (\r\n                <Box \r\n                  sx={{ \r\n                    textAlign: 'center', \r\n                    py: 4,\r\n                    color: 'text.secondary'\r\n                  }}\r\n                >\r\n                  <Typography>No recent activities</Typography>\r\n                </Box>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default Dashboard; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,cAAc,EACdC,aAAa,EACbC,MAAM,QACD,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,YAAY,EAC3BC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,gBAAgB,EACzBC,iBAAiB,IAAIC,UAAU,EAC/BC,QAAQ,IAAIC,QAAQ,EACpBC,KAAK,IAAIC,cAAc,EACvBC,QAAQ,IAAIC,SAAS,EACrBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACrE,MAAMC,KAAK,GAAG9C,QAAQ,CAAC,CAAC;EACxB,MAAM+C,QAAQ,GAAGtC,aAAa,CAACqC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE9D,MAAMC,eAAe,gBAAAN,EAAA,cAAGxD,KAAK,CAAC+D,IAAI,CAAAP,EAAA,CAAC,CAAC;IAAEP,KAAK;IAAEC,KAAK;IAAEC,IAAI;IAAEC,KAAK;IAAEC,OAAO;IAAEC,QAAQ;IAAEU;EAAM,CAAC,KAAK;IAAAR,EAAA;IAC9F,MAAME,KAAK,GAAG9C,QAAQ,CAAC,CAAC;IACxB,MAAM+C,QAAQ,GAAGtC,aAAa,CAACqC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE5D,IAAIR,OAAO,EAAE;MACX,oBACEN,OAAA,CAACxC,IAAI;QAAC0D,EAAE,EAAE;UACRC,MAAM,EAAE,GAAG;UACXC,UAAU,EAAE,wBAAwB;UACpCC,cAAc,EAAE,YAAY;UAC5BC,MAAM,EAAE,iCAAiC;UACzCjB,KAAK,EAAE;QACT,CAAE;QAAAkB,QAAA,eACAvB,OAAA,CAACvC,WAAW;UAACyD,EAAE,EAAE;YACfM,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBP,MAAM,EAAE;UACV,CAAE;UAAAI,QAAA,eACAvB,OAAA,CAACrC,gBAAgB;YAACgE,IAAI,EAAE,EAAG;YAACT,EAAE,EAAE;cAAEb,KAAK,EAAE;YAAQ;UAAE;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEX;IAEA,oBACE/B,OAAA,CAACN,MAAM,CAACsC,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAI,CAAE;MACpCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAE;MAClCE,UAAU,EAAE;QACVC,KAAK,EAAErB,KAAK,GAAG,GAAG;QAClBsB,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE;MACR,CAAE;MAAAjB,QAAA,eAEFvB,OAAA,CAACxC,IAAI;QACH0D,EAAE,EAAE;UACFC,MAAM,EAAE,GAAG;UACXC,UAAU,EAAE,wBAAwB;UACpCC,cAAc,EAAE,YAAY;UAC5BC,MAAM,EAAE,iCAAiC;UACzCjB,KAAK,EAAE,OAAO;UACdoC,MAAM,EAAE,SAAS;UACjBJ,UAAU,EAAE,eAAe;UAC3B,SAAS,EAAE;YACTjB,UAAU,EAAE,wBAAwB;YACpCsB,SAAS,EAAE,kBAAkB;YAC7BC,SAAS,EAAE;UACb;QACF,CAAE;QAAApB,QAAA,eAEFvB,OAAA,CAACvC,WAAW;UAACyD,EAAE,EAAE;YACfM,OAAO,EAAE,MAAM;YACfoB,aAAa,EAAE,QAAQ;YACvBlB,cAAc,EAAE,eAAe;YAC/BP,MAAM,EAAE,MAAM;YACd0B,CAAC,EAAE,CAAC;YACJ,cAAc,EAAE;cAAEC,EAAE,EAAE;YAAE;UAC1B,CAAE;UAAAvB,QAAA,gBACAvB,OAAA,CAAC1C,GAAG;YAAC4D,EAAE,EAAE;cAAEM,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAED,UAAU,EAAE;YAAa,CAAE;YAAAF,QAAA,gBACtFvB,OAAA,CAAC1C,GAAG;cAAAiE,QAAA,gBACFvB,OAAA,CAACtC,UAAU;gBACTqF,OAAO,EAAEnC,QAAQ,GAAG,OAAO,GAAG,OAAQ;gBACtCM,EAAE,EAAE;kBACFgB,OAAO,EAAE,GAAG;kBACZc,UAAU,EAAE,GAAG;kBACfC,QAAQ,EAAE;gBACZ,CAAE;gBAAA1B,QAAA,EAEDrB;cAAK;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACZxB,QAAQ,iBACPP,OAAA,CAACtC,UAAU;gBACTqF,OAAO,EAAC,SAAS;gBACjB7B,EAAE,EAAE;kBACFgB,OAAO,EAAE,GAAG;kBACZe,QAAQ,EAAE,SAAS;kBACnBzB,OAAO,EAAE;gBACX,CAAE;gBAAAD,QAAA,EAEDhB;cAAQ;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN/B,OAAA,CAAC1C,GAAG;cAAC4D,EAAE,EAAE;gBACPgB,OAAO,EAAE,GAAG;gBACZe,QAAQ,EAAErC,QAAQ,GAAG,QAAQ,GAAG;cAClC,CAAE;cAAAW,QAAA,EACCnB;YAAI;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/B,OAAA,CAACtC,UAAU;YACTqF,OAAO,EAAEnC,QAAQ,GAAG,IAAI,GAAG,IAAK;YAChCM,EAAE,EAAE;cACF8B,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAErC,QAAQ,GAAG,SAAS,GAAG,QAAQ;cACzCsC,UAAU,EAAE;YACd,CAAE;YAAA3B,QAAA,EAEDpB;UAAK;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEjB,CAAC;IAAA,QAzGelE,QAAQ,EACLS,aAAa;EAAA,EAwG/B,CAAC;IAAA,QAzGcT,QAAQ,EACLS,aAAa;EAAA,EAwG9B;EAEA,oBACE0B,OAAA,CAACN,MAAM,CAACsC,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEiB,CAAC,EAAE;IAAG,CAAE;IAC/Bf,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEiB,CAAC,EAAE;IAAE,CAAE;IAC9Bd,UAAU,EAAE;MACVe,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAE,GAAG;MACdC,OAAO,EAAE,EAAE;MACXf,QAAQ,EAAE;IACZ,CAAE;IACFgB,UAAU,EAAE;MACVpB,KAAK,EAAE,IAAI;MACXE,UAAU,EAAE;QAAEE,QAAQ,EAAE;MAAI;IAC9B,CAAE;IACFiB,QAAQ,EAAE;MAAErB,KAAK,EAAE;IAAK,CAAE;IAAAZ,QAAA,eAE1BvB,OAAA,CAACxC,IAAI;MACH0D,EAAE,EAAE;QACFC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,2BAA2BT,KAAK,CAAC8C,OAAO,CAACpD,KAAK,CAAC,CAACqD,IAAI,QAAQ/C,KAAK,CAAC8C,OAAO,CAACpD,KAAK,CAAC,CAACsD,IAAI,QAAQ;QACzGtD,KAAK,EAAE,OAAO;QACduD,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClBlB,SAAS,EAAEhC,KAAK,CAACmD,OAAO,CAACxD,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;QACzC+B,UAAU,EAAE,uCAAuC;QACnD,SAAS,EAAE;UACTM,SAAS,EAAEhC,KAAK,CAACmD,OAAO,CAAC,CAAC,CAAC;UAC3BpB,SAAS,EAAE;QACb;MACF,CAAE;MAAAnB,QAAA,eAEFvB,OAAA,CAACvC,WAAW;QAACyD,EAAE,EAAE;UACf2B,CAAC,EAAEjC,QAAQ,GAAG,CAAC,GAAG,CAAC;UACnBO,MAAM,EAAE,MAAM;UACdK,OAAO,EAAE,MAAM;UACfoB,aAAa,EAAE,QAAQ;UACvBlB,cAAc,EAAE;QAClB,CAAE;QAAAH,QAAA,gBACAvB,OAAA,CAAC1C,GAAG;UAAC4D,EAAE,EAAE;YACP0C,QAAQ,EAAE,UAAU;YACpBG,MAAM,EAAE,CAAC;YACTvC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBuC,GAAG,EAAE;UACP,CAAE;UAAAzC,QAAA,gBACAvB,OAAA,CAACN,MAAM,CAACsC,GAAG;YACTC,OAAO,EAAE;cAAEE,KAAK,EAAE;YAAE,CAAE;YACtBC,OAAO,EAAE;cAAED,KAAK,EAAE;YAAE,CAAE;YACtBE,UAAU,EAAE;cAAEC,KAAK,EAAE,GAAG;cAAEc,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAAA9B,QAAA,eAE1DtE,KAAK,CAACgH,YAAY,CAAC7D,IAAI,EAAE;cACxBc,EAAE,EAAE;gBACF+B,QAAQ,EAAErC,QAAQ,GAAG,EAAE,GAAG,EAAE;gBAC5BsB,OAAO,EAAE,GAAG;gBACZgC,MAAM,EAAE;cACV;YACF,CAAC;UAAC;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACb/B,OAAA,CAAC1C,GAAG;YAAAiE,QAAA,gBACFvB,OAAA,CAACtC,UAAU;cACTqF,OAAO,EAAEnC,QAAQ,GAAG,IAAI,GAAG,IAAK;cAChCuD,SAAS,EAAC,KAAK;cACfjD,EAAE,EAAE;gBACF8B,UAAU,EAAE,GAAG;gBACfoB,UAAU,EAAE,GAAG;gBACfC,EAAE,EAAE,GAAG;gBACPnB,UAAU,EAAE;cACd,CAAE;cAAA3B,QAAA,EAEDjB,OAAO,gBACNN,OAAA,CAACN,MAAM,CAACsC,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE;gBAAE,CAAE;gBACxBE,OAAO,EAAE;kBAAEF,OAAO,EAAE;gBAAE,CAAE;gBACxBG,UAAU,EAAE;kBAAEE,QAAQ,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,eAE9BvB,OAAA,CAACrC,gBAAgB;kBAACgE,IAAI,EAAEf,QAAQ,GAAG,EAAE,GAAG,EAAG;kBAACP,KAAK,EAAC;gBAAS;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,gBAEb/B,OAAA,CAACN,MAAM,CAACsC,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEiB,CAAC,EAAE;gBAAG,CAAE;gBAC/Bf,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEiB,CAAC,EAAE;gBAAE,CAAE;gBAC9Bd,UAAU,EAAE;kBAAEC,KAAK,EAAE,GAAG;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAEzCpB;cAAK;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eACb/B,OAAA,CAACtC,UAAU;cACTqF,OAAO,EAAEnC,QAAQ,GAAG,OAAO,GAAG,OAAQ;cACtCM,EAAE,EAAE;gBACFgB,OAAO,EAAE,GAAG;gBACZc,UAAU,EAAE,GAAG;gBACfsB,aAAa,EAAE,OAAO;gBACtBpB,UAAU,EAAE;cACd,CAAE;cAAA3B,QAAA,EAEDrB;YAAK;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACZxB,QAAQ,iBACPP,OAAA,CAACtC,UAAU;cACTqF,OAAO,EAAC,SAAS;cACjB7B,EAAE,EAAE;gBACFgB,OAAO,EAAE,GAAG;gBACZc,UAAU,EAAE,GAAG;gBACfE,UAAU,EAAE,6BAA6B;gBACzC1B,OAAO,EAAE;cACX,CAAE;cAAAD,QAAA,EAEDhB;YAAQ;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/B,OAAA,CAAC1C,GAAG;UACF6G,SAAS,EAAEzE,MAAM,CAACsC,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAE,CAAE;UACrCE,UAAU,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC1CrB,EAAE,EAAE;YACF0C,QAAQ,EAAE,UAAU;YACpBW,KAAK,EAAE,CAAC,EAAE;YACVC,MAAM,EAAE,CAAC,EAAE;YACXN,MAAM,EAAE;UACV,CAAE;UAAA3C,QAAA,eAEDtE,KAAK,CAACgH,YAAY,CAAC7D,IAAI,EAAE;YACxBc,EAAE,EAAE;cAAE+B,QAAQ,EAAErC,QAAQ,GAAG,GAAG,GAAG;YAAI;UACvC,CAAC;QAAC;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEjB,CAAC;;AAED;AAAAvB,GAAA,CAvPMP,QAAQ;EAAA,QACEpC,QAAQ,EACLS,aAAa;AAAA;AAAAmG,EAAA,GAF1BxE,QAAQ;AAwPd,MAAMyE,cAAc,GAAGA,CAACC,MAAM,EAAEhE,KAAK,KAAK;EACxC,MAAMiE,YAAY,GAAG;IACnB,KAAK,EAAEjE,KAAK,CAAC8C,OAAO,CAACoB,IAAI,CAACnB,IAAI;IAC9B,UAAU,EAAE/C,KAAK,CAAC8C,OAAO,CAACqB,OAAO,CAACpB,IAAI;IACtC,aAAa,EAAE/C,KAAK,CAAC8C,OAAO,CAACqB,OAAO,CAACnB,IAAI;IACzC,UAAU,EAAEhD,KAAK,CAAC8C,OAAO,CAACsB,OAAO,CAACrB,IAAI;IACtC,UAAU,EAAE/C,KAAK,CAAC8C,OAAO,CAACuB,KAAK,CAACtB;EAClC,CAAC;EACD,OAAOkB,YAAY,CAACD,MAAM,CAAC,IAAIhE,KAAK,CAAC8C,OAAO,CAACwB,IAAI,CAAC,GAAG,CAAC;AACxD,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAACC,QAAQ,EAAExE,KAAK,KAAK;EAC5C,MAAMyE,cAAc,GAAG;IACrB,KAAK,EAAEzE,KAAK,CAAC8C,OAAO,CAACsB,OAAO,CAACrB,IAAI;IACjC,QAAQ,EAAE/C,KAAK,CAAC8C,OAAO,CAACqB,OAAO,CAACpB,IAAI;IACpC,MAAM,EAAE/C,KAAK,CAAC8C,OAAO,CAACuB,KAAK,CAACtB,IAAI;IAChC,UAAU,EAAE/C,KAAK,CAAC8C,OAAO,CAACuB,KAAK,CAACrB;EAClC,CAAC;EACD,OAAOyB,cAAc,CAACD,QAAQ,CAAC,IAAIxE,KAAK,CAAC8C,OAAO,CAACwB,IAAI,CAAC,GAAG,CAAC;AAC5D,CAAC;;AAED;AACA,MAAMI,eAAe,GAAIC,SAAS,IAAK;EACrC,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;EAE5B,IAAI;IACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAChC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;;IAE3D;IACA,IAAIG,WAAW,GAAG,EAAE,EAAE;MACpB,OAAOH,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IAAIN,WAAW,GAAG,GAAG,EAAE;MAC1B,OAAOH,IAAI,CAACU,kBAAkB,CAAC,OAAO,EAAE;QACtCC,OAAO,EAAE,OAAO;QAChBJ,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACA;IAAA,KACK;MACH,OAAOT,IAAI,CAACU,kBAAkB,CAAC,OAAO,EAAE;QACtCE,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfP,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;IACdsB,OAAO,CAACtB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,cAAc;EACvB;AACF,CAAC;AAED,MAAMuB,YAAY,gBAAAC,GAAA,cAAGvJ,KAAK,CAAC+D,IAAI,CAAAyF,GAAA,GAAAD,GAAA,CAAC,CAAC;EAAEE,QAAQ;EAAEzF;AAAM,CAAC,KAAK;EAAAuF,GAAA;EACvD,MAAM7F,KAAK,GAAG9C,QAAQ,CAAC,CAAC;;EAExB;EACA;EACA,MAAM8I,gBAAgB,GAAGD,QAAQ,CAACE,iBAAiB,IAAIF,QAAQ,CAACG,cAAc;;EAE9E;EACA,MAAMC,kBAAkB,GAAGzJ,OAAO,CAAC,MAAMgI,eAAe,CAACsB,gBAAgB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE/F;EACA,MAAMI,WAAW,GAAG1J,OAAO,CAAC,MAAMqH,cAAc,CAACgC,QAAQ,CAACM,MAAM,EAAErG,KAAK,CAAC,EAAE,CAAC+F,QAAQ,CAACM,MAAM,EAAErG,KAAK,CAAC,CAAC;EACnG,MAAMsG,aAAa,GAAG5J,OAAO,CAAC,MAAM6H,gBAAgB,CAACwB,QAAQ,CAACQ,QAAQ,EAAEvG,KAAK,CAAC,EAAE,CAAC+F,QAAQ,CAACQ,QAAQ,EAAEvG,KAAK,CAAC,CAAC;EAE3G,oBACEX,OAAA,CAACN,MAAM,CAACsC,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEiF,CAAC,EAAE,CAAC;IAAG,CAAE;IAChC/E,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEiF,CAAC,EAAE;IAAE,CAAE;IAC9B9E,UAAU,EAAE;MACVC,KAAK,EAAEqD,IAAI,CAACyB,GAAG,CAACnG,KAAK,GAAG,IAAI,EAAE,GAAG,CAAC;MAAE;MACpCsB,QAAQ,EAAE,GAAG;MAAE;MACfC,IAAI,EAAE;IACR,CAAE;IAAAjB,QAAA,eAEFvB,OAAA,CAACjC,QAAQ;MACPmD,EAAE,EAAE;QACFmG,OAAO,EAAE,kBAAkB;QAC3BC,YAAY,EAAE,CAAC;QACfjD,EAAE,EAAE,CAAC;QACL1B,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE;UACT0E,OAAO,EAAE,cAAc;UACvB3E,SAAS,EAAE,iBAAiB;UAC5BL,UAAU,EAAE,sBAAsB,CAAE;QACtC;MACF,CAAE;MAAAd,QAAA,gBAEFvB,OAAA,CAAC/B,YAAY;QAAAsD,QAAA,eACXvB,OAAA,CAACf,UAAU;UAACiC,EAAE,EAAE;YAAEb,KAAK,EAAE0G;UAAY;QAAE;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACf/B,OAAA,CAAChC,YAAY;QACXuJ,OAAO,eACLvH,OAAA,CAAC1C,GAAG;UAAC4D,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEuC,GAAG,EAAE,CAAC;YAAEK,EAAE,EAAE;UAAI,CAAE;UAAA9C,QAAA,gBAClEvB,OAAA,CAACtC,UAAU;YAACqF,OAAO,EAAC,WAAW;YAAC7B,EAAE,EAAE;cAAE8B,UAAU,EAAE,GAAG;cAAEwE,IAAI,EAAE;YAAE,CAAE;YAAAjG,QAAA,GAAC,GAC/D,EAACmF,QAAQ,CAACe,eAAe,EAAC,KAAG,EAACf,QAAQ,CAACgB,WAAW;UAAA;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EACZ2E,QAAQ,CAACQ,QAAQ,iBAChBlH,OAAA,CAAC5B,IAAI;YACHuJ,KAAK,EAAEjB,QAAQ,CAACQ,QAAS;YACzBvF,IAAI,EAAC,OAAO;YACZT,EAAE,EAAE;cACFmG,OAAO,EAAE,GAAGJ,aAAa,IAAI;cAC7B5G,KAAK,EAAE4G,aAAa;cACpBjE,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE;YACZ;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACD6F,SAAS,eACP5H,OAAA,CAAC1C,GAAG;UAAC4D,EAAE,EAAE;YAAE2G,EAAE,EAAE;UAAI,CAAE;UAAAtG,QAAA,GAClBmF,QAAQ,CAACoB,eAAe,iBACvB9H,OAAA,CAACtC,UAAU;YAACqF,OAAO,EAAC,OAAO;YAAC1C,KAAK,EAAC,gBAAgB;YAACa,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAI,CAAE;YAAA9C,QAAA,EAChEmF,QAAQ,CAACoB;UAAe;YAAAlG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACb,eACD/B,OAAA,CAAC1C,GAAG;YAAC4D,EAAE,EAAE;cAAEM,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEuC,GAAG,EAAE,CAAC;cAAE+D,QAAQ,EAAE;YAAO,CAAE;YAAAxG,QAAA,gBAC3EvB,OAAA,CAAC5B,IAAI;cACHuJ,KAAK,EAAEjB,QAAQ,CAACM,MAAO;cACvBrF,IAAI,EAAC,OAAO;cACZT,EAAE,EAAE;gBACFmG,OAAO,EAAE,GAAGN,WAAW,IAAI;gBAC3B1G,KAAK,EAAE0G,WAAW;gBAClB/D,UAAU,EAAE;cACd;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACD2E,QAAQ,CAACsB,QAAQ,iBAChBhI,OAAA,CAAC5B,IAAI;cACHuJ,KAAK,EAAEjB,QAAQ,CAACsB,QAAS;cACzBrG,IAAI,EAAC,OAAO;cACZoB,OAAO,EAAC,UAAU;cAClB7B,EAAE,EAAE;gBAAE+B,QAAQ,EAAE;cAAS;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACF,eACD/B,OAAA,CAACtC,UAAU;cAACqF,OAAO,EAAC,SAAS;cAAC1C,KAAK,EAAC,gBAAgB;cAAAkB,QAAA,EACjDuF;YAAkB;cAAAlF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEjB,CAAC;EAAA,QA9FelE,QAAQ;AAAA,EA8FvB,CAAC;EAAA,QA9FcA,QAAQ;AAAA,EA8FtB;AAACoK,GAAA,GA/FG1B,YAAY;AAiGlB,SAAS2B,SAASA,CAAA,EAAG;EAAAC,GAAA;EACnB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnL,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoL,UAAU,EAAEC,aAAa,CAAC,GAAGrL,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoD,OAAO,EAAEkI,UAAU,CAAC,GAAGtL,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8H,KAAK,EAAEyD,QAAQ,CAAC,GAAGvL,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAMyD,KAAK,GAAG9C,QAAQ,CAAC,CAAC;EACxB,MAAM+C,QAAQ,GAAGtC,aAAa,CAACqC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,MAAM4H,kBAAkB,GAAGtL,WAAW,CAAC,YAAY;IACjD,IAAI;MACFoL,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACG,aAAa,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5DlJ,KAAK,CAACmJ,GAAG,CAAC,sBAAsB,EAAE;QAChCC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;UACP,eAAe,EAAE,YAAY,CAAC;QAChC;MACF,CAAC,CAAC,EACFrJ,KAAK,CAACmJ,GAAG,CAAC,kCAAkC,EAAE;QAC5CC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;UACP,eAAe,EAAE,YAAY,CAAC;QAChC;MACF,CAAC,CAAC,CACH,CAAC;MAEFZ,QAAQ,CAACM,aAAa,CAACO,IAAI,CAAC;MAC5BX,aAAa,CAACK,kBAAkB,CAACM,IAAI,CAAC;MACtCT,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZ7C,OAAO,CAACtB,KAAK,CAAC,gCAAgC,EAAEmE,GAAG,CAAC;MACpDV,QAAQ,CAAC,wDAAwD,CAAC;IACpE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAENrL,SAAS,CAAC,MAAM;IACduL,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,MAAMU,SAAS,GAAG/L,OAAO,CAAC,MAAM,CAC9B;IACE6C,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,CAAAiI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,eAAe,KAAI,CAAC;IAClCjJ,IAAI,eAAEJ,OAAA,CAACvB,cAAc;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxB1B,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CAAAiI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,kBAAkB,KAAI,CAAC;IACrClJ,IAAI,eAAEJ,OAAA,CAACrB,YAAY;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtB1B,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAAiI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmB,iBAAiB,KAAI,CAAC;IACpCnJ,IAAI,eAAEJ,OAAA,CAACnB,WAAW;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrB1B,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,CAAAiI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoB,sBAAsB,KAAI,CAAC;IACzCpJ,IAAI,eAAEJ,OAAA,CAACjB,gBAAgB;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1B1B,KAAK,EAAE;EACT,CAAC,CACF,EAAE,CAAC+H,KAAK,CAAC,CAAC;EAEX,MAAMqB,gBAAgB,GAAGpM,OAAO,CAAC,MAAM;IACrC,IAAI,EAAC+K,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEsB,YAAY,GAAE,OAAO,EAAE;IAEnC,OAAO,CACL;MACExJ,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAEiI,KAAK,CAACsB,YAAY,CAACC,sBAAsB,IAAI,CAAC;MACrDvJ,IAAI,eAAEJ,OAAA,CAACvB,cAAc;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxB1B,KAAK,EAAE,MAAM;MACbE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,GAAGiI,KAAK,CAACsB,YAAY,CAACE,cAAc,IAAI,CAAC,GAAG;MACnDxJ,IAAI,eAAEJ,OAAA,CAACrB,YAAY;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtB1B,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,GAAGiI,KAAK,CAACsB,YAAY,CAACG,kBAAkB,IAAI,CAAC,GAAG;MACvDzJ,IAAI,eAAEJ,OAAA,CAACb,QAAQ;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClB1B,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,GAAGwF,IAAI,CAACmE,KAAK,CAAC1B,KAAK,CAACsB,YAAY,CAACK,kBAAkB,IAAI,CAAC,CAAC,GAAG;MACnE3J,IAAI,eAAEJ,OAAA,CAACP,cAAc;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxB1B,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,CACF;EACH,CAAC,EAAE,CAAC6H,KAAK,CAAC,CAAC;EAEX,oBACEpI,OAAA,CAAC1C,GAAG;IAAC4D,EAAE,EAAE;MAAE2B,CAAC,EAAE;QAAEmH,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAAE,CAAE;IAAA1I,QAAA,gBAC/BvB,OAAA,CAACtC,UAAU;MAACqF,OAAO,EAAC,IAAI;MAAC7B,EAAE,EAAE;QAAEmD,EAAE,EAAE,CAAC;QAAErB,UAAU,EAAE;MAAI,CAAE;MAAAzB,QAAA,EAAC;IAEzD;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZiD,KAAK,iBACJhF,OAAA,CAACpC,KAAK;MACJsM,QAAQ,EAAC,OAAO;MAChBhJ,EAAE,EAAE;QAAEmD,EAAE,EAAE;MAAE,CAAE;MACd8F,MAAM,eACJnK,OAAA,CAACN,MAAM,CAACsC,GAAG;QAACuB,UAAU,EAAE;UAAEpB,KAAK,EAAE;QAAK,CAAE;QAAAZ,QAAA,eACtCvB,OAAA,CAACzB,MAAM;UAAC8B,KAAK,EAAC,SAAS;UAACsB,IAAI,EAAC,OAAO;UAACyI,OAAO,EAAE1B,kBAAmB;UAAAnH,QAAA,EAAC;QAElE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb;MAAAR,QAAA,EAEAyD;IAAK;MAAApD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAED/B,OAAA,CAACzC,IAAI;MAAC8M,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA/I,QAAA,GACxB6H,SAAS,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAEvJ,KAAK,kBACzBjB,OAAA,CAACzC,IAAI;QAACkN,IAAI;QAACT,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACS,EAAE,EAAE,CAAE;QAAAnJ,QAAA,eAC9BvB,OAAA,CAACC,QAAQ;UAAA,GAAKuK,IAAI;UAAElK,OAAO,EAAEA;QAAQ;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GADJyI,IAAI,CAACtK,KAAK;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE1C,CACP,CAAC,EAGD0H,gBAAgB,CAACkB,MAAM,GAAG,CAAC,iBAC1B3K,OAAA,CAACzC,IAAI;QAACkN,IAAI;QAACT,EAAE,EAAE,EAAG;QAAAzI,QAAA,eAChBvB,OAAA,CAACxC,IAAI;UACH2G,SAAS,EAAEzE,MAAM,CAACsC,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAE,CAAE;UAC9Bd,UAAU,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC1CrB,EAAE,EAAE;YACFE,UAAU,EAAE,mDAAmD;YAC/Df,KAAK,EAAE,OAAO;YACdwD,QAAQ,EAAE,SAAS;YACnBD,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE;cACXgH,OAAO,EAAE,IAAI;cACbhH,QAAQ,EAAE,UAAU;cACpBiH,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPvG,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTpD,UAAU,EAAE,uBAAuB;cACnCC,cAAc,EAAE,YAAY;cAC5BiG,YAAY,EAAE,SAAS;cACvBvD,MAAM,EAAE;YACV;UACF,CAAE;UAAAxC,QAAA,eAEFvB,OAAA,CAACvC,WAAW;YAACyD,EAAE,EAAE;cAAE0C,QAAQ,EAAE,UAAU;cAAEG,MAAM,EAAE;YAAE,CAAE;YAAAxC,QAAA,gBACnDvB,OAAA,CAACtC,UAAU;cAACqF,OAAO,EAAC,IAAI;cAAC7B,EAAE,EAAE;gBAAEmD,EAAE,EAAE,CAAC;gBAAErB,UAAU,EAAE,GAAG;gBAAE3C,KAAK,EAAE;cAAQ,CAAE;cAAAkB,QAAA,EAAC;YAEzE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/B,OAAA,CAACzC,IAAI;cAAC8M,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA/I,QAAA,EACxBkI,gBAAgB,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEvJ,KAAK,kBAChCjB,OAAA,CAACzC,IAAI;gBAACkN,IAAI;gBAACT,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAACS,EAAE,EAAE,CAAE;gBAAAnJ,QAAA,eAC9BvB,OAAA,CAACe,eAAe;kBAAA,GAAKyJ,IAAI;kBAAElK,OAAO,EAAEA,OAAQ;kBAACW,KAAK,EAAEA;gBAAM;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC,GADzByI,IAAI,CAACtK,KAAK;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE1C,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,eAED/B,OAAA,CAACzC,IAAI;QAACkN,IAAI;QAACT,EAAE,EAAE,EAAG;QAAAzI,QAAA,eAChBvB,OAAA,CAACxC,IAAI;UACH2G,SAAS,EAAEzE,MAAM,CAACsC,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAE,CAAE;UAC9Bd,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BpB,EAAE,EAAE;YACF2C,QAAQ,EAAE,SAAS;YACnB1C,MAAM,EAAE,MAAM;YACd4J,SAAS,EAAE;UACb,CAAE;UAAAxJ,QAAA,eAEFvB,OAAA,CAACvC,WAAW;YAAA8D,QAAA,gBACVvB,OAAA,CAACtC,UAAU;cAACqF,OAAO,EAAC,IAAI;cAAC7B,EAAE,EAAE;gBAAEmD,EAAE,EAAE,CAAC;gBAAErB,UAAU,EAAE;cAAI,CAAE;cAAAzB,QAAA,EAAC;YAEzD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZzB,OAAO,gBACNN,OAAA,CAAC1C,GAAG;cAAC4D,EAAE,EAAE;gBAAEM,OAAO,EAAE,MAAM;gBAAEE,cAAc,EAAE,QAAQ;gBAAEmB,CAAC,EAAE;cAAE,CAAE;cAAAtB,QAAA,eAC3DvB,OAAA,CAACrC,gBAAgB;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,GACJuG,UAAU,CAACqC,MAAM,GAAG,CAAC,gBACvB3K,OAAA,CAAClC,IAAI;cAACoD,EAAE,EAAE;gBAAE2B,CAAC,EAAE;cAAE,CAAE;cAAAtB,QAAA,EAChB+G,UAAU,CAAC0C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACT,GAAG,CAAC,CAAC7D,QAAQ,EAAEzF,KAAK,kBAC1CjB,OAAA,CAACuG,YAAY;gBAEXG,QAAQ,EAAEA,QAAS;gBACnBzF,KAAK,EAAEA;cAAM,GAFR,GAAGyF,QAAQ,CAACuE,WAAW,IAAIvE,QAAQ,CAACM,MAAM,EAAE;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGlD,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAEP/B,OAAA,CAAC1C,GAAG;cACF4D,EAAE,EAAE;gBACFgK,SAAS,EAAE,QAAQ;gBACnBC,EAAE,EAAE,CAAC;gBACL9K,KAAK,EAAE;cACT,CAAE;cAAAkB,QAAA,eAEFvB,OAAA,CAACtC,UAAU;gBAAA6D,QAAA,EAAC;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACoG,GAAA,CAhOQD,SAAS;EAAA,QAKFrK,QAAQ,EACLS,aAAa;AAAA;AAAA8M,GAAA,GANvBlD,SAAS;AAkOlB,eAAeA,SAAS;AAAC,IAAAzD,EAAA,EAAAgC,GAAA,EAAAwB,GAAA,EAAAmD,GAAA;AAAAC,YAAA,CAAA5G,EAAA;AAAA4G,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAApD,GAAA;AAAAoD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}