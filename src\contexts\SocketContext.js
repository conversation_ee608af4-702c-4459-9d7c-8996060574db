import React, { createContext, useContext, useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';

const SocketContext = createContext(null);

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const { user, refreshUserPermissions, logout } = useAuth();

  useEffect(() => {
    // Only connect if user is authenticated
    if (user && user.empCode) {
      console.log('Connecting to Socket.IO server...');
      
      // Create socket connection to the backend server
      const getSocketUrl = () => {
        // In development, connect to the backend server directly
        if (process.env.NODE_ENV === 'development') {
          const currentHost = window.location.hostname;
          if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
            return 'http://localhost:1976';
          }
          return `http://${currentHost}:1976`;
        }
        // In production, use the same origin
        return window.location.origin;
      };

      const newSocket = io(getSocketUrl(), {
        transports: ['websocket', 'polling']
      });

      // Handle connection
      newSocket.on('connect', () => {
        console.log('Connected to Socket.IO server');
        setConnected(true);
        
        // Authenticate with the server
        const token = localStorage.getItem('token');
        if (token) {
          newSocket.emit('authenticate', token);
        }
      });

      // Handle disconnection
      newSocket.on('disconnect', () => {
        console.log('Disconnected from Socket.IO server');
        setConnected(false);
      });

      // Handle authentication errors
      newSocket.on('auth_error', (error) => {
        console.error('Socket authentication error:', error);
      });

      // Handle permission updates
      newSocket.on('permission_updated', async (data) => {
        console.log('Permission update received:', data);

        // Automatically refresh user permissions
        const success = await refreshUserPermissions();
        if (success) {
          console.log('User permissions refreshed automatically');

          // Show a notification to the user
          if (window.showPermissionUpdateNotification) {
            window.showPermissionUpdateNotification('Your permissions have been updated automatically!');
          }

          // Force a page reload to update the UI immediately
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      });

      // Handle session termination
      newSocket.on('session_terminated', (data) => {
        console.log('Session terminated:', data);

        // Show alert to user
        alert(data.message || 'Your session has been terminated because you logged in from another device.');

        // Logout the user
        logout();

        // Redirect to login page
        window.location.href = '/login';
      });

      setSocket(newSocket);

      // Cleanup on unmount
      return () => {
        console.log('Cleaning up socket connection');
        newSocket.disconnect();
      };
    } else {
      // Disconnect if user is not authenticated
      if (socket) {
        socket.disconnect();
        setSocket(null);
        setConnected(false);
      }
    }
  }, [user, refreshUserPermissions]);

  const value = {
    socket,
    connected
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

export default SocketContext;
