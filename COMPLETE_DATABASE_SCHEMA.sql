-- =====================================================
-- INTERNAL COMPLAINTS PORTAL - COMPLETE DATABASE SCHEMA
-- =====================================================
-- This script contains all tables, triggers, and stored procedures
-- required for the Internal Complaints Portal to function properly
-- =====================================================

USE [Internal Complaints]
GO

-- =====================================================
-- 1. CORE TABLES
-- =====================================================

-- 1.1 Department Table (Must exist first - referenced by Employee)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Department' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Department] (
        [DeptID] [numeric](18, 0) NOT NULL PRIMARY KEY,
        [DeptName] [varchar](50) NOT NULL DEFAULT (' '),
        [DeptPosition] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [Editable] [varchar](1) NOT NULL DEFAULT ('Y'),
        [ParentDeptId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [BrkTimeConsiderInPlan] [varchar](1) NOT NULL DEFAULT ('Y'),
        [PlanReq] [varchar](1) NOT NULL DEFAULT ('N'),
        [DeptGroupID] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [DeptGroupName] [varchar](50) NOT NULL DEFAULT (''),
        [ConsiderStkRep] [varchar](1) NOT NULL DEFAULT ('Y'),
        [DCode] [varchar](10) NOT NULL DEFAULT (''),
        [MaintDept] [varchar](1) NOT NULL DEFAULT ('N'),
        [LocationIDs] [varchar](250) NOT NULL DEFAULT (''),
        [BrkTimeNotReqInProdn] [varchar](1) NOT NULL DEFAULT ('Y'),
        [EntryEmpId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [EntryComputer] [varchar](100) NOT NULL DEFAULT (''),
        [DeptLevel] [numeric](18, 0) NOT NULL DEFAULT ((1)),
        [SubDeptIds] [varchar](250) NOT NULL DEFAULT (''),
        [active] [varchar](1) NOT NULL DEFAULT ('Y'),
        [LocationId] [numeric](18, 0) NOT NULL DEFAULT ((1)),
        [DeptCostType] [varchar](500) NOT NULL DEFAULT ('Direct'),
        [DepartmentGroup] [varchar](50) NOT NULL DEFAULT (''),
        [MaxNoOfUsers] [numeric](18, 0) NOT NULL DEFAULT ((-1)),
        [PlpCal] [varchar](1) NOT NULL DEFAULT ('I'),
        [Plpper] [float] NOT NULL DEFAULT ((75)),
        [LoadedOnCompName] [varchar](500) NOT NULL DEFAULT (''),
        [Line] [varchar](1) NOT NULL DEFAULT ('N'),
        [WeeklyHolidays] [varchar](1) NOT NULL DEFAULT ('Y')
    )
END
GO

-- 1.2 Employee Table (References Department)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Employee' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Employee] (
        [EmpId] [numeric](18, 0) NOT NULL PRIMARY KEY,
        [EmpCode] [varchar](50) NOT NULL,
        [EmpName] [varchar](50) NOT NULL,
        [FatherName] [varchar](50) NOT NULL DEFAULT (' '),
        [Address] [varchar](250) NOT NULL DEFAULT (' '),
        [PinCode] [varchar](50) NOT NULL DEFAULT (' '),
        [Phone] [varchar](50) NOT NULL DEFAULT (' '),
        [EMailId] [varchar](8000) NOT NULL DEFAULT (' '),
        [BirthDate] [datetime] NULL,
        [JoinDate] [datetime] NULL,
        [DeptId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [CatId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [Attachment] [varchar](8000) NOT NULL DEFAULT (' '),
        [AttachPhoto] [varchar](8000) NOT NULL DEFAULT (' '),
        [BloodGroup] [varchar](5) NOT NULL DEFAULT (' '),
        [BasicPay] [float] NOT NULL DEFAULT ((0)),
        [Gender] [varchar](50) NOT NULL DEFAULT (' '),
        [BankNo] [varchar](50) NOT NULL DEFAULT (' '),
        [Wages] [varchar](50) NOT NULL DEFAULT (' '),
        [MaritalStatus] [varchar](50) NOT NULL DEFAULT (' '),
        [Resign] [varchar](1) NOT NULL DEFAULT (' '),
        [ResignDate] [datetime] NULL,
        [AccountID] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [AccountHeadId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [AccExpenseID] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [LoginName] [varchar](50) NULL DEFAULT (' '),
        [Password] [varchar](50) NOT NULL DEFAULT (' '),
        [RunOnce] [varchar](1) NOT NULL DEFAULT ('N'),
        [StockReportReminder] [varchar](3) NOT NULL DEFAULT (' '),
        [AdminAccess] [varchar](1) NOT NULL DEFAULT ('N'),
        [CardId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [Description] [varchar](50) NOT NULL DEFAULT (' '),
        [MaximumPOValue] [float] NULL DEFAULT ((0)),
        [AccBonusAcId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [IncrementedDate] [datetime] NULL,
        [DocCtrlPwd] [varchar](100) NOT NULL DEFAULT (' '),
        [StockReportReminderForMG] [varchar](1) NOT NULL DEFAULT (' '),
        [DId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [Grade] [varchar](50) NOT NULL DEFAULT (' '),
        [QID] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [ExpId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [CompanyHead] [varchar](1) NOT NULL DEFAULT ('N'),
        [Authority] [varchar](100) NOT NULL DEFAULT (' '),
        [Interviewed] [varchar](1) NOT NULL DEFAULT ('N'),
        [ReasonForLeaving] [varchar](100) NOT NULL DEFAULT (' '),
        [CostCenter] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [ConfirmationDate] [datetime] NULL,
        [ShiftRotate] [varchar](1) NOT NULL DEFAULT ('N'),
        [PRApproval] [varchar](1) NOT NULL DEFAULT ('N'),
        [PORelease] [varchar](1) NOT NULL DEFAULT ('N'),
        [CardNo] [varchar](50) NOT NULL DEFAULT (''),
        [Notes] [varchar](1000) NOT NULL DEFAULT (''),
        [Paddress] [varchar](500) NOT NULL DEFAULT (''),
        [Block] [varchar](50) NOT NULL DEFAULT (''),
        [Cases] [varchar](50) NOT NULL DEFAULT (''),
        [OP_Permission] [varchar](50) NOT NULL DEFAULT (''),
        [Nac_Code] [varchar](50) NOT NULL DEFAULT (''),
        [Absconding] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [ETSEAdequate] [varchar](1) NOT NULL DEFAULT ('N'),
        [ETSEComments] [varchar](250) NOT NULL DEFAULT (''),
        [ShiftRotationPattern] [varchar](1000) NULL DEFAULT (''),
        [ShiftRotationEndDate] [datetime] NULL,
        [RotationInterval] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [AutoAttReq] [varchar](1) NOT NULL DEFAULT ('N'),
        [PanNo] [varchar](100) NOT NULL DEFAULT ('PANNOTAVBL'),
        [Setters] [varchar](2) NOT NULL DEFAULT ('N'),
        [EmpAttDesc] [varchar](800) NOT NULL DEFAULT (''),
        [ppserv] [varchar](1000) NOT NULL DEFAULT (''),
        [MonthDayId] [varchar](1) NOT NULL DEFAULT ('W'),
        [SalOnWeeklyOff] [varchar](1) NOT NULL DEFAULT ('N'),
        [SalOnWeeklyOffGo] [varchar](1) NOT NULL DEFAULT ('N'),
        [WeekOff_HourlyGo] [varchar](1) NOT NULL DEFAULT ('N'),
        [WeekOff_Hourly] [varchar](1) NOT NULL DEFAULT ('N'),
        [StandardHours] [varchar](50) NOT NULL DEFAULT ('8,8,8,8,8,8,8'),
        [MonthDay] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [Form5Remarks] [varchar](8000) NOT NULL DEFAULT (''),
        [UnAvailGenHol_Present] [varchar](1) NOT NULL DEFAULT ('N'),
        [UnAvailGenHol_Absent] [varchar](1) NOT NULL DEFAULT ('N'),
        [ActualRent] [float] NOT NULL DEFAULT ((0)),
        [EntryEmpId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [EntryComputer] [varchar](100) NOT NULL DEFAULT (''),
        [Exclusiveuser] [varchar](1) NOT NULL DEFAULT ('N'),
        [WorkDaysBasedOnAbsent] [varchar](1) NOT NULL DEFAULT ('N'),
        [IsHandicapped] [varchar](1) NOT NULL DEFAULT ('N'),
        [AccExpenseSubID] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [AccBonusSubID] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [ReportUser] [varchar](1) NOT NULL DEFAULT ('N'),
        [LocationId] [numeric](18, 0) NOT NULL DEFAULT ((1)),
        [UserGroupId] [numeric](18, 0) NOT NULL DEFAULT ('0'),
        [PayrollLocationId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [ServiceID] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [IFSCCode] [varchar](50) NOT NULL DEFAULT (''),
        [Bank_BranchName] [varchar](100) NOT NULL DEFAULT (''),
        [ReportTo] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [EarlierEmployerName] [varchar](50) NOT NULL DEFAULT (''),
        [EarlierEmployerLocation] [varchar](50) NOT NULL DEFAULT (''),
        [EarlierDesignation] [varchar](50) NOT NULL DEFAULT (''),
        [EarlierWorkedFrom] [date] NULL,
        [EarlierWorkedTo] [date] NULL,
        [EarlierSalaryDrawn] [float] NOT NULL DEFAULT ((0)),
        [EarlierReportingTo] [varchar](50) NOT NULL DEFAULT (''),
        [AttachmentForEarlierEmprDetails] [varchar](8000) NOT NULL DEFAULT (''),
        [IsSrCitizen] [varchar](1) NOT NULL DEFAULT ('N'),
        [BankName] [varchar](50) NOT NULL DEFAULT (''),
        [SubIds] [varchar](1000) NOT NULL DEFAULT (''),
        [EmailPwd] [varchar](50) NOT NULL DEFAULT (''),
        [Cityid] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [EffectiveDate] [date] NOT NULL DEFAULT (''),
        [Ctc] [float] NOT NULL DEFAULT ((0)),
        [ExFromPayRoll] [varchar](5) NOT NULL DEFAULT ('N'),
        [SignatureImage] [image] NULL,
        [EmailUserName] [varchar](100) NOT NULL DEFAULT (''),
        [IsContractEmp] [varchar](1) NOT NULL DEFAULT ('N'),
        [RFID] [nvarchar](1000) NOT NULL DEFAULT (''),
        [RFIDEffFrom] [date] NULL,
        [RFIDEffTo] [date] NULL,
        [ContractorTypeId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [Cont_BadgeNo] [varchar](1000) NOT NULL DEFAULT (''),
        [Status_EffectiveDate] [datetime] NULL,
        [StatusId] [numeric](18, 0) NOT NULL DEFAULT ((0)),
        [Territory] [varchar](100) NOT NULL DEFAULT (''),
        [MobileAppRegId] [varchar](500) NULL DEFAULT (''),
        [MobileAppLocUpdateMins] [numeric](18, 0) NOT NULL DEFAULT ((15)),
        [Deptids] [varchar](50) NOT NULL DEFAULT (''),
        [DefaultESS] [varchar](1) NOT NULL DEFAULT ('N'),
        [AadharNo] [numeric](18, 0) NOT NULL DEFAULT ((0))
    )
END
GO

-- 1.3 Complaints_Employee Table (Synced from Employee for complaints system)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Complaints_Employee' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Complaints_Employee] (
        [EmpID] [int] NOT NULL PRIMARY KEY,
        [EmpCode] [nvarchar](50) NOT NULL,
        [EmpName] [nvarchar](100) NULL,
        [Password] [nvarchar](100) NULL,
        [DeptID] [int] NULL,
        [DeptName] [nvarchar](100) NULL,
        [CreatedDate] [datetime] NULL DEFAULT (getdate())
    )
END
GO

-- 1.4 Complaints Table (Main complaints data)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Complaints' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Complaints] (
        [ComplaintId] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [ComplaintNumber] [nvarchar](50) NOT NULL UNIQUE,
        [Title] [nvarchar](200) NOT NULL,
        [Description] [nvarchar](max) NULL,
        [SubmittedByEmpCode] [nvarchar](50) NOT NULL,
        [StatusId] [int] NOT NULL DEFAULT (1),
        [Priority] [nvarchar](20) NOT NULL DEFAULT ('Medium'),
        [Category] [nvarchar](100) NULL,
        [SubmissionDate] [datetime] NOT NULL DEFAULT (getdate()),
        [LastUpdateDate] [datetime] NOT NULL DEFAULT (getdate()),
        [ResolutionNotes] [nvarchar](max) NULL,
        [IsConfidential] [bit] NOT NULL DEFAULT (0)
    )
END
GO

-- 1.5 ComplaintAssignments Table (Track complaint assignments)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplaintAssignments' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplaintAssignments] (
        [AssignmentId] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [ComplaintId] [int] NOT NULL,
        [AssignedToEmpCode] [nvarchar](50) NOT NULL,
        [AssignedByEmpCode] [nvarchar](50) NOT NULL,
        [AssignmentDate] [datetime] NOT NULL DEFAULT (getdate()),
        [Notes] [nvarchar](500) NULL,
        FOREIGN KEY ([ComplaintId]) REFERENCES [Complaints]([ComplaintId])
    )
END
GO

-- 1.6 ComplaintStatusHistory Table (Track status changes)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplaintStatusHistory' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplaintStatusHistory] (
        [HistoryId] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [ComplaintId] [int] NOT NULL,
        [OldStatusId] [int] NULL,
        [NewStatusId] [int] NOT NULL,
        [ChangedByEmpCode] [nvarchar](50) NOT NULL,
        [ChangeDate] [datetime] NOT NULL DEFAULT (getdate()),
        [Notes] [nvarchar](500) NULL,
        FOREIGN KEY ([ComplaintId]) REFERENCES [Complaints]([ComplaintId])
    )
END
GO

-- 1.7 ComplaintAttachments Table (File attachments)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplaintAttachments' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplaintAttachments] (
        [AttachmentId] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [ComplaintId] [int] NOT NULL,
        [FileName] [nvarchar](255) NOT NULL,
        [OriginalFileName] [nvarchar](255) NOT NULL,
        [FilePath] [nvarchar](500) NOT NULL,
        [FileSize] [bigint] NOT NULL,
        [MimeType] [nvarchar](100) NULL,
        [UploadedByEmpCode] [nvarchar](50) NOT NULL,
        [UploadDate] [datetime] NOT NULL DEFAULT (getdate()),
        FOREIGN KEY ([ComplaintId]) REFERENCES [Complaints]([ComplaintId])
    )
END
GO

-- 1.8 AuthorizedDepartments Table (Departments authorized to handle complaints)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AuthorizedDepartments' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[AuthorizedDepartments] (
        [Id] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [DeptName] [nvarchar](100) NOT NULL,
        [IsActive] [bit] NOT NULL DEFAULT (1),
        [CreatedDate] [datetime] NOT NULL DEFAULT (getdate())
    )
END
GO

-- 1.9 EmployeePermissions Table (User permissions for complaints system)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmployeePermissions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[EmployeePermissions] (
        [Id] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [EmpCode] [nvarchar](50) NOT NULL,
        [CanAssign] [bit] NOT NULL DEFAULT (0),
        [CanUpdateStatus] [bit] NOT NULL DEFAULT (0),
        [CanViewDashboard] [bit] NOT NULL DEFAULT (0),
        [CreatedDate] [datetime] NOT NULL DEFAULT (getdate()),
        [UpdatedDate] [datetime] NOT NULL DEFAULT (getdate())
    )
END
GO

-- =====================================================
-- 2. INDEXES FOR PERFORMANCE
-- =====================================================

-- Index on Complaints for better query performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Complaints_SubmittedBy')
    CREATE INDEX IX_Complaints_SubmittedBy ON Complaints(SubmittedByEmpCode)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Complaints_Status')
    CREATE INDEX IX_Complaints_Status ON Complaints(StatusId)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Complaints_SubmissionDate')
    CREATE INDEX IX_Complaints_SubmissionDate ON Complaints(SubmissionDate)
GO

-- Index on ComplaintAssignments
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ComplaintAssignments_AssignedTo')
    CREATE INDEX IX_ComplaintAssignments_AssignedTo ON ComplaintAssignments(AssignedToEmpCode)
GO

-- Index on Complaints_Employee
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ComplaintsEmployee_EmpCode')
    CREATE INDEX IX_ComplaintsEmployee_EmpCode ON Complaints_Employee(EmpCode)
GO

-- =====================================================
-- 3. TRIGGERS
-- =====================================================

-- 3.1 Trigger to sync Employee INSERT to Complaints_Employee
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_Employee_Insert')
    DROP TRIGGER TR_Employee_Insert
GO

CREATE TRIGGER TR_Employee_Insert
ON Employee
AFTER INSERT
AS
BEGIN
    SET NOCOUNT ON;

    INSERT INTO Complaints_Employee (EmpID, EmpCode, EmpName, Password, DeptID, DeptName)
    SELECT
        i.EmpID,
        i.EmpCode,
        i.EmpName,
        '1234',
        i.DeptID,
        d.DeptName
    FROM inserted i
    INNER JOIN Department d ON i.DeptID = d.DeptID;
END
GO

-- 3.2 Trigger to sync Employee UPDATE to Complaints_Employee
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_Employee_AfterUpdate')
    DROP TRIGGER TR_Employee_AfterUpdate
GO

CREATE TRIGGER TR_Employee_AfterUpdate
ON Employee
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    EXEC SyncEmployeesToComplaints;
END
GO

-- 3.3 Trigger to update LastUpdateDate on Complaints when updated
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_Complaints_UpdateDate')
    DROP TRIGGER TR_Complaints_UpdateDate
GO

CREATE TRIGGER TR_Complaints_UpdateDate
ON Complaints
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE Complaints
    SET LastUpdateDate = GETDATE()
    WHERE ComplaintId IN (SELECT ComplaintId FROM inserted);
END
GO

-- 3.4 Trigger to log status changes in ComplaintStatusHistory
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_Complaints_StatusChange')
    DROP TRIGGER TR_Complaints_StatusChange
GO

CREATE TRIGGER TR_Complaints_StatusChange
ON Complaints
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;

    -- Only log if StatusId actually changed
    INSERT INTO ComplaintStatusHistory (ComplaintId, OldStatusId, NewStatusId, ChangedByEmpCode, Notes)
    SELECT
        i.ComplaintId,
        d.StatusId as OldStatusId,
        i.StatusId as NewStatusId,
        i.SubmittedByEmpCode, -- This should be updated to track who made the change
        'Status changed via system'
    FROM inserted i
    INNER JOIN deleted d ON i.ComplaintId = d.ComplaintId
    WHERE i.StatusId != d.StatusId;
END
GO

-- =====================================================
-- 4. STORED PROCEDURES
-- =====================================================

-- 4.1 Stored Procedure to sync Employee data to Complaints_Employee
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'SyncEmployeesToComplaints')
    DROP PROCEDURE SyncEmployeesToComplaints
GO

CREATE PROCEDURE SyncEmployeesToComplaints
AS
BEGIN
    SET NOCOUNT ON;

    -- Update existing records
    UPDATE ce
    SET
        EmpName = e.EmpName,
        DeptID = e.DeptId,
        DeptName = d.DeptName
    FROM Complaints_Employee ce
    INNER JOIN Employee e ON ce.EmpID = e.EmpId
    INNER JOIN Department d ON e.DeptId = d.DeptID;

    -- Insert new employees that don't exist in Complaints_Employee
    INSERT INTO Complaints_Employee (EmpID, EmpCode, EmpName, Password, DeptID, DeptName)
    SELECT
        e.EmpId,
        e.EmpCode,
        e.EmpName,
        '1234',
        e.DeptId,
        d.DeptName
    FROM Employee e
    INNER JOIN Department d ON e.DeptId = d.DeptID
    WHERE NOT EXISTS (
        SELECT 1 FROM Complaints_Employee ce WHERE ce.EmpID = e.EmpId
    );
END
GO

-- 4.2 Stored Procedure to generate complaint numbers
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'GenerateComplaintNumber')
    DROP PROCEDURE GenerateComplaintNumber
GO

CREATE PROCEDURE GenerateComplaintNumber
    @ComplaintNumber NVARCHAR(50) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Year NVARCHAR(4) = CAST(YEAR(GETDATE()) AS NVARCHAR(4));
    DECLARE @Month NVARCHAR(2) = RIGHT('0' + CAST(MONTH(GETDATE()) AS NVARCHAR(2)), 2);
    DECLARE @NextNumber INT;

    -- Get the next sequential number for this month
    SELECT @NextNumber = ISNULL(MAX(CAST(RIGHT(ComplaintNumber, 4) AS INT)), 0) + 1
    FROM Complaints
    WHERE ComplaintNumber LIKE 'COMP-' + @Year + @Month + '%';

    -- Format: COMP-YYYYMM-NNNN
    SET @ComplaintNumber = 'COMP-' + @Year + @Month + '-' + RIGHT('0000' + CAST(@NextNumber AS NVARCHAR(4)), 4);
END
GO

-- =====================================================
-- 5. INITIAL DATA SETUP
-- =====================================================

-- 5.1 Insert default authorized departments
IF NOT EXISTS (SELECT * FROM AuthorizedDepartments WHERE DeptName = 'Administration')
BEGIN
    INSERT INTO AuthorizedDepartments (DeptName, IsActive)
    VALUES ('Administration', 1)
END
GO

IF NOT EXISTS (SELECT * FROM AuthorizedDepartments WHERE DeptName = 'Information Technology')
BEGIN
    INSERT INTO AuthorizedDepartments (DeptName, IsActive)
    VALUES ('Information Technology', 1)
END
GO

-- 5.2 Set up admin permissions for Administration and IT departments
INSERT INTO EmployeePermissions (EmpCode, CanAssign, CanUpdateStatus, CanViewDashboard)
SELECT DISTINCT
    ce.EmpCode,
    1, -- CanAssign
    1, -- CanUpdateStatus
    1  -- CanViewDashboard
FROM Complaints_Employee ce
WHERE (ce.DeptName LIKE 'Administration%' OR ce.DeptName = 'Information Technology')
AND NOT EXISTS (
    SELECT 1 FROM EmployeePermissions ep WHERE ep.EmpCode = ce.EmpCode
)
GO

-- =====================================================
-- 6. VIEWS FOR REPORTING
-- =====================================================

-- 6.1 View for complaint summary with employee details
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ComplaintSummary')
    DROP VIEW vw_ComplaintSummary
GO

CREATE VIEW vw_ComplaintSummary
AS
SELECT
    c.ComplaintId,
    c.ComplaintNumber,
    c.Title,
    c.Description,
    c.SubmittedByEmpCode,
    submitter.EmpName as SubmittedByName,
    submitter.DeptName as SubmittedByDepartment,
    CASE
        WHEN c.StatusId = 1 THEN 'New'
        WHEN c.StatusId = 2 THEN 'Assigned'
        WHEN c.StatusId = 3 THEN 'In Progress'
        WHEN c.StatusId = 4 THEN 'Resolved'
        WHEN c.StatusId = 5 THEN 'Rejected'
        ELSE 'Unknown'
    END as Status,
    c.Priority,
    c.Category,
    c.SubmissionDate,
    c.LastUpdateDate,
    c.ResolutionNotes,
    c.IsConfidential,
    assigned.EmpName as AssignedToName,
    assigned.DeptName as AssignedToDepartment
FROM Complaints c
LEFT JOIN Complaints_Employee submitter ON c.SubmittedByEmpCode = submitter.EmpCode
LEFT JOIN (
    SELECT
        ca.ComplaintId,
        ca.AssignedToEmpCode,
        ROW_NUMBER() OVER (PARTITION BY ca.ComplaintId ORDER BY ca.AssignmentDate DESC) as rn
    FROM ComplaintAssignments ca
) latest_assignment ON c.ComplaintId = latest_assignment.ComplaintId AND latest_assignment.rn = 1
LEFT JOIN Complaints_Employee assigned ON latest_assignment.AssignedToEmpCode = assigned.EmpCode
GO

-- =====================================================
-- 7. SECURITY AND PERMISSIONS
-- =====================================================

-- Grant necessary permissions to application user
-- Note: Replace 'ComplaintsAppUser' with your actual application database user
-- GRANT SELECT, INSERT, UPDATE ON Complaints TO ComplaintsAppUser
-- GRANT SELECT, INSERT, UPDATE ON Complaints_Employee TO ComplaintsAppUser
-- GRANT SELECT ON Employee TO ComplaintsAppUser
-- GRANT SELECT ON Department TO ComplaintsAppUser
-- GRANT SELECT, INSERT, UPDATE ON ComplaintAssignments TO ComplaintsAppUser
-- GRANT SELECT, INSERT ON ComplaintStatusHistory TO ComplaintsAppUser
-- GRANT SELECT, INSERT, DELETE ON ComplaintAttachments TO ComplaintsAppUser
-- GRANT SELECT ON AuthorizedDepartments TO ComplaintsAppUser
-- GRANT SELECT, INSERT, UPDATE ON EmployeePermissions TO ComplaintsAppUser
-- GRANT EXECUTE ON SyncEmployeesToComplaints TO ComplaintsAppUser
-- GRANT EXECUTE ON GenerateComplaintNumber TO ComplaintsAppUser

-- =====================================================
-- 8. COMPLETION MESSAGE
-- =====================================================

PRINT '=============================================='
PRINT 'INTERNAL COMPLAINTS PORTAL DATABASE SETUP COMPLETE'
PRINT '=============================================='
PRINT 'Tables Created:'
PRINT '- Department (Core department data)'
PRINT '- Employee (Core employee data)'
PRINT '- Complaints_Employee (Synced employee data for complaints)'
PRINT '- Complaints (Main complaints table)'
PRINT '- ComplaintAssignments (Assignment tracking)'
PRINT '- ComplaintStatusHistory (Status change history)'
PRINT '- ComplaintAttachments (File attachments)'
PRINT '- AuthorizedDepartments (Authorized departments)'
PRINT '- EmployeePermissions (User permissions)'
PRINT ''
PRINT 'Triggers Created:'
PRINT '- TR_Employee_Insert (Sync new employees)'
PRINT '- TR_Employee_AfterUpdate (Sync employee updates)'
PRINT '- TR_Complaints_UpdateDate (Update last modified date)'
PRINT '- TR_Complaints_StatusChange (Log status changes)'
PRINT ''
PRINT 'Stored Procedures Created:'
PRINT '- SyncEmployeesToComplaints (Manual sync procedure)'
PRINT '- GenerateComplaintNumber (Generate unique complaint numbers)'
PRINT ''
PRINT 'Views Created:'
PRINT '- vw_ComplaintSummary (Comprehensive complaint view)'
PRINT ''
PRINT 'The Internal Complaints Portal is ready to use!'
PRINT '=============================================='
