{"ast": null, "code": "function buildProjectionTransform(delta, treeScale, latestTransform) {\n  let transform = \"\";\n  /**\n   * The translations we use to calculate are always relative to the viewport coordinate space.\n   * But when we apply scales, we also scale the coordinate space of an element and its children.\n   * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n   * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n   */\n  const xTranslate = delta.x.translate / treeScale.x;\n  const yTranslate = delta.y.translate / treeScale.y;\n  if (xTranslate || yTranslate) {\n    transform = \"translate3d(\".concat(xTranslate, \"px, \").concat(yTranslate, \"px, 0) \");\n  }\n  /**\n   * Apply scale correction for the tree transform.\n   * This will apply scale to the screen-orientated axes.\n   */\n  if (treeScale.x !== 1 || treeScale.y !== 1) {\n    transform += \"scale(\".concat(1 / treeScale.x, \", \").concat(1 / treeScale.y, \") \");\n  }\n  if (latestTransform) {\n    const {\n      rotate,\n      rotateX,\n      rotateY\n    } = latestTransform;\n    if (rotate) transform += \"rotate(\".concat(rotate, \"deg) \");\n    if (rotateX) transform += \"rotateX(\".concat(rotateX, \"deg) \");\n    if (rotateY) transform += \"rotateY(\".concat(rotateY, \"deg) \");\n  }\n  /**\n   * Apply scale to match the size of the element to the size we want it.\n   * This will apply scale to the element-orientated axes.\n   */\n  const elementScaleX = delta.x.scale * treeScale.x;\n  const elementScaleY = delta.y.scale * treeScale.y;\n  if (elementScaleX !== 1 || elementScaleY !== 1) {\n    transform += \"scale(\".concat(elementScaleX, \", \").concat(elementScaleY, \")\");\n  }\n  return transform || \"none\";\n}\nexport { buildProjectionTransform };", "map": {"version": 3, "names": ["buildProjectionTransform", "delta", "treeScale", "latestTransform", "transform", "xTranslate", "x", "translate", "yTranslate", "y", "concat", "rotate", "rotateX", "rotateY", "elementScaleX", "scale", "elementScaleY"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/framer-motion/dist/es/projection/styles/transform.mjs"], "sourcesContent": ["function buildProjectionTransform(delta, treeScale, latestTransform) {\n    let transform = \"\";\n    /**\n     * The translations we use to calculate are always relative to the viewport coordinate space.\n     * But when we apply scales, we also scale the coordinate space of an element and its children.\n     * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n     * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n     */\n    const xTranslate = delta.x.translate / treeScale.x;\n    const yTranslate = delta.y.translate / treeScale.y;\n    if (xTranslate || yTranslate) {\n        transform = `translate3d(${xTranslate}px, ${yTranslate}px, 0) `;\n    }\n    /**\n     * Apply scale correction for the tree transform.\n     * This will apply scale to the screen-orientated axes.\n     */\n    if (treeScale.x !== 1 || treeScale.y !== 1) {\n        transform += `scale(${1 / treeScale.x}, ${1 / treeScale.y}) `;\n    }\n    if (latestTransform) {\n        const { rotate, rotateX, rotateY } = latestTransform;\n        if (rotate)\n            transform += `rotate(${rotate}deg) `;\n        if (rotateX)\n            transform += `rotateX(${rotateX}deg) `;\n        if (rotateY)\n            transform += `rotateY(${rotateY}deg) `;\n    }\n    /**\n     * Apply scale to match the size of the element to the size we want it.\n     * This will apply scale to the element-orientated axes.\n     */\n    const elementScaleX = delta.x.scale * treeScale.x;\n    const elementScaleY = delta.y.scale * treeScale.y;\n    if (elementScaleX !== 1 || elementScaleY !== 1) {\n        transform += `scale(${elementScaleX}, ${elementScaleY})`;\n    }\n    return transform || \"none\";\n}\n\nexport { buildProjectionTransform };\n"], "mappings": "AAAA,SAASA,wBAAwBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAE;EACjE,IAAIC,SAAS,GAAG,EAAE;EAClB;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMC,UAAU,GAAGJ,KAAK,CAACK,CAAC,CAACC,SAAS,GAAGL,SAAS,CAACI,CAAC;EAClD,MAAME,UAAU,GAAGP,KAAK,CAACQ,CAAC,CAACF,SAAS,GAAGL,SAAS,CAACO,CAAC;EAClD,IAAIJ,UAAU,IAAIG,UAAU,EAAE;IAC1BJ,SAAS,kBAAAM,MAAA,CAAkBL,UAAU,UAAAK,MAAA,CAAOF,UAAU,YAAS;EACnE;EACA;AACJ;AACA;AACA;EACI,IAAIN,SAAS,CAACI,CAAC,KAAK,CAAC,IAAIJ,SAAS,CAACO,CAAC,KAAK,CAAC,EAAE;IACxCL,SAAS,aAAAM,MAAA,CAAa,CAAC,GAAGR,SAAS,CAACI,CAAC,QAAAI,MAAA,CAAK,CAAC,GAAGR,SAAS,CAACO,CAAC,OAAI;EACjE;EACA,IAAIN,eAAe,EAAE;IACjB,MAAM;MAAEQ,MAAM;MAAEC,OAAO;MAAEC;IAAQ,CAAC,GAAGV,eAAe;IACpD,IAAIQ,MAAM,EACNP,SAAS,cAAAM,MAAA,CAAcC,MAAM,UAAO;IACxC,IAAIC,OAAO,EACPR,SAAS,eAAAM,MAAA,CAAeE,OAAO,UAAO;IAC1C,IAAIC,OAAO,EACPT,SAAS,eAAAM,MAAA,CAAeG,OAAO,UAAO;EAC9C;EACA;AACJ;AACA;AACA;EACI,MAAMC,aAAa,GAAGb,KAAK,CAACK,CAAC,CAACS,KAAK,GAAGb,SAAS,CAACI,CAAC;EACjD,MAAMU,aAAa,GAAGf,KAAK,CAACQ,CAAC,CAACM,KAAK,GAAGb,SAAS,CAACO,CAAC;EACjD,IAAIK,aAAa,KAAK,CAAC,IAAIE,aAAa,KAAK,CAAC,EAAE;IAC5CZ,SAAS,aAAAM,MAAA,CAAaI,aAAa,QAAAJ,MAAA,CAAKM,aAAa,MAAG;EAC5D;EACA,OAAOZ,SAAS,IAAI,MAAM;AAC9B;AAEA,SAASJ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}