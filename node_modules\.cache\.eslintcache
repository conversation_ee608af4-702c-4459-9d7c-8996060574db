[{"C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\theme.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\App.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\contexts\\AuthContext.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\AuthorityManagement.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\Dashboard.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\Login.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ComplaintsList.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ChangePassword.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\components\\Layout.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\utils\\axiosConfig.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\config.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ComplaintDetails.js": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\NewComplaint.js": "14"}, {"size": 694, "mtime": 1749296435050, "results": "15", "hashOfConfig": "16"}, {"size": 5310, "mtime": 1749297097805, "results": "17", "hashOfConfig": "16"}, {"size": 3535, "mtime": 1749443725945, "results": "18", "hashOfConfig": "16"}, {"size": 6540, "mtime": 1749447831058, "results": "19", "hashOfConfig": "16"}, {"size": 13852, "mtime": 1749447878557, "results": "20", "hashOfConfig": "16"}, {"size": 20522, "mtime": 1749447593338, "results": "21", "hashOfConfig": "16"}, {"size": 8193, "mtime": 1749446144331, "results": "22", "hashOfConfig": "16"}, {"size": 12573, "mtime": 1749285498427, "results": "23", "hashOfConfig": "16"}, {"size": 6486, "mtime": 1749293671196, "results": "24", "hashOfConfig": "16"}, {"size": 7576, "mtime": 1749447970067, "results": "25", "hashOfConfig": "16"}, {"size": 3583, "mtime": 1749445811506, "results": "26", "hashOfConfig": "16"}, {"size": 160, "mtime": 1749277067827, "results": "27", "hashOfConfig": "16"}, {"size": 31125, "mtime": 1749292642731, "results": "28", "hashOfConfig": "16"}, {"size": 12367, "mtime": 1749293529503, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "g5dtua", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\theme.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\AuthorityManagement.js", ["72", "73", "74"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\Dashboard.js", ["75", "76", "77", "78", "79", "80", "81", "82", "83"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\Login.js", ["84", "85", "86"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ComplaintsList.js", ["87", "88"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ChangePassword.js", ["89"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\components\\Layout.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\utils\\axiosConfig.js", ["90"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\config.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ComplaintDetails.js", ["91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\NewComplaint.js", [], [], {"ruleId": "107", "severity": 1, "message": "108", "line": 34, "column": 8, "nodeType": "109", "messageId": "110", "endLine": 34, "endColumn": 14}, {"ruleId": "111", "severity": 1, "message": "112", "line": 162, "column": 8, "nodeType": "113", "endLine": 162, "endColumn": 10, "suggestions": "114"}, {"ruleId": "111", "severity": 1, "message": "112", "line": 202, "column": 8, "nodeType": "113", "endLine": 202, "endColumn": 43, "suggestions": "115"}, {"ruleId": "107", "severity": 1, "message": "116", "line": 15, "column": 3, "nodeType": "109", "messageId": "110", "endLine": 15, "endColumn": 10}, {"ruleId": "107", "severity": 1, "message": "117", "line": 16, "column": 3, "nodeType": "109", "messageId": "110", "endLine": 16, "endColumn": 8}, {"ruleId": "107", "severity": 1, "message": "118", "line": 18, "column": 3, "nodeType": "109", "messageId": "110", "endLine": 18, "endColumn": 17}, {"ruleId": "107", "severity": 1, "message": "119", "line": 29, "column": 12, "nodeType": "109", "messageId": "110", "endLine": 29, "endColumn": 26}, {"ruleId": "107", "severity": 1, "message": "120", "line": 30, "column": 15, "nodeType": "109", "messageId": "110", "endLine": 30, "endColumn": 24}, {"ruleId": "107", "severity": 1, "message": "121", "line": 33, "column": 18, "nodeType": "109", "messageId": "110", "endLine": 33, "endColumn": 33}, {"ruleId": "107", "severity": 1, "message": "122", "line": 35, "column": 10, "nodeType": "109", "messageId": "110", "endLine": 35, "endColumn": 16}, {"ruleId": "107", "severity": 1, "message": "123", "line": 35, "column": 18, "nodeType": "109", "messageId": "110", "endLine": 35, "endColumn": 37}, {"ruleId": "107", "severity": 1, "message": "124", "line": 452, "column": 9, "nodeType": "109", "messageId": "110", "endLine": 452, "endColumn": 17}, {"ruleId": "107", "severity": 1, "message": "125", "line": 31, "column": 18, "nodeType": "109", "messageId": "110", "endLine": 31, "endColumn": 22}, {"ruleId": "126", "severity": 2, "message": "127", "line": 54, "column": 33, "nodeType": "109", "messageId": "128", "endLine": 54, "endColumn": 39}, {"ruleId": "126", "severity": 2, "message": "127", "line": 54, "column": 49, "nodeType": "109", "messageId": "128", "endLine": 54, "endColumn": 55}, {"ruleId": "107", "severity": 1, "message": "129", "line": 6, "column": 3, "nodeType": "109", "messageId": "110", "endLine": 6, "endColumn": 14}, {"ruleId": "107", "severity": 1, "message": "125", "line": 53, "column": 11, "nodeType": "109", "messageId": "110", "endLine": 53, "endColumn": 15}, {"ruleId": "107", "severity": 1, "message": "125", "line": 34, "column": 11, "nodeType": "109", "messageId": "110", "endLine": 34, "endColumn": 15}, {"ruleId": "107", "severity": 1, "message": "130", "line": 15, "column": 11, "nodeType": "109", "messageId": "110", "endLine": 15, "endColumn": 22}, {"ruleId": "107", "severity": 1, "message": "131", "line": 1, "column": 51, "nodeType": "109", "messageId": "110", "endLine": 1, "endColumn": 59}, {"ruleId": "107", "severity": 1, "message": "132", "line": 34, "column": 17, "nodeType": "109", "messageId": "110", "endLine": 34, "endColumn": 31}, {"ruleId": "107", "severity": 1, "message": "133", "line": 35, "column": 22, "nodeType": "109", "messageId": "110", "endLine": 35, "endColumn": 30}, {"ruleId": "107", "severity": 1, "message": "134", "line": 41, "column": 18, "nodeType": "109", "messageId": "110", "endLine": 41, "endColumn": 26}, {"ruleId": "107", "severity": 1, "message": "135", "line": 44, "column": 10, "nodeType": "109", "messageId": "110", "endLine": 44, "endColumn": 24}, {"ruleId": "107", "severity": 1, "message": "136", "line": 45, "column": 10, "nodeType": "109", "messageId": "110", "endLine": 45, "endColumn": 30}, {"ruleId": "107", "severity": 1, "message": "137", "line": 46, "column": 10, "nodeType": "109", "messageId": "110", "endLine": 46, "endColumn": 24}, {"ruleId": "107", "severity": 1, "message": "138", "line": 56, "column": 7, "nodeType": "109", "messageId": "110", "endLine": 56, "endColumn": 20}, {"ruleId": "107", "severity": 1, "message": "139", "line": 59, "column": 7, "nodeType": "109", "messageId": "110", "endLine": 59, "endColumn": 22}, {"ruleId": "107", "severity": 1, "message": "140", "line": 146, "column": 10, "nodeType": "109", "messageId": "110", "endLine": 146, "endColumn": 25}, {"ruleId": "107", "severity": 1, "message": "141", "line": 146, "column": 27, "nodeType": "109", "messageId": "110", "endLine": 146, "endColumn": 45}, {"ruleId": "107", "severity": 1, "message": "142", "line": 151, "column": 10, "nodeType": "109", "messageId": "110", "endLine": 151, "endColumn": 21}, {"ruleId": "107", "severity": 1, "message": "143", "line": 152, "column": 10, "nodeType": "109", "messageId": "110", "endLine": 152, "endColumn": 28}, {"ruleId": "107", "severity": 1, "message": "144", "line": 155, "column": 10, "nodeType": "109", "messageId": "110", "endLine": 155, "endColumn": 17}, {"ruleId": "107", "severity": 1, "message": "145", "line": 155, "column": 19, "nodeType": "109", "messageId": "110", "endLine": 155, "endColumn": 29}, {"ruleId": "111", "severity": 1, "message": "146", "line": 230, "column": 6, "nodeType": "113", "endLine": 230, "endColumn": 29, "suggestions": "147"}, "no-unused-vars", "'config' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'showNotification'. Either include it or remove the dependency array.", "ArrayExpression", ["148"], ["149"], "'Divider' is defined but never used.", "'Paper' is defined but never used.", "'LinearProgress' is defined but never used.", "'EfficiencyIcon' is defined but never used.", "'TrendIcon' is defined but never used.", "'AnimatePresence' is defined but never used.", "'format' is defined but never used.", "'formatDistanceToNow' is defined but never used.", "'isMobile' is assigned a value but never used.", "'user' is assigned a value but never used.", "no-restricted-globals", "Unexpected use of 'screen'.", "defaultMessage", "'CardContent' is defined but never used.", "'currentPort' is assigned a value but never used.", "'Suspense' is defined but never used.", "'AttachFileIcon' is defined but never used.", "'FileIcon' is defined but never used.", "'parseISO' is defined but never used.", "'DateTimePicker' is defined but never used.", "'LocalizationProvider' is defined but never used.", "'AdapterDateFns' is defined but never used.", "'statusOptions' is assigned a value but never used.", "'DetailsSkeleton' is assigned a value but never used.", "'resolutionNotes' is assigned a value but never used.", "'setResolutionNotes' is assigned a value but never used.", "'departments' is assigned a value but never used.", "'selectedDepartment' is assigned a value but never used.", "'dueDate' is assigned a value but never used.", "'setDueDate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadingTimeout'. Either include it or remove the dependency array.", ["150"], {"desc": "151", "fix": "152"}, {"desc": "153", "fix": "154"}, {"desc": "155", "fix": "156"}, "Update the dependencies array to be: [showNotification]", {"range": "157", "text": "158"}, "Update the dependencies array to be: [employees, refreshUserPermissions, showNotification]", {"range": "159", "text": "160"}, "Update the dependencies array to be: [fetchComplaintDetails, loadingTimeout]", {"range": "161", "text": "162"}, [5978, 5980], "[showNotification]", [7337, 7372], "[employees, refreshUserPermissions, showNotification]", [7453, 7476], "[fetchComplaintDetails, loadingTimeout]"]