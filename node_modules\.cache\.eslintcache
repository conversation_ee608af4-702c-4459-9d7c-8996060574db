[{"C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\theme.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\App.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\contexts\\AuthContext.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\AuthorityManagement.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\Dashboard.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\Login.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ComplaintsList.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ChangePassword.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\components\\Layout.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\utils\\axiosConfig.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\config.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ComplaintDetails.js": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\NewComplaint.js": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\contexts\\SocketContext.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\components\\PermissionNotification.js": "16"}, {"size": 694, "mtime": 1749296435050, "results": "17", "hashOfConfig": "18"}, {"size": 5310, "mtime": 1749297097805, "results": "19", "hashOfConfig": "18"}, {"size": 3652, "mtime": 1749448208992, "results": "20", "hashOfConfig": "18"}, {"size": 6809, "mtime": 1749449792650, "results": "21", "hashOfConfig": "18"}, {"size": 13852, "mtime": 1749447878557, "results": "22", "hashOfConfig": "18"}, {"size": 19348, "mtime": 1749454620573, "results": "23", "hashOfConfig": "18"}, {"size": 13136, "mtime": 1749453667702, "results": "24", "hashOfConfig": "18"}, {"size": 17421, "mtime": 1749452166132, "results": "25", "hashOfConfig": "18"}, {"size": 6486, "mtime": 1749293671196, "results": "26", "hashOfConfig": "18"}, {"size": 8171, "mtime": 1749454429575, "results": "27", "hashOfConfig": "18"}, {"size": 3583, "mtime": 1749445811506, "results": "28", "hashOfConfig": "18"}, {"size": 160, "mtime": 1749277067827, "results": "29", "hashOfConfig": "18"}, {"size": 30314, "mtime": 1749453424478, "results": "30", "hashOfConfig": "18"}, {"size": 12367, "mtime": 1749293529503, "results": "31", "hashOfConfig": "18"}, {"size": 3823, "mtime": 1749449828917, "results": "32", "hashOfConfig": "18"}, {"size": 1470, "mtime": 1749448220120, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "g5dtua", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\theme.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\AuthorityManagement.js", ["82", "83", "84"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\Dashboard.js", ["85", "86", "87", "88", "89", "90", "91", "92", "93"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\Login.js", ["94"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ComplaintsList.js", ["95"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ChangePassword.js", ["96"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\components\\Layout.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\utils\\axiosConfig.js", ["97"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\config.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\ComplaintDetails.js", ["98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112", "113"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\pages\\NewComplaint.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\contexts\\SocketContext.js", ["114"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\INTERNAL COMPLAINTS\\src\\components\\PermissionNotification.js", [], [], {"ruleId": "115", "severity": 1, "message": "116", "line": 34, "column": 8, "nodeType": "117", "messageId": "118", "endLine": 34, "endColumn": 14}, {"ruleId": "119", "severity": 1, "message": "120", "line": 162, "column": 8, "nodeType": "121", "endLine": 162, "endColumn": 10, "suggestions": "122"}, {"ruleId": "119", "severity": 1, "message": "120", "line": 202, "column": 8, "nodeType": "121", "endLine": 202, "endColumn": 43, "suggestions": "123"}, {"ruleId": "115", "severity": 1, "message": "124", "line": 15, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 15, "endColumn": 10}, {"ruleId": "115", "severity": 1, "message": "125", "line": 16, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 16, "endColumn": 8}, {"ruleId": "115", "severity": 1, "message": "126", "line": 18, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 18, "endColumn": 17}, {"ruleId": "115", "severity": 1, "message": "127", "line": 29, "column": 12, "nodeType": "117", "messageId": "118", "endLine": 29, "endColumn": 26}, {"ruleId": "115", "severity": 1, "message": "128", "line": 30, "column": 15, "nodeType": "117", "messageId": "118", "endLine": 30, "endColumn": 24}, {"ruleId": "115", "severity": 1, "message": "129", "line": 33, "column": 18, "nodeType": "117", "messageId": "118", "endLine": 33, "endColumn": 33}, {"ruleId": "115", "severity": 1, "message": "130", "line": 35, "column": 18, "nodeType": "117", "messageId": "118", "endLine": 35, "endColumn": 37}, {"ruleId": "115", "severity": 1, "message": "131", "line": 269, "column": 7, "nodeType": "117", "messageId": "118", "endLine": 269, "endColumn": 22}, {"ruleId": "115", "severity": 1, "message": "132", "line": 376, "column": 9, "nodeType": "117", "messageId": "118", "endLine": 376, "endColumn": 17}, {"ruleId": "115", "severity": 1, "message": "133", "line": 31, "column": 18, "nodeType": "117", "messageId": "118", "endLine": 31, "endColumn": 22}, {"ruleId": "115", "severity": 1, "message": "133", "line": 53, "column": 11, "nodeType": "117", "messageId": "118", "endLine": 53, "endColumn": 15}, {"ruleId": "115", "severity": 1, "message": "133", "line": 34, "column": 11, "nodeType": "117", "messageId": "118", "endLine": 34, "endColumn": 15}, {"ruleId": "115", "severity": 1, "message": "134", "line": 15, "column": 11, "nodeType": "117", "messageId": "118", "endLine": 15, "endColumn": 22}, {"ruleId": "115", "severity": 1, "message": "135", "line": 1, "column": 51, "nodeType": "117", "messageId": "118", "endLine": 1, "endColumn": 59}, {"ruleId": "115", "severity": 1, "message": "136", "line": 34, "column": 17, "nodeType": "117", "messageId": "118", "endLine": 34, "endColumn": 31}, {"ruleId": "115", "severity": 1, "message": "137", "line": 35, "column": 22, "nodeType": "117", "messageId": "118", "endLine": 35, "endColumn": 30}, {"ruleId": "115", "severity": 1, "message": "138", "line": 41, "column": 18, "nodeType": "117", "messageId": "118", "endLine": 41, "endColumn": 26}, {"ruleId": "115", "severity": 1, "message": "139", "line": 43, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 43, "endColumn": 24}, {"ruleId": "115", "severity": 1, "message": "140", "line": 44, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 44, "endColumn": 30}, {"ruleId": "115", "severity": 1, "message": "141", "line": 45, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 45, "endColumn": 24}, {"ruleId": "115", "severity": 1, "message": "142", "line": 55, "column": 7, "nodeType": "117", "messageId": "118", "endLine": 55, "endColumn": 20}, {"ruleId": "115", "severity": 1, "message": "143", "line": 58, "column": 7, "nodeType": "117", "messageId": "118", "endLine": 58, "endColumn": 22}, {"ruleId": "115", "severity": 1, "message": "144", "line": 145, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 145, "endColumn": 25}, {"ruleId": "115", "severity": 1, "message": "145", "line": 145, "column": 27, "nodeType": "117", "messageId": "118", "endLine": 145, "endColumn": 45}, {"ruleId": "115", "severity": 1, "message": "146", "line": 150, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 150, "endColumn": 21}, {"ruleId": "115", "severity": 1, "message": "147", "line": 151, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 151, "endColumn": 28}, {"ruleId": "115", "severity": 1, "message": "148", "line": 154, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 154, "endColumn": 17}, {"ruleId": "115", "severity": 1, "message": "149", "line": 154, "column": 19, "nodeType": "117", "messageId": "118", "endLine": 154, "endColumn": 29}, {"ruleId": "119", "severity": 1, "message": "150", "line": 217, "column": 6, "nodeType": "121", "endLine": 217, "endColumn": 29, "suggestions": "151"}, {"ruleId": "119", "severity": 1, "message": "152", "line": 116, "column": 6, "nodeType": "121", "endLine": 116, "endColumn": 36, "suggestions": "153"}, "no-unused-vars", "'config' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'showNotification'. Either include it or remove the dependency array.", "ArrayExpression", ["154"], ["155"], "'Divider' is defined but never used.", "'Paper' is defined but never used.", "'LinearProgress' is defined but never used.", "'EfficiencyIcon' is defined but never used.", "'TrendIcon' is defined but never used.", "'AnimatePresence' is defined but never used.", "'formatDistanceToNow' is defined but never used.", "'formatTimestamp' is assigned a value but never used.", "'isMobile' is assigned a value but never used.", "'user' is assigned a value but never used.", "'currentPort' is assigned a value but never used.", "'Suspense' is defined but never used.", "'AttachFileIcon' is defined but never used.", "'FileIcon' is defined but never used.", "'parseISO' is defined but never used.", "'DateTimePicker' is defined but never used.", "'LocalizationProvider' is defined but never used.", "'AdapterDateFns' is defined but never used.", "'statusOptions' is assigned a value but never used.", "'DetailsSkeleton' is assigned a value but never used.", "'resolutionNotes' is assigned a value but never used.", "'setResolutionNotes' is assigned a value but never used.", "'departments' is assigned a value but never used.", "'selectedDepartment' is assigned a value but never used.", "'dueDate' is assigned a value but never used.", "'setDueDate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadingTimeout'. Either include it or remove the dependency array.", ["156"], "React Hook useEffect has missing dependencies: 'logout' and 'socket'. Either include them or remove the dependency array.", ["157"], {"desc": "158", "fix": "159"}, {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, {"desc": "164", "fix": "165"}, "Update the dependencies array to be: [showNotification]", {"range": "166", "text": "167"}, "Update the dependencies array to be: [employees, refreshUserPermissions, showNotification]", {"range": "168", "text": "169"}, "Update the dependencies array to be: [fetchComplaintDetails, loadingTimeout]", {"range": "170", "text": "171"}, "Update the dependencies array to be: [user, refreshUserPermissions, logout, socket]", {"range": "172", "text": "173"}, [5978, 5980], "[showNotification]", [7337, 7372], "[employees, refreshUserPermissions, showNotification]", [6997, 7020], "[fetchComplaintDetails, loadingTimeout]", [3599, 3629], "[user, refreshUserPermissions, logout, socket]"]