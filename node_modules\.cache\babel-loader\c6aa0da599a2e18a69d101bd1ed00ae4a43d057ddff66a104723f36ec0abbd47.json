{"ast": null, "code": "import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nexport function getModalUtilityClass(slot) {\n  return generateUtilityClass('MuiModal', slot);\n}\nexport const modalClasses = generateUtilityClasses('MuiModal', ['root', 'hidden', 'backdrop']);", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getModalUtilityClass", "slot", "modalClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Modal/modalClasses.js"], "sourcesContent": ["import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nexport function getModalUtilityClass(slot) {\n  return generateUtilityClass('MuiModal', slot);\n}\nexport const modalClasses = generateUtilityClasses('MuiModal', ['root', 'hidden', 'backdrop']);"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOF,oBAAoB,CAAC,UAAU,EAAEE,IAAI,CAAC;AAC/C;AACA,OAAO,MAAMC,YAAY,GAAGJ,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}