{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport const getPickersSlideTransitionUtilityClass = slot =>\n// TODO v6: Rename 'PrivatePickersSlideTransition' to 'MuiPickersSlideTransition' to follow convention\ngenerateUtilityClass('PrivatePickersSlideTransition', slot);\nexport const pickersSlideTransitionClasses = generateUtilityClasses(\n// TODO v6: Rename 'PrivatePickersSlideTransition' to 'MuiPickersSlideTransition' to follow convention\n'PrivatePickersSlideTransition', ['root', 'slideEnter-left', 'slideEnter-right', 'slideEnterActive', 'slideExit', 'slideExitActiveLeft-left', 'slideExitActiveLeft-right']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getPickersSlideTransitionUtilityClass", "slot", "pickersSlideTransitionClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/CalendarPicker/pickersSlideTransitionClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport const getPickersSlideTransitionUtilityClass = slot => // TODO v6: Rename 'PrivatePickersSlideTransition' to 'MuiPickersSlideTransition' to follow convention\ngenerateUtilityClass('PrivatePickersSlideTransition', slot);\nexport const pickersSlideTransitionClasses = generateUtilityClasses( // TODO v6: Rename 'PrivatePickersSlideTransition' to 'MuiPickersSlideTransition' to follow convention\n'PrivatePickersSlideTransition', ['root', 'slideEnter-left', 'slideEnter-right', 'slideEnterActive', 'slideExit', 'slideExitActiveLeft-left', 'slideExitActiveLeft-right']);"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,eAAe;AAC5E,OAAO,MAAMC,qCAAqC,GAAGC,IAAI;AAAI;AAC7DH,oBAAoB,CAAC,+BAA+B,EAAEG,IAAI,CAAC;AAC3D,OAAO,MAAMC,6BAA6B,GAAGH,sBAAsB;AAAE;AACrE,+BAA+B,EAAE,CAAC,MAAM,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,WAAW,EAAE,0BAA0B,EAAE,2BAA2B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}