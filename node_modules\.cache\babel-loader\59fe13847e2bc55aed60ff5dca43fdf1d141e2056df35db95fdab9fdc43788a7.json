{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useControlled as useControlled, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible, visuallyHidden } from '@mui/utils';\nimport { areArraysEqual, extractEventHandlers } from '../utils';\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction asc(a, b) {\n  return a - b;\n}\nfunction clamp(value, min, max) {\n  if (value == null) {\n    return min;\n  }\n  return Math.min(Math.max(min, value), max);\n}\nfunction findClosest(values, currentValue) {\n  var _values$reduce;\n  const {\n    index: closestIndex\n  } = (_values$reduce = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null)) != null ? _values$reduce : {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex(_ref) {\n  let {\n    values,\n    newValue,\n    index\n  } = _ref;\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb(_ref2) {\n  let {\n    sliderRef,\n    activeIndex,\n    setActive\n  } = _ref2;\n  var _sliderRef$current, _doc$activeElement;\n  const doc = ownerDocument(sliderRef.current);\n  if (!((_sliderRef$current = sliderRef.current) != null && _sliderRef$current.contains(doc.activeElement)) || Number(doc == null || (_doc$activeElement = doc.activeElement) == null ? void 0 : _doc$activeElement.getAttribute('data-index')) !== activeIndex) {\n    var _sliderRef$current2;\n    (_sliderRef$current2 = sliderRef.current) == null || _sliderRef$current2.querySelector(\"[type=\\\"range\\\"][data-index=\\\"\".concat(activeIndex, \"\\\"]\")).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: \"\".concat(percent, \"%\")\n    }),\n    leap: percent => ({\n      width: \"\".concat(percent, \"%\")\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: \"\".concat(percent, \"%\")\n    }),\n    leap: percent => ({\n      width: \"\".concat(percent, \"%\")\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: \"\".concat(percent, \"%\")\n    }),\n    leap: percent => ({\n      height: \"\".concat(percent, \"%\")\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\n/**\n *\n * Demos:\n *\n * - [Slider](https://mui.com/base-ui/react-slider/#hook)\n *\n * API:\n *\n * - [useSlider API](https://mui.com/base-ui/react-slider/hooks-api/#use-slider)\n */\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef();\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef();\n  const handleFocusRef = useForkRef(focusVisibleRef, sliderRef);\n  const handleRef = useForkRef(ref, handleFocusRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers == null || (_otherHandlers$onFocu = otherHandlers.onFocus) == null || _otherHandlers$onFocu.call(otherHandlers, event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers == null || (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      var _document$activeEleme;\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/s/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      (_document$activeEleme = document.activeElement) == null || _document$activeEleme.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    var _otherHandlers$onChan;\n    (_otherHandlers$onChan = otherHandlers.onChange) == null || _otherHandlers$onChan.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n\n    // @ts-ignore\n    let newValue = event.target.valueAsNumber;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue > maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue < marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, newValue);\n    }\n  };\n  const previousIndex = React.useRef();\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = _ref3 => {\n    let {\n      finger,\n      move = false\n    } = _ref3;\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.indexOf('vertical') === 0) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.indexOf('-reverse') !== -1) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove);\n    doc.addEventListener('touchend', handleTouchEnd);\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      // @ts-ignore\n      slider.removeEventListener('touchstart', handleTouchStart, {\n        passive: doesSupportTouchActionNone()\n      });\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    (_otherHandlers$onMous = otherHandlers.onMouseDown) == null || _otherHandlers$onMous.call(otherHandlers, event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove);\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({}, externalProps, {\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    (_otherHandlers$onMous2 = otherHandlers.onMouseOver) == null || _otherHandlers$onMous2.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous3;\n    (_otherHandlers$onMous3 = otherHandlers.onMouseLeave) == null || _otherHandlers$onMous3.call(otherHandlers, event);\n    setOpen(-1);\n  };\n  const getThumbProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return _extends({}, externalProps, externalHandlers, ownEventHandlers);\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  const getHiddenInputProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var _parameters$step;\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : (_parameters$step = parameters.step) != null ? _parameters$step : undefined,\n      disabled\n    }, externalProps, mergedEventHandlers, {\n      style: _extends({}, visuallyHidden, {\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%'\n      })\n    });\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_ownerDocument", "ownerDocument", "unstable_useControlled", "useControlled", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_useEventCallback", "useEventCallback", "unstable_useForkRef", "useForkRef", "unstable_useIsFocusVisible", "useIsFocusVisible", "visuallyHidden", "areArraysEqual", "extractEventHandlers", "INTENTIONAL_DRAG_COUNT_THRESHOLD", "asc", "a", "b", "clamp", "value", "min", "max", "Math", "findClosest", "values", "currentValue", "_values$reduce", "index", "closestIndex", "reduce", "acc", "distance", "abs", "trackFinger", "event", "touchId", "current", "undefined", "changedTouches", "touchEvent", "i", "length", "touch", "identifier", "x", "clientX", "y", "clientY", "valueToPercent", "percentToValue", "percent", "getDecimalPrecision", "num", "parts", "toExponential", "split", "mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseInt", "decimalPart", "toString", "roundValueToStep", "step", "nearest", "round", "Number", "toFixed", "setValueIndex", "_ref", "newValue", "output", "slice", "sort", "focusThumb", "_ref2", "sliderRef", "activeIndex", "setActive", "_sliderRef$current", "_doc$activeElement", "doc", "contains", "activeElement", "getAttribute", "_sliderRef$current2", "querySelector", "concat", "focus", "areValuesEqual", "oldValue", "axisProps", "horizontal", "offset", "left", "leap", "width", "right", "vertical", "bottom", "height", "Identity", "cachedSupportsTouchActionNone", "doesSupportTouchActionNone", "CSS", "supports", "useSlider", "parameters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultValue", "disabled", "disableSwap", "isRtl", "marks", "marksProp", "name", "onChange", "onChangeCommitted", "orientation", "rootRef", "ref", "scale", "tabIndex", "valueProp", "useRef", "active", "useState", "open", "<PERSON><PERSON><PERSON>", "dragging", "setDragging", "moveCount", "valueDerived", "setValueState", "controlled", "default", "handleChange", "thumbIndex", "nativeEvent", "clonedEvent", "constructor", "type", "Object", "defineProperty", "writable", "range", "Array", "isArray", "map", "floor", "_", "marksV<PERSON>ues", "mark", "isFocusVisibleRef", "onBlur", "handleBlurVisible", "onFocus", "handleFocusVisible", "focusVisibleRef", "focusedThumbIndex", "setFocusedThumbIndex", "handleFocusRef", "handleRef", "createHandleHiddenInputFocus", "otherHandlers", "_otherHandlers$onFocu", "currentTarget", "call", "createHandleHiddenInputBlur", "_otherHandlers$onBlur", "document", "_document$activeEleme", "blur", "createHandleHiddenInputChange", "_otherHandlers$onChan", "marksIndex", "indexOf", "target", "valueAsNumber", "maxMarksValue", "Infinity", "previousValue", "previousIndex", "axis", "getFingerNewValue", "_ref3", "finger", "move", "slider", "getBoundingClientRect", "handleTouchMove", "buttons", "handleTouchEnd", "stopListening", "handleTouchStart", "preventDefault", "addEventListener", "useCallback", "removeEventListener", "useEffect", "passive", "createHandleMouseDown", "_otherHandlers$onMous", "onMouseDown", "defaultPrevented", "button", "trackOffset", "trackLeap", "getRootProps", "externalProps", "arguments", "externalHandlers", "ownEventHandlers", "mergedEventHandlers", "createHandleMouseOver", "_otherHandlers$onMous2", "onMouseOver", "createHandleMouseLeave", "_otherHandlers$onMous3", "onMouseLeave", "getThumbProps", "getThumbStyle", "pointerEvents", "getHiddenInputProps", "_parameters$step", "style", "direction"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/useSlider/useSlider.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useControlled as useControlled, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible, visuallyHidden } from '@mui/utils';\nimport { areArraysEqual, extractEventHandlers } from '../utils';\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction asc(a, b) {\n  return a - b;\n}\nfunction clamp(value, min, max) {\n  if (value == null) {\n    return min;\n  }\n  return Math.min(Math.max(min, value), max);\n}\nfunction findClosest(values, currentValue) {\n  var _values$reduce;\n  const {\n    index: closestIndex\n  } = (_values$reduce = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null)) != null ? _values$reduce : {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  var _sliderRef$current, _doc$activeElement;\n  const doc = ownerDocument(sliderRef.current);\n  if (!((_sliderRef$current = sliderRef.current) != null && _sliderRef$current.contains(doc.activeElement)) || Number(doc == null || (_doc$activeElement = doc.activeElement) == null ? void 0 : _doc$activeElement.getAttribute('data-index')) !== activeIndex) {\n    var _sliderRef$current2;\n    (_sliderRef$current2 = sliderRef.current) == null || _sliderRef$current2.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\n/**\n *\n * Demos:\n *\n * - [Slider](https://mui.com/base-ui/react-slider/#hook)\n *\n * API:\n *\n * - [useSlider API](https://mui.com/base-ui/react-slider/hooks-api/#use-slider)\n */\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef();\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef();\n  const handleFocusRef = useForkRef(focusVisibleRef, sliderRef);\n  const handleRef = useForkRef(ref, handleFocusRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers == null || (_otherHandlers$onFocu = otherHandlers.onFocus) == null || _otherHandlers$onFocu.call(otherHandlers, event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers == null || (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      var _document$activeEleme;\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/s/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      (_document$activeEleme = document.activeElement) == null || _document$activeEleme.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    var _otherHandlers$onChan;\n    (_otherHandlers$onChan = otherHandlers.onChange) == null || _otherHandlers$onChan.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n\n    // @ts-ignore\n    let newValue = event.target.valueAsNumber;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue > maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue < marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, newValue);\n    }\n  };\n  const previousIndex = React.useRef();\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.indexOf('vertical') === 0) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.indexOf('-reverse') !== -1) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove);\n    doc.addEventListener('touchend', handleTouchEnd);\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      // @ts-ignore\n      slider.removeEventListener('touchstart', handleTouchStart, {\n        passive: doesSupportTouchActionNone()\n      });\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    (_otherHandlers$onMous = otherHandlers.onMouseDown) == null || _otherHandlers$onMous.call(otherHandlers, event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove);\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({}, externalProps, {\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    (_otherHandlers$onMous2 = otherHandlers.onMouseOver) == null || _otherHandlers$onMous2.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous3;\n    (_otherHandlers$onMous3 = otherHandlers.onMouseLeave) == null || _otherHandlers$onMous3.call(otherHandlers, event);\n    setOpen(-1);\n  };\n  const getThumbProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return _extends({}, externalProps, externalHandlers, ownEventHandlers);\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  const getHiddenInputProps = (externalProps = {}) => {\n    var _parameters$step;\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : (_parameters$step = parameters.step) != null ? _parameters$step : undefined,\n      disabled\n    }, externalProps, mergedEventHandlers, {\n      style: _extends({}, visuallyHidden, {\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%'\n      })\n    });\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,cAAc,QAAQ,YAAY;AACjT,SAASC,cAAc,EAAEC,oBAAoB,QAAQ,UAAU;AAC/D,MAAMC,gCAAgC,GAAG,CAAC;AAC1C,SAASC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAOD,CAAC,GAAGC,CAAC;AACd;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9B,IAAIF,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOC,GAAG;EACZ;EACA,OAAOE,IAAI,CAACF,GAAG,CAACE,IAAI,CAACD,GAAG,CAACD,GAAG,EAAED,KAAK,CAAC,EAAEE,GAAG,CAAC;AAC5C;AACA,SAASE,WAAWA,CAACC,MAAM,EAAEC,YAAY,EAAE;EACzC,IAAIC,cAAc;EAClB,MAAM;IACJC,KAAK,EAAEC;EACT,CAAC,GAAG,CAACF,cAAc,GAAGF,MAAM,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEX,KAAK,EAAEQ,KAAK,KAAK;IACzD,MAAMI,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAACP,YAAY,GAAGN,KAAK,CAAC;IAC/C,IAAIW,GAAG,KAAK,IAAI,IAAIC,QAAQ,GAAGD,GAAG,CAACC,QAAQ,IAAIA,QAAQ,KAAKD,GAAG,CAACC,QAAQ,EAAE;MACxE,OAAO;QACLA,QAAQ;QACRJ;MACF,CAAC;IACH;IACA,OAAOG,GAAG;EACZ,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,GAAGJ,cAAc,GAAG,CAAC,CAAC;EACvC,OAAOE,YAAY;AACrB;AACA,SAASK,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACnC;EACA,IAAIA,OAAO,CAACC,OAAO,KAAKC,SAAS,IAAIH,KAAK,CAACI,cAAc,EAAE;IACzD,MAAMC,UAAU,GAAGL,KAAK;IACxB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACD,cAAc,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC5D,MAAME,KAAK,GAAGH,UAAU,CAACD,cAAc,CAACE,CAAC,CAAC;MAC1C,IAAIE,KAAK,CAACC,UAAU,KAAKR,OAAO,CAACC,OAAO,EAAE;QACxC,OAAO;UACLQ,CAAC,EAAEF,KAAK,CAACG,OAAO;UAChBC,CAAC,EAAEJ,KAAK,CAACK;QACX,CAAC;MACH;IACF;IACA,OAAO,KAAK;EACd;;EAEA;EACA,OAAO;IACLH,CAAC,EAAEV,KAAK,CAACW,OAAO;IAChBC,CAAC,EAAEZ,KAAK,CAACa;EACX,CAAC;AACH;AACA,OAAO,SAASC,cAAcA,CAAC7B,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9C,OAAO,CAACF,KAAK,GAAGC,GAAG,IAAI,GAAG,IAAIC,GAAG,GAAGD,GAAG,CAAC;AAC1C;AACA,SAAS6B,cAAcA,CAACC,OAAO,EAAE9B,GAAG,EAAEC,GAAG,EAAE;EACzC,OAAO,CAACA,GAAG,GAAGD,GAAG,IAAI8B,OAAO,GAAG9B,GAAG;AACpC;AACA,SAAS+B,mBAAmBA,CAACC,GAAG,EAAE;EAChC;EACA;EACA,IAAI9B,IAAI,CAACU,GAAG,CAACoB,GAAG,CAAC,GAAG,CAAC,EAAE;IACrB,MAAMC,KAAK,GAAGD,GAAG,CAACE,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,kBAAkB,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjD,OAAO,CAACC,kBAAkB,GAAGA,kBAAkB,CAACf,MAAM,GAAG,CAAC,IAAIgB,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACtF;EACA,MAAMK,WAAW,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOG,WAAW,GAAGA,WAAW,CAACjB,MAAM,GAAG,CAAC;AAC7C;AACA,SAASmB,gBAAgBA,CAACzC,KAAK,EAAE0C,IAAI,EAAEzC,GAAG,EAAE;EAC1C,MAAM0C,OAAO,GAAGxC,IAAI,CAACyC,KAAK,CAAC,CAAC5C,KAAK,GAAGC,GAAG,IAAIyC,IAAI,CAAC,GAAGA,IAAI,GAAGzC,GAAG;EAC7D,OAAO4C,MAAM,CAACF,OAAO,CAACG,OAAO,CAACd,mBAAmB,CAACU,IAAI,CAAC,CAAC,CAAC;AAC3D;AACA,SAASK,aAAaA,CAAAC,IAAA,EAInB;EAAA,IAJoB;IACrB3C,MAAM;IACN4C,QAAQ;IACRzC;EACF,CAAC,GAAAwC,IAAA;EACC,MAAME,MAAM,GAAG7C,MAAM,CAAC8C,KAAK,CAAC,CAAC;EAC7BD,MAAM,CAAC1C,KAAK,CAAC,GAAGyC,QAAQ;EACxB,OAAOC,MAAM,CAACE,IAAI,CAACxD,GAAG,CAAC;AACzB;AACA,SAASyD,UAAUA,CAAAC,KAAA,EAIhB;EAAA,IAJiB;IAClBC,SAAS;IACTC,WAAW;IACXC;EACF,CAAC,GAAAH,KAAA;EACC,IAAII,kBAAkB,EAAEC,kBAAkB;EAC1C,MAAMC,GAAG,GAAG/E,aAAa,CAAC0E,SAAS,CAACtC,OAAO,CAAC;EAC5C,IAAI,EAAE,CAACyC,kBAAkB,GAAGH,SAAS,CAACtC,OAAO,KAAK,IAAI,IAAIyC,kBAAkB,CAACG,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,CAAC,IAAIjB,MAAM,CAACe,GAAG,IAAI,IAAI,IAAI,CAACD,kBAAkB,GAAGC,GAAG,CAACE,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,kBAAkB,CAACI,YAAY,CAAC,YAAY,CAAC,CAAC,KAAKP,WAAW,EAAE;IAC7P,IAAIQ,mBAAmB;IACvB,CAACA,mBAAmB,GAAGT,SAAS,CAACtC,OAAO,KAAK,IAAI,IAAI+C,mBAAmB,CAACC,aAAa,kCAAAC,MAAA,CAA+BV,WAAW,QAAI,CAAC,CAACW,KAAK,CAAC,CAAC;EAC/I;EACA,IAAIV,SAAS,EAAE;IACbA,SAAS,CAACD,WAAW,CAAC;EACxB;AACF;AACA,SAASY,cAAcA,CAACnB,QAAQ,EAAEoB,QAAQ,EAAE;EAC1C,IAAI,OAAOpB,QAAQ,KAAK,QAAQ,IAAI,OAAOoB,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOpB,QAAQ,KAAKoB,QAAQ;EAC9B;EACA,IAAI,OAAOpB,QAAQ,KAAK,QAAQ,IAAI,OAAOoB,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAO5E,cAAc,CAACwD,QAAQ,EAAEoB,QAAQ,CAAC;EAC3C;EACA,OAAO,KAAK;AACd;AACA,MAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE;IACVC,MAAM,EAAEzC,OAAO,KAAK;MAClB0C,IAAI,KAAAP,MAAA,CAAKnC,OAAO;IAClB,CAAC,CAAC;IACF2C,IAAI,EAAE3C,OAAO,KAAK;MAChB4C,KAAK,KAAAT,MAAA,CAAKnC,OAAO;IACnB,CAAC;EACH,CAAC;EACD,oBAAoB,EAAE;IACpByC,MAAM,EAAEzC,OAAO,KAAK;MAClB6C,KAAK,KAAAV,MAAA,CAAKnC,OAAO;IACnB,CAAC,CAAC;IACF2C,IAAI,EAAE3C,OAAO,KAAK;MAChB4C,KAAK,KAAAT,MAAA,CAAKnC,OAAO;IACnB,CAAC;EACH,CAAC;EACD8C,QAAQ,EAAE;IACRL,MAAM,EAAEzC,OAAO,KAAK;MAClB+C,MAAM,KAAAZ,MAAA,CAAKnC,OAAO;IACpB,CAAC,CAAC;IACF2C,IAAI,EAAE3C,OAAO,KAAK;MAChBgD,MAAM,KAAAb,MAAA,CAAKnC,OAAO;IACpB,CAAC;EACH;AACF,CAAC;AACD,OAAO,MAAMiD,QAAQ,GAAGvD,CAAC,IAAIA,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIwD,6BAA6B;AACjC,SAASC,0BAA0BA,CAAA,EAAG;EACpC,IAAID,6BAA6B,KAAK/D,SAAS,EAAE;IAC/C,IAAI,OAAOiE,GAAG,KAAK,WAAW,IAAI,OAAOA,GAAG,CAACC,QAAQ,KAAK,UAAU,EAAE;MACpEH,6BAA6B,GAAGE,GAAG,CAACC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC;IACtE,CAAC,MAAM;MACLH,6BAA6B,GAAG,IAAI;IACtC;EACF;EACA,OAAOA,6BAA6B;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,SAASA,CAACC,UAAU,EAAE;EACpC,MAAM;IACJ,iBAAiB,EAAEC,cAAc;IACjCC,YAAY;IACZC,QAAQ,GAAG,KAAK;IAChBC,WAAW,GAAG,KAAK;IACnBC,KAAK,GAAG,KAAK;IACbC,KAAK,EAAEC,SAAS,GAAG,KAAK;IACxB3F,GAAG,GAAG,GAAG;IACTD,GAAG,GAAG,CAAC;IACP6F,IAAI;IACJC,QAAQ;IACRC,iBAAiB;IACjBC,WAAW,GAAG,YAAY;IAC1BC,OAAO,EAAEC,GAAG;IACZC,KAAK,GAAGpB,QAAQ;IAChBtC,IAAI,GAAG,CAAC;IACR2D,QAAQ;IACRrG,KAAK,EAAEsG;EACT,CAAC,GAAGhB,UAAU;EACd,MAAMtE,OAAO,GAAGrC,KAAK,CAAC4H,MAAM,CAAC,CAAC;EAC9B;EACA;EACA;EACA,MAAM,CAACC,MAAM,EAAE/C,SAAS,CAAC,GAAG9E,KAAK,CAAC8H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhI,KAAK,CAAC8H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGlI,KAAK,CAAC8H,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMK,SAAS,GAAGnI,KAAK,CAAC4H,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM,CAACQ,YAAY,EAAEC,aAAa,CAAC,GAAGjI,aAAa,CAAC;IAClDkI,UAAU,EAAEX,SAAS;IACrBY,OAAO,EAAE1B,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGvF,GAAG;IAClD6F,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMqB,YAAY,GAAGpB,QAAQ,KAAK,CAAChF,KAAK,EAAEf,KAAK,EAAEoH,UAAU,KAAK;IAC9D;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAGtG,KAAK,CAACsG,WAAW,IAAItG,KAAK;IAC9C;IACA,MAAMuG,WAAW,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAACG,IAAI,EAAEH,WAAW,CAAC;IAC9EI,MAAM,CAACC,cAAc,CAACJ,WAAW,EAAE,QAAQ,EAAE;MAC3CK,QAAQ,EAAE,IAAI;MACd3H,KAAK,EAAE;QACLA,KAAK;QACL8F;MACF;IACF,CAAC,CAAC;IACFC,QAAQ,CAACuB,WAAW,EAAEtH,KAAK,EAAEoH,UAAU,CAAC;EAC1C,CAAC,CAAC;EACF,MAAMQ,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACf,YAAY,CAAC;EACzC,IAAI1G,MAAM,GAAGuH,KAAK,GAAGb,YAAY,CAAC5D,KAAK,CAAC,CAAC,CAACC,IAAI,CAACxD,GAAG,CAAC,GAAG,CAACmH,YAAY,CAAC;EACpE1G,MAAM,GAAGA,MAAM,CAAC0H,GAAG,CAAC/H,KAAK,IAAID,KAAK,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,CAAC,CAAC;EACpD,MAAM0F,KAAK,GAAGC,SAAS,KAAK,IAAI,IAAInD,IAAI,KAAK,IAAI,GAAG,CAAC,GAAGmF,KAAK,CAAC1H,IAAI,CAAC6H,KAAK,CAAC,CAAC9H,GAAG,GAAGD,GAAG,IAAIyC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACqF,GAAG,CAAC,CAACE,CAAC,EAAEzH,KAAK,MAAM;IACpHR,KAAK,EAAEC,GAAG,GAAGyC,IAAI,GAAGlC;EACtB,CAAC,CAAC,CAAC,GAAGqF,SAAS,IAAI,EAAE;EACrB,MAAMqC,WAAW,GAAGtC,KAAK,CAACmC,GAAG,CAACI,IAAI,IAAIA,IAAI,CAACnI,KAAK,CAAC;EACjD,MAAM;IACJoI,iBAAiB;IACjBC,MAAM,EAAEC,iBAAiB;IACzBC,OAAO,EAAEC,kBAAkB;IAC3BrC,GAAG,EAAEsC;EACP,CAAC,GAAGlJ,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACmJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhK,KAAK,CAAC8H,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpE,MAAMlD,SAAS,GAAG5E,KAAK,CAAC4H,MAAM,CAAC,CAAC;EAChC,MAAMqC,cAAc,GAAGvJ,UAAU,CAACoJ,eAAe,EAAElF,SAAS,CAAC;EAC7D,MAAMsF,SAAS,GAAGxJ,UAAU,CAAC8G,GAAG,EAAEyC,cAAc,CAAC;EACjD,MAAME,4BAA4B,GAAGC,aAAa,IAAIhI,KAAK,IAAI;IAC7D,IAAIiI,qBAAqB;IACzB,MAAMxI,KAAK,GAAGqC,MAAM,CAAC9B,KAAK,CAACkI,aAAa,CAAClF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpEyE,kBAAkB,CAACzH,KAAK,CAAC;IACzB,IAAIqH,iBAAiB,CAACnH,OAAO,KAAK,IAAI,EAAE;MACtC0H,oBAAoB,CAACnI,KAAK,CAAC;IAC7B;IACAmG,OAAO,CAACnG,KAAK,CAAC;IACduI,aAAa,IAAI,IAAI,IAAI,CAACC,qBAAqB,GAAGD,aAAa,CAACR,OAAO,KAAK,IAAI,IAAIS,qBAAqB,CAACE,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;EACtI,CAAC;EACD,MAAMoI,2BAA2B,GAAGJ,aAAa,IAAIhI,KAAK,IAAI;IAC5D,IAAIqI,qBAAqB;IACzBd,iBAAiB,CAACvH,KAAK,CAAC;IACxB,IAAIqH,iBAAiB,CAACnH,OAAO,KAAK,KAAK,EAAE;MACvC0H,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC1B;IACAhC,OAAO,CAAC,CAAC,CAAC,CAAC;IACXoC,aAAa,IAAI,IAAI,IAAI,CAACK,qBAAqB,GAAGL,aAAa,CAACV,MAAM,KAAK,IAAI,IAAIe,qBAAqB,CAACF,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;EACrI,CAAC;EACD9B,iBAAiB,CAAC,MAAM;IACtB,IAAIwG,QAAQ,IAAIlC,SAAS,CAACtC,OAAO,CAAC4C,QAAQ,CAACwF,QAAQ,CAACvF,aAAa,CAAC,EAAE;MAClE,IAAIwF,qBAAqB;MACzB;MACA;MACA;MACA;MACA,CAACA,qBAAqB,GAAGD,QAAQ,CAACvF,aAAa,KAAK,IAAI,IAAIwF,qBAAqB,CAACC,IAAI,CAAC,CAAC;IAC1F;EACF,CAAC,EAAE,CAAC9D,QAAQ,CAAC,CAAC;EACd,IAAIA,QAAQ,IAAIe,MAAM,KAAK,CAAC,CAAC,EAAE;IAC7B/C,SAAS,CAAC,CAAC,CAAC,CAAC;EACf;EACA,IAAIgC,QAAQ,IAAIiD,iBAAiB,KAAK,CAAC,CAAC,EAAE;IACxCC,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC1B;EACA,MAAMa,6BAA6B,GAAGT,aAAa,IAAIhI,KAAK,IAAI;IAC9D,IAAI0I,qBAAqB;IACzB,CAACA,qBAAqB,GAAGV,aAAa,CAAChD,QAAQ,KAAK,IAAI,IAAI0D,qBAAqB,CAACP,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;IAC5G,MAAMP,KAAK,GAAGqC,MAAM,CAAC9B,KAAK,CAACkI,aAAa,CAAClF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE,MAAM/D,KAAK,GAAGK,MAAM,CAACG,KAAK,CAAC;IAC3B,MAAMkJ,UAAU,GAAGxB,WAAW,CAACyB,OAAO,CAAC3J,KAAK,CAAC;;IAE7C;IACA,IAAIiD,QAAQ,GAAGlC,KAAK,CAAC6I,MAAM,CAACC,aAAa;IACzC,IAAIjE,KAAK,IAAIlD,IAAI,IAAI,IAAI,EAAE;MACzB,MAAMoH,aAAa,GAAG5B,WAAW,CAACA,WAAW,CAAC5G,MAAM,GAAG,CAAC,CAAC;MACzD,IAAI2B,QAAQ,GAAG6G,aAAa,EAAE;QAC5B7G,QAAQ,GAAG6G,aAAa;MAC1B,CAAC,MAAM,IAAI7G,QAAQ,GAAGiF,WAAW,CAAC,CAAC,CAAC,EAAE;QACpCjF,QAAQ,GAAGiF,WAAW,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM;QACLjF,QAAQ,GAAGA,QAAQ,GAAGjD,KAAK,GAAGkI,WAAW,CAACwB,UAAU,GAAG,CAAC,CAAC,GAAGxB,WAAW,CAACwB,UAAU,GAAG,CAAC,CAAC;MACzF;IACF;IACAzG,QAAQ,GAAGlD,KAAK,CAACkD,QAAQ,EAAEhD,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAI0H,KAAK,EAAE;MACT;MACA,IAAIlC,WAAW,EAAE;QACfzC,QAAQ,GAAGlD,KAAK,CAACkD,QAAQ,EAAE5C,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,IAAI,CAACuJ,QAAQ,EAAE1J,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,IAAIuJ,QAAQ,CAAC;MAC3F;MACA,MAAMC,aAAa,GAAG/G,QAAQ;MAC9BA,QAAQ,GAAGF,aAAa,CAAC;QACvB1C,MAAM;QACN4C,QAAQ;QACRzC;MACF,CAAC,CAAC;MACF,IAAIgD,WAAW,GAAGhD,KAAK;;MAEvB;MACA,IAAI,CAACkF,WAAW,EAAE;QAChBlC,WAAW,GAAGP,QAAQ,CAAC0G,OAAO,CAACK,aAAa,CAAC;MAC/C;MACA3G,UAAU,CAAC;QACTE,SAAS;QACTC;MACF,CAAC,CAAC;IACJ;IACAwD,aAAa,CAAC/D,QAAQ,CAAC;IACvB0F,oBAAoB,CAACnI,KAAK,CAAC;IAC3B,IAAI2G,YAAY,IAAI,CAAC/C,cAAc,CAACnB,QAAQ,EAAE8D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACpG,KAAK,EAAEkC,QAAQ,EAAEzC,KAAK,CAAC;IACtC;IACA,IAAIwF,iBAAiB,EAAE;MACrBA,iBAAiB,CAACjF,KAAK,EAAEkC,QAAQ,CAAC;IACpC;EACF,CAAC;EACD,MAAMgH,aAAa,GAAGtL,KAAK,CAAC4H,MAAM,CAAC,CAAC;EACpC,IAAI2D,IAAI,GAAGjE,WAAW;EACtB,IAAIN,KAAK,IAAIM,WAAW,KAAK,YAAY,EAAE;IACzCiE,IAAI,IAAI,UAAU;EACpB;EACA,MAAMC,iBAAiB,GAAGC,KAAA,IAGpB;IAAA,IAHqB;MACzBC,MAAM;MACNC,IAAI,GAAG;IACT,CAAC,GAAAF,KAAA;IACC,MAAM;MACJnJ,OAAO,EAAEsJ;IACX,CAAC,GAAGhH,SAAS;IACb,MAAM;MACJoB,KAAK;MACLI,MAAM;MACND,MAAM;MACNL;IACF,CAAC,GAAG8F,MAAM,CAACC,qBAAqB,CAAC,CAAC;IAClC,IAAIzI,OAAO;IACX,IAAImI,IAAI,CAACP,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;MAClC5H,OAAO,GAAG,CAAC+C,MAAM,GAAGuF,MAAM,CAAC1I,CAAC,IAAIoD,MAAM;IACxC,CAAC,MAAM;MACLhD,OAAO,GAAG,CAACsI,MAAM,CAAC5I,CAAC,GAAGgD,IAAI,IAAIE,KAAK;IACrC;IACA,IAAIuF,IAAI,CAACP,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MACnC5H,OAAO,GAAG,CAAC,GAAGA,OAAO;IACvB;IACA,IAAIkB,QAAQ;IACZA,QAAQ,GAAGnB,cAAc,CAACC,OAAO,EAAE9B,GAAG,EAAEC,GAAG,CAAC;IAC5C,IAAIwC,IAAI,EAAE;MACRO,QAAQ,GAAGR,gBAAgB,CAACQ,QAAQ,EAAEP,IAAI,EAAEzC,GAAG,CAAC;IAClD,CAAC,MAAM;MACL,MAAMQ,YAAY,GAAGL,WAAW,CAAC8H,WAAW,EAAEjF,QAAQ,CAAC;MACvDA,QAAQ,GAAGiF,WAAW,CAACzH,YAAY,CAAC;IACtC;IACAwC,QAAQ,GAAGlD,KAAK,CAACkD,QAAQ,EAAEhD,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAIsD,WAAW,GAAG,CAAC;IACnB,IAAIoE,KAAK,EAAE;MACT,IAAI,CAAC0C,IAAI,EAAE;QACT9G,WAAW,GAAGpD,WAAW,CAACC,MAAM,EAAE4C,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACLO,WAAW,GAAGyG,aAAa,CAAChJ,OAAO;MACrC;;MAEA;MACA,IAAIyE,WAAW,EAAE;QACfzC,QAAQ,GAAGlD,KAAK,CAACkD,QAAQ,EAAE5C,MAAM,CAACmD,WAAW,GAAG,CAAC,CAAC,IAAI,CAACuG,QAAQ,EAAE1J,MAAM,CAACmD,WAAW,GAAG,CAAC,CAAC,IAAIuG,QAAQ,CAAC;MACvG;MACA,MAAMC,aAAa,GAAG/G,QAAQ;MAC9BA,QAAQ,GAAGF,aAAa,CAAC;QACvB1C,MAAM;QACN4C,QAAQ;QACRzC,KAAK,EAAEgD;MACT,CAAC,CAAC;;MAEF;MACA,IAAI,EAAEkC,WAAW,IAAI4E,IAAI,CAAC,EAAE;QAC1B9G,WAAW,GAAGP,QAAQ,CAAC0G,OAAO,CAACK,aAAa,CAAC;QAC7CC,aAAa,CAAChJ,OAAO,GAAGuC,WAAW;MACrC;IACF;IACA,OAAO;MACLP,QAAQ;MACRO;IACF,CAAC;EACH,CAAC;EACD,MAAMiH,eAAe,GAAGtL,gBAAgB,CAACkI,WAAW,IAAI;IACtD,MAAMgD,MAAM,GAAGvJ,WAAW,CAACuG,WAAW,EAAErG,OAAO,CAAC;IAChD,IAAI,CAACqJ,MAAM,EAAE;MACX;IACF;IACAvD,SAAS,CAAC7F,OAAO,IAAI,CAAC;;IAEtB;IACA;IACA,IAAIoG,WAAW,CAACG,IAAI,KAAK,WAAW,IAAIH,WAAW,CAACqD,OAAO,KAAK,CAAC,EAAE;MACjE;MACAC,cAAc,CAACtD,WAAW,CAAC;MAC3B;IACF;IACA,MAAM;MACJpE,QAAQ;MACRO;IACF,CAAC,GAAG2G,iBAAiB,CAAC;MACpBE,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACFjH,UAAU,CAAC;MACTE,SAAS;MACTC,WAAW;MACXC;IACF,CAAC,CAAC;IACFuD,aAAa,CAAC/D,QAAQ,CAAC;IACvB,IAAI,CAAC2D,QAAQ,IAAIE,SAAS,CAAC7F,OAAO,GAAGtB,gCAAgC,EAAE;MACrEkH,WAAW,CAAC,IAAI,CAAC;IACnB;IACA,IAAIM,YAAY,IAAI,CAAC/C,cAAc,CAACnB,QAAQ,EAAE8D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACE,WAAW,EAAEpE,QAAQ,EAAEO,WAAW,CAAC;IAClD;EACF,CAAC,CAAC;EACF,MAAMmH,cAAc,GAAGxL,gBAAgB,CAACkI,WAAW,IAAI;IACrD,MAAMgD,MAAM,GAAGvJ,WAAW,CAACuG,WAAW,EAAErG,OAAO,CAAC;IAChD6F,WAAW,CAAC,KAAK,CAAC;IAClB,IAAI,CAACwD,MAAM,EAAE;MACX;IACF;IACA,MAAM;MACJpH;IACF,CAAC,GAAGkH,iBAAiB,CAAC;MACpBE,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACF7G,SAAS,CAAC,CAAC,CAAC,CAAC;IACb,IAAI4D,WAAW,CAACG,IAAI,KAAK,UAAU,EAAE;MACnCb,OAAO,CAAC,CAAC,CAAC,CAAC;IACb;IACA,IAAIX,iBAAiB,EAAE;MACrBA,iBAAiB,CAACqB,WAAW,EAAEpE,QAAQ,CAAC;IAC1C;IACAjC,OAAO,CAACC,OAAO,GAAGC,SAAS;;IAE3B;IACA0J,aAAa,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAG1L,gBAAgB,CAACkI,WAAW,IAAI;IACvD,IAAI5B,QAAQ,EAAE;MACZ;IACF;IACA;IACA,IAAI,CAACP,0BAA0B,CAAC,CAAC,EAAE;MACjCmC,WAAW,CAACyD,cAAc,CAAC,CAAC;IAC9B;IACA,MAAMvJ,KAAK,GAAG8F,WAAW,CAAClG,cAAc,CAAC,CAAC,CAAC;IAC3C,IAAII,KAAK,IAAI,IAAI,EAAE;MACjB;MACAP,OAAO,CAACC,OAAO,GAAGM,KAAK,CAACC,UAAU;IACpC;IACA,MAAM6I,MAAM,GAAGvJ,WAAW,CAACuG,WAAW,EAAErG,OAAO,CAAC;IAChD,IAAIqJ,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJpH,QAAQ;QACRO;MACF,CAAC,GAAG2G,iBAAiB,CAAC;QACpBE;MACF,CAAC,CAAC;MACFhH,UAAU,CAAC;QACTE,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFuD,aAAa,CAAC/D,QAAQ,CAAC;MACvB,IAAIkE,YAAY,IAAI,CAAC/C,cAAc,CAACnB,QAAQ,EAAE8D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACE,WAAW,EAAEpE,QAAQ,EAAEO,WAAW,CAAC;MAClD;IACF;IACAsD,SAAS,CAAC7F,OAAO,GAAG,CAAC;IACrB,MAAM2C,GAAG,GAAG/E,aAAa,CAAC0E,SAAS,CAACtC,OAAO,CAAC;IAC5C2C,GAAG,CAACmH,gBAAgB,CAAC,WAAW,EAAEN,eAAe,CAAC;IAClD7G,GAAG,CAACmH,gBAAgB,CAAC,UAAU,EAAEJ,cAAc,CAAC;EAClD,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGjM,KAAK,CAACqM,WAAW,CAAC,MAAM;IAC5C,MAAMpH,GAAG,GAAG/E,aAAa,CAAC0E,SAAS,CAACtC,OAAO,CAAC;IAC5C2C,GAAG,CAACqH,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrD7G,GAAG,CAACqH,mBAAmB,CAAC,SAAS,EAAEN,cAAc,CAAC;IAClD/G,GAAG,CAACqH,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrD7G,GAAG,CAACqH,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;EACrD,CAAC,EAAE,CAACA,cAAc,EAAEF,eAAe,CAAC,CAAC;EACrC9L,KAAK,CAACuM,SAAS,CAAC,MAAM;IACpB,MAAM;MACJjK,OAAO,EAAEsJ;IACX,CAAC,GAAGhH,SAAS;IACbgH,MAAM,CAACQ,gBAAgB,CAAC,YAAY,EAAEF,gBAAgB,EAAE;MACtDM,OAAO,EAAEjG,0BAA0B,CAAC;IACtC,CAAC,CAAC;IACF,OAAO,MAAM;MACX;MACAqF,MAAM,CAACU,mBAAmB,CAAC,YAAY,EAAEJ,gBAAgB,EAAE;QACzDM,OAAO,EAAEjG,0BAA0B,CAAC;MACtC,CAAC,CAAC;MACF0F,aAAa,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,EAAEC,gBAAgB,CAAC,CAAC;EACrClM,KAAK,CAACuM,SAAS,CAAC,MAAM;IACpB,IAAIzF,QAAQ,EAAE;MACZmF,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACnF,QAAQ,EAAEmF,aAAa,CAAC,CAAC;EAC7B,MAAMQ,qBAAqB,GAAGrC,aAAa,IAAIhI,KAAK,IAAI;IACtD,IAAIsK,qBAAqB;IACzB,CAACA,qBAAqB,GAAGtC,aAAa,CAACuC,WAAW,KAAK,IAAI,IAAID,qBAAqB,CAACnC,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;IAC/G,IAAI0E,QAAQ,EAAE;MACZ;IACF;IACA,IAAI1E,KAAK,CAACwK,gBAAgB,EAAE;MAC1B;IACF;;IAEA;IACA,IAAIxK,KAAK,CAACyK,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;;IAEA;IACAzK,KAAK,CAAC+J,cAAc,CAAC,CAAC;IACtB,MAAMT,MAAM,GAAGvJ,WAAW,CAACC,KAAK,EAAEC,OAAO,CAAC;IAC1C,IAAIqJ,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJpH,QAAQ;QACRO;MACF,CAAC,GAAG2G,iBAAiB,CAAC;QACpBE;MACF,CAAC,CAAC;MACFhH,UAAU,CAAC;QACTE,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFuD,aAAa,CAAC/D,QAAQ,CAAC;MACvB,IAAIkE,YAAY,IAAI,CAAC/C,cAAc,CAACnB,QAAQ,EAAE8D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACpG,KAAK,EAAEkC,QAAQ,EAAEO,WAAW,CAAC;MAC5C;IACF;IACAsD,SAAS,CAAC7F,OAAO,GAAG,CAAC;IACrB,MAAM2C,GAAG,GAAG/E,aAAa,CAAC0E,SAAS,CAACtC,OAAO,CAAC;IAC5C2C,GAAG,CAACmH,gBAAgB,CAAC,WAAW,EAAEN,eAAe,CAAC;IAClD7G,GAAG,CAACmH,gBAAgB,CAAC,SAAS,EAAEJ,cAAc,CAAC;EACjD,CAAC;EACD,MAAMc,WAAW,GAAG5J,cAAc,CAAC+F,KAAK,GAAGvH,MAAM,CAAC,CAAC,CAAC,GAAGJ,GAAG,EAAEA,GAAG,EAAEC,GAAG,CAAC;EACrE,MAAMwL,SAAS,GAAG7J,cAAc,CAACxB,MAAM,CAACA,MAAM,CAACiB,MAAM,GAAG,CAAC,CAAC,EAAErB,GAAG,EAAEC,GAAG,CAAC,GAAGuL,WAAW;EACnF,MAAME,YAAY,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBC,aAAa,GAAAC,SAAA,CAAAvK,MAAA,QAAAuK,SAAA,QAAA3K,SAAA,GAAA2K,SAAA,MAAG,CAAC,CAAC;IACtC,MAAMC,gBAAgB,GAAGpM,oBAAoB,CAACkM,aAAa,CAAC;IAC5D,MAAMG,gBAAgB,GAAG;MACvBT,WAAW,EAAEF,qBAAqB,CAACU,gBAAgB,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,MAAME,mBAAmB,GAAGtN,QAAQ,CAAC,CAAC,CAAC,EAAEoN,gBAAgB,EAAEC,gBAAgB,CAAC;IAC5E,OAAOrN,QAAQ,CAAC,CAAC,CAAC,EAAEkN,aAAa,EAAE;MACjCzF,GAAG,EAAE0C;IACP,CAAC,EAAEmD,mBAAmB,CAAC;EACzB,CAAC;EACD,MAAMC,qBAAqB,GAAGlD,aAAa,IAAIhI,KAAK,IAAI;IACtD,IAAImL,sBAAsB;IAC1B,CAACA,sBAAsB,GAAGnD,aAAa,CAACoD,WAAW,KAAK,IAAI,IAAID,sBAAsB,CAAChD,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;IACjH,MAAMP,KAAK,GAAGqC,MAAM,CAAC9B,KAAK,CAACkI,aAAa,CAAClF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE4C,OAAO,CAACnG,KAAK,CAAC;EAChB,CAAC;EACD,MAAM4L,sBAAsB,GAAGrD,aAAa,IAAIhI,KAAK,IAAI;IACvD,IAAIsL,sBAAsB;IAC1B,CAACA,sBAAsB,GAAGtD,aAAa,CAACuD,YAAY,KAAK,IAAI,IAAID,sBAAsB,CAACnD,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;IAClH4F,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EACD,MAAM4F,aAAa,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBX,aAAa,GAAAC,SAAA,CAAAvK,MAAA,QAAAuK,SAAA,QAAA3K,SAAA,GAAA2K,SAAA,MAAG,CAAC,CAAC;IACvC,MAAMC,gBAAgB,GAAGpM,oBAAoB,CAACkM,aAAa,CAAC;IAC5D,MAAMG,gBAAgB,GAAG;MACvBI,WAAW,EAAEF,qBAAqB,CAACH,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC1DQ,YAAY,EAAEF,sBAAsB,CAACN,gBAAgB,IAAI,CAAC,CAAC;IAC7D,CAAC;IACD,OAAOpN,QAAQ,CAAC,CAAC,CAAC,EAAEkN,aAAa,EAAEE,gBAAgB,EAAEC,gBAAgB,CAAC;EACxE,CAAC;EACD,MAAMS,aAAa,GAAGhM,KAAK,IAAI;IAC7B,OAAO;MACL;MACAiM,aAAa,EAAEjG,MAAM,KAAK,CAAC,CAAC,IAAIA,MAAM,KAAKhG,KAAK,GAAG,MAAM,GAAGU;IAC9D,CAAC;EACH,CAAC;EACD,MAAMwL,mBAAmB,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBd,aAAa,GAAAC,SAAA,CAAAvK,MAAA,QAAAuK,SAAA,QAAA3K,SAAA,GAAA2K,SAAA,MAAG,CAAC,CAAC;IAC7C,IAAIc,gBAAgB;IACpB,MAAMb,gBAAgB,GAAGpM,oBAAoB,CAACkM,aAAa,CAAC;IAC5D,MAAMG,gBAAgB,GAAG;MACvBhG,QAAQ,EAAEyD,6BAA6B,CAACsC,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC/DvD,OAAO,EAAEO,4BAA4B,CAACgD,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC7DzD,MAAM,EAAEc,2BAA2B,CAAC2C,gBAAgB,IAAI,CAAC,CAAC;IAC5D,CAAC;IACD,MAAME,mBAAmB,GAAGtN,QAAQ,CAAC,CAAC,CAAC,EAAEoN,gBAAgB,EAAEC,gBAAgB,CAAC;IAC5E,OAAOrN,QAAQ,CAAC;MACd2H,QAAQ;MACR,iBAAiB,EAAEd,cAAc;MACjC,kBAAkB,EAAEU,WAAW;MAC/B,eAAe,EAAEG,KAAK,CAAClG,GAAG,CAAC;MAC3B,eAAe,EAAEkG,KAAK,CAACnG,GAAG,CAAC;MAC3B6F,IAAI;MACJ0B,IAAI,EAAE,OAAO;MACbvH,GAAG,EAAEqF,UAAU,CAACrF,GAAG;MACnBC,GAAG,EAAEoF,UAAU,CAACpF,GAAG;MACnBwC,IAAI,EAAE4C,UAAU,CAAC5C,IAAI,KAAK,IAAI,IAAI4C,UAAU,CAACM,KAAK,GAAG,KAAK,GAAG,CAAC+G,gBAAgB,GAAGrH,UAAU,CAAC5C,IAAI,KAAK,IAAI,GAAGiK,gBAAgB,GAAGzL,SAAS;MACxIuE;IACF,CAAC,EAAEmG,aAAa,EAAEI,mBAAmB,EAAE;MACrCY,KAAK,EAAElO,QAAQ,CAAC,CAAC,CAAC,EAAEc,cAAc,EAAE;QAClCqN,SAAS,EAAElH,KAAK,GAAG,KAAK,GAAG,KAAK;QAChC;QACAhB,KAAK,EAAE,MAAM;QACbI,MAAM,EAAE;MACV,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLyB,MAAM;IACN0D,IAAI,EAAEA,IAAI;IACV5F,SAAS;IACTsC,QAAQ;IACR8B,iBAAiB;IACjBgE,mBAAmB;IACnBf,YAAY;IACZY,aAAa;IACb3G,KAAK,EAAEA,KAAK;IACZc,IAAI;IACJkB,KAAK;IACL1B,OAAO,EAAE2C,SAAS;IAClB6C,SAAS;IACTD,WAAW;IACXpL,MAAM;IACNmM;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}