{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getTabsUtilityClass(slot) {\n  return generateUtilityClass('MuiTabs', slot);\n}\nexport const tabsClasses = generateUtilityClasses('MuiTabs', ['root', 'horizontal', 'vertical']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabsUtilityClass", "slot", "tabsClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Tabs/tabsClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getTabsUtilityClass(slot) {\n  return generateUtilityClass('MuiTabs', slot);\n}\nexport const tabsClasses = generateUtilityClasses('MuiTabs', ['root', 'horizontal', 'vertical']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOH,oBAAoB,CAAC,SAAS,EAAEG,IAAI,CAAC;AAC9C;AACA,OAAO,MAAMC,WAAW,GAAGH,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}