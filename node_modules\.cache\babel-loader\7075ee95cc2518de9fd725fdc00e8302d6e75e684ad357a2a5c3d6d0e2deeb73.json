{"ast": null, "code": "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" || typeof Blob !== \"undefined\" && Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\" ? ArrayBuffer.isView(obj) : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = (_ref, supportsBinary, callback) => {\n  let {\n    type,\n    data\n  } = _ref;\n  if (withNativeBlob && data instanceof Blob) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(data, callback);\n    }\n  } else if (withNativeArrayBuffer && (data instanceof ArrayBuffer || isView(data))) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(new Blob([data]), callback);\n    }\n  }\n  // plain string\n  return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n  const fileReader = new FileReader();\n  fileReader.onload = function () {\n    const content = fileReader.result.split(\",\")[1];\n    callback(\"b\" + (content || \"\"));\n  };\n  return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n  if (data instanceof Uint8Array) {\n    return data;\n  } else if (data instanceof ArrayBuffer) {\n    return new Uint8Array(data);\n  } else {\n    return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n  }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n  if (withNativeBlob && packet.data instanceof Blob) {\n    return packet.data.arrayBuffer().then(toArray).then(callback);\n  } else if (withNativeArrayBuffer && (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n    return callback(toArray(packet.data));\n  }\n  encodePacket(packet, false, encoded => {\n    if (!TEXT_ENCODER) {\n      TEXT_ENCODER = new TextEncoder();\n    }\n    callback(TEXT_ENCODER.encode(encoded));\n  });\n}\nexport { encodePacket };", "map": {"version": 3, "names": ["PACKET_TYPES", "withNativeBlob", "Blob", "Object", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "_ref", "supportsBinary", "callback", "type", "data", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "encodePacketToBinary", "packet", "arrayBuffer", "then", "encoded", "TextEncoder", "encode"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/engine.io-parser/build/esm/encodePacket.browser.js"], "sourcesContent": ["import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,cAAc;AAC3C,MAAMC,cAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,IAAI,CAAC,KAAK,0BAA2B;AAC5E,MAAMK,qBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU;AAC/D;AACA,MAAMC,MAAM,GAAIC,GAAG,IAAK;EACpB,OAAO,OAAOF,WAAW,CAACC,MAAM,KAAK,UAAU,GACzCD,WAAW,CAACC,MAAM,CAACC,GAAG,CAAC,GACvBA,GAAG,IAAIA,GAAG,CAACC,MAAM,YAAYH,WAAW;AAClD,CAAC;AACD,MAAMI,YAAY,GAAGA,CAAAC,IAAA,EAAiBC,cAAc,EAAEC,QAAQ,KAAK;EAAA,IAA7C;IAAEC,IAAI;IAAEC;EAAK,CAAC,GAAAJ,IAAA;EAChC,IAAIZ,cAAc,IAAIgB,IAAI,YAAYf,IAAI,EAAE;IACxC,IAAIY,cAAc,EAAE;MAChB,OAAOC,QAAQ,CAACE,IAAI,CAAC;IACzB,CAAC,MACI;MACD,OAAOC,kBAAkB,CAACD,IAAI,EAAEF,QAAQ,CAAC;IAC7C;EACJ,CAAC,MACI,IAAIR,qBAAqB,KACzBU,IAAI,YAAYT,WAAW,IAAIC,MAAM,CAACQ,IAAI,CAAC,CAAC,EAAE;IAC/C,IAAIH,cAAc,EAAE;MAChB,OAAOC,QAAQ,CAACE,IAAI,CAAC;IACzB,CAAC,MACI;MACD,OAAOC,kBAAkB,CAAC,IAAIhB,IAAI,CAAC,CAACe,IAAI,CAAC,CAAC,EAAEF,QAAQ,CAAC;IACzD;EACJ;EACA;EACA,OAAOA,QAAQ,CAACf,YAAY,CAACgB,IAAI,CAAC,IAAIC,IAAI,IAAI,EAAE,CAAC,CAAC;AACtD,CAAC;AACD,MAAMC,kBAAkB,GAAGA,CAACD,IAAI,EAAEF,QAAQ,KAAK;EAC3C,MAAMI,UAAU,GAAG,IAAIC,UAAU,CAAC,CAAC;EACnCD,UAAU,CAACE,MAAM,GAAG,YAAY;IAC5B,MAAMC,OAAO,GAAGH,UAAU,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/CT,QAAQ,CAAC,GAAG,IAAIO,OAAO,IAAI,EAAE,CAAC,CAAC;EACnC,CAAC;EACD,OAAOH,UAAU,CAACM,aAAa,CAACR,IAAI,CAAC;AACzC,CAAC;AACD,SAASS,OAAOA,CAACT,IAAI,EAAE;EACnB,IAAIA,IAAI,YAAYU,UAAU,EAAE;IAC5B,OAAOV,IAAI;EACf,CAAC,MACI,IAAIA,IAAI,YAAYT,WAAW,EAAE;IAClC,OAAO,IAAImB,UAAU,CAACV,IAAI,CAAC;EAC/B,CAAC,MACI;IACD,OAAO,IAAIU,UAAU,CAACV,IAAI,CAACN,MAAM,EAAEM,IAAI,CAACW,UAAU,EAAEX,IAAI,CAACY,UAAU,CAAC;EACxE;AACJ;AACA,IAAIC,YAAY;AAChB,OAAO,SAASC,oBAAoBA,CAACC,MAAM,EAAEjB,QAAQ,EAAE;EACnD,IAAId,cAAc,IAAI+B,MAAM,CAACf,IAAI,YAAYf,IAAI,EAAE;IAC/C,OAAO8B,MAAM,CAACf,IAAI,CAACgB,WAAW,CAAC,CAAC,CAACC,IAAI,CAACR,OAAO,CAAC,CAACQ,IAAI,CAACnB,QAAQ,CAAC;EACjE,CAAC,MACI,IAAIR,qBAAqB,KACzByB,MAAM,CAACf,IAAI,YAAYT,WAAW,IAAIC,MAAM,CAACuB,MAAM,CAACf,IAAI,CAAC,CAAC,EAAE;IAC7D,OAAOF,QAAQ,CAACW,OAAO,CAACM,MAAM,CAACf,IAAI,CAAC,CAAC;EACzC;EACAL,YAAY,CAACoB,MAAM,EAAE,KAAK,EAAGG,OAAO,IAAK;IACrC,IAAI,CAACL,YAAY,EAAE;MACfA,YAAY,GAAG,IAAIM,WAAW,CAAC,CAAC;IACpC;IACArB,QAAQ,CAACe,YAAY,CAACO,MAAM,CAACF,OAAO,CAAC,CAAC;EAC1C,CAAC,CAAC;AACN;AACA,SAASvB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}