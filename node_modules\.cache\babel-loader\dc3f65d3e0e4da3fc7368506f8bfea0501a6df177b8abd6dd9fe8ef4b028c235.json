{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"onClick\", \"onTouchStart\"];\nimport * as React from 'react';\nimport Grow from '@mui/material/Grow';\nimport Paper from '@mui/material/Paper';\nimport Popper from '@mui/material/Popper';\nimport TrapFocus from '@mui/material/Unstable_TrapFocus';\nimport { useForkRef, useEventCallback, ownerDocument } from '@mui/material/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/material';\nimport { PickersActionBar } from '../../PickersActionBar';\nimport { getPickersPopperUtilityClass } from './pickersPopperClasses';\nimport { getActiveElement } from '../utils/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPickersPopperUtilityClass, classes);\n};\nconst PickersPopperRoot = styled(Popper, {\n  name: 'MuiPickersPopper',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    zIndex: theme.zIndex.modal\n  };\n});\nconst PickersPopperPaper = styled(Paper, {\n  name: 'MuiPickersPopper',\n  slot: 'Paper',\n  overridesResolver: (_, styles) => styles.paper\n})(_ref2 => {\n  let {\n    ownerState\n  } = _ref2;\n  return _extends({\n    transformOrigin: 'top center',\n    outline: 0\n  }, ownerState.placement === 'top' && {\n    transformOrigin: 'bottom center'\n  });\n});\nfunction clickedRootScrollbar(event, doc) {\n  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;\n}\n\n/**\n * Based on @mui/material/ClickAwayListener without the customization.\n * We can probably strip away even more since children won't be portaled.\n * @param {boolean} active Only listen to clicks when the popper is opened.\n * @param {(event: MouseEvent | TouchEvent) => void} onClickAway The callback to call when clicking outside the popper.\n * @returns {Array} The ref and event handler to listen to the outside clicks.\n */\nfunction useClickAwayListener(active, onClickAway) {\n  const movedRef = React.useRef(false);\n  const syntheticEventRef = React.useRef(false);\n  const nodeRef = React.useRef(null);\n  const activatedRef = React.useRef(false);\n  React.useEffect(() => {\n    if (!active) {\n      return undefined;\n    } // Ensure that this hook is not \"activated\" synchronously.\n    // https://github.com/facebook/react/issues/20074\n\n    function armClickAwayListener() {\n      activatedRef.current = true;\n    }\n    document.addEventListener('mousedown', armClickAwayListener, true);\n    document.addEventListener('touchstart', armClickAwayListener, true);\n    return () => {\n      document.removeEventListener('mousedown', armClickAwayListener, true);\n      document.removeEventListener('touchstart', armClickAwayListener, true);\n      activatedRef.current = false;\n    };\n  }, [active]); // The handler doesn't take event.defaultPrevented into account:\n  //\n  // event.preventDefault() is meant to stop default behaviors like\n  // clicking a checkbox to check it, hitting a button to submit a form,\n  // and hitting left arrow to move the cursor in a text input etc.\n  // Only special HTML elements have these default behaviors.\n\n  const handleClickAway = useEventCallback(event => {\n    if (!activatedRef.current) {\n      return;\n    } // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n\n    const insideReactTree = syntheticEventRef.current;\n    syntheticEventRef.current = false;\n    const doc = ownerDocument(nodeRef.current); // 1. IE11 support, which trigger the handleClickAway even after the unbind\n    // 2. The child might render null.\n    // 3. Behave like a blur listener.\n\n    if (!nodeRef.current ||\n    // is a TouchEvent?\n    'clientX' in event && clickedRootScrollbar(event, doc)) {\n      return;\n    } // Do not act if user performed touchmove\n\n    if (movedRef.current) {\n      movedRef.current = false;\n      return;\n    }\n    let insideDOM; // If not enough, can use https://github.com/DieterHolvoet/event-propagation-path/blob/master/propagationPath.js\n\n    if (event.composedPath) {\n      insideDOM = event.composedPath().indexOf(nodeRef.current) > -1;\n    } else {\n      insideDOM = !doc.documentElement.contains(event.target) || nodeRef.current.contains(event.target);\n    }\n    if (!insideDOM && !insideReactTree) {\n      onClickAway(event);\n    }\n  }); // Keep track of mouse/touch events that bubbled up through the portal.\n\n  const handleSynthetic = () => {\n    syntheticEventRef.current = true;\n  };\n  React.useEffect(() => {\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      const handleTouchMove = () => {\n        movedRef.current = true;\n      };\n      doc.addEventListener('touchstart', handleClickAway);\n      doc.addEventListener('touchmove', handleTouchMove);\n      return () => {\n        doc.removeEventListener('touchstart', handleClickAway);\n        doc.removeEventListener('touchmove', handleTouchMove);\n      };\n    }\n    return undefined;\n  }, [active, handleClickAway]);\n  React.useEffect(() => {\n    // TODO This behavior is not tested automatically\n    // It's unclear whether this is due to different update semantics in test (batched in act() vs discrete on click).\n    // Or if this is a timing related issues due to different Transition components\n    // Once we get rid of all the manual scheduling (e.g. setTimeout(update, 0)) we can revisit this code+test.\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      doc.addEventListener('click', handleClickAway);\n      return () => {\n        doc.removeEventListener('click', handleClickAway); // cleanup `handleClickAway`\n\n        syntheticEventRef.current = false;\n      };\n    }\n    return undefined;\n  }, [active, handleClickAway]);\n  return [nodeRef, handleSynthetic, handleSynthetic];\n}\nexport function PickersPopper(inProps) {\n  var _components$ActionBar;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersPopper'\n  });\n  const {\n    anchorEl,\n    children,\n    containerRef = null,\n    onBlur,\n    onClose,\n    onClear,\n    onAccept,\n    onCancel,\n    onSetToday,\n    open,\n    PopperProps,\n    role,\n    TransitionComponent = Grow,\n    TrapFocusProps,\n    PaperProps = {},\n    components,\n    componentsProps\n  } = props;\n  React.useEffect(() => {\n    function handleKeyDown(nativeEvent) {\n      // IE11, Edge (prior to using Bink?) use 'Esc'\n      if (open && (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc')) {\n        onClose();\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [onClose, open]);\n  const lastFocusedElementRef = React.useRef(null);\n  React.useEffect(() => {\n    if (role === 'tooltip') {\n      return;\n    }\n    if (open) {\n      lastFocusedElementRef.current = getActiveElement(document);\n    } else if (lastFocusedElementRef.current && lastFocusedElementRef.current instanceof HTMLElement) {\n      // make sure the button is flushed with updated label, before returning focus to it\n      // avoids issue, where screen reader could fail to announce selected date after selection\n      setTimeout(() => {\n        if (lastFocusedElementRef.current instanceof HTMLElement) {\n          lastFocusedElementRef.current.focus();\n        }\n      });\n    }\n  }, [open, role]);\n  const [clickAwayRef, onPaperClick, onPaperTouchStart] = useClickAwayListener(open, onBlur != null ? onBlur : onClose);\n  const paperRef = React.useRef(null);\n  const handleRef = useForkRef(paperRef, containerRef);\n  const handlePaperRef = useForkRef(handleRef, clickAwayRef);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const {\n      onClick: onPaperClickProp,\n      onTouchStart: onPaperTouchStartProp\n    } = PaperProps,\n    otherPaperProps = _objectWithoutPropertiesLoose(PaperProps, _excluded);\n  const handleKeyDown = event => {\n    if (event.key === 'Escape') {\n      // stop the propagation to avoid closing parent modal\n      event.stopPropagation();\n      onClose();\n    }\n  };\n  const ActionBar = (_components$ActionBar = components == null ? void 0 : components.ActionBar) != null ? _components$ActionBar : PickersActionBar;\n  const PaperContent = (components == null ? void 0 : components.PaperContent) || React.Fragment;\n  return /*#__PURE__*/_jsx(PickersPopperRoot, _extends({\n    transition: true,\n    role: role,\n    open: open,\n    anchorEl: anchorEl,\n    onKeyDown: handleKeyDown,\n    className: classes.root\n  }, PopperProps, {\n    children: _ref3 => {\n      let {\n        TransitionProps,\n        placement\n      } = _ref3;\n      return /*#__PURE__*/_jsx(TrapFocus, _extends({\n        open: open,\n        disableAutoFocus: true // pickers are managing focus position manually\n        // without this prop the focus is returned to the button before `aria-label` is updated\n        // which would force screen readers to read too old label\n        ,\n\n        disableRestoreFocus: true,\n        disableEnforceFocus: role === 'tooltip',\n        isEnabled: () => true\n      }, TrapFocusProps, {\n        children: /*#__PURE__*/_jsx(TransitionComponent, _extends({}, TransitionProps, {\n          children: /*#__PURE__*/_jsx(PickersPopperPaper, _extends({\n            tabIndex: -1,\n            elevation: 8,\n            ref: handlePaperRef,\n            onClick: event => {\n              onPaperClick(event);\n              if (onPaperClickProp) {\n                onPaperClickProp(event);\n              }\n            },\n            onTouchStart: event => {\n              onPaperTouchStart(event);\n              if (onPaperTouchStartProp) {\n                onPaperTouchStartProp(event);\n              }\n            },\n            ownerState: _extends({}, ownerState, {\n              placement\n            }),\n            className: classes.paper\n          }, otherPaperProps, {\n            children: /*#__PURE__*/_jsxs(PaperContent, _extends({}, componentsProps == null ? void 0 : componentsProps.paperContent, {\n              children: [children, /*#__PURE__*/_jsx(ActionBar, _extends({\n                onAccept: onAccept,\n                onClear: onClear,\n                onCancel: onCancel,\n                onSetToday: onSetToday,\n                actions: []\n              }, componentsProps == null ? void 0 : componentsProps.actionBar))]\n            }))\n          }))\n        }))\n      }));\n    }\n  }));\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "Grow", "Paper", "<PERSON><PERSON>", "TrapFocus", "useForkRef", "useEventCallback", "ownerDocument", "styled", "useThemeProps", "unstable_composeClasses", "composeClasses", "PickersActionBar", "getPickersPopperUtilityClass", "getActiveElement", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "slots", "root", "paper", "PickersPopperRoot", "name", "slot", "overridesResolver", "_", "styles", "_ref", "theme", "zIndex", "modal", "PickersPopperPaper", "_ref2", "transform<PERSON><PERSON>in", "outline", "placement", "clickedRootScrollbar", "event", "doc", "documentElement", "clientWidth", "clientX", "clientHeight", "clientY", "useClickAwayListener", "active", "onClickAway", "movedRef", "useRef", "syntheticEventRef", "nodeRef", "activatedRef", "useEffect", "undefined", "armClickAwayListener", "current", "document", "addEventListener", "removeEventListener", "handleClickAway", "insideReactTree", "insideDOM", "<PERSON><PERSON><PERSON>", "indexOf", "contains", "target", "handleSynthetic", "handleTouchMove", "PickersPopper", "inProps", "_components$ActionBar", "props", "anchorEl", "children", "containerRef", "onBlur", "onClose", "onClear", "onAccept", "onCancel", "onSetToday", "open", "PopperProps", "role", "TransitionComponent", "TrapFocusProps", "PaperProps", "components", "componentsProps", "handleKeyDown", "nativeEvent", "key", "lastFocusedElementRef", "HTMLElement", "setTimeout", "focus", "clickAwayRef", "onPaperClick", "onPaperTouchStart", "paperRef", "handleRef", "handlePaperRef", "onClick", "onPaperClickProp", "onTouchStart", "onPaperTouchStartProp", "otherPaperProps", "stopPropagation", "ActionBar", "PaperContent", "Fragment", "transition", "onKeyDown", "className", "_ref3", "TransitionProps", "disableAutoFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableEnforceFocus", "isEnabled", "tabIndex", "elevation", "ref", "paperContent", "actions", "actionBar"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/components/PickersPopper.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"onClick\", \"onTouchStart\"];\nimport * as React from 'react';\nimport Grow from '@mui/material/Grow';\nimport Paper from '@mui/material/Paper';\nimport Popper from '@mui/material/Popper';\nimport TrapFocus from '@mui/material/Unstable_TrapFocus';\nimport { useForkRef, useEventCallback, ownerDocument } from '@mui/material/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/material';\nimport { PickersActionBar } from '../../PickersActionBar';\nimport { getPickersPopperUtilityClass } from './pickersPopperClasses';\nimport { getActiveElement } from '../utils/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPickersPopperUtilityClass, classes);\n};\n\nconst PickersPopperRoot = styled(Popper, {\n  name: 'MuiPickersPopper',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})(({\n  theme\n}) => ({\n  zIndex: theme.zIndex.modal\n}));\nconst PickersPopperPaper = styled(Paper, {\n  name: 'MuiPickersPopper',\n  slot: 'Paper',\n  overridesResolver: (_, styles) => styles.paper\n})(({\n  ownerState\n}) => _extends({\n  transformOrigin: 'top center',\n  outline: 0\n}, ownerState.placement === 'top' && {\n  transformOrigin: 'bottom center'\n}));\n\nfunction clickedRootScrollbar(event, doc) {\n  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;\n}\n\n/**\n * Based on @mui/material/ClickAwayListener without the customization.\n * We can probably strip away even more since children won't be portaled.\n * @param {boolean} active Only listen to clicks when the popper is opened.\n * @param {(event: MouseEvent | TouchEvent) => void} onClickAway The callback to call when clicking outside the popper.\n * @returns {Array} The ref and event handler to listen to the outside clicks.\n */\nfunction useClickAwayListener(active, onClickAway) {\n  const movedRef = React.useRef(false);\n  const syntheticEventRef = React.useRef(false);\n  const nodeRef = React.useRef(null);\n  const activatedRef = React.useRef(false);\n  React.useEffect(() => {\n    if (!active) {\n      return undefined;\n    } // Ensure that this hook is not \"activated\" synchronously.\n    // https://github.com/facebook/react/issues/20074\n\n\n    function armClickAwayListener() {\n      activatedRef.current = true;\n    }\n\n    document.addEventListener('mousedown', armClickAwayListener, true);\n    document.addEventListener('touchstart', armClickAwayListener, true);\n    return () => {\n      document.removeEventListener('mousedown', armClickAwayListener, true);\n      document.removeEventListener('touchstart', armClickAwayListener, true);\n      activatedRef.current = false;\n    };\n  }, [active]); // The handler doesn't take event.defaultPrevented into account:\n  //\n  // event.preventDefault() is meant to stop default behaviors like\n  // clicking a checkbox to check it, hitting a button to submit a form,\n  // and hitting left arrow to move the cursor in a text input etc.\n  // Only special HTML elements have these default behaviors.\n\n  const handleClickAway = useEventCallback(event => {\n    if (!activatedRef.current) {\n      return;\n    } // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n\n\n    const insideReactTree = syntheticEventRef.current;\n    syntheticEventRef.current = false;\n    const doc = ownerDocument(nodeRef.current); // 1. IE11 support, which trigger the handleClickAway even after the unbind\n    // 2. The child might render null.\n    // 3. Behave like a blur listener.\n\n    if (!nodeRef.current || // is a TouchEvent?\n    'clientX' in event && clickedRootScrollbar(event, doc)) {\n      return;\n    } // Do not act if user performed touchmove\n\n\n    if (movedRef.current) {\n      movedRef.current = false;\n      return;\n    }\n\n    let insideDOM; // If not enough, can use https://github.com/DieterHolvoet/event-propagation-path/blob/master/propagationPath.js\n\n    if (event.composedPath) {\n      insideDOM = event.composedPath().indexOf(nodeRef.current) > -1;\n    } else {\n      insideDOM = !doc.documentElement.contains(event.target) || nodeRef.current.contains(event.target);\n    }\n\n    if (!insideDOM && !insideReactTree) {\n      onClickAway(event);\n    }\n  }); // Keep track of mouse/touch events that bubbled up through the portal.\n\n  const handleSynthetic = () => {\n    syntheticEventRef.current = true;\n  };\n\n  React.useEffect(() => {\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n\n      const handleTouchMove = () => {\n        movedRef.current = true;\n      };\n\n      doc.addEventListener('touchstart', handleClickAway);\n      doc.addEventListener('touchmove', handleTouchMove);\n      return () => {\n        doc.removeEventListener('touchstart', handleClickAway);\n        doc.removeEventListener('touchmove', handleTouchMove);\n      };\n    }\n\n    return undefined;\n  }, [active, handleClickAway]);\n  React.useEffect(() => {\n    // TODO This behavior is not tested automatically\n    // It's unclear whether this is due to different update semantics in test (batched in act() vs discrete on click).\n    // Or if this is a timing related issues due to different Transition components\n    // Once we get rid of all the manual scheduling (e.g. setTimeout(update, 0)) we can revisit this code+test.\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      doc.addEventListener('click', handleClickAway);\n      return () => {\n        doc.removeEventListener('click', handleClickAway); // cleanup `handleClickAway`\n\n        syntheticEventRef.current = false;\n      };\n    }\n\n    return undefined;\n  }, [active, handleClickAway]);\n  return [nodeRef, handleSynthetic, handleSynthetic];\n}\n\nexport function PickersPopper(inProps) {\n  var _components$ActionBar;\n\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersPopper'\n  });\n  const {\n    anchorEl,\n    children,\n    containerRef = null,\n    onBlur,\n    onClose,\n    onClear,\n    onAccept,\n    onCancel,\n    onSetToday,\n    open,\n    PopperProps,\n    role,\n    TransitionComponent = Grow,\n    TrapFocusProps,\n    PaperProps = {},\n    components,\n    componentsProps\n  } = props;\n  React.useEffect(() => {\n    function handleKeyDown(nativeEvent) {\n      // IE11, Edge (prior to using Bink?) use 'Esc'\n      if (open && (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc')) {\n        onClose();\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [onClose, open]);\n  const lastFocusedElementRef = React.useRef(null);\n  React.useEffect(() => {\n    if (role === 'tooltip') {\n      return;\n    }\n\n    if (open) {\n      lastFocusedElementRef.current = getActiveElement(document);\n    } else if (lastFocusedElementRef.current && lastFocusedElementRef.current instanceof HTMLElement) {\n      // make sure the button is flushed with updated label, before returning focus to it\n      // avoids issue, where screen reader could fail to announce selected date after selection\n      setTimeout(() => {\n        if (lastFocusedElementRef.current instanceof HTMLElement) {\n          lastFocusedElementRef.current.focus();\n        }\n      });\n    }\n  }, [open, role]);\n  const [clickAwayRef, onPaperClick, onPaperTouchStart] = useClickAwayListener(open, onBlur != null ? onBlur : onClose);\n  const paperRef = React.useRef(null);\n  const handleRef = useForkRef(paperRef, containerRef);\n  const handlePaperRef = useForkRef(handleRef, clickAwayRef);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n\n  const {\n    onClick: onPaperClickProp,\n    onTouchStart: onPaperTouchStartProp\n  } = PaperProps,\n        otherPaperProps = _objectWithoutPropertiesLoose(PaperProps, _excluded);\n\n  const handleKeyDown = event => {\n    if (event.key === 'Escape') {\n      // stop the propagation to avoid closing parent modal\n      event.stopPropagation();\n      onClose();\n    }\n  };\n\n  const ActionBar = (_components$ActionBar = components == null ? void 0 : components.ActionBar) != null ? _components$ActionBar : PickersActionBar;\n  const PaperContent = (components == null ? void 0 : components.PaperContent) || React.Fragment;\n  return /*#__PURE__*/_jsx(PickersPopperRoot, _extends({\n    transition: true,\n    role: role,\n    open: open,\n    anchorEl: anchorEl,\n    onKeyDown: handleKeyDown,\n    className: classes.root\n  }, PopperProps, {\n    children: ({\n      TransitionProps,\n      placement\n    }) => /*#__PURE__*/_jsx(TrapFocus, _extends({\n      open: open,\n      disableAutoFocus: true // pickers are managing focus position manually\n      // without this prop the focus is returned to the button before `aria-label` is updated\n      // which would force screen readers to read too old label\n      ,\n      disableRestoreFocus: true,\n      disableEnforceFocus: role === 'tooltip',\n      isEnabled: () => true\n    }, TrapFocusProps, {\n      children: /*#__PURE__*/_jsx(TransitionComponent, _extends({}, TransitionProps, {\n        children: /*#__PURE__*/_jsx(PickersPopperPaper, _extends({\n          tabIndex: -1,\n          elevation: 8,\n          ref: handlePaperRef,\n          onClick: event => {\n            onPaperClick(event);\n\n            if (onPaperClickProp) {\n              onPaperClickProp(event);\n            }\n          },\n          onTouchStart: event => {\n            onPaperTouchStart(event);\n\n            if (onPaperTouchStartProp) {\n              onPaperTouchStartProp(event);\n            }\n          },\n          ownerState: _extends({}, ownerState, {\n            placement\n          }),\n          className: classes.paper\n        }, otherPaperProps, {\n          children: /*#__PURE__*/_jsxs(PaperContent, _extends({}, componentsProps == null ? void 0 : componentsProps.paperContent, {\n            children: [children, /*#__PURE__*/_jsx(ActionBar, _extends({\n              onAccept: onAccept,\n              onClear: onClear,\n              onCancel: onCancel,\n              onSetToday: onSetToday,\n              actions: []\n            }, componentsProps == null ? void 0 : componentsProps.actionBar))]\n          }))\n        }))\n      }))\n    }))\n  }));\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,SAAS,MAAM,kCAAkC;AACxD,SAASC,UAAU,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,qBAAqB;AACjF,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,eAAe;AACzE,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,4BAA4B,QAAQ,wBAAwB;AACrE,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAEjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAOb,cAAc,CAACW,KAAK,EAAET,4BAA4B,EAAEQ,OAAO,CAAC;AACrE,CAAC;AAED,MAAMI,iBAAiB,GAAGjB,MAAM,CAACL,MAAM,EAAE;EACvCuB,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC3C,CAAC,CAAC,CAACQ,IAAA;EAAA,IAAC;IACFC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,MAAM,EAAED,KAAK,CAACC,MAAM,CAACC;EACvB,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,kBAAkB,GAAG3B,MAAM,CAACN,KAAK,EAAE;EACvCwB,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC3C,CAAC,CAAC,CAACY,KAAA;EAAA,IAAC;IACFhB;EACF,CAAC,GAAAgB,KAAA;EAAA,OAAKtC,QAAQ,CAAC;IACbuC,eAAe,EAAE,YAAY;IAC7BC,OAAO,EAAE;EACX,CAAC,EAAElB,UAAU,CAACmB,SAAS,KAAK,KAAK,IAAI;IACnCF,eAAe,EAAE;EACnB,CAAC,CAAC;AAAA,EAAC;AAEH,SAASG,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxC,OAAOA,GAAG,CAACC,eAAe,CAACC,WAAW,GAAGH,KAAK,CAACI,OAAO,IAAIH,GAAG,CAACC,eAAe,CAACG,YAAY,GAAGL,KAAK,CAACM,OAAO;AAC5G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,MAAM,EAAEC,WAAW,EAAE;EACjD,MAAMC,QAAQ,GAAGnD,KAAK,CAACoD,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMC,iBAAiB,GAAGrD,KAAK,CAACoD,MAAM,CAAC,KAAK,CAAC;EAC7C,MAAME,OAAO,GAAGtD,KAAK,CAACoD,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMG,YAAY,GAAGvD,KAAK,CAACoD,MAAM,CAAC,KAAK,CAAC;EACxCpD,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpB,IAAI,CAACP,MAAM,EAAE;MACX,OAAOQ,SAAS;IAClB,CAAC,CAAC;IACF;;IAGA,SAASC,oBAAoBA,CAAA,EAAG;MAC9BH,YAAY,CAACI,OAAO,GAAG,IAAI;IAC7B;IAEAC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEH,oBAAoB,EAAE,IAAI,CAAC;IAClEE,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAEH,oBAAoB,EAAE,IAAI,CAAC;IACnE,OAAO,MAAM;MACXE,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEJ,oBAAoB,EAAE,IAAI,CAAC;MACrEE,QAAQ,CAACE,mBAAmB,CAAC,YAAY,EAAEJ,oBAAoB,EAAE,IAAI,CAAC;MACtEH,YAAY,CAACI,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC,CAAC,CAAC;EACd;EACA;EACA;EACA;EACA;;EAEA,MAAMc,eAAe,GAAGzD,gBAAgB,CAACmC,KAAK,IAAI;IAChD,IAAI,CAACc,YAAY,CAACI,OAAO,EAAE;MACzB;IACF,CAAC,CAAC;IACF;;IAGA,MAAMK,eAAe,GAAGX,iBAAiB,CAACM,OAAO;IACjDN,iBAAiB,CAACM,OAAO,GAAG,KAAK;IACjC,MAAMjB,GAAG,GAAGnC,aAAa,CAAC+C,OAAO,CAACK,OAAO,CAAC,CAAC,CAAC;IAC5C;IACA;;IAEA,IAAI,CAACL,OAAO,CAACK,OAAO;IAAI;IACxB,SAAS,IAAIlB,KAAK,IAAID,oBAAoB,CAACC,KAAK,EAAEC,GAAG,CAAC,EAAE;MACtD;IACF,CAAC,CAAC;;IAGF,IAAIS,QAAQ,CAACQ,OAAO,EAAE;MACpBR,QAAQ,CAACQ,OAAO,GAAG,KAAK;MACxB;IACF;IAEA,IAAIM,SAAS,CAAC,CAAC;;IAEf,IAAIxB,KAAK,CAACyB,YAAY,EAAE;MACtBD,SAAS,GAAGxB,KAAK,CAACyB,YAAY,CAAC,CAAC,CAACC,OAAO,CAACb,OAAO,CAACK,OAAO,CAAC,GAAG,CAAC,CAAC;IAChE,CAAC,MAAM;MACLM,SAAS,GAAG,CAACvB,GAAG,CAACC,eAAe,CAACyB,QAAQ,CAAC3B,KAAK,CAAC4B,MAAM,CAAC,IAAIf,OAAO,CAACK,OAAO,CAACS,QAAQ,CAAC3B,KAAK,CAAC4B,MAAM,CAAC;IACnG;IAEA,IAAI,CAACJ,SAAS,IAAI,CAACD,eAAe,EAAE;MAClCd,WAAW,CAACT,KAAK,CAAC;IACpB;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ,MAAM6B,eAAe,GAAGA,CAAA,KAAM;IAC5BjB,iBAAiB,CAACM,OAAO,GAAG,IAAI;EAClC,CAAC;EAED3D,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpB,IAAIP,MAAM,EAAE;MACV,MAAMP,GAAG,GAAGnC,aAAa,CAAC+C,OAAO,CAACK,OAAO,CAAC;MAE1C,MAAMY,eAAe,GAAGA,CAAA,KAAM;QAC5BpB,QAAQ,CAACQ,OAAO,GAAG,IAAI;MACzB,CAAC;MAEDjB,GAAG,CAACmB,gBAAgB,CAAC,YAAY,EAAEE,eAAe,CAAC;MACnDrB,GAAG,CAACmB,gBAAgB,CAAC,WAAW,EAAEU,eAAe,CAAC;MAClD,OAAO,MAAM;QACX7B,GAAG,CAACoB,mBAAmB,CAAC,YAAY,EAAEC,eAAe,CAAC;QACtDrB,GAAG,CAACoB,mBAAmB,CAAC,WAAW,EAAES,eAAe,CAAC;MACvD,CAAC;IACH;IAEA,OAAOd,SAAS;EAClB,CAAC,EAAE,CAACR,MAAM,EAAEc,eAAe,CAAC,CAAC;EAC7B/D,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpB;IACA;IACA;IACA;IACA,IAAIP,MAAM,EAAE;MACV,MAAMP,GAAG,GAAGnC,aAAa,CAAC+C,OAAO,CAACK,OAAO,CAAC;MAC1CjB,GAAG,CAACmB,gBAAgB,CAAC,OAAO,EAAEE,eAAe,CAAC;MAC9C,OAAO,MAAM;QACXrB,GAAG,CAACoB,mBAAmB,CAAC,OAAO,EAAEC,eAAe,CAAC,CAAC,CAAC;;QAEnDV,iBAAiB,CAACM,OAAO,GAAG,KAAK;MACnC,CAAC;IACH;IAEA,OAAOF,SAAS;EAClB,CAAC,EAAE,CAACR,MAAM,EAAEc,eAAe,CAAC,CAAC;EAC7B,OAAO,CAACT,OAAO,EAAEgB,eAAe,EAAEA,eAAe,CAAC;AACpD;AAEA,OAAO,SAASE,aAAaA,CAACC,OAAO,EAAE;EACrC,IAAIC,qBAAqB;EAEzB,MAAMC,KAAK,GAAGlE,aAAa,CAAC;IAC1BkE,KAAK,EAAEF,OAAO;IACd/C,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJkD,QAAQ;IACRC,QAAQ;IACRC,YAAY,GAAG,IAAI;IACnBC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,IAAI;IACJC,WAAW;IACXC,IAAI;IACJC,mBAAmB,GAAGvF,IAAI;IAC1BwF,cAAc;IACdC,UAAU,GAAG,CAAC,CAAC;IACfC,UAAU;IACVC;EACF,CAAC,GAAGjB,KAAK;EACT3E,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpB,SAASqC,aAAaA,CAACC,WAAW,EAAE;MAClC;MACA,IAAIT,IAAI,KAAKS,WAAW,CAACC,GAAG,KAAK,QAAQ,IAAID,WAAW,CAACC,GAAG,KAAK,KAAK,CAAC,EAAE;QACvEf,OAAO,CAAC,CAAC;MACX;IACF;IAEApB,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEgC,aAAa,CAAC;IACnD,OAAO,MAAM;MACXjC,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAE+B,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACb,OAAO,EAAEK,IAAI,CAAC,CAAC;EACnB,MAAMW,qBAAqB,GAAGhG,KAAK,CAACoD,MAAM,CAAC,IAAI,CAAC;EAChDpD,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpB,IAAI+B,IAAI,KAAK,SAAS,EAAE;MACtB;IACF;IAEA,IAAIF,IAAI,EAAE;MACRW,qBAAqB,CAACrC,OAAO,GAAG7C,gBAAgB,CAAC8C,QAAQ,CAAC;IAC5D,CAAC,MAAM,IAAIoC,qBAAqB,CAACrC,OAAO,IAAIqC,qBAAqB,CAACrC,OAAO,YAAYsC,WAAW,EAAE;MAChG;MACA;MACAC,UAAU,CAAC,MAAM;QACf,IAAIF,qBAAqB,CAACrC,OAAO,YAAYsC,WAAW,EAAE;UACxDD,qBAAqB,CAACrC,OAAO,CAACwC,KAAK,CAAC,CAAC;QACvC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACd,IAAI,EAAEE,IAAI,CAAC,CAAC;EAChB,MAAM,CAACa,YAAY,EAAEC,YAAY,EAAEC,iBAAiB,CAAC,GAAGtD,oBAAoB,CAACqC,IAAI,EAAEN,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGC,OAAO,CAAC;EACrH,MAAMuB,QAAQ,GAAGvG,KAAK,CAACoD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMoD,SAAS,GAAGnG,UAAU,CAACkG,QAAQ,EAAEzB,YAAY,CAAC;EACpD,MAAM2B,cAAc,GAAGpG,UAAU,CAACmG,SAAS,EAAEJ,YAAY,CAAC;EAC1D,MAAMhF,UAAU,GAAGuD,KAAK;EACxB,MAAMtD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAE7C,MAAM;MACJsF,OAAO,EAAEC,gBAAgB;MACzBC,YAAY,EAAEC;IAChB,CAAC,GAAGnB,UAAU;IACRoB,eAAe,GAAGjH,6BAA6B,CAAC6F,UAAU,EAAE3F,SAAS,CAAC;EAE5E,MAAM8F,aAAa,GAAGpD,KAAK,IAAI;IAC7B,IAAIA,KAAK,CAACsD,GAAG,KAAK,QAAQ,EAAE;MAC1B;MACAtD,KAAK,CAACsE,eAAe,CAAC,CAAC;MACvB/B,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAMgC,SAAS,GAAG,CAACtC,qBAAqB,GAAGiB,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACqB,SAAS,KAAK,IAAI,GAAGtC,qBAAqB,GAAG9D,gBAAgB;EACjJ,MAAMqG,YAAY,GAAG,CAACtB,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACsB,YAAY,KAAKjH,KAAK,CAACkH,QAAQ;EAC9F,OAAO,aAAalG,IAAI,CAACS,iBAAiB,EAAE3B,QAAQ,CAAC;IACnDqH,UAAU,EAAE,IAAI;IAChB5B,IAAI,EAAEA,IAAI;IACVF,IAAI,EAAEA,IAAI;IACVT,QAAQ,EAAEA,QAAQ;IAClBwC,SAAS,EAAEvB,aAAa;IACxBwB,SAAS,EAAEhG,OAAO,CAACE;EACrB,CAAC,EAAE+D,WAAW,EAAE;IACdT,QAAQ,EAAEyC,KAAA;MAAA,IAAC;QACTC,eAAe;QACfhF;MACF,CAAC,GAAA+E,KAAA;MAAA,OAAK,aAAatG,IAAI,CAACZ,SAAS,EAAEN,QAAQ,CAAC;QAC1CuF,IAAI,EAAEA,IAAI;QACVmC,gBAAgB,EAAE,IAAI,CAAC;QACvB;QACA;QAAA;;QAEAC,mBAAmB,EAAE,IAAI;QACzBC,mBAAmB,EAAEnC,IAAI,KAAK,SAAS;QACvCoC,SAAS,EAAEA,CAAA,KAAM;MACnB,CAAC,EAAElC,cAAc,EAAE;QACjBZ,QAAQ,EAAE,aAAa7D,IAAI,CAACwE,mBAAmB,EAAE1F,QAAQ,CAAC,CAAC,CAAC,EAAEyH,eAAe,EAAE;UAC7E1C,QAAQ,EAAE,aAAa7D,IAAI,CAACmB,kBAAkB,EAAErC,QAAQ,CAAC;YACvD8H,QAAQ,EAAE,CAAC,CAAC;YACZC,SAAS,EAAE,CAAC;YACZC,GAAG,EAAErB,cAAc;YACnBC,OAAO,EAAEjE,KAAK,IAAI;cAChB4D,YAAY,CAAC5D,KAAK,CAAC;cAEnB,IAAIkE,gBAAgB,EAAE;gBACpBA,gBAAgB,CAAClE,KAAK,CAAC;cACzB;YACF,CAAC;YACDmE,YAAY,EAAEnE,KAAK,IAAI;cACrB6D,iBAAiB,CAAC7D,KAAK,CAAC;cAExB,IAAIoE,qBAAqB,EAAE;gBACzBA,qBAAqB,CAACpE,KAAK,CAAC;cAC9B;YACF,CAAC;YACDrB,UAAU,EAAEtB,QAAQ,CAAC,CAAC,CAAC,EAAEsB,UAAU,EAAE;cACnCmB;YACF,CAAC,CAAC;YACF8E,SAAS,EAAEhG,OAAO,CAACG;UACrB,CAAC,EAAEsF,eAAe,EAAE;YAClBjC,QAAQ,EAAE,aAAa3D,KAAK,CAAC+F,YAAY,EAAEnH,QAAQ,CAAC,CAAC,CAAC,EAAE8F,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACmC,YAAY,EAAE;cACvHlD,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAa7D,IAAI,CAACgG,SAAS,EAAElH,QAAQ,CAAC;gBACzDoF,QAAQ,EAAEA,QAAQ;gBAClBD,OAAO,EAAEA,OAAO;gBAChBE,QAAQ,EAAEA,QAAQ;gBAClBC,UAAU,EAAEA,UAAU;gBACtB4C,OAAO,EAAE;cACX,CAAC,EAAEpC,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACqC,SAAS,CAAC,CAAC;YACnE,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;IAAA;EACL,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}