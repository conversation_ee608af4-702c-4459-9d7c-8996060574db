{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback,useMemo}from'react';import{Box,Grid,Card,CardContent,Typography,CircularProgress,Alert,useTheme,List,ListItem,ListItemText,ListItemIcon,Chip,useMediaQuery}from'@mui/material';import{Assignment as ComplaintsIcon,CheckCircle as ResolvedIcon,Pending as PendingIcon,Error as HighPriorityIcon,FiberManualRecord as StatusIcon,Schedule as TimeIcon,AccessTime as ResolutionIcon,Dashboard as DashboardIcon,BarChart as InsightsIcon}from'@mui/icons-material';import{motion}from'framer-motion';import axios from'../utils/axiosConfig';import{format}from'date-fns';import{useSocket}from'../contexts/SocketContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MonthlyStatCard=/*#__PURE__*/React.memo(_ref=>{let{title,value,icon,color,loading,subtitle,index}=_ref;const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('sm'));if(loading){return/*#__PURE__*/_jsx(Card,{sx:{height:120,background:'rgba(255,255,255,0.15)',backdropFilter:'blur(10px)',border:'1px solid rgba(255,255,255,0.2)',color:'white'},children:/*#__PURE__*/_jsx(CardContent,{sx:{display:'flex',alignItems:'center',justifyContent:'center',height:'100%'},children:/*#__PURE__*/_jsx(CircularProgress,{size:24,sx:{color:'white'}})})});}return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Card,{sx:{height:120,background:'rgba(255,255,255,0.9)',color:'text.primary',borderRadius:2,boxShadow:theme.shadows[1],transition:'transform 0.2s ease, box-shadow 0.2s ease','&:hover':{transform:'translateY(-1px)',boxShadow:theme.shadows[2]}},children:/*#__PURE__*/_jsxs(CardContent,{sx:{display:'flex',flexDirection:'column',justifyContent:'space-between',height:'100%',p:2,'&:last-child':{pb:2}},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'flex-start'},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:isMobile?\"body2\":\"body1\",sx:{opacity:0.9,fontWeight:500,fontSize:'0.875rem'},children:title}),subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{opacity:0.7,fontSize:'0.75rem',display:'block'},children:subtitle})]}),/*#__PURE__*/_jsx(Box,{sx:{opacity:0.8,fontSize:isMobile?'1.2rem':'1.5rem'},children:icon})]}),/*#__PURE__*/_jsx(Typography,{variant:isMobile?\"h6\":\"h5\",sx:{fontWeight:700,fontSize:isMobile?'1.25rem':'1.5rem',textShadow:'0px 2px 4px rgba(0,0,0,0.3)'},children:value})]})})});});const StatCard=_ref2=>{let{title,value,icon,color,loading,subtitle}=_ref2;const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('sm'));return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Card,{sx:{height:'100%',background:\"linear-gradient(135deg, \".concat(theme.palette[color].main,\" 0%, \").concat(theme.palette[color].dark,\" 100%)\"),color:'white',position:'relative',overflow:'hidden',borderRadius:2,boxShadow:theme.shadows[2],transition:'transform 0.2s ease, box-shadow 0.2s ease','&:hover':{transform:'translateY(-2px)',boxShadow:theme.shadows[4]}},children:/*#__PURE__*/_jsxs(CardContent,{sx:{p:isMobile?2:3,height:'100%',display:'flex',flexDirection:'column',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1,display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(motion.div,{initial:{scale:0},animate:{scale:1},transition:{delay:0.2,type:\"spring\",stiffness:120},children:/*#__PURE__*/React.cloneElement(icon,{sx:{fontSize:isMobile?32:48,opacity:0.9,filter:'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'}})}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:isMobile?\"h5\":\"h4\",component:\"div\",sx:{fontWeight:700,lineHeight:1.2,mb:0.5,textShadow:'0px 2px 4px rgba(0,0,0,0.2)'},children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:isMobile?20:24,color:\"inherit\"}):value}),/*#__PURE__*/_jsx(Typography,{variant:isMobile?\"body2\":\"body1\",sx:{opacity:0.9,fontWeight:500,letterSpacing:'0.5px',textShadow:'0px 1px 2px rgba(0,0,0,0.2)'},children:title}),subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{opacity:0.8,fontWeight:400,textShadow:'0px 1px 2px rgba(0,0,0,0.2)',display:'block'},children:subtitle})]})]}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',right:-20,bottom:-20,opacity:0.15},children:/*#__PURE__*/React.cloneElement(icon,{sx:{fontSize:isMobile?100:140}})})]})})});};// Memoized color functions to prevent recalculation\nconst getStatusColor=(status,theme)=>{const statusColors={'New':theme.palette.info.main,'Assigned':theme.palette.warning.main,'In Progress':theme.palette.warning.dark,'Resolved':theme.palette.success.main,'Rejected':theme.palette.error.main};return statusColors[status]||theme.palette.grey[500];};const getPriorityColor=(priority,theme)=>{const priorityColors={'Low':theme.palette.success.main,'Medium':theme.palette.warning.main,'High':theme.palette.error.main,'Critical':theme.palette.error.dark};return priorityColors[priority]||theme.palette.grey[500];};const ActivityItem=/*#__PURE__*/React.memo(_ref3=>{let{activity,index}=_ref3;const theme=useTheme();// Memoize colors\nconst statusColor=useMemo(()=>getStatusColor(activity.Status,theme),[activity.Status,theme]);const priorityColor=useMemo(()=>getPriorityColor(activity.Priority,theme),[activity.Priority,theme]);return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(ListItem,{sx:{bgcolor:'background.paper',borderRadius:2,mb:1,boxShadow:1,'&:hover':{bgcolor:'action.hover',transform:'translateX(4px)',transition:'transform 0.15s ease'// Faster transition\n}},children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(StatusIcon,{sx:{color:statusColor}})}),/*#__PURE__*/_jsx(ListItemText,{primary:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,mb:0.5},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",sx:{fontWeight:500,flex:1},children:[\"#\",activity.ComplaintNumber,\" - \",activity.description]}),activity.Priority&&/*#__PURE__*/_jsx(Chip,{label:activity.Priority,size:\"small\",sx:{bgcolor:\"\".concat(priorityColor,\"15\"),color:priorityColor,fontWeight:500,fontSize:'0.7rem'}})]}),secondary:/*#__PURE__*/_jsxs(Box,{sx:{mt:0.5},children:[activity.activityDetails&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:0.5},children:activity.activityDetails}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(Chip,{label:activity.Status,size:\"small\",sx:{bgcolor:\"\".concat(statusColor,\"15\"),color:statusColor,fontWeight:500}}),activity.Category&&/*#__PURE__*/_jsx(Chip,{label:activity.Category,size:\"small\",variant:\"outlined\",sx:{fontSize:'0.7rem'}})]})]})})]})});});function Dashboard(){const[stats,setStats]=useState(null);const[activities,setActivities]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('sm'));const{socket}=useSocket();const fetchDashboardData=useCallback(async()=>{try{setLoading(true);// Fetch data with optimized timeout and caching\nconst[statsResponse,activitiesResponse]=await Promise.all([axios.get('/api/dashboard/stats',{timeout:10000,// 10 second timeout\nheaders:{'Cache-Control':'max-age=60'// Cache for 1 minute\n}}),axios.get('/api/dashboard/recent-activities',{timeout:10000,// 10 second timeout\nheaders:{'Cache-Control':'no-cache'// Always fetch fresh data for activities\n}})]);setStats(statsResponse.data);setActivities(activitiesResponse.data);setError(null);}catch(err){console.error('Error fetching dashboard data:',err);setError('Failed to load dashboard data. Please try again later.');}finally{setLoading(false);}},[]);useEffect(()=>{fetchDashboardData();// Set up auto-refresh every 30 seconds\nconst interval=setInterval(fetchDashboardData,30000);// Listen for real-time updates via Socket.IO\nif(socket){// Listen for status updates to refresh dashboard\nsocket.on('status_updated',()=>{console.log('Status update received, refreshing dashboard...');fetchDashboardData();});// Listen for new complaints to refresh dashboard\nsocket.on('complaint_created',()=>{console.log('New complaint received, refreshing dashboard...');fetchDashboardData();});}return()=>{clearInterval(interval);if(socket){socket.off('status_updated');socket.off('complaint_created');}};},[fetchDashboardData,socket]);const statCards=useMemo(()=>[{title:'Total Complaints',value:(stats===null||stats===void 0?void 0:stats.totalComplaints)||0,icon:/*#__PURE__*/_jsx(ComplaintsIcon,{}),color:'primary'},{title:'Resolved',value:(stats===null||stats===void 0?void 0:stats.resolvedComplaints)||0,icon:/*#__PURE__*/_jsx(ResolvedIcon,{}),color:'success'},{title:'Pending',value:(stats===null||stats===void 0?void 0:stats.pendingComplaints)||0,icon:/*#__PURE__*/_jsx(PendingIcon,{}),color:'warning'},{title:'High Priority',value:(stats===null||stats===void 0?void 0:stats.highPriorityComplaints)||0,icon:/*#__PURE__*/_jsx(HighPriorityIcon,{}),color:'error'}],[stats]);const monthlyStatCards=useMemo(()=>{if(!(stats!==null&&stats!==void 0&&stats.monthlyStats))return[];return[{title:'This Month',value:stats.monthlyStats.totalMonthlyComplaints||0,icon:/*#__PURE__*/_jsx(ComplaintsIcon,{}),color:'info',subtitle:'New complaints'},{title:'Resolution Rate',value:\"\".concat(stats.monthlyStats.resolutionRate||0,\"%\"),icon:/*#__PURE__*/_jsx(ResolvedIcon,{}),color:'success',subtitle:'Monthly average'},{title:'Response Time',value:\"\".concat(stats.monthlyStats.responseEfficiency||0,\"%\"),icon:/*#__PURE__*/_jsx(TimeIcon,{}),color:'warning',subtitle:'New tickets in 24h'},{title:'Avg Resolution',value:\"\".concat(Math.round(stats.monthlyStats.avgResolutionHours||0),\"h\"),icon:/*#__PURE__*/_jsx(ResolutionIcon,{}),color:'primary',subtitle:'Hours to resolve'}];},[stats]);return/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',position:'relative','&::before':{content:'\"\"',position:'absolute',top:0,left:0,right:0,bottom:0,background:'url(\"data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")',opacity:0.3,zIndex:0}},children:/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1,p:{xs:2,sm:3}},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'center',mb:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:{xs:50,sm:60,md:70},height:{xs:50,sm:60,md:70},borderRadius:'50%',background:'rgba(255, 255, 255, 0.95)',display:'flex',alignItems:'center',justifyContent:'center',mr:2,boxShadow:'0 8px 32px rgba(0, 0, 0, 0.3)',border:'2px solid rgba(255, 255, 255, 0.8)',overflow:'hidden'},children:/*#__PURE__*/_jsx(\"img\",{src:\"/prk-logo.jpg\",alt:\"PRK Company Logo\",style:{width:'85%',height:'85%',objectFit:'contain',borderRadius:'50%'}})}),/*#__PURE__*/_jsx(DashboardIcon,{sx:{fontSize:{xs:'2.5rem',sm:'3rem',md:'3.5rem'},color:'white',mr:2,filter:'drop-shadow(0px 4px 8px rgba(0,0,0,0.3))'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h3\",sx:{fontWeight:700,color:'white',fontSize:{xs:'2rem',sm:'2.5rem',md:'3rem'},textShadow:'0px 4px 8px rgba(0,0,0,0.3)',letterSpacing:'-0.02em'},children:\"Dashboard\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{mb:4,color:'rgba(255,255,255,0.9)',textAlign:'center',fontWeight:400,fontSize:{xs:'1rem',sm:'1.25rem'}},children:\"Internal Complaints Management System\"})]}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:3},children:error}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[statCards.map((card,index)=>/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(StatCard,_objectSpread(_objectSpread({},card),{},{loading:loading}))},card.title)),monthlyStatCards.length>0&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Card,{sx:{background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',color:'white'},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(InsightsIcon,{sx:{mr:1.5,color:'white'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:'white'},children:\"Monthly Performance Insights\"})]}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:3,children:monthlyStatCards.map((card,index)=>/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(MonthlyStatCard,_objectSpread(_objectSpread({},card),{},{loading:loading,index:index}))},card.title))})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Card,{sx:{height:'100%',minHeight:400},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{mb:2,fontWeight:600},children:\"Recent Activities\"}),loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',p:4},children:/*#__PURE__*/_jsx(CircularProgress,{})}):activities.length>0?/*#__PURE__*/_jsx(List,{sx:{p:0},children:activities.slice(0,8).map((activity,index)=>/*#__PURE__*/_jsx(ActivityItem,{activity:activity,index:index},\"\".concat(activity.ComplaintId,\"-\").concat(activity.Status)))}):/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4,color:'text.secondary'},children:/*#__PURE__*/_jsx(Typography,{children:\"No recent activities\"})})]})})})]})]})});}export default Dashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "CircularProgress", "<PERSON><PERSON>", "useTheme", "List", "ListItem", "ListItemText", "ListItemIcon", "Chip", "useMediaQuery", "Assignment", "ComplaintsIcon", "CheckCircle", "ResolvedIcon", "Pending", "PendingIcon", "Error", "HighPriorityIcon", "FiberManualRecord", "StatusIcon", "Schedule", "TimeIcon", "AccessTime", "ResolutionIcon", "Dashboard", "DashboardIcon", "<PERSON><PERSON><PERSON>", "InsightsIcon", "motion", "axios", "format", "useSocket", "jsx", "_jsx", "jsxs", "_jsxs", "MonthlyStatCard", "memo", "_ref", "title", "value", "icon", "color", "loading", "subtitle", "index", "theme", "isMobile", "breakpoints", "down", "sx", "height", "background", "<PERSON><PERSON>ilter", "border", "children", "display", "alignItems", "justifyContent", "size", "borderRadius", "boxShadow", "shadows", "transition", "transform", "flexDirection", "p", "pb", "variant", "opacity", "fontWeight", "fontSize", "textShadow", "StatCard", "_ref2", "concat", "palette", "main", "dark", "position", "overflow", "zIndex", "gap", "div", "initial", "scale", "animate", "delay", "type", "stiffness", "cloneElement", "filter", "component", "lineHeight", "mb", "letterSpacing", "right", "bottom", "getStatusColor", "status", "statusColors", "info", "warning", "success", "error", "grey", "getPriorityColor", "priority", "priorityColors", "ActivityItem", "_ref3", "activity", "statusColor", "Status", "priorityColor", "Priority", "bgcolor", "primary", "flex", "ComplaintNumber", "description", "label", "secondary", "mt", "activityDetails", "flexWrap", "Category", "stats", "setStats", "activities", "setActivities", "setLoading", "setError", "socket", "fetchDashboardData", "statsResponse", "activitiesResponse", "Promise", "all", "get", "timeout", "headers", "data", "err", "console", "interval", "setInterval", "on", "log", "clearInterval", "off", "statCards", "totalComplaints", "resolvedComplaints", "pendingComplaints", "highPriorityComplaints", "monthlyStatCards", "monthlyStats", "totalMonthlyComplaints", "resolutionRate", "responseEfficiency", "Math", "round", "avgResolutionHours", "minHeight", "content", "top", "left", "xs", "sm", "width", "md", "mr", "src", "alt", "style", "objectFit", "textAlign", "severity", "container", "spacing", "map", "card", "item", "_objectSpread", "length", "slice", "ComplaintId", "py"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport {\r\n  Box,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  CircularProgress,\r\n  Alert,\r\n  useTheme,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Chip,\r\n  useMediaQuery,\r\n} from '@mui/material';\r\nimport {\r\n  Assignment as ComplaintsIcon,\r\n  CheckCircle as ResolvedIcon,\r\n  Pending as PendingIcon,\r\n  Error as HighPriorityIcon,\r\n  FiberManualRecord as StatusIcon,\r\n  Schedule as TimeIcon,\r\n  AccessTime as ResolutionIcon,\r\n  Dashboard as DashboardIcon,\r\n  BarChart as InsightsIcon,\r\n} from '@mui/icons-material';\r\nimport { motion } from 'framer-motion';\r\nimport axios from '../utils/axiosConfig';\r\nimport { format } from 'date-fns';\r\nimport { useSocket } from '../contexts/SocketContext';\r\n\r\nconst MonthlyStatCard = React.memo(({ title, value, icon, color, loading, subtitle, index }) => {\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\n  if (loading) {\r\n    return (\r\n      <Card sx={{\r\n        height: 120,\r\n        background: 'rgba(255,255,255,0.15)',\r\n        backdropFilter: 'blur(10px)',\r\n        border: '1px solid rgba(255,255,255,0.2)',\r\n        color: 'white'\r\n      }}>\r\n        <CardContent sx={{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          height: '100%'\r\n        }}>\r\n          <CircularProgress size={24} sx={{ color: 'white' }} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <Card\r\n        sx={{\r\n          height: 120,\r\n          background: 'rgba(255,255,255,0.9)',\r\n          color: 'text.primary',\r\n          borderRadius: 2,\r\n          boxShadow: theme.shadows[1],\r\n          transition: 'transform 0.2s ease, box-shadow 0.2s ease',\r\n          '&:hover': {\r\n            transform: 'translateY(-1px)',\r\n            boxShadow: theme.shadows[2],\r\n          },\r\n        }}\r\n      >\r\n        <CardContent sx={{\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'space-between',\r\n          height: '100%',\r\n          p: 2,\r\n          '&:last-child': { pb: 2 }\r\n        }}>\r\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\r\n            <Box>\r\n              <Typography\r\n                variant={isMobile ? \"body2\" : \"body1\"}\r\n                sx={{\r\n                  opacity: 0.9,\r\n                  fontWeight: 500,\r\n                  fontSize: '0.875rem'\r\n                }}\r\n              >\r\n                {title}\r\n              </Typography>\r\n              {subtitle && (\r\n                <Typography\r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    opacity: 0.7,\r\n                    fontSize: '0.75rem',\r\n                    display: 'block'\r\n                  }}\r\n                >\r\n                  {subtitle}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n            <Box sx={{\r\n              opacity: 0.8,\r\n              fontSize: isMobile ? '1.2rem' : '1.5rem'\r\n            }}>\r\n              {icon}\r\n            </Box>\r\n          </Box>\r\n\r\n          <Typography\r\n            variant={isMobile ? \"h6\" : \"h5\"}\r\n            sx={{\r\n              fontWeight: 700,\r\n              fontSize: isMobile ? '1.25rem' : '1.5rem',\r\n              textShadow: '0px 2px 4px rgba(0,0,0,0.3)'\r\n            }}\r\n          >\r\n            {value}\r\n          </Typography>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n});\r\n\r\nconst StatCard = ({ title, value, icon, color, loading, subtitle }) => {\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\n  return (\r\n    <div>\r\n      <Card\r\n        sx={{\r\n          height: '100%',\r\n          background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\r\n          color: 'white',\r\n          position: 'relative',\r\n          overflow: 'hidden',\r\n          borderRadius: 2,\r\n          boxShadow: theme.shadows[2],\r\n          transition: 'transform 0.2s ease, box-shadow 0.2s ease',\r\n          '&:hover': {\r\n            transform: 'translateY(-2px)',\r\n            boxShadow: theme.shadows[4],\r\n          },\r\n        }}\r\n      >\r\n        <CardContent sx={{ \r\n          p: isMobile ? 2 : 3,\r\n          height: '100%',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'space-between'\r\n        }}>\r\n          <Box sx={{ \r\n            position: 'relative', \r\n            zIndex: 1,\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 2\r\n          }}>\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 120 }}\r\n            >\r\n              {React.cloneElement(icon, { \r\n                sx: { \r\n                  fontSize: isMobile ? 32 : 48,\r\n                  opacity: 0.9,\r\n                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\r\n                } \r\n              })}\r\n            </motion.div>\r\n            <Box>\r\n              <Typography \r\n                variant={isMobile ? \"h5\" : \"h4\"} \r\n                component=\"div\" \r\n                sx={{ \r\n                  fontWeight: 700,\r\n                  lineHeight: 1.2,\r\n                  mb: 0.5,\r\n                  textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {loading ? (\r\n                  <CircularProgress size={isMobile ? 20 : 24} color=\"inherit\" />\r\n                ) : (\r\n                  value\r\n                )}\r\n              </Typography>\r\n              <Typography\r\n                variant={isMobile ? \"body2\" : \"body1\"}\r\n                sx={{\r\n                  opacity: 0.9,\r\n                  fontWeight: 500,\r\n                  letterSpacing: '0.5px',\r\n                  textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {title}\r\n              </Typography>\r\n              {subtitle && (\r\n                <Typography\r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    opacity: 0.8,\r\n                    fontWeight: 400,\r\n                    textShadow: '0px 1px 2px rgba(0,0,0,0.2)',\r\n                    display: 'block'\r\n                  }}\r\n                >\r\n                  {subtitle}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n          </Box>\r\n          <Box\r\n            sx={{\r\n              position: 'absolute',\r\n              right: -20,\r\n              bottom: -20,\r\n              opacity: 0.15,\r\n            }}\r\n          >\r\n            {React.cloneElement(icon, {\r\n              sx: { fontSize: isMobile ? 100 : 140 }\r\n            })}\r\n          </Box>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Memoized color functions to prevent recalculation\r\nconst getStatusColor = (status, theme) => {\r\n  const statusColors = {\r\n    'New': theme.palette.info.main,\r\n    'Assigned': theme.palette.warning.main,\r\n    'In Progress': theme.palette.warning.dark,\r\n    'Resolved': theme.palette.success.main,\r\n    'Rejected': theme.palette.error.main,\r\n  };\r\n  return statusColors[status] || theme.palette.grey[500];\r\n};\r\n\r\nconst getPriorityColor = (priority, theme) => {\r\n  const priorityColors = {\r\n    'Low': theme.palette.success.main,\r\n    'Medium': theme.palette.warning.main,\r\n    'High': theme.palette.error.main,\r\n    'Critical': theme.palette.error.dark,\r\n  };\r\n  return priorityColors[priority] || theme.palette.grey[500];\r\n};\r\n\r\n\r\n\r\nconst ActivityItem = React.memo(({ activity, index }) => {\r\n  const theme = useTheme();\r\n\r\n\r\n\r\n  // Memoize colors\r\n  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);\r\n  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);\r\n\r\n  return (\r\n    <div>\r\n      <ListItem\r\n        sx={{\r\n          bgcolor: 'background.paper',\r\n          borderRadius: 2,\r\n          mb: 1,\r\n          boxShadow: 1,\r\n          '&:hover': {\r\n            bgcolor: 'action.hover',\r\n            transform: 'translateX(4px)',\r\n            transition: 'transform 0.15s ease', // Faster transition\r\n          },\r\n        }}\r\n      >\r\n        <ListItemIcon>\r\n          <StatusIcon sx={{ color: statusColor }} />\r\n        </ListItemIcon>\r\n        <ListItemText\r\n          primary={\r\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>\r\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, flex: 1 }}>\r\n                #{activity.ComplaintNumber} - {activity.description}\r\n              </Typography>\r\n              {activity.Priority && (\r\n                <Chip\r\n                  label={activity.Priority}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${priorityColor}15`,\r\n                    color: priorityColor,\r\n                    fontWeight: 500,\r\n                    fontSize: '0.7rem'\r\n                  }}\r\n                />\r\n              )}\r\n            </Box>\r\n          }\r\n          secondary={\r\n            <Box sx={{ mt: 0.5 }}>\r\n              {activity.activityDetails && (\r\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 0.5 }}>\r\n                  {activity.activityDetails}\r\n                </Typography>\r\n              )}\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>\r\n                <Chip\r\n                  label={activity.Status}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${statusColor}15`,\r\n                    color: statusColor,\r\n                    fontWeight: 500\r\n                  }}\r\n                />\r\n                {activity.Category && (\r\n                  <Chip\r\n                    label={activity.Category}\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ fontSize: '0.7rem' }}\r\n                  />\r\n                )}\r\n\r\n              </Box>\r\n            </Box>\r\n          }\r\n        />\r\n      </ListItem>\r\n    </div>\r\n  );\r\n});\r\n\r\nfunction Dashboard() {\r\n  const [stats, setStats] = useState(null);\r\n  const [activities, setActivities] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n  const { socket } = useSocket();\r\n\r\n  const fetchDashboardData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Fetch data with optimized timeout and caching\r\n      const [statsResponse, activitiesResponse] = await Promise.all([\r\n        axios.get('/api/dashboard/stats', {\r\n          timeout: 10000, // 10 second timeout\r\n          headers: {\r\n            'Cache-Control': 'max-age=60' // Cache for 1 minute\r\n          }\r\n        }),\r\n        axios.get('/api/dashboard/recent-activities', {\r\n          timeout: 10000, // 10 second timeout\r\n          headers: {\r\n            'Cache-Control': 'no-cache' // Always fetch fresh data for activities\r\n          }\r\n        })\r\n      ]);\r\n\r\n      setStats(statsResponse.data);\r\n      setActivities(activitiesResponse.data);\r\n      setError(null);\r\n    } catch (err) {\r\n      console.error('Error fetching dashboard data:', err);\r\n      setError('Failed to load dashboard data. Please try again later.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n\r\n    // Set up auto-refresh every 30 seconds\r\n    const interval = setInterval(fetchDashboardData, 30000);\r\n\r\n    // Listen for real-time updates via Socket.IO\r\n    if (socket) {\r\n      // Listen for status updates to refresh dashboard\r\n      socket.on('status_updated', () => {\r\n        console.log('Status update received, refreshing dashboard...');\r\n        fetchDashboardData();\r\n      });\r\n\r\n      // Listen for new complaints to refresh dashboard\r\n      socket.on('complaint_created', () => {\r\n        console.log('New complaint received, refreshing dashboard...');\r\n        fetchDashboardData();\r\n      });\r\n    }\r\n\r\n    return () => {\r\n      clearInterval(interval);\r\n      if (socket) {\r\n        socket.off('status_updated');\r\n        socket.off('complaint_created');\r\n      }\r\n    };\r\n  }, [fetchDashboardData, socket]);\r\n\r\n  const statCards = useMemo(() => [\r\n    {\r\n      title: 'Total Complaints',\r\n      value: stats?.totalComplaints || 0,\r\n      icon: <ComplaintsIcon />,\r\n      color: 'primary'\r\n    },\r\n    {\r\n      title: 'Resolved',\r\n      value: stats?.resolvedComplaints || 0,\r\n      icon: <ResolvedIcon />,\r\n      color: 'success'\r\n    },\r\n    {\r\n      title: 'Pending',\r\n      value: stats?.pendingComplaints || 0,\r\n      icon: <PendingIcon />,\r\n      color: 'warning'\r\n    },\r\n    {\r\n      title: 'High Priority',\r\n      value: stats?.highPriorityComplaints || 0,\r\n      icon: <HighPriorityIcon />,\r\n      color: 'error'\r\n    }\r\n  ], [stats]);\r\n\r\n  const monthlyStatCards = useMemo(() => {\r\n    if (!stats?.monthlyStats) return [];\r\n\r\n    return [\r\n      {\r\n        title: 'This Month',\r\n        value: stats.monthlyStats.totalMonthlyComplaints || 0,\r\n        icon: <ComplaintsIcon />,\r\n        color: 'info',\r\n        subtitle: 'New complaints'\r\n      },\r\n      {\r\n        title: 'Resolution Rate',\r\n        value: `${stats.monthlyStats.resolutionRate || 0}%`,\r\n        icon: <ResolvedIcon />,\r\n        color: 'success',\r\n        subtitle: 'Monthly average'\r\n      },\r\n      {\r\n        title: 'Response Time',\r\n        value: `${stats.monthlyStats.responseEfficiency || 0}%`,\r\n        icon: <TimeIcon />,\r\n        color: 'warning',\r\n        subtitle: 'New tickets in 24h'\r\n      },\r\n      {\r\n        title: 'Avg Resolution',\r\n        value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,\r\n        icon: <ResolutionIcon />,\r\n        color: 'primary',\r\n        subtitle: 'Hours to resolve'\r\n      }\r\n    ];\r\n  }, [stats]);\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        minHeight: '100vh',\r\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n        position: 'relative',\r\n        '&::before': {\r\n          content: '\"\"',\r\n          position: 'absolute',\r\n          top: 0,\r\n          left: 0,\r\n          right: 0,\r\n          bottom: 0,\r\n          background: 'url(\"data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")',\r\n          opacity: 0.3,\r\n          zIndex: 0\r\n        }\r\n      }}\r\n    >\r\n      <Box sx={{\r\n        position: 'relative',\r\n        zIndex: 1,\r\n        p: { xs: 2, sm: 3 }\r\n      }}>\r\n        <div>\r\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>\r\n            {/* Company Logo */}\r\n            <Box\r\n              sx={{\r\n                width: { xs: 50, sm: 60, md: 70 },\r\n                height: { xs: 50, sm: 60, md: 70 },\r\n                borderRadius: '50%',\r\n                background: 'rgba(255, 255, 255, 0.95)',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                mr: 2,\r\n                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',\r\n                border: '2px solid rgba(255, 255, 255, 0.8)',\r\n                overflow: 'hidden',\r\n              }}\r\n            >\r\n              <img\r\n                src=\"/prk-logo.jpg\"\r\n                alt=\"PRK Company Logo\"\r\n                style={{\r\n                  width: '85%',\r\n                  height: '85%',\r\n                  objectFit: 'contain',\r\n                  borderRadius: '50%',\r\n                }}\r\n              />\r\n            </Box>\r\n            <DashboardIcon\r\n              sx={{\r\n                fontSize: { xs: '2.5rem', sm: '3rem', md: '3.5rem' },\r\n                color: 'white',\r\n                mr: 2,\r\n                filter: 'drop-shadow(0px 4px 8px rgba(0,0,0,0.3))'\r\n              }}\r\n            />\r\n            <Typography\r\n              variant=\"h3\"\r\n              sx={{\r\n                fontWeight: 700,\r\n                color: 'white',\r\n                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },\r\n                textShadow: '0px 4px 8px rgba(0,0,0,0.3)',\r\n                letterSpacing: '-0.02em'\r\n              }}\r\n            >\r\n              Dashboard\r\n            </Typography>\r\n          </Box>\r\n          <Typography\r\n            variant=\"h6\"\r\n            sx={{\r\n              mb: 4,\r\n              color: 'rgba(255,255,255,0.9)',\r\n              textAlign: 'center',\r\n              fontWeight: 400,\r\n              fontSize: { xs: '1rem', sm: '1.25rem' }\r\n            }}\r\n          >\r\n            Internal Complaints Management System\r\n          </Typography>\r\n        </div>\r\n\r\n        {error && (\r\n          <Alert\r\n            severity=\"error\"\r\n            sx={{ mb: 3 }}\r\n          >\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n      <Grid container spacing={3}>\r\n        {statCards.map((card, index) => (\r\n          <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n            <StatCard {...card} loading={loading} />\r\n          </Grid>\r\n        ))}\r\n\r\n        {/* Monthly Statistics Section */}\r\n        {monthlyStatCards.length > 0 && (\r\n          <Grid item xs={12}>\r\n            <Card\r\n              sx={{\r\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n                color: 'white',\r\n              }}\r\n            >\r\n              <CardContent>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\r\n                  <InsightsIcon sx={{ mr: 1.5, color: 'white' }} />\r\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'white' }}>\r\n                    Monthly Performance Insights\r\n                  </Typography>\r\n                </Box>\r\n                <Grid container spacing={3}>\r\n                  {monthlyStatCards.map((card, index) => (\r\n                    <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n                      <MonthlyStatCard {...card} loading={loading} index={index} />\r\n                    </Grid>\r\n                  ))}\r\n                </Grid>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n        )}\r\n\r\n        <Grid item xs={12}>\r\n          <Card\r\n            sx={{\r\n              height: '100%',\r\n              minHeight: 400,\r\n            }}\r\n          >\r\n            <CardContent>\r\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\r\n                Recent Activities\r\n              </Typography>\r\n              {loading ? (\r\n                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>\r\n                  <CircularProgress />\r\n                </Box>\r\n              ) : activities.length > 0 ? (\r\n                <List sx={{ p: 0 }}>\r\n                  {activities.slice(0, 8).map((activity, index) => (\r\n                    <ActivityItem\r\n                      key={`${activity.ComplaintId}-${activity.Status}`}\r\n                      activity={activity}\r\n                      index={index}\r\n                    />\r\n                  ))}\r\n                </List>\r\n              ) : (\r\n                <Box \r\n                  sx={{ \r\n                    textAlign: 'center', \r\n                    py: 4,\r\n                    color: 'text.secondary'\r\n                  }}\r\n                >\r\n                  <Typography>No recent activities</Typography>\r\n                </Box>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n      </Box>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default Dashboard; "], "mappings": "0JAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,OAAO,KAAQ,OAAO,CACxE,OACEC,GAAG,CACHC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,UAAU,CACVC,gBAAgB,CAChBC,KAAK,CACLC,QAAQ,CACRC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,IAAI,CACJC,aAAa,KACR,eAAe,CACtB,OACEC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,WAAW,GAAI,CAAAC,YAAY,CAC3BC,OAAO,GAAI,CAAAC,WAAW,CACtBC,KAAK,GAAI,CAAAC,gBAAgB,CACzBC,iBAAiB,GAAI,CAAAC,UAAU,CAC/BC,QAAQ,GAAI,CAAAC,QAAQ,CACpBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,QAAQ,GAAI,CAAAC,YAAY,KACnB,qBAAqB,CAC5B,OAASC,MAAM,KAAQ,eAAe,CACtC,MAAO,CAAAC,KAAK,KAAM,sBAAsB,CACxC,OAASC,MAAM,KAAQ,UAAU,CACjC,OAASC,SAAS,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtD,KAAM,CAAAC,eAAe,cAAG7C,KAAK,CAAC8C,IAAI,CAACC,IAAA,EAA6D,IAA5D,CAAEC,KAAK,CAAEC,KAAK,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,KAAM,CAAC,CAAAP,IAAA,CACzF,KAAM,CAAAQ,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA4C,QAAQ,CAAGtC,aAAa,CAACqC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAE5D,GAAIN,OAAO,CAAE,CACX,mBACEV,IAAA,CAACnC,IAAI,EAACoD,EAAE,CAAE,CACRC,MAAM,CAAE,GAAG,CACXC,UAAU,CAAE,wBAAwB,CACpCC,cAAc,CAAE,YAAY,CAC5BC,MAAM,CAAE,iCAAiC,CACzCZ,KAAK,CAAE,OACT,CAAE,CAAAa,QAAA,cACAtB,IAAA,CAAClC,WAAW,EAACmD,EAAE,CAAE,CACfM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBP,MAAM,CAAE,MACV,CAAE,CAAAI,QAAA,cACAtB,IAAA,CAAChC,gBAAgB,EAAC0D,IAAI,CAAE,EAAG,CAACT,EAAE,CAAE,CAAER,KAAK,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC3C,CAAC,CACV,CAAC,CAEX,CAEA,mBACET,IAAA,QAAAsB,QAAA,cACEtB,IAAA,CAACnC,IAAI,EACHoD,EAAE,CAAE,CACFC,MAAM,CAAE,GAAG,CACXC,UAAU,CAAE,uBAAuB,CACnCV,KAAK,CAAE,cAAc,CACrBkB,YAAY,CAAE,CAAC,CACfC,SAAS,CAAEf,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC,CAC3BC,UAAU,CAAE,2CAA2C,CACvD,SAAS,CAAE,CACTC,SAAS,CAAE,kBAAkB,CAC7BH,SAAS,CAAEf,KAAK,CAACgB,OAAO,CAAC,CAAC,CAC5B,CACF,CAAE,CAAAP,QAAA,cAEFpB,KAAA,CAACpC,WAAW,EAACmD,EAAE,CAAE,CACfM,OAAO,CAAE,MAAM,CACfS,aAAa,CAAE,QAAQ,CACvBP,cAAc,CAAE,eAAe,CAC/BP,MAAM,CAAE,MAAM,CACde,CAAC,CAAE,CAAC,CACJ,cAAc,CAAE,CAAEC,EAAE,CAAE,CAAE,CAC1B,CAAE,CAAAZ,QAAA,eACApB,KAAA,CAACvC,GAAG,EAACsD,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,cAAc,CAAE,eAAe,CAAED,UAAU,CAAE,YAAa,CAAE,CAAAF,QAAA,eACtFpB,KAAA,CAACvC,GAAG,EAAA2D,QAAA,eACFtB,IAAA,CAACjC,UAAU,EACToE,OAAO,CAAErB,QAAQ,CAAG,OAAO,CAAG,OAAQ,CACtCG,EAAE,CAAE,CACFmB,OAAO,CAAE,GAAG,CACZC,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,UACZ,CAAE,CAAAhB,QAAA,CAEDhB,KAAK,CACI,CAAC,CACZK,QAAQ,eACPX,IAAA,CAACjC,UAAU,EACToE,OAAO,CAAC,SAAS,CACjBlB,EAAE,CAAE,CACFmB,OAAO,CAAE,GAAG,CACZE,QAAQ,CAAE,SAAS,CACnBf,OAAO,CAAE,OACX,CAAE,CAAAD,QAAA,CAEDX,QAAQ,CACC,CACb,EACE,CAAC,cACNX,IAAA,CAACrC,GAAG,EAACsD,EAAE,CAAE,CACPmB,OAAO,CAAE,GAAG,CACZE,QAAQ,CAAExB,QAAQ,CAAG,QAAQ,CAAG,QAClC,CAAE,CAAAQ,QAAA,CACCd,IAAI,CACF,CAAC,EACH,CAAC,cAENR,IAAA,CAACjC,UAAU,EACToE,OAAO,CAAErB,QAAQ,CAAG,IAAI,CAAG,IAAK,CAChCG,EAAE,CAAE,CACFoB,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAExB,QAAQ,CAAG,SAAS,CAAG,QAAQ,CACzCyB,UAAU,CAAE,6BACd,CAAE,CAAAjB,QAAA,CAEDf,KAAK,CACI,CAAC,EACF,CAAC,CACV,CAAC,CACJ,CAAC,CAEV,CAAC,CAAC,CAEF,KAAM,CAAAiC,QAAQ,CAAGC,KAAA,EAAsD,IAArD,CAAEnC,KAAK,CAAEC,KAAK,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,QAAS,CAAC,CAAA8B,KAAA,CAChE,KAAM,CAAA5B,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA4C,QAAQ,CAAGtC,aAAa,CAACqC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAE5D,mBACEhB,IAAA,QAAAsB,QAAA,cACEtB,IAAA,CAACnC,IAAI,EACHoD,EAAE,CAAE,CACFC,MAAM,CAAE,MAAM,CACdC,UAAU,4BAAAuB,MAAA,CAA6B7B,KAAK,CAAC8B,OAAO,CAAClC,KAAK,CAAC,CAACmC,IAAI,UAAAF,MAAA,CAAQ7B,KAAK,CAAC8B,OAAO,CAAClC,KAAK,CAAC,CAACoC,IAAI,UAAQ,CACzGpC,KAAK,CAAE,OAAO,CACdqC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,QAAQ,CAClBpB,YAAY,CAAE,CAAC,CACfC,SAAS,CAAEf,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC,CAC3BC,UAAU,CAAE,2CAA2C,CACvD,SAAS,CAAE,CACTC,SAAS,CAAE,kBAAkB,CAC7BH,SAAS,CAAEf,KAAK,CAACgB,OAAO,CAAC,CAAC,CAC5B,CACF,CAAE,CAAAP,QAAA,cAEFpB,KAAA,CAACpC,WAAW,EAACmD,EAAE,CAAE,CACfgB,CAAC,CAAEnB,QAAQ,CAAG,CAAC,CAAG,CAAC,CACnBI,MAAM,CAAE,MAAM,CACdK,OAAO,CAAE,MAAM,CACfS,aAAa,CAAE,QAAQ,CACvBP,cAAc,CAAE,eAClB,CAAE,CAAAH,QAAA,eACApB,KAAA,CAACvC,GAAG,EAACsD,EAAE,CAAE,CACP6B,QAAQ,CAAE,UAAU,CACpBE,MAAM,CAAE,CAAC,CACTzB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpByB,GAAG,CAAE,CACP,CAAE,CAAA3B,QAAA,eACAtB,IAAA,CAACL,MAAM,CAACuD,GAAG,EACTC,OAAO,CAAE,CAAEC,KAAK,CAAE,CAAE,CAAE,CACtBC,OAAO,CAAE,CAAED,KAAK,CAAE,CAAE,CAAE,CACtBtB,UAAU,CAAE,CAAEwB,KAAK,CAAE,GAAG,CAAEC,IAAI,CAAE,QAAQ,CAAEC,SAAS,CAAE,GAAI,CAAE,CAAAlC,QAAA,cAE1DhE,KAAK,CAACmG,YAAY,CAACjD,IAAI,CAAE,CACxBS,EAAE,CAAE,CACFqB,QAAQ,CAAExB,QAAQ,CAAG,EAAE,CAAG,EAAE,CAC5BsB,OAAO,CAAE,GAAG,CACZsB,MAAM,CAAE,0CACV,CACF,CAAC,CAAC,CACQ,CAAC,cACbxD,KAAA,CAACvC,GAAG,EAAA2D,QAAA,eACFtB,IAAA,CAACjC,UAAU,EACToE,OAAO,CAAErB,QAAQ,CAAG,IAAI,CAAG,IAAK,CAChC6C,SAAS,CAAC,KAAK,CACf1C,EAAE,CAAE,CACFoB,UAAU,CAAE,GAAG,CACfuB,UAAU,CAAE,GAAG,CACfC,EAAE,CAAE,GAAG,CACPtB,UAAU,CAAE,6BACd,CAAE,CAAAjB,QAAA,CAEDZ,OAAO,cACNV,IAAA,CAAChC,gBAAgB,EAAC0D,IAAI,CAAEZ,QAAQ,CAAG,EAAE,CAAG,EAAG,CAACL,KAAK,CAAC,SAAS,CAAE,CAAC,CAE9DF,KACD,CACS,CAAC,cACbP,IAAA,CAACjC,UAAU,EACToE,OAAO,CAAErB,QAAQ,CAAG,OAAO,CAAG,OAAQ,CACtCG,EAAE,CAAE,CACFmB,OAAO,CAAE,GAAG,CACZC,UAAU,CAAE,GAAG,CACfyB,aAAa,CAAE,OAAO,CACtBvB,UAAU,CAAE,6BACd,CAAE,CAAAjB,QAAA,CAEDhB,KAAK,CACI,CAAC,CACZK,QAAQ,eACPX,IAAA,CAACjC,UAAU,EACToE,OAAO,CAAC,SAAS,CACjBlB,EAAE,CAAE,CACFmB,OAAO,CAAE,GAAG,CACZC,UAAU,CAAE,GAAG,CACfE,UAAU,CAAE,6BAA6B,CACzChB,OAAO,CAAE,OACX,CAAE,CAAAD,QAAA,CAEDX,QAAQ,CACC,CACb,EACE,CAAC,EACH,CAAC,cACNX,IAAA,CAACrC,GAAG,EACFsD,EAAE,CAAE,CACF6B,QAAQ,CAAE,UAAU,CACpBiB,KAAK,CAAE,CAAC,EAAE,CACVC,MAAM,CAAE,CAAC,EAAE,CACX5B,OAAO,CAAE,IACX,CAAE,CAAAd,QAAA,cAEDhE,KAAK,CAACmG,YAAY,CAACjD,IAAI,CAAE,CACxBS,EAAE,CAAE,CAAEqB,QAAQ,CAAExB,QAAQ,CAAG,GAAG,CAAG,GAAI,CACvC,CAAC,CAAC,CACC,CAAC,EACK,CAAC,CACV,CAAC,CACJ,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAmD,cAAc,CAAGA,CAACC,MAAM,CAAErD,KAAK,GAAK,CACxC,KAAM,CAAAsD,YAAY,CAAG,CACnB,KAAK,CAAEtD,KAAK,CAAC8B,OAAO,CAACyB,IAAI,CAACxB,IAAI,CAC9B,UAAU,CAAE/B,KAAK,CAAC8B,OAAO,CAAC0B,OAAO,CAACzB,IAAI,CACtC,aAAa,CAAE/B,KAAK,CAAC8B,OAAO,CAAC0B,OAAO,CAACxB,IAAI,CACzC,UAAU,CAAEhC,KAAK,CAAC8B,OAAO,CAAC2B,OAAO,CAAC1B,IAAI,CACtC,UAAU,CAAE/B,KAAK,CAAC8B,OAAO,CAAC4B,KAAK,CAAC3B,IAClC,CAAC,CACD,MAAO,CAAAuB,YAAY,CAACD,MAAM,CAAC,EAAIrD,KAAK,CAAC8B,OAAO,CAAC6B,IAAI,CAAC,GAAG,CAAC,CACxD,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAACC,QAAQ,CAAE7D,KAAK,GAAK,CAC5C,KAAM,CAAA8D,cAAc,CAAG,CACrB,KAAK,CAAE9D,KAAK,CAAC8B,OAAO,CAAC2B,OAAO,CAAC1B,IAAI,CACjC,QAAQ,CAAE/B,KAAK,CAAC8B,OAAO,CAAC0B,OAAO,CAACzB,IAAI,CACpC,MAAM,CAAE/B,KAAK,CAAC8B,OAAO,CAAC4B,KAAK,CAAC3B,IAAI,CAChC,UAAU,CAAE/B,KAAK,CAAC8B,OAAO,CAAC4B,KAAK,CAAC1B,IAClC,CAAC,CACD,MAAO,CAAA8B,cAAc,CAACD,QAAQ,CAAC,EAAI7D,KAAK,CAAC8B,OAAO,CAAC6B,IAAI,CAAC,GAAG,CAAC,CAC5D,CAAC,CAID,KAAM,CAAAI,YAAY,cAAGtH,KAAK,CAAC8C,IAAI,CAACyE,KAAA,EAAyB,IAAxB,CAAEC,QAAQ,CAAElE,KAAM,CAAC,CAAAiE,KAAA,CAClD,KAAM,CAAAhE,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CAIxB;AACA,KAAM,CAAA6G,WAAW,CAAGrH,OAAO,CAAC,IAAMuG,cAAc,CAACa,QAAQ,CAACE,MAAM,CAAEnE,KAAK,CAAC,CAAE,CAACiE,QAAQ,CAACE,MAAM,CAAEnE,KAAK,CAAC,CAAC,CACnG,KAAM,CAAAoE,aAAa,CAAGvH,OAAO,CAAC,IAAM+G,gBAAgB,CAACK,QAAQ,CAACI,QAAQ,CAAErE,KAAK,CAAC,CAAE,CAACiE,QAAQ,CAACI,QAAQ,CAAErE,KAAK,CAAC,CAAC,CAE3G,mBACEb,IAAA,QAAAsB,QAAA,cACEpB,KAAA,CAAC9B,QAAQ,EACP6C,EAAE,CAAE,CACFkE,OAAO,CAAE,kBAAkB,CAC3BxD,YAAY,CAAE,CAAC,CACfkC,EAAE,CAAE,CAAC,CACLjC,SAAS,CAAE,CAAC,CACZ,SAAS,CAAE,CACTuD,OAAO,CAAE,cAAc,CACvBpD,SAAS,CAAE,iBAAiB,CAC5BD,UAAU,CAAE,sBAAwB;AACtC,CACF,CAAE,CAAAR,QAAA,eAEFtB,IAAA,CAAC1B,YAAY,EAAAgD,QAAA,cACXtB,IAAA,CAACd,UAAU,EAAC+B,EAAE,CAAE,CAAER,KAAK,CAAEsE,WAAY,CAAE,CAAE,CAAC,CAC9B,CAAC,cACf/E,IAAA,CAAC3B,YAAY,EACX+G,OAAO,cACLlF,KAAA,CAACvC,GAAG,EAACsD,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEyB,GAAG,CAAE,CAAC,CAAEY,EAAE,CAAE,GAAI,CAAE,CAAAvC,QAAA,eAClEpB,KAAA,CAACnC,UAAU,EAACoE,OAAO,CAAC,WAAW,CAAClB,EAAE,CAAE,CAAEoB,UAAU,CAAE,GAAG,CAAEgD,IAAI,CAAE,CAAE,CAAE,CAAA/D,QAAA,EAAC,GAC/D,CAACwD,QAAQ,CAACQ,eAAe,CAAC,KAAG,CAACR,QAAQ,CAACS,WAAW,EACzC,CAAC,CACZT,QAAQ,CAACI,QAAQ,eAChBlF,IAAA,CAACzB,IAAI,EACHiH,KAAK,CAAEV,QAAQ,CAACI,QAAS,CACzBxD,IAAI,CAAC,OAAO,CACZT,EAAE,CAAE,CACFkE,OAAO,IAAAzC,MAAA,CAAKuC,aAAa,MAAI,CAC7BxE,KAAK,CAAEwE,aAAa,CACpB5C,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,QACZ,CAAE,CACH,CACF,EACE,CACN,CACDmD,SAAS,cACPvF,KAAA,CAACvC,GAAG,EAACsD,EAAE,CAAE,CAAEyE,EAAE,CAAE,GAAI,CAAE,CAAApE,QAAA,EAClBwD,QAAQ,CAACa,eAAe,eACvB3F,IAAA,CAACjC,UAAU,EAACoE,OAAO,CAAC,OAAO,CAAC1B,KAAK,CAAC,gBAAgB,CAACQ,EAAE,CAAE,CAAE4C,EAAE,CAAE,GAAI,CAAE,CAAAvC,QAAA,CAChEwD,QAAQ,CAACa,eAAe,CACf,CACb,cACDzF,KAAA,CAACvC,GAAG,EAACsD,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEyB,GAAG,CAAE,CAAC,CAAE2C,QAAQ,CAAE,MAAO,CAAE,CAAAtE,QAAA,eAC3EtB,IAAA,CAACzB,IAAI,EACHiH,KAAK,CAAEV,QAAQ,CAACE,MAAO,CACvBtD,IAAI,CAAC,OAAO,CACZT,EAAE,CAAE,CACFkE,OAAO,IAAAzC,MAAA,CAAKqC,WAAW,MAAI,CAC3BtE,KAAK,CAAEsE,WAAW,CAClB1C,UAAU,CAAE,GACd,CAAE,CACH,CAAC,CACDyC,QAAQ,CAACe,QAAQ,eAChB7F,IAAA,CAACzB,IAAI,EACHiH,KAAK,CAAEV,QAAQ,CAACe,QAAS,CACzBnE,IAAI,CAAC,OAAO,CACZS,OAAO,CAAC,UAAU,CAClBlB,EAAE,CAAE,CAAEqB,QAAQ,CAAE,QAAS,CAAE,CAC5B,CACF,EAEE,CAAC,EACH,CACN,CACF,CAAC,EACM,CAAC,CACR,CAAC,CAEV,CAAC,CAAC,CAEF,QAAS,CAAA/C,SAASA,CAAA,CAAG,CACnB,KAAM,CAACuG,KAAK,CAAEC,QAAQ,CAAC,CAAGxI,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACyI,UAAU,CAAEC,aAAa,CAAC,CAAG1I,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACmD,OAAO,CAAEwF,UAAU,CAAC,CAAG3I,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACgH,KAAK,CAAE4B,QAAQ,CAAC,CAAG5I,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAAsD,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA4C,QAAQ,CAAGtC,aAAa,CAACqC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAAEoF,MAAO,CAAC,CAAGtG,SAAS,CAAC,CAAC,CAE9B,KAAM,CAAAuG,kBAAkB,CAAG5I,WAAW,CAAC,SAAY,CACjD,GAAI,CACFyI,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAACI,aAAa,CAAEC,kBAAkB,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAC5D7G,KAAK,CAAC8G,GAAG,CAAC,sBAAsB,CAAE,CAChCC,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,CACP,eAAe,CAAE,YAAa;AAChC,CACF,CAAC,CAAC,CACFhH,KAAK,CAAC8G,GAAG,CAAC,kCAAkC,CAAE,CAC5CC,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,CACP,eAAe,CAAE,UAAW;AAC9B,CACF,CAAC,CAAC,CACH,CAAC,CAEFb,QAAQ,CAACO,aAAa,CAACO,IAAI,CAAC,CAC5BZ,aAAa,CAACM,kBAAkB,CAACM,IAAI,CAAC,CACtCV,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,MAAOW,GAAG,CAAE,CACZC,OAAO,CAACxC,KAAK,CAAC,gCAAgC,CAAEuC,GAAG,CAAC,CACpDX,QAAQ,CAAC,wDAAwD,CAAC,CACpE,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN1I,SAAS,CAAC,IAAM,CACd6I,kBAAkB,CAAC,CAAC,CAEpB;AACA,KAAM,CAAAW,QAAQ,CAAGC,WAAW,CAACZ,kBAAkB,CAAE,KAAK,CAAC,CAEvD;AACA,GAAID,MAAM,CAAE,CACV;AACAA,MAAM,CAACc,EAAE,CAAC,gBAAgB,CAAE,IAAM,CAChCH,OAAO,CAACI,GAAG,CAAC,iDAAiD,CAAC,CAC9Dd,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAC,CAEF;AACAD,MAAM,CAACc,EAAE,CAAC,mBAAmB,CAAE,IAAM,CACnCH,OAAO,CAACI,GAAG,CAAC,iDAAiD,CAAC,CAC9Dd,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAC,CACJ,CAEA,MAAO,IAAM,CACXe,aAAa,CAACJ,QAAQ,CAAC,CACvB,GAAIZ,MAAM,CAAE,CACVA,MAAM,CAACiB,GAAG,CAAC,gBAAgB,CAAC,CAC5BjB,MAAM,CAACiB,GAAG,CAAC,mBAAmB,CAAC,CACjC,CACF,CAAC,CACH,CAAC,CAAE,CAAChB,kBAAkB,CAAED,MAAM,CAAC,CAAC,CAEhC,KAAM,CAAAkB,SAAS,CAAG5J,OAAO,CAAC,IAAM,CAC9B,CACE4C,KAAK,CAAE,kBAAkB,CACzBC,KAAK,CAAE,CAAAuF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEyB,eAAe,GAAI,CAAC,CAClC/G,IAAI,cAAER,IAAA,CAACtB,cAAc,GAAE,CAAC,CACxB+B,KAAK,CAAE,SACT,CAAC,CACD,CACEH,KAAK,CAAE,UAAU,CACjBC,KAAK,CAAE,CAAAuF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE0B,kBAAkB,GAAI,CAAC,CACrChH,IAAI,cAAER,IAAA,CAACpB,YAAY,GAAE,CAAC,CACtB6B,KAAK,CAAE,SACT,CAAC,CACD,CACEH,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,CAAAuF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE2B,iBAAiB,GAAI,CAAC,CACpCjH,IAAI,cAAER,IAAA,CAAClB,WAAW,GAAE,CAAC,CACrB2B,KAAK,CAAE,SACT,CAAC,CACD,CACEH,KAAK,CAAE,eAAe,CACtBC,KAAK,CAAE,CAAAuF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE4B,sBAAsB,GAAI,CAAC,CACzClH,IAAI,cAAER,IAAA,CAAChB,gBAAgB,GAAE,CAAC,CAC1ByB,KAAK,CAAE,OACT,CAAC,CACF,CAAE,CAACqF,KAAK,CAAC,CAAC,CAEX,KAAM,CAAA6B,gBAAgB,CAAGjK,OAAO,CAAC,IAAM,CACrC,GAAI,EAACoI,KAAK,SAALA,KAAK,WAALA,KAAK,CAAE8B,YAAY,EAAE,MAAO,EAAE,CAEnC,MAAO,CACL,CACEtH,KAAK,CAAE,YAAY,CACnBC,KAAK,CAAEuF,KAAK,CAAC8B,YAAY,CAACC,sBAAsB,EAAI,CAAC,CACrDrH,IAAI,cAAER,IAAA,CAACtB,cAAc,GAAE,CAAC,CACxB+B,KAAK,CAAE,MAAM,CACbE,QAAQ,CAAE,gBACZ,CAAC,CACD,CACEL,KAAK,CAAE,iBAAiB,CACxBC,KAAK,IAAAmC,MAAA,CAAKoD,KAAK,CAAC8B,YAAY,CAACE,cAAc,EAAI,CAAC,KAAG,CACnDtH,IAAI,cAAER,IAAA,CAACpB,YAAY,GAAE,CAAC,CACtB6B,KAAK,CAAE,SAAS,CAChBE,QAAQ,CAAE,iBACZ,CAAC,CACD,CACEL,KAAK,CAAE,eAAe,CACtBC,KAAK,IAAAmC,MAAA,CAAKoD,KAAK,CAAC8B,YAAY,CAACG,kBAAkB,EAAI,CAAC,KAAG,CACvDvH,IAAI,cAAER,IAAA,CAACZ,QAAQ,GAAE,CAAC,CAClBqB,KAAK,CAAE,SAAS,CAChBE,QAAQ,CAAE,oBACZ,CAAC,CACD,CACEL,KAAK,CAAE,gBAAgB,CACvBC,KAAK,IAAAmC,MAAA,CAAKsF,IAAI,CAACC,KAAK,CAACnC,KAAK,CAAC8B,YAAY,CAACM,kBAAkB,EAAI,CAAC,CAAC,KAAG,CACnE1H,IAAI,cAAER,IAAA,CAACV,cAAc,GAAE,CAAC,CACxBmB,KAAK,CAAE,SAAS,CAChBE,QAAQ,CAAE,kBACZ,CAAC,CACF,CACH,CAAC,CAAE,CAACmF,KAAK,CAAC,CAAC,CAEX,mBACE9F,IAAA,CAACrC,GAAG,EACFsD,EAAE,CAAE,CACFkH,SAAS,CAAE,OAAO,CAClBhH,UAAU,CAAE,mDAAmD,CAC/D2B,QAAQ,CAAE,UAAU,CACpB,WAAW,CAAE,CACXsF,OAAO,CAAE,IAAI,CACbtF,QAAQ,CAAE,UAAU,CACpBuF,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPvE,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACT7C,UAAU,CAAE,mQAAmQ,CAC/QiB,OAAO,CAAE,GAAG,CACZY,MAAM,CAAE,CACV,CACF,CAAE,CAAA1B,QAAA,cAEFpB,KAAA,CAACvC,GAAG,EAACsD,EAAE,CAAE,CACP6B,QAAQ,CAAE,UAAU,CACpBE,MAAM,CAAE,CAAC,CACTf,CAAC,CAAE,CAAEsG,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CACpB,CAAE,CAAAlH,QAAA,eACApB,KAAA,QAAAoB,QAAA,eACEpB,KAAA,CAACvC,GAAG,EAACsD,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,QAAQ,CAAEoC,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,eAElFtB,IAAA,CAACrC,GAAG,EACFsD,EAAE,CAAE,CACFwH,KAAK,CAAE,CAAEF,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEE,EAAE,CAAE,EAAG,CAAC,CACjCxH,MAAM,CAAE,CAAEqH,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEE,EAAE,CAAE,EAAG,CAAC,CAClC/G,YAAY,CAAE,KAAK,CACnBR,UAAU,CAAE,2BAA2B,CACvCI,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBkH,EAAE,CAAE,CAAC,CACL/G,SAAS,CAAE,+BAA+B,CAC1CP,MAAM,CAAE,oCAAoC,CAC5C0B,QAAQ,CAAE,QACZ,CAAE,CAAAzB,QAAA,cAEFtB,IAAA,QACE4I,GAAG,CAAC,eAAe,CACnBC,GAAG,CAAC,kBAAkB,CACtBC,KAAK,CAAE,CACLL,KAAK,CAAE,KAAK,CACZvH,MAAM,CAAE,KAAK,CACb6H,SAAS,CAAE,SAAS,CACpBpH,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,CACC,CAAC,cACN3B,IAAA,CAACR,aAAa,EACZyB,EAAE,CAAE,CACFqB,QAAQ,CAAE,CAAEiG,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAM,CAAEE,EAAE,CAAE,QAAS,CAAC,CACpDjI,KAAK,CAAE,OAAO,CACdkI,EAAE,CAAE,CAAC,CACLjF,MAAM,CAAE,0CACV,CAAE,CACH,CAAC,cACF1D,IAAA,CAACjC,UAAU,EACToE,OAAO,CAAC,IAAI,CACZlB,EAAE,CAAE,CACFoB,UAAU,CAAE,GAAG,CACf5B,KAAK,CAAE,OAAO,CACd6B,QAAQ,CAAE,CAAEiG,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAQ,CAAEE,EAAE,CAAE,MAAO,CAAC,CAClDnG,UAAU,CAAE,6BAA6B,CACzCuB,aAAa,CAAE,SACjB,CAAE,CAAAxC,QAAA,CACH,WAED,CAAY,CAAC,EACV,CAAC,cACNtB,IAAA,CAACjC,UAAU,EACToE,OAAO,CAAC,IAAI,CACZlB,EAAE,CAAE,CACF4C,EAAE,CAAE,CAAC,CACLpD,KAAK,CAAE,uBAAuB,CAC9BuI,SAAS,CAAE,QAAQ,CACnB3G,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,CAAEiG,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,SAAU,CACxC,CAAE,CAAAlH,QAAA,CACH,uCAED,CAAY,CAAC,EACV,CAAC,CAELiD,KAAK,eACJvE,IAAA,CAAC/B,KAAK,EACJgL,QAAQ,CAAC,OAAO,CAChBhI,EAAE,CAAE,CAAE4C,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,CAEbiD,KAAK,CACD,CACR,cAEHrE,KAAA,CAACtC,IAAI,EAACsL,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA7H,QAAA,EACxBgG,SAAS,CAAC8B,GAAG,CAAC,CAACC,IAAI,CAAEzI,KAAK,gBACzBZ,IAAA,CAACpC,IAAI,EAAC0L,IAAI,MAACf,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACE,EAAE,CAAE,CAAE,CAAApH,QAAA,cAC9BtB,IAAA,CAACwC,QAAQ,CAAA+G,aAAA,CAAAA,aAAA,IAAKF,IAAI,MAAE3I,OAAO,CAAEA,OAAQ,EAAE,CAAC,EADJ2I,IAAI,CAAC/I,KAErC,CACP,CAAC,CAGDqH,gBAAgB,CAAC6B,MAAM,CAAG,CAAC,eAC1BxJ,IAAA,CAACpC,IAAI,EAAC0L,IAAI,MAACf,EAAE,CAAE,EAAG,CAAAjH,QAAA,cAChBtB,IAAA,CAACnC,IAAI,EACHoD,EAAE,CAAE,CACFE,UAAU,CAAE,mDAAmD,CAC/DV,KAAK,CAAE,OACT,CAAE,CAAAa,QAAA,cAEFpB,KAAA,CAACpC,WAAW,EAAAwD,QAAA,eACVpB,KAAA,CAACvC,GAAG,EAACsD,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEqC,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,eACxDtB,IAAA,CAACN,YAAY,EAACuB,EAAE,CAAE,CAAE0H,EAAE,CAAE,GAAG,CAAElI,KAAK,CAAE,OAAQ,CAAE,CAAE,CAAC,cACjDT,IAAA,CAACjC,UAAU,EAACoE,OAAO,CAAC,IAAI,CAAClB,EAAE,CAAE,CAAEoB,UAAU,CAAE,GAAG,CAAE5B,KAAK,CAAE,OAAQ,CAAE,CAAAa,QAAA,CAAC,8BAElE,CAAY,CAAC,EACV,CAAC,cACNtB,IAAA,CAACpC,IAAI,EAACsL,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA7H,QAAA,CACxBqG,gBAAgB,CAACyB,GAAG,CAAC,CAACC,IAAI,CAAEzI,KAAK,gBAChCZ,IAAA,CAACpC,IAAI,EAAC0L,IAAI,MAACf,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACE,EAAE,CAAE,CAAE,CAAApH,QAAA,cAC9BtB,IAAA,CAACG,eAAe,CAAAoJ,aAAA,CAAAA,aAAA,IAAKF,IAAI,MAAE3I,OAAO,CAAEA,OAAQ,CAACE,KAAK,CAAEA,KAAM,EAAE,CAAC,EADzByI,IAAI,CAAC/I,KAErC,CACP,CAAC,CACE,CAAC,EACI,CAAC,CACV,CAAC,CACH,CACP,cAEDN,IAAA,CAACpC,IAAI,EAAC0L,IAAI,MAACf,EAAE,CAAE,EAAG,CAAAjH,QAAA,cAChBtB,IAAA,CAACnC,IAAI,EACHoD,EAAE,CAAE,CACFC,MAAM,CAAE,MAAM,CACdiH,SAAS,CAAE,GACb,CAAE,CAAA7G,QAAA,cAEFpB,KAAA,CAACpC,WAAW,EAAAwD,QAAA,eACVtB,IAAA,CAACjC,UAAU,EAACoE,OAAO,CAAC,IAAI,CAAClB,EAAE,CAAE,CAAE4C,EAAE,CAAE,CAAC,CAAExB,UAAU,CAAE,GAAI,CAAE,CAAAf,QAAA,CAAC,mBAEzD,CAAY,CAAC,CACZZ,OAAO,cACNV,IAAA,CAACrC,GAAG,EAACsD,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,cAAc,CAAE,QAAQ,CAAEQ,CAAC,CAAE,CAAE,CAAE,CAAAX,QAAA,cAC3DtB,IAAA,CAAChC,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACJgI,UAAU,CAACwD,MAAM,CAAG,CAAC,cACvBxJ,IAAA,CAAC7B,IAAI,EAAC8C,EAAE,CAAE,CAAEgB,CAAC,CAAE,CAAE,CAAE,CAAAX,QAAA,CAChB0E,UAAU,CAACyD,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACL,GAAG,CAAC,CAACtE,QAAQ,CAAElE,KAAK,gBAC1CZ,IAAA,CAAC4E,YAAY,EAEXE,QAAQ,CAAEA,QAAS,CACnBlE,KAAK,CAAEA,KAAM,KAAA8B,MAAA,CAFLoC,QAAQ,CAAC4E,WAAW,MAAAhH,MAAA,CAAIoC,QAAQ,CAACE,MAAM,CAGhD,CACF,CAAC,CACE,CAAC,cAEPhF,IAAA,CAACrC,GAAG,EACFsD,EAAE,CAAE,CACF+H,SAAS,CAAE,QAAQ,CACnBW,EAAE,CAAE,CAAC,CACLlJ,KAAK,CAAE,gBACT,CAAE,CAAAa,QAAA,cAEFtB,IAAA,CAACjC,UAAU,EAAAuD,QAAA,CAAC,sBAAoB,CAAY,CAAC,CAC1C,CACN,EACU,CAAC,CACV,CAAC,CACH,CAAC,EACH,CAAC,EACF,CAAC,CACH,CAAC,CAEV,CAEA,cAAe,CAAA/B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}