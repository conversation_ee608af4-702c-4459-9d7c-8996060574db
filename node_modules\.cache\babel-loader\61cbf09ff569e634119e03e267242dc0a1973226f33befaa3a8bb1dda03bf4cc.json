{"ast": null, "code": "export { unstable_generateUtilityClass as generateUtilityClass } from '@mui/utils';", "map": {"version": 3, "names": ["unstable_generateUtilityClass", "generateUtilityClass"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/generateUtilityClass/index.js"], "sourcesContent": ["export { unstable_generateUtilityClass as generateUtilityClass } from '@mui/utils';"], "mappings": "AAAA,SAASA,6BAA6B,IAAIC,oBAAoB,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}