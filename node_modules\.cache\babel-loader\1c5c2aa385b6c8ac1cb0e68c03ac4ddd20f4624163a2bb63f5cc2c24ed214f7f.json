{"ast": null, "code": "export const defaultReduceAnimations = typeof navigator !== 'undefined' && /(android)/i.test(navigator.userAgent);", "map": {"version": 3, "names": ["defaultReduceAnimations", "navigator", "test", "userAgent"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/utils/defaultReduceAnimations.js"], "sourcesContent": ["export const defaultReduceAnimations = typeof navigator !== 'undefined' && /(android)/i.test(navigator.userAgent);"], "mappings": "AAAA,OAAO,MAAMA,uBAAuB,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAI,YAAY,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}