{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, CircularProgress, Alert, useTheme, List, ListItem, ListItemText, ListItemIcon, Divider, Paper, Chip, LinearProgress, useMediaQuery, Button } from '@mui/material';\nimport { Assignment as ComplaintsIcon, CheckCircle as ResolvedIcon, Pending as PendingIcon, Error as HighPriorityIcon, FiberManualRecord as StatusIcon, Schedule as TimeIcon, Speed as EfficiencyIcon, Timeline as TrendIcon, AccessTime as ResolutionIcon, Dashboard as DashboardIcon, BarChart as InsightsIcon } from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport axios from '../utils/axiosConfig';\nimport { format, formatDistanceToNow } from 'date-fns';\nimport { useSocket } from '../contexts/SocketContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MonthlyStatCard = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  title,\n  value,\n  icon,\n  color,\n  loading,\n  subtitle,\n  index\n}) => {\n  _s();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        height: 120,\n        background: 'rgba(255,255,255,0.15)',\n        backdropFilter: 'blur(10px)',\n        border: '1px solid rgba(255,255,255,0.2)',\n        color: 'white'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24,\n          sx: {\n            color: 'white'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        height: 120,\n        background: 'rgba(255,255,255,0.9)',\n        color: 'text.primary',\n        borderRadius: 2,\n        boxShadow: theme.shadows[1],\n        transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n        '&:hover': {\n          transform: 'translateY(-1px)',\n          boxShadow: theme.shadows[2]\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'space-between',\n          height: '100%',\n          p: 2,\n          '&:last-child': {\n            pb: 2\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"body2\" : \"body1\",\n              sx: {\n                opacity: 0.9,\n                fontWeight: 500,\n                fontSize: '0.875rem'\n              },\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                opacity: 0.7,\n                fontSize: '0.75rem',\n                display: 'block'\n              },\n              children: subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              opacity: 0.8,\n              fontSize: isMobile ? '1.2rem' : '1.5rem'\n            },\n            children: icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: isMobile ? \"h6\" : \"h5\",\n          sx: {\n            fontWeight: 700,\n            fontSize: isMobile ? '1.25rem' : '1.5rem',\n            textShadow: '0px 2px 4px rgba(0,0,0,0.3)'\n          },\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}, \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n  return [useTheme, useMediaQuery];\n})), \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c2 = MonthlyStatCard;\nconst StatCard = ({\n  title,\n  value,\n  icon,\n  color,\n  loading,\n  subtitle\n}) => {\n  _s2();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        height: '100%',\n        background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\n        color: 'white',\n        position: 'relative',\n        overflow: 'hidden',\n        borderRadius: 2,\n        boxShadow: theme.shadows[2],\n        transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n        '&:hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: theme.shadows[4]\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: isMobile ? 2 : 3,\n          height: '100%',\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'relative',\n            zIndex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              delay: 0.2,\n              type: \"spring\",\n              stiffness: 120\n            },\n            children: /*#__PURE__*/React.cloneElement(icon, {\n              sx: {\n                fontSize: isMobile ? 32 : 48,\n                opacity: 0.9,\n                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"h5\" : \"h4\",\n              component: \"div\",\n              sx: {\n                fontWeight: 700,\n                lineHeight: 1.2,\n                mb: 0.5,\n                textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\n              },\n              children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: isMobile ? 20 : 24,\n                color: \"inherit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this) : value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"body2\" : \"body1\",\n              sx: {\n                opacity: 0.9,\n                fontWeight: 500,\n                letterSpacing: '0.5px',\n                textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\n              },\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                opacity: 0.8,\n                fontWeight: 400,\n                textShadow: '0px 1px 2px rgba(0,0,0,0.2)',\n                display: 'block'\n              },\n              children: subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'absolute',\n            right: -20,\n            bottom: -20,\n            opacity: 0.15\n          },\n          children: /*#__PURE__*/React.cloneElement(icon, {\n            sx: {\n              fontSize: isMobile ? 100 : 140\n            }\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n\n// Memoized color functions to prevent recalculation\n_s2(StatCard, \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c3 = StatCard;\nconst getStatusColor = (status, theme) => {\n  const statusColors = {\n    'New': theme.palette.info.main,\n    'Assigned': theme.palette.warning.main,\n    'In Progress': theme.palette.warning.dark,\n    'Resolved': theme.palette.success.main,\n    'Rejected': theme.palette.error.main\n  };\n  return statusColors[status] || theme.palette.grey[500];\n};\nconst getPriorityColor = (priority, theme) => {\n  const priorityColors = {\n    'Low': theme.palette.success.main,\n    'Medium': theme.palette.warning.main,\n    'High': theme.palette.error.main,\n    'Critical': theme.palette.error.dark\n  };\n  return priorityColors[priority] || theme.palette.grey[500];\n};\n\n// Optimized timestamp formatting function - consistent with complaint details\nconst formatTimestamp = timestamp => {\n  if (!timestamp) return 'N/A';\n  try {\n    const date = new Date(timestamp);\n    if (isNaN(date.getTime())) {\n      console.warn('Invalid timestamp:', timestamp);\n      return 'N/A';\n    }\n\n    // Use the same format as complaint details - PPpp gives \"Jun 9, 2025, 11:27:36 AM\"\n    return format(date, 'PPpp');\n  } catch (error) {\n    console.error('Error formatting timestamp:', error);\n    return 'Invalid date';\n  }\n};\nconst ActivityItem = /*#__PURE__*/_s3(/*#__PURE__*/React.memo(_c4 = _s3(({\n  activity,\n  index\n}) => {\n  _s3();\n  const theme = useTheme();\n\n  // Memoize colors\n  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);\n  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(ListItem, {\n      sx: {\n        bgcolor: 'background.paper',\n        borderRadius: 2,\n        mb: 1,\n        boxShadow: 1,\n        '&:hover': {\n          bgcolor: 'action.hover',\n          transform: 'translateX(4px)',\n          transition: 'transform 0.15s ease' // Faster transition\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(StatusIcon, {\n          sx: {\n            color: statusColor\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            mb: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 500,\n              flex: 1\n            },\n            children: [\"#\", activity.ComplaintNumber, \" - \", activity.description]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), activity.Priority && /*#__PURE__*/_jsxDEV(Chip, {\n            label: activity.Priority,\n            size: \"small\",\n            sx: {\n              bgcolor: `${priorityColor}15`,\n              color: priorityColor,\n              fontWeight: 500,\n              fontSize: '0.7rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this),\n        secondary: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 0.5\n          },\n          children: [activity.activityDetails && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 0.5\n            },\n            children: activity.activityDetails\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              label: activity.Status,\n              size: \"small\",\n              sx: {\n                bgcolor: `${statusColor}15`,\n                color: statusColor,\n                fontWeight: 500\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), activity.Category && /*#__PURE__*/_jsxDEV(Chip, {\n              label: activity.Category,\n              size: \"small\",\n              variant: \"outlined\",\n              sx: {\n                fontSize: '0.7rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 300,\n    columnNumber: 5\n  }, this);\n}, \"o0BaSXnzBaXh/W+F64SAqGC2Z0M=\", false, function () {\n  return [useTheme];\n})), \"o0BaSXnzBaXh/W+F64SAqGC2Z0M=\", false, function () {\n  return [useTheme];\n});\n_c5 = ActivityItem;\nfunction Dashboard() {\n  _s4();\n  const [stats, setStats] = useState(null);\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const {\n    socket\n  } = useSocket();\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n\n      // Fetch data with optimized timeout and caching\n      const [statsResponse, activitiesResponse] = await Promise.all([axios.get('/api/dashboard/stats', {\n        timeout: 10000,\n        // 10 second timeout\n        headers: {\n          'Cache-Control': 'max-age=60' // Cache for 1 minute\n        }\n      }), axios.get('/api/dashboard/recent-activities', {\n        timeout: 10000,\n        // 10 second timeout\n        headers: {\n          'Cache-Control': 'no-cache' // Always fetch fresh data for activities\n        }\n      })]);\n      setStats(statsResponse.data);\n      setActivities(activitiesResponse.data);\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching dashboard data:', err);\n      setError('Failed to load dashboard data. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchDashboardData();\n\n    // Set up auto-refresh every 30 seconds\n    const interval = setInterval(fetchDashboardData, 30000);\n\n    // Listen for real-time updates via Socket.IO\n    if (socket) {\n      // Listen for status updates to refresh dashboard\n      socket.on('status_updated', () => {\n        console.log('Status update received, refreshing dashboard...');\n        fetchDashboardData();\n      });\n\n      // Listen for new complaints to refresh dashboard\n      socket.on('complaint_created', () => {\n        console.log('New complaint received, refreshing dashboard...');\n        fetchDashboardData();\n      });\n    }\n    return () => {\n      clearInterval(interval);\n      if (socket) {\n        socket.off('status_updated');\n        socket.off('complaint_created');\n      }\n    };\n  }, [fetchDashboardData, socket]);\n  const statCards = useMemo(() => [{\n    title: 'Total Complaints',\n    value: (stats === null || stats === void 0 ? void 0 : stats.totalComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(ComplaintsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 13\n    }, this),\n    color: 'primary'\n  }, {\n    title: 'Resolved',\n    value: (stats === null || stats === void 0 ? void 0 : stats.resolvedComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(ResolvedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 13\n    }, this),\n    color: 'success'\n  }, {\n    title: 'Pending',\n    value: (stats === null || stats === void 0 ? void 0 : stats.pendingComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(PendingIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 13\n    }, this),\n    color: 'warning'\n  }, {\n    title: 'High Priority',\n    value: (stats === null || stats === void 0 ? void 0 : stats.highPriorityComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(HighPriorityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 13\n    }, this),\n    color: 'error'\n  }], [stats]);\n  const monthlyStatCards = useMemo(() => {\n    if (!(stats !== null && stats !== void 0 && stats.monthlyStats)) return [];\n    return [{\n      title: 'This Month',\n      value: stats.monthlyStats.totalMonthlyComplaints || 0,\n      icon: /*#__PURE__*/_jsxDEV(ComplaintsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 15\n      }, this),\n      color: 'info',\n      subtitle: 'New complaints'\n    }, {\n      title: 'Resolution Rate',\n      value: `${stats.monthlyStats.resolutionRate || 0}%`,\n      icon: /*#__PURE__*/_jsxDEV(ResolvedIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 15\n      }, this),\n      color: 'success',\n      subtitle: 'Monthly average'\n    }, {\n      title: 'Response Time',\n      value: `${stats.monthlyStats.responseEfficiency || 0}%`,\n      icon: /*#__PURE__*/_jsxDEV(TimeIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 15\n      }, this),\n      color: 'warning',\n      subtitle: 'New tickets in 24h'\n    }, {\n      title: 'Avg Resolution',\n      value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,\n      icon: /*#__PURE__*/_jsxDEV(ResolutionIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 15\n      }, this),\n      color: 'primary',\n      subtitle: 'Hours to resolve'\n    }];\n  }, [stats]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      position: 'relative',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'url(\"data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")',\n        opacity: 0.3,\n        zIndex: 0\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        zIndex: 1,\n        p: {\n          xs: 2,\n          sm: 3\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          sx: {\n            mb: 1,\n            fontWeight: 700,\n            color: 'white',\n            textAlign: 'center',\n            fontSize: {\n              xs: '2rem',\n              sm: '2.5rem',\n              md: '3rem'\n            },\n            textShadow: '0px 4px 8px rgba(0,0,0,0.3)',\n            letterSpacing: '-0.02em'\n          },\n          children: \"\\uD83D\\uDCCA Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 4,\n            color: 'rgba(255,255,255,0.9)',\n            textAlign: 'center',\n            fontWeight: 400,\n            fontSize: {\n              xs: '1rem',\n              sm: '1.25rem'\n            }\n          },\n          children: \"Internal Complaints Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [statCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            ...card,\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this)\n        }, card.title, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 11\n        }, this)), monthlyStatCards.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 3,\n                  fontWeight: 600,\n                  color: 'white'\n                },\n                children: \"\\uD83D\\uDCCA Monthly Performance Insights\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 3,\n                children: monthlyStatCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 6,\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(MonthlyStatCard, {\n                    ...card,\n                    loading: loading,\n                    index: index\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 23\n                  }, this)\n                }, card.title, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              height: '100%',\n              minHeight: 400\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 600\n                },\n                children: \"Recent Activities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 15\n              }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'center',\n                  p: 4\n                },\n                children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 17\n              }, this) : activities.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n                sx: {\n                  p: 0\n                },\n                children: activities.slice(0, 8).map((activity, index) => /*#__PURE__*/_jsxDEV(ActivityItem, {\n                  activity: activity,\n                  index: index\n                }, `${activity.ComplaintId}-${activity.Status}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 17\n              }, this) : /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  py: 4,\n                  color: 'text.secondary'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  children: \"No recent activities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 505,\n    columnNumber: 5\n  }, this);\n}\n_s4(Dashboard, \"+5xHOEl+CAhlePZKwdRbLSaYLPs=\", false, function () {\n  return [useTheme, useMediaQuery, useSocket];\n});\n_c6 = Dashboard;\nexport default Dashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"MonthlyStatCard$React.memo\");\n$RefreshReg$(_c2, \"MonthlyStatCard\");\n$RefreshReg$(_c3, \"StatCard\");\n$RefreshReg$(_c4, \"ActivityItem$React.memo\");\n$RefreshReg$(_c5, \"ActivityItem\");\n$RefreshReg$(_c6, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "CircularProgress", "<PERSON><PERSON>", "useTheme", "List", "ListItem", "ListItemText", "ListItemIcon", "Divider", "Paper", "Chip", "LinearProgress", "useMediaQuery", "<PERSON><PERSON>", "Assignment", "ComplaintsIcon", "CheckCircle", "ResolvedIcon", "Pending", "PendingIcon", "Error", "HighPriorityIcon", "FiberManualRecord", "StatusIcon", "Schedule", "TimeIcon", "Speed", "EfficiencyIcon", "Timeline", "TrendIcon", "AccessTime", "ResolutionIcon", "Dashboard", "DashboardIcon", "<PERSON><PERSON><PERSON>", "InsightsIcon", "motion", "AnimatePresence", "axios", "format", "formatDistanceToNow", "useSocket", "jsxDEV", "_jsxDEV", "MonthlyStatCard", "_s", "memo", "_c", "title", "value", "icon", "color", "loading", "subtitle", "index", "theme", "isMobile", "breakpoints", "down", "sx", "height", "background", "<PERSON><PERSON>ilter", "border", "children", "display", "alignItems", "justifyContent", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderRadius", "boxShadow", "shadows", "transition", "transform", "flexDirection", "p", "pb", "variant", "opacity", "fontWeight", "fontSize", "textShadow", "_c2", "StatCard", "_s2", "palette", "main", "dark", "position", "overflow", "zIndex", "gap", "div", "initial", "scale", "animate", "delay", "type", "stiffness", "cloneElement", "filter", "component", "lineHeight", "mb", "letterSpacing", "right", "bottom", "_c3", "getStatusColor", "status", "statusColors", "info", "warning", "success", "error", "grey", "getPriorityColor", "priority", "priorityColors", "formatTimestamp", "timestamp", "date", "Date", "isNaN", "getTime", "console", "warn", "ActivityItem", "_s3", "_c4", "activity", "statusColor", "Status", "priorityColor", "Priority", "bgcolor", "primary", "flex", "ComplaintNumber", "description", "label", "secondary", "mt", "activityDetails", "flexWrap", "Category", "_c5", "_s4", "stats", "setStats", "activities", "setActivities", "setLoading", "setError", "socket", "fetchDashboardData", "statsResponse", "activitiesResponse", "Promise", "all", "get", "timeout", "headers", "data", "err", "interval", "setInterval", "on", "log", "clearInterval", "off", "statCards", "totalComplaints", "resolvedComplaints", "pendingComplaints", "highPriorityComplaints", "monthlyStatCards", "monthlyStats", "totalMonthlyComplaints", "resolutionRate", "responseEfficiency", "Math", "round", "avgResolutionHours", "minHeight", "content", "top", "left", "xs", "sm", "textAlign", "md", "severity", "container", "spacing", "map", "card", "item", "length", "slice", "ComplaintId", "py", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport {\r\n  Box,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  CircularProgress,\r\n  Alert,\r\n  useTheme,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Divider,\r\n  Paper,\r\n  Chip,\r\n  LinearProgress,\r\n  useMediaQuery,\r\n  Button,\r\n} from '@mui/material';\r\nimport {\r\n  Assignment as ComplaintsIcon,\r\n  CheckCircle as ResolvedIcon,\r\n  Pending as PendingIcon,\r\n  Error as HighPriorityIcon,\r\n  FiberManualRecord as StatusIcon,\r\n  Schedule as TimeIcon,\r\n  Speed as EfficiencyIcon,\r\n  Timeline as TrendIcon,\r\n  AccessTime as ResolutionIcon,\r\n  Dashboard as DashboardIcon,\r\n  BarChart as InsightsIcon,\r\n} from '@mui/icons-material';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport axios from '../utils/axiosConfig';\r\nimport { format, formatDistanceToNow } from 'date-fns';\r\nimport { useSocket } from '../contexts/SocketContext';\r\n\r\nconst MonthlyStatCard = React.memo(({ title, value, icon, color, loading, subtitle, index }) => {\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\n  if (loading) {\r\n    return (\r\n      <Card sx={{\r\n        height: 120,\r\n        background: 'rgba(255,255,255,0.15)',\r\n        backdropFilter: 'blur(10px)',\r\n        border: '1px solid rgba(255,255,255,0.2)',\r\n        color: 'white'\r\n      }}>\r\n        <CardContent sx={{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          height: '100%'\r\n        }}>\r\n          <CircularProgress size={24} sx={{ color: 'white' }} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <Card\r\n        sx={{\r\n          height: 120,\r\n          background: 'rgba(255,255,255,0.9)',\r\n          color: 'text.primary',\r\n          borderRadius: 2,\r\n          boxShadow: theme.shadows[1],\r\n          transition: 'transform 0.2s ease, box-shadow 0.2s ease',\r\n          '&:hover': {\r\n            transform: 'translateY(-1px)',\r\n            boxShadow: theme.shadows[2],\r\n          },\r\n        }}\r\n      >\r\n        <CardContent sx={{\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'space-between',\r\n          height: '100%',\r\n          p: 2,\r\n          '&:last-child': { pb: 2 }\r\n        }}>\r\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\r\n            <Box>\r\n              <Typography\r\n                variant={isMobile ? \"body2\" : \"body1\"}\r\n                sx={{\r\n                  opacity: 0.9,\r\n                  fontWeight: 500,\r\n                  fontSize: '0.875rem'\r\n                }}\r\n              >\r\n                {title}\r\n              </Typography>\r\n              {subtitle && (\r\n                <Typography\r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    opacity: 0.7,\r\n                    fontSize: '0.75rem',\r\n                    display: 'block'\r\n                  }}\r\n                >\r\n                  {subtitle}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n            <Box sx={{\r\n              opacity: 0.8,\r\n              fontSize: isMobile ? '1.2rem' : '1.5rem'\r\n            }}>\r\n              {icon}\r\n            </Box>\r\n          </Box>\r\n\r\n          <Typography\r\n            variant={isMobile ? \"h6\" : \"h5\"}\r\n            sx={{\r\n              fontWeight: 700,\r\n              fontSize: isMobile ? '1.25rem' : '1.5rem',\r\n              textShadow: '0px 2px 4px rgba(0,0,0,0.3)'\r\n            }}\r\n          >\r\n            {value}\r\n          </Typography>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n});\r\n\r\nconst StatCard = ({ title, value, icon, color, loading, subtitle }) => {\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\n  return (\r\n    <div>\r\n      <Card\r\n        sx={{\r\n          height: '100%',\r\n          background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\r\n          color: 'white',\r\n          position: 'relative',\r\n          overflow: 'hidden',\r\n          borderRadius: 2,\r\n          boxShadow: theme.shadows[2],\r\n          transition: 'transform 0.2s ease, box-shadow 0.2s ease',\r\n          '&:hover': {\r\n            transform: 'translateY(-2px)',\r\n            boxShadow: theme.shadows[4],\r\n          },\r\n        }}\r\n      >\r\n        <CardContent sx={{ \r\n          p: isMobile ? 2 : 3,\r\n          height: '100%',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'space-between'\r\n        }}>\r\n          <Box sx={{ \r\n            position: 'relative', \r\n            zIndex: 1,\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 2\r\n          }}>\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 120 }}\r\n            >\r\n              {React.cloneElement(icon, { \r\n                sx: { \r\n                  fontSize: isMobile ? 32 : 48,\r\n                  opacity: 0.9,\r\n                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\r\n                } \r\n              })}\r\n            </motion.div>\r\n            <Box>\r\n              <Typography \r\n                variant={isMobile ? \"h5\" : \"h4\"} \r\n                component=\"div\" \r\n                sx={{ \r\n                  fontWeight: 700,\r\n                  lineHeight: 1.2,\r\n                  mb: 0.5,\r\n                  textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {loading ? (\r\n                  <CircularProgress size={isMobile ? 20 : 24} color=\"inherit\" />\r\n                ) : (\r\n                  value\r\n                )}\r\n              </Typography>\r\n              <Typography\r\n                variant={isMobile ? \"body2\" : \"body1\"}\r\n                sx={{\r\n                  opacity: 0.9,\r\n                  fontWeight: 500,\r\n                  letterSpacing: '0.5px',\r\n                  textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {title}\r\n              </Typography>\r\n              {subtitle && (\r\n                <Typography\r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    opacity: 0.8,\r\n                    fontWeight: 400,\r\n                    textShadow: '0px 1px 2px rgba(0,0,0,0.2)',\r\n                    display: 'block'\r\n                  }}\r\n                >\r\n                  {subtitle}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n          </Box>\r\n          <Box\r\n            sx={{\r\n              position: 'absolute',\r\n              right: -20,\r\n              bottom: -20,\r\n              opacity: 0.15,\r\n            }}\r\n          >\r\n            {React.cloneElement(icon, {\r\n              sx: { fontSize: isMobile ? 100 : 140 }\r\n            })}\r\n          </Box>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Memoized color functions to prevent recalculation\r\nconst getStatusColor = (status, theme) => {\r\n  const statusColors = {\r\n    'New': theme.palette.info.main,\r\n    'Assigned': theme.palette.warning.main,\r\n    'In Progress': theme.palette.warning.dark,\r\n    'Resolved': theme.palette.success.main,\r\n    'Rejected': theme.palette.error.main,\r\n  };\r\n  return statusColors[status] || theme.palette.grey[500];\r\n};\r\n\r\nconst getPriorityColor = (priority, theme) => {\r\n  const priorityColors = {\r\n    'Low': theme.palette.success.main,\r\n    'Medium': theme.palette.warning.main,\r\n    'High': theme.palette.error.main,\r\n    'Critical': theme.palette.error.dark,\r\n  };\r\n  return priorityColors[priority] || theme.palette.grey[500];\r\n};\r\n\r\n// Optimized timestamp formatting function - consistent with complaint details\r\nconst formatTimestamp = (timestamp) => {\r\n  if (!timestamp) return 'N/A';\r\n\r\n  try {\r\n    const date = new Date(timestamp);\r\n\r\n    if (isNaN(date.getTime())) {\r\n      console.warn('Invalid timestamp:', timestamp);\r\n      return 'N/A';\r\n    }\r\n\r\n    // Use the same format as complaint details - PPpp gives \"Jun 9, 2025, 11:27:36 AM\"\r\n    return format(date, 'PPpp');\r\n  } catch (error) {\r\n    console.error('Error formatting timestamp:', error);\r\n    return 'Invalid date';\r\n  }\r\n};\r\n\r\nconst ActivityItem = React.memo(({ activity, index }) => {\r\n  const theme = useTheme();\r\n\r\n\r\n\r\n  // Memoize colors\r\n  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);\r\n  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);\r\n\r\n  return (\r\n    <div>\r\n      <ListItem\r\n        sx={{\r\n          bgcolor: 'background.paper',\r\n          borderRadius: 2,\r\n          mb: 1,\r\n          boxShadow: 1,\r\n          '&:hover': {\r\n            bgcolor: 'action.hover',\r\n            transform: 'translateX(4px)',\r\n            transition: 'transform 0.15s ease', // Faster transition\r\n          },\r\n        }}\r\n      >\r\n        <ListItemIcon>\r\n          <StatusIcon sx={{ color: statusColor }} />\r\n        </ListItemIcon>\r\n        <ListItemText\r\n          primary={\r\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>\r\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, flex: 1 }}>\r\n                #{activity.ComplaintNumber} - {activity.description}\r\n              </Typography>\r\n              {activity.Priority && (\r\n                <Chip\r\n                  label={activity.Priority}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${priorityColor}15`,\r\n                    color: priorityColor,\r\n                    fontWeight: 500,\r\n                    fontSize: '0.7rem'\r\n                  }}\r\n                />\r\n              )}\r\n            </Box>\r\n          }\r\n          secondary={\r\n            <Box sx={{ mt: 0.5 }}>\r\n              {activity.activityDetails && (\r\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 0.5 }}>\r\n                  {activity.activityDetails}\r\n                </Typography>\r\n              )}\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>\r\n                <Chip\r\n                  label={activity.Status}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${statusColor}15`,\r\n                    color: statusColor,\r\n                    fontWeight: 500\r\n                  }}\r\n                />\r\n                {activity.Category && (\r\n                  <Chip\r\n                    label={activity.Category}\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ fontSize: '0.7rem' }}\r\n                  />\r\n                )}\r\n\r\n              </Box>\r\n            </Box>\r\n          }\r\n        />\r\n      </ListItem>\r\n    </div>\r\n  );\r\n});\r\n\r\nfunction Dashboard() {\r\n  const [stats, setStats] = useState(null);\r\n  const [activities, setActivities] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n  const { socket } = useSocket();\r\n\r\n  const fetchDashboardData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Fetch data with optimized timeout and caching\r\n      const [statsResponse, activitiesResponse] = await Promise.all([\r\n        axios.get('/api/dashboard/stats', {\r\n          timeout: 10000, // 10 second timeout\r\n          headers: {\r\n            'Cache-Control': 'max-age=60' // Cache for 1 minute\r\n          }\r\n        }),\r\n        axios.get('/api/dashboard/recent-activities', {\r\n          timeout: 10000, // 10 second timeout\r\n          headers: {\r\n            'Cache-Control': 'no-cache' // Always fetch fresh data for activities\r\n          }\r\n        })\r\n      ]);\r\n\r\n      setStats(statsResponse.data);\r\n      setActivities(activitiesResponse.data);\r\n      setError(null);\r\n    } catch (err) {\r\n      console.error('Error fetching dashboard data:', err);\r\n      setError('Failed to load dashboard data. Please try again later.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n\r\n    // Set up auto-refresh every 30 seconds\r\n    const interval = setInterval(fetchDashboardData, 30000);\r\n\r\n    // Listen for real-time updates via Socket.IO\r\n    if (socket) {\r\n      // Listen for status updates to refresh dashboard\r\n      socket.on('status_updated', () => {\r\n        console.log('Status update received, refreshing dashboard...');\r\n        fetchDashboardData();\r\n      });\r\n\r\n      // Listen for new complaints to refresh dashboard\r\n      socket.on('complaint_created', () => {\r\n        console.log('New complaint received, refreshing dashboard...');\r\n        fetchDashboardData();\r\n      });\r\n    }\r\n\r\n    return () => {\r\n      clearInterval(interval);\r\n      if (socket) {\r\n        socket.off('status_updated');\r\n        socket.off('complaint_created');\r\n      }\r\n    };\r\n  }, [fetchDashboardData, socket]);\r\n\r\n  const statCards = useMemo(() => [\r\n    {\r\n      title: 'Total Complaints',\r\n      value: stats?.totalComplaints || 0,\r\n      icon: <ComplaintsIcon />,\r\n      color: 'primary'\r\n    },\r\n    {\r\n      title: 'Resolved',\r\n      value: stats?.resolvedComplaints || 0,\r\n      icon: <ResolvedIcon />,\r\n      color: 'success'\r\n    },\r\n    {\r\n      title: 'Pending',\r\n      value: stats?.pendingComplaints || 0,\r\n      icon: <PendingIcon />,\r\n      color: 'warning'\r\n    },\r\n    {\r\n      title: 'High Priority',\r\n      value: stats?.highPriorityComplaints || 0,\r\n      icon: <HighPriorityIcon />,\r\n      color: 'error'\r\n    }\r\n  ], [stats]);\r\n\r\n  const monthlyStatCards = useMemo(() => {\r\n    if (!stats?.monthlyStats) return [];\r\n\r\n    return [\r\n      {\r\n        title: 'This Month',\r\n        value: stats.monthlyStats.totalMonthlyComplaints || 0,\r\n        icon: <ComplaintsIcon />,\r\n        color: 'info',\r\n        subtitle: 'New complaints'\r\n      },\r\n      {\r\n        title: 'Resolution Rate',\r\n        value: `${stats.monthlyStats.resolutionRate || 0}%`,\r\n        icon: <ResolvedIcon />,\r\n        color: 'success',\r\n        subtitle: 'Monthly average'\r\n      },\r\n      {\r\n        title: 'Response Time',\r\n        value: `${stats.monthlyStats.responseEfficiency || 0}%`,\r\n        icon: <TimeIcon />,\r\n        color: 'warning',\r\n        subtitle: 'New tickets in 24h'\r\n      },\r\n      {\r\n        title: 'Avg Resolution',\r\n        value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,\r\n        icon: <ResolutionIcon />,\r\n        color: 'primary',\r\n        subtitle: 'Hours to resolve'\r\n      }\r\n    ];\r\n  }, [stats]);\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        minHeight: '100vh',\r\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n        position: 'relative',\r\n        '&::before': {\r\n          content: '\"\"',\r\n          position: 'absolute',\r\n          top: 0,\r\n          left: 0,\r\n          right: 0,\r\n          bottom: 0,\r\n          background: 'url(\"data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")',\r\n          opacity: 0.3,\r\n          zIndex: 0\r\n        }\r\n      }}\r\n    >\r\n      <Box sx={{\r\n        position: 'relative',\r\n        zIndex: 1,\r\n        p: { xs: 2, sm: 3 }\r\n      }}>\r\n        <div>\r\n          <Typography\r\n            variant=\"h3\"\r\n            sx={{\r\n              mb: 1,\r\n              fontWeight: 700,\r\n              color: 'white',\r\n              textAlign: 'center',\r\n              fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },\r\n              textShadow: '0px 4px 8px rgba(0,0,0,0.3)',\r\n              letterSpacing: '-0.02em'\r\n            }}\r\n          >\r\n            📊 Dashboard\r\n          </Typography>\r\n          <Typography\r\n            variant=\"h6\"\r\n            sx={{\r\n              mb: 4,\r\n              color: 'rgba(255,255,255,0.9)',\r\n              textAlign: 'center',\r\n              fontWeight: 400,\r\n              fontSize: { xs: '1rem', sm: '1.25rem' }\r\n            }}\r\n          >\r\n            Internal Complaints Management System\r\n          </Typography>\r\n        </div>\r\n\r\n        {error && (\r\n          <Alert\r\n            severity=\"error\"\r\n            sx={{ mb: 3 }}\r\n          >\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n      <Grid container spacing={3}>\r\n        {statCards.map((card, index) => (\r\n          <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n            <StatCard {...card} loading={loading} />\r\n          </Grid>\r\n        ))}\r\n\r\n        {/* Monthly Statistics Section */}\r\n        {monthlyStatCards.length > 0 && (\r\n          <Grid item xs={12}>\r\n            <Card\r\n              sx={{\r\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n                color: 'white',\r\n              }}\r\n            >\r\n              <CardContent>\r\n                <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600, color: 'white' }}>\r\n                  📊 Monthly Performance Insights\r\n                </Typography>\r\n                <Grid container spacing={3}>\r\n                  {monthlyStatCards.map((card, index) => (\r\n                    <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n                      <MonthlyStatCard {...card} loading={loading} index={index} />\r\n                    </Grid>\r\n                  ))}\r\n                </Grid>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n        )}\r\n\r\n        <Grid item xs={12}>\r\n          <Card\r\n            sx={{\r\n              height: '100%',\r\n              minHeight: 400,\r\n            }}\r\n          >\r\n            <CardContent>\r\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\r\n                Recent Activities\r\n              </Typography>\r\n              {loading ? (\r\n                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>\r\n                  <CircularProgress />\r\n                </Box>\r\n              ) : activities.length > 0 ? (\r\n                <List sx={{ p: 0 }}>\r\n                  {activities.slice(0, 8).map((activity, index) => (\r\n                    <ActivityItem\r\n                      key={`${activity.ComplaintId}-${activity.Status}`}\r\n                      activity={activity}\r\n                      index={index}\r\n                    />\r\n                  ))}\r\n                </List>\r\n              ) : (\r\n                <Box \r\n                  sx={{ \r\n                    textAlign: 'center', \r\n                    py: 4,\r\n                    color: 'text.secondary'\r\n                  }}\r\n                >\r\n                  <Typography>No recent activities</Typography>\r\n                </Box>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n      </Box>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default Dashboard; "], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,cAAc,EACdC,aAAa,EACbC,MAAM,QACD,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,YAAY,EAC3BC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,gBAAgB,EACzBC,iBAAiB,IAAIC,UAAU,EAC/BC,QAAQ,IAAIC,QAAQ,EACpBC,KAAK,IAAIC,cAAc,EACvBC,QAAQ,IAAIC,SAAS,EACrBC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AACtD,SAASC,SAAS,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,eAAe,gBAAAC,EAAA,cAAGtD,KAAK,CAACuD,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,KAAK;EAAEC,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC,QAAQ;EAAEC;AAAM,CAAC,KAAK;EAAAT,EAAA;EAC9F,MAAMU,KAAK,GAAGpD,QAAQ,CAAC,CAAC;EACxB,MAAMqD,QAAQ,GAAG5C,aAAa,CAAC2C,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,IAAIN,OAAO,EAAE;IACX,oBACET,OAAA,CAAC7C,IAAI;MAAC6D,EAAE,EAAE;QACRC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,wBAAwB;QACpCC,cAAc,EAAE,YAAY;QAC5BC,MAAM,EAAE,iCAAiC;QACzCZ,KAAK,EAAE;MACT,CAAE;MAAAa,QAAA,eACArB,OAAA,CAAC5C,WAAW;QAAC4D,EAAE,EAAE;UACfM,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBP,MAAM,EAAE;QACV,CAAE;QAAAI,QAAA,eACArB,OAAA,CAAC1C,gBAAgB;UAACmE,IAAI,EAAE,EAAG;UAACT,EAAE,EAAE;YAAER,KAAK,EAAE;UAAQ;QAAE;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX;EAEA,oBACE7B,OAAA;IAAAqB,QAAA,eACErB,OAAA,CAAC7C,IAAI;MACH6D,EAAE,EAAE;QACFC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,uBAAuB;QACnCV,KAAK,EAAE,cAAc;QACrBsB,YAAY,EAAE,CAAC;QACfC,SAAS,EAAEnB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;QAC3BC,UAAU,EAAE,2CAA2C;QACvD,SAAS,EAAE;UACTC,SAAS,EAAE,kBAAkB;UAC7BH,SAAS,EAAEnB,KAAK,CAACoB,OAAO,CAAC,CAAC;QAC5B;MACF,CAAE;MAAAX,QAAA,eAEFrB,OAAA,CAAC5C,WAAW;QAAC4D,EAAE,EAAE;UACfM,OAAO,EAAE,MAAM;UACfa,aAAa,EAAE,QAAQ;UACvBX,cAAc,EAAE,eAAe;UAC/BP,MAAM,EAAE,MAAM;UACdmB,CAAC,EAAE,CAAC;UACJ,cAAc,EAAE;YAAEC,EAAE,EAAE;UAAE;QAC1B,CAAE;QAAAhB,QAAA,gBACArB,OAAA,CAAC/C,GAAG;UAAC+D,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE;UAAa,CAAE;UAAAF,QAAA,gBACtFrB,OAAA,CAAC/C,GAAG;YAAAoE,QAAA,gBACFrB,OAAA,CAAC3C,UAAU;cACTiF,OAAO,EAAEzB,QAAQ,GAAG,OAAO,GAAG,OAAQ;cACtCG,EAAE,EAAE;gBACFuB,OAAO,EAAE,GAAG;gBACZC,UAAU,EAAE,GAAG;gBACfC,QAAQ,EAAE;cACZ,CAAE;cAAApB,QAAA,EAEDhB;YAAK;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACZnB,QAAQ,iBACPV,OAAA,CAAC3C,UAAU;cACTiF,OAAO,EAAC,SAAS;cACjBtB,EAAE,EAAE;gBACFuB,OAAO,EAAE,GAAG;gBACZE,QAAQ,EAAE,SAAS;gBACnBnB,OAAO,EAAE;cACX,CAAE;cAAAD,QAAA,EAEDX;YAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN7B,OAAA,CAAC/C,GAAG;YAAC+D,EAAE,EAAE;cACPuB,OAAO,EAAE,GAAG;cACZE,QAAQ,EAAE5B,QAAQ,GAAG,QAAQ,GAAG;YAClC,CAAE;YAAAQ,QAAA,EACCd;UAAI;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7B,OAAA,CAAC3C,UAAU;UACTiF,OAAO,EAAEzB,QAAQ,GAAG,IAAI,GAAG,IAAK;UAChCG,EAAE,EAAE;YACFwB,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE5B,QAAQ,GAAG,SAAS,GAAG,QAAQ;YACzC6B,UAAU,EAAE;UACd,CAAE;UAAArB,QAAA,EAEDf;QAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;EAAA,QA/FerE,QAAQ,EACLS,aAAa;AAAA,EA8F/B,CAAC;EAAA,QA/FcT,QAAQ,EACLS,aAAa;AAAA,EA8F9B;AAAC0E,GAAA,GAhGG1C,eAAe;AAkGrB,MAAM2C,QAAQ,GAAGA,CAAC;EAAEvC,KAAK;EAAEC,KAAK;EAAEC,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAmC,GAAA;EACrE,MAAMjC,KAAK,GAAGpD,QAAQ,CAAC,CAAC;EACxB,MAAMqD,QAAQ,GAAG5C,aAAa,CAAC2C,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,oBACEf,OAAA;IAAAqB,QAAA,eACErB,OAAA,CAAC7C,IAAI;MACH6D,EAAE,EAAE;QACFC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,2BAA2BN,KAAK,CAACkC,OAAO,CAACtC,KAAK,CAAC,CAACuC,IAAI,QAAQnC,KAAK,CAACkC,OAAO,CAACtC,KAAK,CAAC,CAACwC,IAAI,QAAQ;QACzGxC,KAAK,EAAE,OAAO;QACdyC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClBpB,YAAY,EAAE,CAAC;QACfC,SAAS,EAAEnB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;QAC3BC,UAAU,EAAE,2CAA2C;QACvD,SAAS,EAAE;UACTC,SAAS,EAAE,kBAAkB;UAC7BH,SAAS,EAAEnB,KAAK,CAACoB,OAAO,CAAC,CAAC;QAC5B;MACF,CAAE;MAAAX,QAAA,eAEFrB,OAAA,CAAC5C,WAAW;QAAC4D,EAAE,EAAE;UACfoB,CAAC,EAAEvB,QAAQ,GAAG,CAAC,GAAG,CAAC;UACnBI,MAAM,EAAE,MAAM;UACdK,OAAO,EAAE,MAAM;UACfa,aAAa,EAAE,QAAQ;UACvBX,cAAc,EAAE;QAClB,CAAE;QAAAH,QAAA,gBACArB,OAAA,CAAC/C,GAAG;UAAC+D,EAAE,EAAE;YACPiC,QAAQ,EAAE,UAAU;YACpBE,MAAM,EAAE,CAAC;YACT7B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB6B,GAAG,EAAE;UACP,CAAE;UAAA/B,QAAA,gBACArB,OAAA,CAACP,MAAM,CAAC4D,GAAG;YACTC,OAAO,EAAE;cAAEC,KAAK,EAAE;YAAE,CAAE;YACtBC,OAAO,EAAE;cAAED,KAAK,EAAE;YAAE,CAAE;YACtBtB,UAAU,EAAE;cAAEwB,KAAK,EAAE,GAAG;cAAEC,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAAAtC,QAAA,eAE1DzE,KAAK,CAACgH,YAAY,CAACrD,IAAI,EAAE;cACxBS,EAAE,EAAE;gBACFyB,QAAQ,EAAE5B,QAAQ,GAAG,EAAE,GAAG,EAAE;gBAC5B0B,OAAO,EAAE,GAAG;gBACZsB,MAAM,EAAE;cACV;YACF,CAAC;UAAC;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACb7B,OAAA,CAAC/C,GAAG;YAAAoE,QAAA,gBACFrB,OAAA,CAAC3C,UAAU;cACTiF,OAAO,EAAEzB,QAAQ,GAAG,IAAI,GAAG,IAAK;cAChCiD,SAAS,EAAC,KAAK;cACf9C,EAAE,EAAE;gBACFwB,UAAU,EAAE,GAAG;gBACfuB,UAAU,EAAE,GAAG;gBACfC,EAAE,EAAE,GAAG;gBACPtB,UAAU,EAAE;cACd,CAAE;cAAArB,QAAA,EAEDZ,OAAO,gBACNT,OAAA,CAAC1C,gBAAgB;gBAACmE,IAAI,EAAEZ,QAAQ,GAAG,EAAE,GAAG,EAAG;gBAACL,KAAK,EAAC;cAAS;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GAE9DvB;YACD;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eACb7B,OAAA,CAAC3C,UAAU;cACTiF,OAAO,EAAEzB,QAAQ,GAAG,OAAO,GAAG,OAAQ;cACtCG,EAAE,EAAE;gBACFuB,OAAO,EAAE,GAAG;gBACZC,UAAU,EAAE,GAAG;gBACfyB,aAAa,EAAE,OAAO;gBACtBvB,UAAU,EAAE;cACd,CAAE;cAAArB,QAAA,EAEDhB;YAAK;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACZnB,QAAQ,iBACPV,OAAA,CAAC3C,UAAU;cACTiF,OAAO,EAAC,SAAS;cACjBtB,EAAE,EAAE;gBACFuB,OAAO,EAAE,GAAG;gBACZC,UAAU,EAAE,GAAG;gBACfE,UAAU,EAAE,6BAA6B;gBACzCpB,OAAO,EAAE;cACX,CAAE;cAAAD,QAAA,EAEDX;YAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7B,OAAA,CAAC/C,GAAG;UACF+D,EAAE,EAAE;YACFiC,QAAQ,EAAE,UAAU;YACpBiB,KAAK,EAAE,CAAC,EAAE;YACVC,MAAM,EAAE,CAAC,EAAE;YACX5B,OAAO,EAAE;UACX,CAAE;UAAAlB,QAAA,eAEDzE,KAAK,CAACgH,YAAY,CAACrD,IAAI,EAAE;YACxBS,EAAE,EAAE;cAAEyB,QAAQ,EAAE5B,QAAQ,GAAG,GAAG,GAAG;YAAI;UACvC,CAAC;QAAC;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;;AAED;AAAAgB,GAAA,CA9GMD,QAAQ;EAAA,QACEpF,QAAQ,EACLS,aAAa;AAAA;AAAAmG,GAAA,GAF1BxB,QAAQ;AA+Gd,MAAMyB,cAAc,GAAGA,CAACC,MAAM,EAAE1D,KAAK,KAAK;EACxC,MAAM2D,YAAY,GAAG;IACnB,KAAK,EAAE3D,KAAK,CAACkC,OAAO,CAAC0B,IAAI,CAACzB,IAAI;IAC9B,UAAU,EAAEnC,KAAK,CAACkC,OAAO,CAAC2B,OAAO,CAAC1B,IAAI;IACtC,aAAa,EAAEnC,KAAK,CAACkC,OAAO,CAAC2B,OAAO,CAACzB,IAAI;IACzC,UAAU,EAAEpC,KAAK,CAACkC,OAAO,CAAC4B,OAAO,CAAC3B,IAAI;IACtC,UAAU,EAAEnC,KAAK,CAACkC,OAAO,CAAC6B,KAAK,CAAC5B;EAClC,CAAC;EACD,OAAOwB,YAAY,CAACD,MAAM,CAAC,IAAI1D,KAAK,CAACkC,OAAO,CAAC8B,IAAI,CAAC,GAAG,CAAC;AACxD,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAACC,QAAQ,EAAElE,KAAK,KAAK;EAC5C,MAAMmE,cAAc,GAAG;IACrB,KAAK,EAAEnE,KAAK,CAACkC,OAAO,CAAC4B,OAAO,CAAC3B,IAAI;IACjC,QAAQ,EAAEnC,KAAK,CAACkC,OAAO,CAAC2B,OAAO,CAAC1B,IAAI;IACpC,MAAM,EAAEnC,KAAK,CAACkC,OAAO,CAAC6B,KAAK,CAAC5B,IAAI;IAChC,UAAU,EAAEnC,KAAK,CAACkC,OAAO,CAAC6B,KAAK,CAAC3B;EAClC,CAAC;EACD,OAAO+B,cAAc,CAACD,QAAQ,CAAC,IAAIlE,KAAK,CAACkC,OAAO,CAAC8B,IAAI,CAAC,GAAG,CAAC;AAC5D,CAAC;;AAED;AACA,MAAMI,eAAe,GAAIC,SAAS,IAAK;EACrC,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;EAE5B,IAAI;IACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAEhC,IAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MACzBC,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEN,SAAS,CAAC;MAC7C,OAAO,KAAK;IACd;;IAEA;IACA,OAAOrF,MAAM,CAACsF,IAAI,EAAE,MAAM,CAAC;EAC7B,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdW,OAAO,CAACX,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,cAAc;EACvB;AACF,CAAC;AAED,MAAMa,YAAY,gBAAAC,GAAA,cAAG7I,KAAK,CAACuD,IAAI,CAAAuF,GAAA,GAAAD,GAAA,CAAC,CAAC;EAAEE,QAAQ;EAAEhF;AAAM,CAAC,KAAK;EAAA8E,GAAA;EACvD,MAAM7E,KAAK,GAAGpD,QAAQ,CAAC,CAAC;;EAIxB;EACA,MAAMoI,WAAW,GAAG5I,OAAO,CAAC,MAAMqH,cAAc,CAACsB,QAAQ,CAACE,MAAM,EAAEjF,KAAK,CAAC,EAAE,CAAC+E,QAAQ,CAACE,MAAM,EAAEjF,KAAK,CAAC,CAAC;EACnG,MAAMkF,aAAa,GAAG9I,OAAO,CAAC,MAAM6H,gBAAgB,CAACc,QAAQ,CAACI,QAAQ,EAAEnF,KAAK,CAAC,EAAE,CAAC+E,QAAQ,CAACI,QAAQ,EAAEnF,KAAK,CAAC,CAAC;EAE3G,oBACEZ,OAAA;IAAAqB,QAAA,eACErB,OAAA,CAACtC,QAAQ;MACPsD,EAAE,EAAE;QACFgF,OAAO,EAAE,kBAAkB;QAC3BlE,YAAY,EAAE,CAAC;QACfkC,EAAE,EAAE,CAAC;QACLjC,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE;UACTiE,OAAO,EAAE,cAAc;UACvB9D,SAAS,EAAE,iBAAiB;UAC5BD,UAAU,EAAE,sBAAsB,CAAE;QACtC;MACF,CAAE;MAAAZ,QAAA,gBAEFrB,OAAA,CAACpC,YAAY;QAAAyD,QAAA,eACXrB,OAAA,CAACpB,UAAU;UAACoC,EAAE,EAAE;YAAER,KAAK,EAAEoF;UAAY;QAAE;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACf7B,OAAA,CAACrC,YAAY;QACXsI,OAAO,eACLjG,OAAA,CAAC/C,GAAG;UAAC+D,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE6B,GAAG,EAAE,CAAC;YAAEY,EAAE,EAAE;UAAI,CAAE;UAAA3C,QAAA,gBAClErB,OAAA,CAAC3C,UAAU;YAACiF,OAAO,EAAC,WAAW;YAACtB,EAAE,EAAE;cAAEwB,UAAU,EAAE,GAAG;cAAE0D,IAAI,EAAE;YAAE,CAAE;YAAA7E,QAAA,GAAC,GAC/D,EAACsE,QAAQ,CAACQ,eAAe,EAAC,KAAG,EAACR,QAAQ,CAACS,WAAW;UAAA;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EACZ8D,QAAQ,CAACI,QAAQ,iBAChB/F,OAAA,CAACjC,IAAI;YACHsI,KAAK,EAAEV,QAAQ,CAACI,QAAS;YACzBtE,IAAI,EAAC,OAAO;YACZT,EAAE,EAAE;cACFgF,OAAO,EAAE,GAAGF,aAAa,IAAI;cAC7BtF,KAAK,EAAEsF,aAAa;cACpBtD,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE;YACZ;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACDyE,SAAS,eACPtG,OAAA,CAAC/C,GAAG;UAAC+D,EAAE,EAAE;YAAEuF,EAAE,EAAE;UAAI,CAAE;UAAAlF,QAAA,GAClBsE,QAAQ,CAACa,eAAe,iBACvBxG,OAAA,CAAC3C,UAAU;YAACiF,OAAO,EAAC,OAAO;YAAC9B,KAAK,EAAC,gBAAgB;YAACQ,EAAE,EAAE;cAAEgD,EAAE,EAAE;YAAI,CAAE;YAAA3C,QAAA,EAChEsE,QAAQ,CAACa;UAAe;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACb,eACD7B,OAAA,CAAC/C,GAAG;YAAC+D,EAAE,EAAE;cAAEM,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAE6B,GAAG,EAAE,CAAC;cAAEqD,QAAQ,EAAE;YAAO,CAAE;YAAApF,QAAA,gBAC3ErB,OAAA,CAACjC,IAAI;cACHsI,KAAK,EAAEV,QAAQ,CAACE,MAAO;cACvBpE,IAAI,EAAC,OAAO;cACZT,EAAE,EAAE;gBACFgF,OAAO,EAAE,GAAGJ,WAAW,IAAI;gBAC3BpF,KAAK,EAAEoF,WAAW;gBAClBpD,UAAU,EAAE;cACd;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACD8D,QAAQ,CAACe,QAAQ,iBAChB1G,OAAA,CAACjC,IAAI;cACHsI,KAAK,EAAEV,QAAQ,CAACe,QAAS;cACzBjF,IAAI,EAAC,OAAO;cACZa,OAAO,EAAC,UAAU;cAClBtB,EAAE,EAAE;gBAAEyB,QAAQ,EAAE;cAAS;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;EAAA,QA/EerE,QAAQ;AAAA,EA+EvB,CAAC;EAAA,QA/EcA,QAAQ;AAAA,EA+EtB;AAACmJ,GAAA,GAhFGnB,YAAY;AAkFlB,SAASnG,SAASA,CAAA,EAAG;EAAAuH,GAAA;EACnB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjK,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkK,UAAU,EAAEC,aAAa,CAAC,GAAGnK,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4D,OAAO,EAAEwG,UAAU,CAAC,GAAGpK,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8H,KAAK,EAAEuC,QAAQ,CAAC,GAAGrK,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM+D,KAAK,GAAGpD,QAAQ,CAAC,CAAC;EACxB,MAAMqD,QAAQ,GAAG5C,aAAa,CAAC2C,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAM;IAAEoG;EAAO,CAAC,GAAGrH,SAAS,CAAC,CAAC;EAE9B,MAAMsH,kBAAkB,GAAGrK,WAAW,CAAC,YAAY;IACjD,IAAI;MACFkK,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACI,aAAa,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5D7H,KAAK,CAAC8H,GAAG,CAAC,sBAAsB,EAAE;QAChCC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;UACP,eAAe,EAAE,YAAY,CAAC;QAChC;MACF,CAAC,CAAC,EACFhI,KAAK,CAAC8H,GAAG,CAAC,kCAAkC,EAAE;QAC5CC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU,CAAC;QAC9B;MACF,CAAC,CAAC,CACH,CAAC;MAEFb,QAAQ,CAACO,aAAa,CAACO,IAAI,CAAC;MAC5BZ,aAAa,CAACM,kBAAkB,CAACM,IAAI,CAAC;MACtCV,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZvC,OAAO,CAACX,KAAK,CAAC,gCAAgC,EAAEkD,GAAG,CAAC;MACpDX,QAAQ,CAAC,wDAAwD,CAAC;IACpE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAENnK,SAAS,CAAC,MAAM;IACdsK,kBAAkB,CAAC,CAAC;;IAEpB;IACA,MAAMU,QAAQ,GAAGC,WAAW,CAACX,kBAAkB,EAAE,KAAK,CAAC;;IAEvD;IACA,IAAID,MAAM,EAAE;MACV;MACAA,MAAM,CAACa,EAAE,CAAC,gBAAgB,EAAE,MAAM;QAChC1C,OAAO,CAAC2C,GAAG,CAAC,iDAAiD,CAAC;QAC9Db,kBAAkB,CAAC,CAAC;MACtB,CAAC,CAAC;;MAEF;MACAD,MAAM,CAACa,EAAE,CAAC,mBAAmB,EAAE,MAAM;QACnC1C,OAAO,CAAC2C,GAAG,CAAC,iDAAiD,CAAC;QAC9Db,kBAAkB,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXc,aAAa,CAACJ,QAAQ,CAAC;MACvB,IAAIX,MAAM,EAAE;QACVA,MAAM,CAACgB,GAAG,CAAC,gBAAgB,CAAC;QAC5BhB,MAAM,CAACgB,GAAG,CAAC,mBAAmB,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACf,kBAAkB,EAAED,MAAM,CAAC,CAAC;EAEhC,MAAMiB,SAAS,GAAGpL,OAAO,CAAC,MAAM,CAC9B;IACEqD,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,CAAAuG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwB,eAAe,KAAI,CAAC;IAClC9H,IAAI,eAAEP,OAAA,CAAC5B,cAAc;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBrB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CAAAuG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyB,kBAAkB,KAAI,CAAC;IACrC/H,IAAI,eAAEP,OAAA,CAAC1B,YAAY;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBrB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAAuG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0B,iBAAiB,KAAI,CAAC;IACpChI,IAAI,eAAEP,OAAA,CAACxB,WAAW;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBrB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,CAAAuG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2B,sBAAsB,KAAI,CAAC;IACzCjI,IAAI,eAAEP,OAAA,CAACtB,gBAAgB;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BrB,KAAK,EAAE;EACT,CAAC,CACF,EAAE,CAACqG,KAAK,CAAC,CAAC;EAEX,MAAM4B,gBAAgB,GAAGzL,OAAO,CAAC,MAAM;IACrC,IAAI,EAAC6J,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE6B,YAAY,GAAE,OAAO,EAAE;IAEnC,OAAO,CACL;MACErI,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAEuG,KAAK,CAAC6B,YAAY,CAACC,sBAAsB,IAAI,CAAC;MACrDpI,IAAI,eAAEP,OAAA,CAAC5B,cAAc;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxBrB,KAAK,EAAE,MAAM;MACbE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,GAAGuG,KAAK,CAAC6B,YAAY,CAACE,cAAc,IAAI,CAAC,GAAG;MACnDrI,IAAI,eAAEP,OAAA,CAAC1B,YAAY;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBrB,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,GAAGuG,KAAK,CAAC6B,YAAY,CAACG,kBAAkB,IAAI,CAAC,GAAG;MACvDtI,IAAI,eAAEP,OAAA,CAAClB,QAAQ;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClBrB,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,GAAGwI,IAAI,CAACC,KAAK,CAAClC,KAAK,CAAC6B,YAAY,CAACM,kBAAkB,IAAI,CAAC,CAAC,GAAG;MACnEzI,IAAI,eAAEP,OAAA,CAACZ,cAAc;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxBrB,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,CACF;EACH,CAAC,EAAE,CAACmG,KAAK,CAAC,CAAC;EAEX,oBACE7G,OAAA,CAAC/C,GAAG;IACF+D,EAAE,EAAE;MACFiI,SAAS,EAAE,OAAO;MAClB/H,UAAU,EAAE,mDAAmD;MAC/D+B,QAAQ,EAAE,UAAU;MACpB,WAAW,EAAE;QACXiG,OAAO,EAAE,IAAI;QACbjG,QAAQ,EAAE,UAAU;QACpBkG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPlF,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTjD,UAAU,EAAE,mQAAmQ;QAC/QqB,OAAO,EAAE,GAAG;QACZY,MAAM,EAAE;MACV;IACF,CAAE;IAAA9B,QAAA,eAEFrB,OAAA,CAAC/C,GAAG;MAAC+D,EAAE,EAAE;QACPiC,QAAQ,EAAE,UAAU;QACpBE,MAAM,EAAE,CAAC;QACTf,CAAC,EAAE;UAAEiH,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE;MACpB,CAAE;MAAAjI,QAAA,gBACArB,OAAA;QAAAqB,QAAA,gBACErB,OAAA,CAAC3C,UAAU;UACTiF,OAAO,EAAC,IAAI;UACZtB,EAAE,EAAE;YACFgD,EAAE,EAAE,CAAC;YACLxB,UAAU,EAAE,GAAG;YACfhC,KAAK,EAAE,OAAO;YACd+I,SAAS,EAAE,QAAQ;YACnB9G,QAAQ,EAAE;cAAE4G,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE,QAAQ;cAAEE,EAAE,EAAE;YAAO,CAAC;YAClD9G,UAAU,EAAE,6BAA6B;YACzCuB,aAAa,EAAE;UACjB,CAAE;UAAA5C,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7B,OAAA,CAAC3C,UAAU;UACTiF,OAAO,EAAC,IAAI;UACZtB,EAAE,EAAE;YACFgD,EAAE,EAAE,CAAC;YACLxD,KAAK,EAAE,uBAAuB;YAC9B+I,SAAS,EAAE,QAAQ;YACnB/G,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE;cAAE4G,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAU;UACxC,CAAE;UAAAjI,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAEL8C,KAAK,iBACJ3E,OAAA,CAACzC,KAAK;QACJkM,QAAQ,EAAC,OAAO;QAChBzI,EAAE,EAAE;UAAEgD,EAAE,EAAE;QAAE,CAAE;QAAA3C,QAAA,EAEbsD;MAAK;QAAAjD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEH7B,OAAA,CAAC9C,IAAI;QAACwM,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAtI,QAAA,GACxB+G,SAAS,CAACwB,GAAG,CAAC,CAACC,IAAI,EAAElJ,KAAK,kBACzBX,OAAA,CAAC9C,IAAI;UAAC4M,IAAI;UAACT,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACE,EAAE,EAAE,CAAE;UAAAnI,QAAA,eAC9BrB,OAAA,CAAC4C,QAAQ;YAAA,GAAKiH,IAAI;YAAEpJ,OAAO,EAAEA;UAAQ;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC,GADJgI,IAAI,CAACxJ,KAAK;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE1C,CACP,CAAC,EAGD4G,gBAAgB,CAACsB,MAAM,GAAG,CAAC,iBAC1B/J,OAAA,CAAC9C,IAAI;UAAC4M,IAAI;UAACT,EAAE,EAAE,EAAG;UAAAhI,QAAA,eAChBrB,OAAA,CAAC7C,IAAI;YACH6D,EAAE,EAAE;cACFE,UAAU,EAAE,mDAAmD;cAC/DV,KAAK,EAAE;YACT,CAAE;YAAAa,QAAA,eAEFrB,OAAA,CAAC5C,WAAW;cAAAiE,QAAA,gBACVrB,OAAA,CAAC3C,UAAU;gBAACiF,OAAO,EAAC,IAAI;gBAACtB,EAAE,EAAE;kBAAEgD,EAAE,EAAE,CAAC;kBAAExB,UAAU,EAAE,GAAG;kBAAEhC,KAAK,EAAE;gBAAQ,CAAE;gBAAAa,QAAA,EAAC;cAEzE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7B,OAAA,CAAC9C,IAAI;gBAACwM,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAtI,QAAA,EACxBoH,gBAAgB,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAElJ,KAAK,kBAChCX,OAAA,CAAC9C,IAAI;kBAAC4M,IAAI;kBAACT,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAACE,EAAE,EAAE,CAAE;kBAAAnI,QAAA,eAC9BrB,OAAA,CAACC,eAAe;oBAAA,GAAK4J,IAAI;oBAAEpJ,OAAO,EAAEA,OAAQ;oBAACE,KAAK,EAAEA;kBAAM;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC,GADzBgI,IAAI,CAACxJ,KAAK;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE1C,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACP,eAED7B,OAAA,CAAC9C,IAAI;UAAC4M,IAAI;UAACT,EAAE,EAAE,EAAG;UAAAhI,QAAA,eAChBrB,OAAA,CAAC7C,IAAI;YACH6D,EAAE,EAAE;cACFC,MAAM,EAAE,MAAM;cACdgI,SAAS,EAAE;YACb,CAAE;YAAA5H,QAAA,eAEFrB,OAAA,CAAC5C,WAAW;cAAAiE,QAAA,gBACVrB,OAAA,CAAC3C,UAAU;gBAACiF,OAAO,EAAC,IAAI;gBAACtB,EAAE,EAAE;kBAAEgD,EAAE,EAAE,CAAC;kBAAExB,UAAU,EAAE;gBAAI,CAAE;gBAAAnB,QAAA,EAAC;cAEzD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZpB,OAAO,gBACNT,OAAA,CAAC/C,GAAG;gBAAC+D,EAAE,EAAE;kBAAEM,OAAO,EAAE,MAAM;kBAAEE,cAAc,EAAE,QAAQ;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAAAf,QAAA,eAC3DrB,OAAA,CAAC1C,gBAAgB;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,GACJkF,UAAU,CAACgD,MAAM,GAAG,CAAC,gBACvB/J,OAAA,CAACvC,IAAI;gBAACuD,EAAE,EAAE;kBAAEoB,CAAC,EAAE;gBAAE,CAAE;gBAAAf,QAAA,EAChB0F,UAAU,CAACiD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACJ,GAAG,CAAC,CAACjE,QAAQ,EAAEhF,KAAK,kBAC1CX,OAAA,CAACwF,YAAY;kBAEXG,QAAQ,EAAEA,QAAS;kBACnBhF,KAAK,EAAEA;gBAAM,GAFR,GAAGgF,QAAQ,CAACsE,WAAW,IAAItE,QAAQ,CAACE,MAAM,EAAE;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGlD,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,gBAEP7B,OAAA,CAAC/C,GAAG;gBACF+D,EAAE,EAAE;kBACFuI,SAAS,EAAE,QAAQ;kBACnBW,EAAE,EAAE,CAAC;kBACL1J,KAAK,EAAE;gBACT,CAAE;gBAAAa,QAAA,eAEFrB,OAAA,CAAC3C,UAAU;kBAAAgE,QAAA,EAAC;gBAAoB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC+E,GAAA,CA7QQvH,SAAS;EAAA,QAKF7B,QAAQ,EACLS,aAAa,EACX6B,SAAS;AAAA;AAAAqK,GAAA,GAPrB9K,SAAS;AA+QlB,eAAeA,SAAS;AAAC,IAAAe,EAAA,EAAAuC,GAAA,EAAAyB,GAAA,EAAAsB,GAAA,EAAAiB,GAAA,EAAAwD,GAAA;AAAAC,YAAA,CAAAhK,EAAA;AAAAgK,YAAA,CAAAzH,GAAA;AAAAyH,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAzD,GAAA;AAAAyD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}