{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\contexts\\\\SocketContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { io } from 'socket.io-client';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SocketContext = /*#__PURE__*/createContext(null);\nexport const useSocket = () => {\n  _s();\n  const context = useContext(SocketContext);\n  if (!context) {\n    throw new Error('useSocket must be used within a SocketProvider');\n  }\n  return context;\n};\n_s(useSocket, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const SocketProvider = ({\n  children\n}) => {\n  _s2();\n  const [socket, setSocket] = useState(null);\n  const [connected, setConnected] = useState(false);\n  const {\n    user,\n    refreshUserPermissions,\n    logout\n  } = useAuth();\n  useEffect(() => {\n    // Only connect if user is authenticated\n    if (user && user.empCode) {\n      console.log('Connecting to Socket.IO server...');\n\n      // Create socket connection to the backend server\n      const getSocketUrl = () => {\n        // In development, connect to the backend server directly\n        if (process.env.NODE_ENV === 'development') {\n          const currentHost = window.location.hostname;\n          if (currentHost === 'localhost' || currentHost === '127.0.0.1') {\n            return 'http://localhost:1976';\n          }\n          return `http://${currentHost}:1976`;\n        }\n        // In production, use the same origin\n        return window.location.origin;\n      };\n      const newSocket = io(getSocketUrl(), {\n        transports: ['websocket', 'polling']\n      });\n\n      // Handle connection\n      newSocket.on('connect', () => {\n        console.log('Connected to Socket.IO server');\n        setConnected(true);\n\n        // Authenticate with the server\n        const token = localStorage.getItem('token');\n        if (token) {\n          newSocket.emit('authenticate', token);\n        }\n      });\n\n      // Handle disconnection\n      newSocket.on('disconnect', () => {\n        console.log('Disconnected from Socket.IO server');\n        setConnected(false);\n      });\n\n      // Handle authentication errors\n      newSocket.on('auth_error', error => {\n        console.error('Socket authentication error:', error);\n      });\n\n      // Handle permission updates\n      newSocket.on('permission_updated', async data => {\n        console.log('Permission update received:', data);\n\n        // Automatically refresh user permissions\n        const success = await refreshUserPermissions();\n        if (success) {\n          console.log('User permissions refreshed automatically');\n\n          // Show a notification to the user\n          if (window.showPermissionUpdateNotification) {\n            window.showPermissionUpdateNotification('Your permissions have been updated automatically!');\n          }\n\n          // Force a page reload to update the UI immediately\n          setTimeout(() => {\n            window.location.reload();\n          }, 1000);\n        }\n      });\n\n      // Handle session termination\n      newSocket.on('session_terminated', data => {\n        console.log('Session terminated:', data);\n\n        // Show alert to user\n        alert(data.message || 'Your session has been terminated because you logged in from another device.');\n\n        // Logout the user\n        logout();\n\n        // Redirect to login page\n        window.location.href = '/login';\n      });\n      setSocket(newSocket);\n\n      // Cleanup on unmount\n      return () => {\n        console.log('Cleaning up socket connection');\n        newSocket.disconnect();\n      };\n    } else {\n      // Disconnect if user is not authenticated\n      if (socket) {\n        socket.disconnect();\n        setSocket(null);\n        setConnected(false);\n      }\n    }\n  }, [user, refreshUserPermissions]);\n  const value = {\n    socket,\n    connected\n  };\n  return /*#__PURE__*/_jsxDEV(SocketContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_s2(SocketProvider, \"MVVDsUAgTENrlLSmDYN2W58bSV0=\", false, function () {\n  return [useAuth];\n});\n_c = SocketProvider;\nexport default SocketContext;\nvar _c;\n$RefreshReg$(_c, \"SocketProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "io", "useAuth", "jsxDEV", "_jsxDEV", "SocketContext", "useSocket", "_s", "context", "Error", "SocketProvider", "children", "_s2", "socket", "setSocket", "connected", "setConnected", "user", "refreshUserPermissions", "logout", "empCode", "console", "log", "getSocketUrl", "process", "env", "NODE_ENV", "currentHost", "window", "location", "hostname", "origin", "newSocket", "transports", "on", "token", "localStorage", "getItem", "emit", "error", "data", "success", "showPermissionUpdateNotification", "setTimeout", "reload", "alert", "message", "href", "disconnect", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/contexts/SocketContext.js"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { io } from 'socket.io-client';\nimport { useAuth } from './AuthContext';\n\nconst SocketContext = createContext(null);\n\nexport const useSocket = () => {\n  const context = useContext(SocketContext);\n  if (!context) {\n    throw new Error('useSocket must be used within a SocketProvider');\n  }\n  return context;\n};\n\nexport const SocketProvider = ({ children }) => {\n  const [socket, setSocket] = useState(null);\n  const [connected, setConnected] = useState(false);\n  const { user, refreshUserPermissions, logout } = useAuth();\n\n  useEffect(() => {\n    // Only connect if user is authenticated\n    if (user && user.empCode) {\n      console.log('Connecting to Socket.IO server...');\n      \n      // Create socket connection to the backend server\n      const getSocketUrl = () => {\n        // In development, connect to the backend server directly\n        if (process.env.NODE_ENV === 'development') {\n          const currentHost = window.location.hostname;\n          if (currentHost === 'localhost' || currentHost === '127.0.0.1') {\n            return 'http://localhost:1976';\n          }\n          return `http://${currentHost}:1976`;\n        }\n        // In production, use the same origin\n        return window.location.origin;\n      };\n\n      const newSocket = io(getSocketUrl(), {\n        transports: ['websocket', 'polling']\n      });\n\n      // Handle connection\n      newSocket.on('connect', () => {\n        console.log('Connected to Socket.IO server');\n        setConnected(true);\n        \n        // Authenticate with the server\n        const token = localStorage.getItem('token');\n        if (token) {\n          newSocket.emit('authenticate', token);\n        }\n      });\n\n      // Handle disconnection\n      newSocket.on('disconnect', () => {\n        console.log('Disconnected from Socket.IO server');\n        setConnected(false);\n      });\n\n      // Handle authentication errors\n      newSocket.on('auth_error', (error) => {\n        console.error('Socket authentication error:', error);\n      });\n\n      // Handle permission updates\n      newSocket.on('permission_updated', async (data) => {\n        console.log('Permission update received:', data);\n\n        // Automatically refresh user permissions\n        const success = await refreshUserPermissions();\n        if (success) {\n          console.log('User permissions refreshed automatically');\n\n          // Show a notification to the user\n          if (window.showPermissionUpdateNotification) {\n            window.showPermissionUpdateNotification('Your permissions have been updated automatically!');\n          }\n\n          // Force a page reload to update the UI immediately\n          setTimeout(() => {\n            window.location.reload();\n          }, 1000);\n        }\n      });\n\n      // Handle session termination\n      newSocket.on('session_terminated', (data) => {\n        console.log('Session terminated:', data);\n\n        // Show alert to user\n        alert(data.message || 'Your session has been terminated because you logged in from another device.');\n\n        // Logout the user\n        logout();\n\n        // Redirect to login page\n        window.location.href = '/login';\n      });\n\n      setSocket(newSocket);\n\n      // Cleanup on unmount\n      return () => {\n        console.log('Cleaning up socket connection');\n        newSocket.disconnect();\n      };\n    } else {\n      // Disconnect if user is not authenticated\n      if (socket) {\n        socket.disconnect();\n        setSocket(null);\n        setConnected(false);\n      }\n    }\n  }, [user, refreshUserPermissions]);\n\n  const value = {\n    socket,\n    connected\n  };\n\n  return (\n    <SocketContext.Provider value={value}>\n      {children}\n    </SocketContext.Provider>\n  );\n};\n\nexport default SocketContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC7E,SAASC,EAAE,QAAQ,kBAAkB;AACrC,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,gBAAGR,aAAa,CAAC,IAAI,CAAC;AAEzC,OAAO,MAAMS,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,OAAO,GAAGV,UAAU,CAACO,aAAa,CAAC;EACzC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,SAAS;AAQtB,OAAO,MAAMI,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC9C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEiB,IAAI;IAAEC,sBAAsB;IAAEC;EAAO,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAE1DH,SAAS,CAAC,MAAM;IACd;IACA,IAAIkB,IAAI,IAAIA,IAAI,CAACG,OAAO,EAAE;MACxBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;MAEhD;MACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACzB;QACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;UAC1C,MAAMC,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;UAC5C,IAAIH,WAAW,KAAK,WAAW,IAAIA,WAAW,KAAK,WAAW,EAAE;YAC9D,OAAO,uBAAuB;UAChC;UACA,OAAO,UAAUA,WAAW,OAAO;QACrC;QACA;QACA,OAAOC,MAAM,CAACC,QAAQ,CAACE,MAAM;MAC/B,CAAC;MAED,MAAMC,SAAS,GAAG/B,EAAE,CAACsB,YAAY,CAAC,CAAC,EAAE;QACnCU,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS;MACrC,CAAC,CAAC;;MAEF;MACAD,SAAS,CAACE,EAAE,CAAC,SAAS,EAAE,MAAM;QAC5Bb,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5CN,YAAY,CAAC,IAAI,CAAC;;QAElB;QACA,MAAMmB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAIF,KAAK,EAAE;UACTH,SAAS,CAACM,IAAI,CAAC,cAAc,EAAEH,KAAK,CAAC;QACvC;MACF,CAAC,CAAC;;MAEF;MACAH,SAAS,CAACE,EAAE,CAAC,YAAY,EAAE,MAAM;QAC/Bb,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjDN,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC;;MAEF;MACAgB,SAAS,CAACE,EAAE,CAAC,YAAY,EAAGK,KAAK,IAAK;QACpClB,OAAO,CAACkB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;;MAEF;MACAP,SAAS,CAACE,EAAE,CAAC,oBAAoB,EAAE,MAAOM,IAAI,IAAK;QACjDnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEkB,IAAI,CAAC;;QAEhD;QACA,MAAMC,OAAO,GAAG,MAAMvB,sBAAsB,CAAC,CAAC;QAC9C,IAAIuB,OAAO,EAAE;UACXpB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;UAEvD;UACA,IAAIM,MAAM,CAACc,gCAAgC,EAAE;YAC3Cd,MAAM,CAACc,gCAAgC,CAAC,mDAAmD,CAAC;UAC9F;;UAEA;UACAC,UAAU,CAAC,MAAM;YACff,MAAM,CAACC,QAAQ,CAACe,MAAM,CAAC,CAAC;UAC1B,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,CAAC;;MAEF;MACAZ,SAAS,CAACE,EAAE,CAAC,oBAAoB,EAAGM,IAAI,IAAK;QAC3CnB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkB,IAAI,CAAC;;QAExC;QACAK,KAAK,CAACL,IAAI,CAACM,OAAO,IAAI,6EAA6E,CAAC;;QAEpG;QACA3B,MAAM,CAAC,CAAC;;QAER;QACAS,MAAM,CAACC,QAAQ,CAACkB,IAAI,GAAG,QAAQ;MACjC,CAAC,CAAC;MAEFjC,SAAS,CAACkB,SAAS,CAAC;;MAEpB;MACA,OAAO,MAAM;QACXX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5CU,SAAS,CAACgB,UAAU,CAAC,CAAC;MACxB,CAAC;IACH,CAAC,MAAM;MACL;MACA,IAAInC,MAAM,EAAE;QACVA,MAAM,CAACmC,UAAU,CAAC,CAAC;QACnBlC,SAAS,CAAC,IAAI,CAAC;QACfE,YAAY,CAAC,KAAK,CAAC;MACrB;IACF;EACF,CAAC,EAAE,CAACC,IAAI,EAAEC,sBAAsB,CAAC,CAAC;EAElC,MAAM+B,KAAK,GAAG;IACZpC,MAAM;IACNE;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,aAAa,CAAC6C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAtC,QAAA,EAClCA;EAAQ;IAAAwC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAE7B,CAAC;AAAC1C,GAAA,CAjHWF,cAAc;EAAA,QAGwBR,OAAO;AAAA;AAAAqD,EAAA,GAH7C7C,cAAc;AAmH3B,eAAeL,aAAa;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}