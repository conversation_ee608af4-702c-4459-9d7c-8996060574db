{"ast": null, "code": "'use client';\n\nexport { useMenuItem } from './useMenuItem';\nexport * from './useMenuItem.types';\nexport * from './useMenuItemContextStabilizer';", "map": {"version": 3, "names": ["useMenuItem"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/useMenuItem/index.js"], "sourcesContent": ["'use client';\n\nexport { useMenuItem } from './useMenuItem';\nexport * from './useMenuItem.types';\nexport * from './useMenuItemContextStabilizer';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,WAAW,QAAQ,eAAe;AAC3C,cAAc,qBAAqB;AACnC,cAAc,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}