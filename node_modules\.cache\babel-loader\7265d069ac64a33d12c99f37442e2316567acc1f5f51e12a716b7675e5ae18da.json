{"ast": null, "code": "import * as React from 'react';\nimport { useUtils } from './useUtils';\nimport { getMeridiem, convertToMeridiem } from '../utils/time-utils';\nexport function useNextMonthDisabled(month, _ref) {\n  let {\n    disableFuture,\n    maxDate\n  } = _ref;\n  const utils = useUtils();\n  return React.useMemo(() => {\n    const now = utils.date();\n    const lastEnabledMonth = utils.startOfMonth(disableFuture && utils.isBefore(now, maxDate) ? now : maxDate);\n    return !utils.isAfter(lastEnabledMonth, month);\n  }, [disableFuture, maxDate, month, utils]);\n}\nexport function usePreviousMonthDisabled(month, _ref2) {\n  let {\n    disablePast,\n    minDate\n  } = _ref2;\n  const utils = useUtils();\n  return React.useMemo(() => {\n    const now = utils.date();\n    const firstEnabledMonth = utils.startOfMonth(disablePast && utils.isAfter(now, minDate) ? now : minDate);\n    return !utils.isBefore(firstEnabledMonth, month);\n  }, [disablePast, minDate, month, utils]);\n}\nexport function useMeridiemMode(date, ampm, onChange) {\n  const utils = useUtils();\n  const meridiemMode = getMeridiem(date, utils);\n  const handleMeridiemChange = React.useCallback(mode => {\n    const timeWithMeridiem = date == null ? null : convertToMeridiem(date, mode, Boolean(ampm), utils);\n    onChange(timeWithMeridiem, 'partial');\n  }, [ampm, date, onChange, utils]);\n  return {\n    meridiemMode,\n    handleMeridiemChange\n  };\n}", "map": {"version": 3, "names": ["React", "useUtils", "getMeridiem", "convertToMeridiem", "useNextMonthDisabled", "month", "_ref", "disableFuture", "maxDate", "utils", "useMemo", "now", "date", "last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startOfMonth", "isBefore", "isAfter", "usePreviousMonthDisabled", "_ref2", "disablePast", "minDate", "firstEnabledMonth", "useMeridiemMode", "ampm", "onChange", "meridiemMode", "handleMeridiemChange", "useCallback", "mode", "timeWithMeridiem", "Boolean"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/hooks/date-helpers-hooks.js"], "sourcesContent": ["import * as React from 'react';\nimport { useUtils } from './useUtils';\nimport { getMeridiem, convertToMeridiem } from '../utils/time-utils';\nexport function useNextMonthDisabled(month, {\n  disableFuture,\n  maxDate\n}) {\n  const utils = useUtils();\n  return React.useMemo(() => {\n    const now = utils.date();\n    const lastEnabledMonth = utils.startOfMonth(disableFuture && utils.isBefore(now, maxDate) ? now : maxDate);\n    return !utils.isAfter(lastEnabledMonth, month);\n  }, [disableFuture, maxDate, month, utils]);\n}\nexport function usePreviousMonthDisabled(month, {\n  disablePast,\n  minDate\n}) {\n  const utils = useUtils();\n  return React.useMemo(() => {\n    const now = utils.date();\n    const firstEnabledMonth = utils.startOfMonth(disablePast && utils.isAfter(now, minDate) ? now : minDate);\n    return !utils.isBefore(firstEnabledMonth, month);\n  }, [disablePast, minDate, month, utils]);\n}\nexport function useMeridiemMode(date, ampm, onChange) {\n  const utils = useUtils();\n  const meridiemMode = getMeridiem(date, utils);\n  const handleMeridiemChange = React.useCallback(mode => {\n    const timeWithMeridiem = date == null ? null : convertToMeridiem(date, mode, Boolean(ampm), utils);\n    onChange(timeWithMeridiem, 'partial');\n  }, [ampm, date, onChange, utils]);\n  return {\n    meridiemMode,\n    handleMeridiemChange\n  };\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,qBAAqB;AACpE,OAAO,SAASC,oBAAoBA,CAACC,KAAK,EAAAC,IAAA,EAGvC;EAAA,IAHyC;IAC1CC,aAAa;IACbC;EACF,CAAC,GAAAF,IAAA;EACC,MAAMG,KAAK,GAAGR,QAAQ,CAAC,CAAC;EACxB,OAAOD,KAAK,CAACU,OAAO,CAAC,MAAM;IACzB,MAAMC,GAAG,GAAGF,KAAK,CAACG,IAAI,CAAC,CAAC;IACxB,MAAMC,gBAAgB,GAAGJ,KAAK,CAACK,YAAY,CAACP,aAAa,IAAIE,KAAK,CAACM,QAAQ,CAACJ,GAAG,EAAEH,OAAO,CAAC,GAAGG,GAAG,GAAGH,OAAO,CAAC;IAC1G,OAAO,CAACC,KAAK,CAACO,OAAO,CAACH,gBAAgB,EAAER,KAAK,CAAC;EAChD,CAAC,EAAE,CAACE,aAAa,EAAEC,OAAO,EAAEH,KAAK,EAAEI,KAAK,CAAC,CAAC;AAC5C;AACA,OAAO,SAASQ,wBAAwBA,CAACZ,KAAK,EAAAa,KAAA,EAG3C;EAAA,IAH6C;IAC9CC,WAAW;IACXC;EACF,CAAC,GAAAF,KAAA;EACC,MAAMT,KAAK,GAAGR,QAAQ,CAAC,CAAC;EACxB,OAAOD,KAAK,CAACU,OAAO,CAAC,MAAM;IACzB,MAAMC,GAAG,GAAGF,KAAK,CAACG,IAAI,CAAC,CAAC;IACxB,MAAMS,iBAAiB,GAAGZ,KAAK,CAACK,YAAY,CAACK,WAAW,IAAIV,KAAK,CAACO,OAAO,CAACL,GAAG,EAAES,OAAO,CAAC,GAAGT,GAAG,GAAGS,OAAO,CAAC;IACxG,OAAO,CAACX,KAAK,CAACM,QAAQ,CAACM,iBAAiB,EAAEhB,KAAK,CAAC;EAClD,CAAC,EAAE,CAACc,WAAW,EAAEC,OAAO,EAAEf,KAAK,EAAEI,KAAK,CAAC,CAAC;AAC1C;AACA,OAAO,SAASa,eAAeA,CAACV,IAAI,EAAEW,IAAI,EAAEC,QAAQ,EAAE;EACpD,MAAMf,KAAK,GAAGR,QAAQ,CAAC,CAAC;EACxB,MAAMwB,YAAY,GAAGvB,WAAW,CAACU,IAAI,EAAEH,KAAK,CAAC;EAC7C,MAAMiB,oBAAoB,GAAG1B,KAAK,CAAC2B,WAAW,CAACC,IAAI,IAAI;IACrD,MAAMC,gBAAgB,GAAGjB,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGT,iBAAiB,CAACS,IAAI,EAAEgB,IAAI,EAAEE,OAAO,CAACP,IAAI,CAAC,EAAEd,KAAK,CAAC;IAClGe,QAAQ,CAACK,gBAAgB,EAAE,SAAS,CAAC;EACvC,CAAC,EAAE,CAACN,IAAI,EAAEX,IAAI,EAAEY,QAAQ,EAAEf,KAAK,CAAC,CAAC;EACjC,OAAO;IACLgB,YAAY;IACZC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}