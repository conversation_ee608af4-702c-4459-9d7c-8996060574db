{"ast": null, "code": "export function on(obj, ev, fn) {\n  obj.on(ev, fn);\n  return function subDestroy() {\n    obj.off(ev, fn);\n  };\n}", "map": {"version": 3, "names": ["on", "obj", "ev", "fn", "subDestroy", "off"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/socket.io-client/build/esm/on.js"], "sourcesContent": ["export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n"], "mappings": "AAAA,OAAO,SAASA,EAAEA,CAACC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC5BF,GAAG,CAACD,EAAE,CAACE,EAAE,EAAEC,EAAE,CAAC;EACd,OAAO,SAASC,UAAUA,CAAA,EAAG;IACzBH,GAAG,CAACI,GAAG,CAACH,EAAE,EAAEC,EAAE,CAAC;EACnB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}