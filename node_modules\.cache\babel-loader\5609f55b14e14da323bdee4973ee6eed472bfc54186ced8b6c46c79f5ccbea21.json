{"ast": null, "code": "'use client';\n\nexport * from './useAutocomplete';", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/useAutocomplete/index.js"], "sourcesContent": ["'use client';\n\nexport * from './useAutocomplete';"], "mappings": "AAAA,YAAY;;AAEZ,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}