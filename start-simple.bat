@echo off
setlocal enabledelayedexpansion
color 0A
title Internal Complaints Portal - Simple Startup

echo.
echo ===============================================
echo    INTERNAL COMPLAINTS PORTAL - STARTUP
echo ===============================================
echo.

echo [92m[INFO][0m Starting simple startup sequence...
echo.

REM Kill any existing processes on port 1976
echo [93m[CLEANUP][0m Killing existing processes on port 1976...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :1976') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating process on port 1976 (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)

REM Kill any existing node processes
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo [92m[SUCCESS][0m Cleanup completed!
echo.

REM Check if npm is available
echo [94m[CHECK][0m Verifying Node.js and npm installation...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [91m[ERROR][0m npm is not installed or not in PATH!
    echo Please install Node.js and npm first.
    pause
    exit /b 1
)
echo [92m[SUCCESS][0m Node.js and npm are available!
echo.

REM Install dependencies if needed
if not exist "node_modules" (
    echo [93m[INSTALL][0m Installing dependencies...
    npm install
    if errorlevel 1 (
        echo [91m[ERROR][0m Failed to install dependencies!
        pause
        exit /b 1
    )
    echo [92m[SUCCESS][0m Dependencies installed!
    echo.
)

REM Build the React app
echo [94m[BUILD][0m Building React app for production...
npm run build
if errorlevel 1 (
    echo [91m[ERROR][0m Failed to build React app!
    pause
    exit /b 1
)
echo [92m[SUCCESS][0m React app built successfully!
echo.

REM Start the server and open browser
echo [94m[SERVER][0m Starting backend server...
echo [96m[INFO][0m Server will start on http://localhost:1976
echo [96m[INFO][0m Browser will open automatically in 5 seconds...
echo.

REM Start server in background and capture PID
start /b node server.js
timeout /t 5 /nobreak >nul

REM Open browser
echo [96m[BROWSER][0m Opening browser...
start "" "http://localhost:1976"

echo.
echo [92m[SUCCESS][0m Startup completed!
echo [96m[INFO][0m Application should be running at: http://localhost:1976
echo [96m[LOGIN][0m Use credentials: EMP-M / qwerty
echo.
echo [93m[NOTE][0m Press Ctrl+C to stop the server
echo.

REM Keep monitoring
:monitor
timeout /t 30 /nobreak >nul
echo [90m[%time%][0m Server is running... (Press Ctrl+C to stop)
goto monitor
