@echo off
title Internal Complaints Portal - Simple Startup

echo.
echo ===============================================
echo    INTERNAL COMPLAINTS PORTAL - STARTUP
echo ===============================================
echo.

echo [INFO] Killing existing processes...
taskkill /f /im node.exe >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :1976') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do taskkill /f /pid %%a >nul 2>&1
timeout /t 3 /nobreak >nul

echo [INFO] Starting backend server...
start "Backend Server" cmd /k "title Backend Server && npm run server"
timeout /t 8 /nobreak >nul

echo [INFO] Starting frontend server...
start "Frontend Server" cmd /k "title Frontend Server && npm start"
timeout /t 15 /nobreak >nul

echo.
echo ===============================================
echo           SERVERS STARTED SUCCESSFULLY
echo ===============================================
echo.
echo Backend:  http://localhost:1976
echo Frontend: http://localhost:3000
echo.
echo [BROWSER] Attempting to open browser...

REM Try multiple browser opening methods
cmd /c start "" "http://localhost:3000" 2>nul
timeout /t 1 /nobreak >nul
rundll32 url.dll,FileProtocolHandler "http://localhost:3000" 2>nul
timeout /t 1 /nobreak >nul

REM Try to find and launch specific browsers
for %%i in (chrome.exe msedge.exe firefox.exe) do (
    where %%i >nul 2>&1 && (
        echo [INFO] Found %%i, launching...
        start "" %%i "http://localhost:3000" 2>nul
        goto browser_found
    )
)

:browser_found

echo.
echo ===============================================
echo              READY TO USE
echo ===============================================
echo.
echo 🌐 MAIN URL: http://localhost:3000
echo 👤 LOGIN: EMP-M / qwerty
echo.
echo ⚠️  IF BROWSER DIDN'T OPEN:
echo    1. Open any browser manually
echo    2. Go to: http://localhost:3000
echo    3. Login with: EMP-M / qwerty
echo.
echo 🛑 To stop servers: Close this window or run stop.bat
echo.

:monitor
timeout /t 30 /nobreak >nul
echo [%time%] Servers running... (Close window to stop)
goto monitor
