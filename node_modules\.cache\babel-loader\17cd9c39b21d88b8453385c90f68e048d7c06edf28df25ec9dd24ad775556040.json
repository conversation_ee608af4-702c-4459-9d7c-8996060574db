{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useId as useId } from '@mui/utils';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { useListItem } from '../useList';\nimport { useCompoundItem } from '../useCompound';\n\n/**\n *\n * Demos:\n *\n * - [Select](https://mui.com/base-ui/react-select/#hooks)\n *\n * API:\n *\n * - [useOption API](https://mui.com/base-ui/react-select/hooks-api/#use-option)\n */\nexport function useOption(params) {\n  const {\n    value,\n    label,\n    disabled,\n    rootRef: optionRefParam,\n    id: idParam\n  } = params;\n  const {\n    getRootProps: getListItemProps,\n    highlighted,\n    selected\n  } = useListItem({\n    item: value\n  });\n  const id = useId(idParam);\n  const optionRef = React.useRef(null);\n  const selectOption = React.useMemo(() => ({\n    disabled,\n    label,\n    value,\n    ref: optionRef,\n    id\n  }), [disabled, label, value, id]);\n  const {\n    index\n  } = useCompoundItem(value, selectOption);\n  const handleRef = useForkRef(optionRefParam, optionRef);\n  return {\n    getRootProps: function () {\n      let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      const externalEventHandlers = extractEventHandlers(externalProps);\n      return _extends({}, externalProps, getListItemProps(externalEventHandlers), {\n        id,\n        ref: handleRef,\n        role: 'option',\n        'aria-selected': selected\n      });\n    },\n    highlighted,\n    index,\n    selected,\n    rootRef: handleRef\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useForkRef", "useForkRef", "unstable_useId", "useId", "extractEventHandlers", "useListItem", "useCompoundItem", "useOption", "params", "value", "label", "disabled", "rootRef", "optionRefParam", "id", "idParam", "getRootProps", "getListItemProps", "highlighted", "selected", "item", "optionRef", "useRef", "selectOption", "useMemo", "ref", "index", "handleRef", "externalProps", "arguments", "length", "undefined", "externalEventHandlers", "role"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/useOption/useOption.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useId as useId } from '@mui/utils';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { useListItem } from '../useList';\nimport { useCompoundItem } from '../useCompound';\n\n/**\n *\n * Demos:\n *\n * - [Select](https://mui.com/base-ui/react-select/#hooks)\n *\n * API:\n *\n * - [useOption API](https://mui.com/base-ui/react-select/hooks-api/#use-option)\n */\nexport function useOption(params) {\n  const {\n    value,\n    label,\n    disabled,\n    rootRef: optionRefParam,\n    id: idParam\n  } = params;\n  const {\n    getRootProps: getListItemProps,\n    highlighted,\n    selected\n  } = useListItem({\n    item: value\n  });\n  const id = useId(idParam);\n  const optionRef = React.useRef(null);\n  const selectOption = React.useMemo(() => ({\n    disabled,\n    label,\n    value,\n    ref: optionRef,\n    id\n  }), [disabled, label, value, id]);\n  const {\n    index\n  } = useCompoundItem(value, selectOption);\n  const handleRef = useForkRef(optionRefParam, optionRef);\n  return {\n    getRootProps: (externalProps = {}) => {\n      const externalEventHandlers = extractEventHandlers(externalProps);\n      return _extends({}, externalProps, getListItemProps(externalEventHandlers), {\n        id,\n        ref: handleRef,\n        role: 'option',\n        'aria-selected': selected\n      });\n    },\n    highlighted,\n    index,\n    selected,\n    rootRef: handleRef\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,cAAc,IAAIC,KAAK,QAAQ,YAAY;AACvF,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,WAAW,QAAQ,YAAY;AACxC,SAASC,eAAe,QAAQ,gBAAgB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,MAAM,EAAE;EAChC,MAAM;IACJC,KAAK;IACLC,KAAK;IACLC,QAAQ;IACRC,OAAO,EAAEC,cAAc;IACvBC,EAAE,EAAEC;EACN,CAAC,GAAGP,MAAM;EACV,MAAM;IACJQ,YAAY,EAAEC,gBAAgB;IAC9BC,WAAW;IACXC;EACF,CAAC,GAAGd,WAAW,CAAC;IACde,IAAI,EAAEX;EACR,CAAC,CAAC;EACF,MAAMK,EAAE,GAAGX,KAAK,CAACY,OAAO,CAAC;EACzB,MAAMM,SAAS,GAAGtB,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,YAAY,GAAGxB,KAAK,CAACyB,OAAO,CAAC,OAAO;IACxCb,QAAQ;IACRD,KAAK;IACLD,KAAK;IACLgB,GAAG,EAAEJ,SAAS;IACdP;EACF,CAAC,CAAC,EAAE,CAACH,QAAQ,EAAED,KAAK,EAAED,KAAK,EAAEK,EAAE,CAAC,CAAC;EACjC,MAAM;IACJY;EACF,CAAC,GAAGpB,eAAe,CAACG,KAAK,EAAEc,YAAY,CAAC;EACxC,MAAMI,SAAS,GAAG1B,UAAU,CAACY,cAAc,EAAEQ,SAAS,CAAC;EACvD,OAAO;IACLL,YAAY,EAAE,SAAAA,CAAA,EAAwB;MAAA,IAAvBY,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC/B,MAAMG,qBAAqB,GAAG5B,oBAAoB,CAACwB,aAAa,CAAC;MACjE,OAAO9B,QAAQ,CAAC,CAAC,CAAC,EAAE8B,aAAa,EAAEX,gBAAgB,CAACe,qBAAqB,CAAC,EAAE;QAC1ElB,EAAE;QACFW,GAAG,EAAEE,SAAS;QACdM,IAAI,EAAE,QAAQ;QACd,eAAe,EAAEd;MACnB,CAAC,CAAC;IACJ,CAAC;IACDD,WAAW;IACXQ,KAAK;IACLP,QAAQ;IACRP,OAAO,EAAEe;EACX,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}