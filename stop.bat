@echo off
setlocal enabledelayedexpansion
color 0C
title Internal Complaints Portal - Stop Servers

echo.
echo ===============================================
echo    INTERNAL COMPLAINTS PORTAL - SHUTDOWN
echo ===============================================
echo.

echo [93m[SHUTDOWN][0m Stopping all servers...
echo.

REM Kill processes on port 1976 (Backend)
echo [91m[BACKEND][0m Stopping backend server (port 1976)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :1976') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating backend process (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)

REM Kill processes on port 3000, 3001, 3002 (Frontend development servers)
echo [91m[FRONTEND][0m Stopping frontend development servers (ports 3000-3002)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating frontend process on port 3000 (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3001') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating frontend process on port 3001 (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3002') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating frontend process on port 3002 (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)

REM Kill any remaining node processes
echo [91m[CLEANUP][0m Cleaning up Node.js processes...
taskkill /f /im node.exe >nul 2>&1

REM Close any command windows with our server titles
echo [91m[WINDOWS][0m Closing server windows...
taskkill /f /fi "WINDOWTITLE:*Backend Server*" >nul 2>&1
taskkill /f /fi "WINDOWTITLE:*Frontend Server*" >nul 2>&1
taskkill /f /fi "WINDOWTITLE:*Internal Complaints Portal*" >nul 2>&1

timeout /t 2 /nobreak >nul

echo.
echo [92m[SUCCESS][0m All servers have been stopped!
echo.
echo ===============================================
echo              SHUTDOWN COMPLETED
echo ===============================================
echo.
echo [94m[INFO][0m All Internal Complaints Portal servers are now offline.
echo You can now safely close this window or run start.bat to restart.
echo.

pause
