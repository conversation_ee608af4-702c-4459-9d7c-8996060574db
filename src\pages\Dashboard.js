import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  useTheme,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  useMediaQuery,
} from '@mui/material';
import {
  Assignment as ComplaintsIcon,
  CheckCircle as ResolvedIcon,
  Pending as PendingIcon,
  Error as HighPriorityIcon,
  FiberManualRecord as StatusIcon,
  Schedule as TimeIcon,
  AccessTime as ResolutionIcon,
  Dashboard as DashboardIcon,
  BarChart as InsightsIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import axios from '../utils/axiosConfig';
import { format } from 'date-fns';
import { useSocket } from '../contexts/SocketContext';

const MonthlyStatCard = React.memo(({ title, value, icon, color, loading, subtitle, index }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (loading) {
    return (
      <Card sx={{
        height: 120,
        background: 'rgba(255,255,255,0.15)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        color: 'white'
      }}>
        <CardContent sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%'
        }}>
          <CircularProgress size={24} sx={{ color: 'white' }} />
        </CardContent>
      </Card>
    );
  }

  return (
    <div>
      <Card
        sx={{
          height: 120,
          background: 'rgba(255,255,255,0.9)',
          color: 'text.primary',
          borderRadius: 2,
          boxShadow: theme.shadows[1],
          transition: 'transform 0.2s ease, box-shadow 0.2s ease',
          '&:hover': {
            transform: 'translateY(-1px)',
            boxShadow: theme.shadows[2],
          },
        }}
      >
        <CardContent sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          height: '100%',
          p: 2,
          '&:last-child': { pb: 2 }
        }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box>
              <Typography
                variant={isMobile ? "body2" : "body1"}
                sx={{
                  opacity: 0.9,
                  fontWeight: 500,
                  fontSize: '0.875rem'
                }}
              >
                {title}
              </Typography>
              {subtitle && (
                <Typography
                  variant="caption"
                  sx={{
                    opacity: 0.7,
                    fontSize: '0.75rem',
                    display: 'block'
                  }}
                >
                  {subtitle}
                </Typography>
              )}
            </Box>
            <Box sx={{
              opacity: 0.8,
              fontSize: isMobile ? '1.2rem' : '1.5rem'
            }}>
              {icon}
            </Box>
          </Box>

          <Typography
            variant={isMobile ? "h6" : "h5"}
            sx={{
              fontWeight: 700,
              fontSize: isMobile ? '1.25rem' : '1.5rem',
              textShadow: '0px 2px 4px rgba(0,0,0,0.3)'
            }}
          >
            {value}
          </Typography>
        </CardContent>
      </Card>
    </div>
  );
});

const StatCard = ({ title, value, icon, color, loading, subtitle }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <div>
      <Card
        sx={{
          height: '100%',
          background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          borderRadius: 2,
          boxShadow: theme.shadows[2],
          transition: 'transform 0.2s ease, box-shadow 0.2s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[4],
          },
        }}
      >
        <CardContent sx={{ 
          p: isMobile ? 2 : 3,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between'
        }}>
          <Box sx={{ 
            position: 'relative', 
            zIndex: 1,
            display: 'flex',
            alignItems: 'center',
            gap: 2
          }}>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 120 }}
            >
              {React.cloneElement(icon, { 
                sx: { 
                  fontSize: isMobile ? 32 : 48,
                  opacity: 0.9,
                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'
                } 
              })}
            </motion.div>
            <Box>
              <Typography 
                variant={isMobile ? "h5" : "h4"} 
                component="div" 
                sx={{ 
                  fontWeight: 700,
                  lineHeight: 1.2,
                  mb: 0.5,
                  textShadow: '0px 2px 4px rgba(0,0,0,0.2)'
                }}
              >
                {loading ? (
                  <CircularProgress size={isMobile ? 20 : 24} color="inherit" />
                ) : (
                  value
                )}
              </Typography>
              <Typography
                variant={isMobile ? "body2" : "body1"}
                sx={{
                  opacity: 0.9,
                  fontWeight: 500,
                  letterSpacing: '0.5px',
                  textShadow: '0px 1px 2px rgba(0,0,0,0.2)'
                }}
              >
                {title}
              </Typography>
              {subtitle && (
                <Typography
                  variant="caption"
                  sx={{
                    opacity: 0.8,
                    fontWeight: 400,
                    textShadow: '0px 1px 2px rgba(0,0,0,0.2)',
                    display: 'block'
                  }}
                >
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>
          <Box
            sx={{
              position: 'absolute',
              right: -20,
              bottom: -20,
              opacity: 0.15,
            }}
          >
            {React.cloneElement(icon, {
              sx: { fontSize: isMobile ? 100 : 140 }
            })}
          </Box>
        </CardContent>
      </Card>
    </div>
  );
};

// Memoized color functions to prevent recalculation
const getStatusColor = (status, theme) => {
  const statusColors = {
    'New': theme.palette.info.main,
    'Assigned': theme.palette.warning.main,
    'In Progress': theme.palette.warning.dark,
    'Resolved': theme.palette.success.main,
    'Rejected': theme.palette.error.main,
  };
  return statusColors[status] || theme.palette.grey[500];
};

const getPriorityColor = (priority, theme) => {
  const priorityColors = {
    'Low': theme.palette.success.main,
    'Medium': theme.palette.warning.main,
    'High': theme.palette.error.main,
    'Critical': theme.palette.error.dark,
  };
  return priorityColors[priority] || theme.palette.grey[500];
};



const ActivityItem = React.memo(({ activity, index }) => {
  const theme = useTheme();



  // Memoize colors
  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);
  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);

  return (
    <div>
      <ListItem
        sx={{
          bgcolor: 'background.paper',
          borderRadius: 2,
          mb: 1,
          boxShadow: 1,
          '&:hover': {
            bgcolor: 'action.hover',
            transform: 'translateX(4px)',
            transition: 'transform 0.15s ease', // Faster transition
          },
        }}
      >
        <ListItemIcon>
          <StatusIcon sx={{ color: statusColor }} />
        </ListItemIcon>
        <ListItemText
          primary={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500, flex: 1 }}>
                #{activity.ComplaintNumber} - {activity.description}
              </Typography>
              {activity.Priority && (
                <Chip
                  label={activity.Priority}
                  size="small"
                  sx={{
                    bgcolor: `${priorityColor}15`,
                    color: priorityColor,
                    fontWeight: 500,
                    fontSize: '0.7rem'
                  }}
                />
              )}
            </Box>
          }
          secondary={
            <Box sx={{ mt: 0.5 }}>
              {activity.activityDetails && (
                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                  {activity.activityDetails}
                </Typography>
              )}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  label={activity.Status}
                  size="small"
                  sx={{
                    bgcolor: `${statusColor}15`,
                    color: statusColor,
                    fontWeight: 500
                  }}
                />
                {activity.Category && (
                  <Chip
                    label={activity.Category}
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                )}

              </Box>
            </Box>
          }
        />
      </ListItem>
    </div>
  );
});

function Dashboard() {
  const [stats, setStats] = useState(null);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { socket } = useSocket();

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch data with optimized timeout and caching
      const [statsResponse, activitiesResponse] = await Promise.all([
        axios.get('/api/dashboard/stats', {
          timeout: 10000, // 10 second timeout
          headers: {
            'Cache-Control': 'max-age=60' // Cache for 1 minute
          }
        }),
        axios.get('/api/dashboard/recent-activities', {
          timeout: 10000, // 10 second timeout
          headers: {
            'Cache-Control': 'no-cache' // Always fetch fresh data for activities
          }
        })
      ]);

      setStats(statsResponse.data);
      setActivities(activitiesResponse.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDashboardData();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000);

    // Listen for real-time updates via Socket.IO
    if (socket) {
      // Listen for status updates to refresh dashboard
      socket.on('status_updated', () => {
        console.log('Status update received, refreshing dashboard...');
        fetchDashboardData();
      });

      // Listen for new complaints to refresh dashboard
      socket.on('complaint_created', () => {
        console.log('New complaint received, refreshing dashboard...');
        fetchDashboardData();
      });
    }

    return () => {
      clearInterval(interval);
      if (socket) {
        socket.off('status_updated');
        socket.off('complaint_created');
      }
    };
  }, [fetchDashboardData, socket]);

  const statCards = useMemo(() => [
    {
      title: 'Total Complaints',
      value: stats?.totalComplaints || 0,
      icon: <ComplaintsIcon />,
      color: 'primary'
    },
    {
      title: 'Resolved',
      value: stats?.resolvedComplaints || 0,
      icon: <ResolvedIcon />,
      color: 'success'
    },
    {
      title: 'Pending',
      value: stats?.pendingComplaints || 0,
      icon: <PendingIcon />,
      color: 'warning'
    },
    {
      title: 'High Priority',
      value: stats?.highPriorityComplaints || 0,
      icon: <HighPriorityIcon />,
      color: 'error'
    }
  ], [stats]);

  const monthlyStatCards = useMemo(() => {
    if (!stats?.monthlyStats) return [];

    return [
      {
        title: 'This Month',
        value: stats.monthlyStats.totalMonthlyComplaints || 0,
        icon: <ComplaintsIcon />,
        color: 'info',
        subtitle: 'New complaints'
      },
      {
        title: 'Resolution Rate',
        value: `${stats.monthlyStats.resolutionRate || 0}%`,
        icon: <ResolvedIcon />,
        color: 'success',
        subtitle: 'Monthly average'
      },
      {
        title: 'Response Time',
        value: `${stats.monthlyStats.responseEfficiency || 0}%`,
        icon: <TimeIcon />,
        color: 'warning',
        subtitle: 'New tickets in 24h'
      },
      {
        title: 'Avg Resolution',
        value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,
        icon: <ResolutionIcon />,
        color: 'primary',
        subtitle: 'Hours to resolve'
      }
    ];
  }, [stats]);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          opacity: 0.3,
          zIndex: 0
        }
      }}
    >
      <Box sx={{
        position: 'relative',
        zIndex: 1,
        p: { xs: 2, sm: 3 }
      }}>
        <div>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
            {/* Company Logo */}
            <Box
              sx={{
                width: { xs: 50, sm: 60, md: 70 },
                height: { xs: 50, sm: 60, md: 70 },
                borderRadius: '50%',
                background: 'rgba(255, 255, 255, 0.95)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
                border: '2px solid rgba(255, 255, 255, 0.8)',
                overflow: 'hidden',
              }}
            >
              <img
                src="/prk-logo.jpg"
                alt="PRK Company Logo"
                style={{
                  width: '85%',
                  height: '85%',
                  objectFit: 'contain',
                  borderRadius: '50%',
                }}
              />
            </Box>
            <DashboardIcon
              sx={{
                fontSize: { xs: '2.5rem', sm: '3rem', md: '3.5rem' },
                color: 'white',
                mr: 2,
                filter: 'drop-shadow(0px 4px 8px rgba(0,0,0,0.3))'
              }}
            />
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                color: 'white',
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                textShadow: '0px 4px 8px rgba(0,0,0,0.3)',
                letterSpacing: '-0.02em'
              }}
            >
              Dashboard
            </Typography>
          </Box>
          <Typography
            variant="h6"
            sx={{
              mb: 4,
              color: 'rgba(255,255,255,0.9)',
              textAlign: 'center',
              fontWeight: 400,
              fontSize: { xs: '1rem', sm: '1.25rem' }
            }}
          >
            Internal Complaints Management System
          </Typography>
        </div>

        {error && (
          <Alert
            severity="error"
            sx={{ mb: 3 }}
          >
            {error}
          </Alert>
        )}

      <Grid container spacing={3}>
        {statCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={3} key={card.title}>
            <StatCard {...card} loading={loading} />
          </Grid>
        ))}

        {/* Monthly Statistics Section */}
        {monthlyStatCards.length > 0 && (
          <Grid item xs={12}>
            <Card
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <InsightsIcon sx={{ mr: 1.5, color: 'white' }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'white' }}>
                    Monthly Performance Insights
                  </Typography>
                </Box>
                <Grid container spacing={3}>
                  {monthlyStatCards.map((card, index) => (
                    <Grid item xs={12} sm={6} md={3} key={card.title}>
                      <MonthlyStatCard {...card} loading={loading} index={index} />
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        <Grid item xs={12}>
          <Card
            sx={{
              height: '100%',
              minHeight: 400,
            }}
          >
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Recent Activities
              </Typography>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              ) : activities.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {activities.slice(0, 8).map((activity, index) => (
                    <ActivityItem
                      key={`${activity.ComplaintId}-${activity.Status}`}
                      activity={activity}
                      index={index}
                    />
                  ))}
                </List>
              ) : (
                <Box 
                  sx={{ 
                    textAlign: 'center', 
                    py: 4,
                    color: 'text.secondary'
                  }}
                >
                  <Typography>No recent activities</Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      </Box>
    </Box>
  );
}

export default Dashboard; 