import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  useTheme,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  Chip,
  LinearProgress,
  useMediaQuery,
  Button,
} from '@mui/material';
import {
  Assignment as ComplaintsIcon,
  CheckCircle as ResolvedIcon,
  Pending as PendingIcon,
  Error as HighPriorityIcon,
  FiberManualRecord as StatusIcon,
  Schedule as TimeIcon,
  Speed as EfficiencyIcon,
  Timeline as TrendIcon,
  AccessTime as ResolutionIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import axios from '../utils/axiosConfig';
import { format, formatDistanceToNow } from 'date-fns';
import { useSocket } from '../contexts/SocketContext';

const MonthlyStatCard = React.memo(({ title, value, icon, color, loading, subtitle, index }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (loading) {
    return (
      <Card sx={{
        height: 120,
        background: 'rgba(255,255,255,0.15)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        color: 'white'
      }}>
        <CardContent sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%'
        }}>
          <CircularProgress size={24} sx={{ color: 'white' }} />
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{
        delay: index * 0.1,
        duration: 0.3,
        ease: "easeOut"
      }}
    >
      <Card
        sx={{
          height: 120,
          background: 'rgba(255,255,255,0.15)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255,255,255,0.2)',
          color: 'white',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            background: 'rgba(255,255,255,0.25)',
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.3)',
          },
        }}
      >
        <CardContent sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          height: '100%',
          p: 2,
          '&:last-child': { pb: 2 }
        }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box>
              <Typography
                variant={isMobile ? "body2" : "body1"}
                sx={{
                  opacity: 0.9,
                  fontWeight: 500,
                  fontSize: '0.875rem'
                }}
              >
                {title}
              </Typography>
              {subtitle && (
                <Typography
                  variant="caption"
                  sx={{
                    opacity: 0.7,
                    fontSize: '0.75rem',
                    display: 'block'
                  }}
                >
                  {subtitle}
                </Typography>
              )}
            </Box>
            <Box sx={{
              opacity: 0.8,
              fontSize: isMobile ? '1.2rem' : '1.5rem'
            }}>
              {icon}
            </Box>
          </Box>

          <Typography
            variant={isMobile ? "h6" : "h5"}
            sx={{
              fontWeight: 700,
              fontSize: isMobile ? '1.25rem' : '1.5rem',
              textShadow: '0px 2px 4px rgba(0,0,0,0.3)'
            }}
          >
            {value}
          </Typography>
        </CardContent>
      </Card>
    </motion.div>
  );
});

const StatCard = ({ title, value, icon, color, loading, subtitle }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        type: "spring",
        stiffness: 100,
        damping: 15,
        duration: 0.6 
      }}
      whileHover={{ 
        scale: 1.02,
        transition: { duration: 0.2 }
      }}
      whileTap={{ scale: 0.98 }}
    >
      <Card
        sx={{
          height: '100%',
          background: 'rgba(255, 255, 255, 0.15)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          borderRadius: 3,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            background: 'rgba(255, 255, 255, 0.25)',
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
            transform: 'translateY(-4px)',
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `linear-gradient(135deg, ${theme.palette[color].main}40 0%, ${theme.palette[color].dark}40 100%)`,
            zIndex: 0
          }
        }}
      >
        <CardContent sx={{ 
          p: isMobile ? 2 : 3,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between'
        }}>
          <Box sx={{ 
            position: 'relative', 
            zIndex: 1,
            display: 'flex',
            alignItems: 'center',
            gap: 2
          }}>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 120 }}
            >
              {React.cloneElement(icon, { 
                sx: { 
                  fontSize: isMobile ? 32 : 48,
                  opacity: 0.9,
                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'
                } 
              })}
            </motion.div>
            <Box>
              <Typography 
                variant={isMobile ? "h5" : "h4"} 
                component="div" 
                sx={{ 
                  fontWeight: 700,
                  lineHeight: 1.2,
                  mb: 0.5,
                  textShadow: '0px 2px 4px rgba(0,0,0,0.2)'
                }}
              >
                {loading ? (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <CircularProgress size={isMobile ? 20 : 24} color="inherit" />
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                  >
                    {value}
                  </motion.div>
                )}
              </Typography>
              <Typography
                variant={isMobile ? "body2" : "body1"}
                sx={{
                  opacity: 0.9,
                  fontWeight: 500,
                  letterSpacing: '0.5px',
                  textShadow: '0px 1px 2px rgba(0,0,0,0.2)'
                }}
              >
                {title}
              </Typography>
              {subtitle && (
                <Typography
                  variant="caption"
                  sx={{
                    opacity: 0.8,
                    fontWeight: 400,
                    textShadow: '0px 1px 2px rgba(0,0,0,0.2)',
                    display: 'block'
                  }}
                >
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>
          <Box
            component={motion.div}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 0.15, scale: 2 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            sx={{
              position: 'absolute',
              right: -20,
              bottom: -20,
              filter: 'blur(2px)'
            }}
          >
            {React.cloneElement(icon, { 
              sx: { fontSize: isMobile ? 100 : 140 }
            })}
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Memoized color functions to prevent recalculation
const getStatusColor = (status, theme) => {
  const statusColors = {
    'New': theme.palette.info.main,
    'Assigned': theme.palette.warning.main,
    'In Progress': theme.palette.warning.dark,
    'Resolved': theme.palette.success.main,
    'Rejected': theme.palette.error.main,
  };
  return statusColors[status] || theme.palette.grey[500];
};

const getPriorityColor = (priority, theme) => {
  const priorityColors = {
    'Low': theme.palette.success.main,
    'Medium': theme.palette.warning.main,
    'High': theme.palette.error.main,
    'Critical': theme.palette.error.dark,
  };
  return priorityColors[priority] || theme.palette.grey[500];
};

// Optimized timestamp formatting function - consistent with complaint details
const formatTimestamp = (timestamp) => {
  if (!timestamp) return 'N/A';

  try {
    const date = new Date(timestamp);

    if (isNaN(date.getTime())) {
      console.warn('Invalid timestamp:', timestamp);
      return 'N/A';
    }

    // Use the same format as complaint details - PPpp gives "Jun 9, 2025, 11:27:36 AM"
    return format(date, 'PPpp');
  } catch (error) {
    console.error('Error formatting timestamp:', error);
    return 'Invalid date';
  }
};

const ActivityItem = React.memo(({ activity, index }) => {
  const theme = useTheme();



  // Memoize colors
  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);
  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{
        delay: Math.min(index * 0.05, 0.3), // Reduced delay for better performance
        duration: 0.3, // Reduced duration
        ease: "easeOut"
      }}
    >
      <ListItem
        sx={{
          bgcolor: 'background.paper',
          borderRadius: 2,
          mb: 1,
          boxShadow: 1,
          '&:hover': {
            bgcolor: 'action.hover',
            transform: 'translateX(4px)',
            transition: 'transform 0.15s ease', // Faster transition
          },
        }}
      >
        <ListItemIcon>
          <StatusIcon sx={{ color: statusColor }} />
        </ListItemIcon>
        <ListItemText
          primary={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500, flex: 1 }}>
                #{activity.ComplaintNumber} - {activity.description}
              </Typography>
              {activity.Priority && (
                <Chip
                  label={activity.Priority}
                  size="small"
                  sx={{
                    bgcolor: `${priorityColor}15`,
                    color: priorityColor,
                    fontWeight: 500,
                    fontSize: '0.7rem'
                  }}
                />
              )}
            </Box>
          }
          secondary={
            <Box sx={{ mt: 0.5 }}>
              {activity.activityDetails && (
                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                  {activity.activityDetails}
                </Typography>
              )}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  label={activity.Status}
                  size="small"
                  sx={{
                    bgcolor: `${statusColor}15`,
                    color: statusColor,
                    fontWeight: 500
                  }}
                />
                {activity.Category && (
                  <Chip
                    label={activity.Category}
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                )}

              </Box>
            </Box>
          }
        />
      </ListItem>
    </motion.div>
  );
});

function Dashboard() {
  const [stats, setStats] = useState(null);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { socket } = useSocket();

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch data with optimized timeout and caching
      const [statsResponse, activitiesResponse] = await Promise.all([
        axios.get('/api/dashboard/stats', {
          timeout: 10000, // 10 second timeout
          headers: {
            'Cache-Control': 'max-age=60' // Cache for 1 minute
          }
        }),
        axios.get('/api/dashboard/recent-activities', {
          timeout: 10000, // 10 second timeout
          headers: {
            'Cache-Control': 'no-cache' // Always fetch fresh data for activities
          }
        })
      ]);

      setStats(statsResponse.data);
      setActivities(activitiesResponse.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDashboardData();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000);

    // Listen for real-time updates via Socket.IO
    if (socket) {
      // Listen for status updates to refresh dashboard
      socket.on('status_updated', () => {
        console.log('Status update received, refreshing dashboard...');
        fetchDashboardData();
      });

      // Listen for new complaints to refresh dashboard
      socket.on('complaint_created', () => {
        console.log('New complaint received, refreshing dashboard...');
        fetchDashboardData();
      });
    }

    return () => {
      clearInterval(interval);
      if (socket) {
        socket.off('status_updated');
        socket.off('complaint_created');
      }
    };
  }, [fetchDashboardData, socket]);

  const statCards = useMemo(() => [
    {
      title: 'Total Complaints',
      value: stats?.totalComplaints || 0,
      icon: <ComplaintsIcon />,
      color: 'primary'
    },
    {
      title: 'Resolved',
      value: stats?.resolvedComplaints || 0,
      icon: <ResolvedIcon />,
      color: 'success'
    },
    {
      title: 'Pending',
      value: stats?.pendingComplaints || 0,
      icon: <PendingIcon />,
      color: 'warning'
    },
    {
      title: 'High Priority',
      value: stats?.highPriorityComplaints || 0,
      icon: <HighPriorityIcon />,
      color: 'error'
    }
  ], [stats]);

  const monthlyStatCards = useMemo(() => {
    if (!stats?.monthlyStats) return [];

    return [
      {
        title: 'This Month',
        value: stats.monthlyStats.totalMonthlyComplaints || 0,
        icon: <ComplaintsIcon />,
        color: 'info',
        subtitle: 'New complaints'
      },
      {
        title: 'Resolution Rate',
        value: `${stats.monthlyStats.resolutionRate || 0}%`,
        icon: <ResolvedIcon />,
        color: 'success',
        subtitle: 'Monthly average'
      },
      {
        title: 'Response Time',
        value: `${stats.monthlyStats.responseEfficiency || 0}%`,
        icon: <TimeIcon />,
        color: 'warning',
        subtitle: 'Within 24h'
      },
      {
        title: 'Avg Resolution',
        value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,
        icon: <ResolutionIcon />,
        color: 'primary',
        subtitle: 'Hours to resolve'
      }
    ];
  }, [stats]);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          opacity: 0.3,
          zIndex: 0
        }
      }}
    >
      <Box sx={{
        position: 'relative',
        zIndex: 1,
        p: { xs: 2, sm: 3 }
      }}>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, type: "spring" }}
        >
          <Typography
            variant="h3"
            sx={{
              mb: 1,
              fontWeight: 700,
              color: 'white',
              textAlign: 'center',
              fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
              textShadow: '0px 4px 8px rgba(0,0,0,0.3)',
              letterSpacing: '-0.02em'
            }}
          >
            📊 Dashboard
          </Typography>
          <Typography
            variant="h6"
            sx={{
              mb: 4,
              color: 'rgba(255,255,255,0.9)',
              textAlign: 'center',
              fontWeight: 400,
              fontSize: { xs: '1rem', sm: '1.25rem' }
            }}
          >
            Internal Complaints Management System
          </Typography>
        </motion.div>

        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.3, type: "spring" }}
          >
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 3,
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                '& .MuiAlert-icon': {
                  fontSize: '1.5rem'
                }
              }}
              action={
                <motion.div whileHover={{ scale: 1.05 }}>
                  <Button
                    color="inherit"
                    size="small"
                    onClick={fetchDashboardData}
                    sx={{
                      borderRadius: 2,
                      textTransform: 'none',
                      fontWeight: 600
                    }}
                  >
                    Retry
                  </Button>
                </motion.div>
              }
            >
              {error}
            </Alert>
          </motion.div>
        )}

      <Grid container spacing={3}>
        {statCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={3} key={card.title}>
            <StatCard {...card} loading={loading} />
          </Grid>
        ))}

        {/* Monthly Statistics Section */}
        {monthlyStatCards.length > 0 && (
          <Grid item xs={12}>
            <Card
              component={motion.div}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                overflow: 'visible',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'rgba(255,255,255,0.1)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 'inherit',
                  zIndex: 0
                }
              }}
            >
              <CardContent sx={{ position: 'relative', zIndex: 1 }}>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 600, color: 'white' }}>
                  📊 Monthly Performance Insights
                </Typography>
                <Grid container spacing={3}>
                  {monthlyStatCards.map((card, index) => (
                    <Grid item xs={12} sm={6} md={3} key={card.title}>
                      <MonthlyStatCard {...card} loading={loading} index={index} />
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        <Grid item xs={12}>
          <Card
            component={motion.div}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            sx={{
              overflow: 'visible',
              height: '100%',
              minHeight: 400,
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: 3,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            }}
          >
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Recent Activities
              </Typography>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              ) : activities.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {activities.slice(0, 8).map((activity, index) => (
                    <ActivityItem
                      key={`${activity.ComplaintId}-${activity.Status}`}
                      activity={activity}
                      index={index}
                    />
                  ))}
                </List>
              ) : (
                <Box 
                  sx={{ 
                    textAlign: 'center', 
                    py: 4,
                    color: 'text.secondary'
                  }}
                >
                  <Typography>No recent activities</Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      </Box>
    </Box>
  );
}

export default Dashboard; 