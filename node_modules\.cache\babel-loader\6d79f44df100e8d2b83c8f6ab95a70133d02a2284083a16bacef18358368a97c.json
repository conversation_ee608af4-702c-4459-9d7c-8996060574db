{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, CircularProgress, Alert, useTheme, List, ListItem, ListItemText, ListItemIcon, Divider, Paper, Chip, LinearProgress, useMediaQuery, Button } from '@mui/material';\nimport { Assignment as ComplaintsIcon, CheckCircle as ResolvedIcon, Pending as PendingIcon, Error as HighPriorityIcon, FiberManualRecord as StatusIcon, Schedule as TimeIcon, Speed as EfficiencyIcon, Timeline as TrendIcon, AccessTime as ResolutionIcon } from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport axios from '../utils/axiosConfig';\nimport { format, formatDistanceToNow } from 'date-fns';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatCard = ({\n  title,\n  value,\n  icon,\n  color,\n  loading,\n  subtitle\n}) => {\n  _s();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      type: \"spring\",\n      stiffness: 100,\n      damping: 15,\n      duration: 0.6\n    },\n    whileHover: {\n      scale: 1.02,\n      transition: {\n        duration: 0.2\n      }\n    },\n    whileTap: {\n      scale: 0.98\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        height: '100%',\n        background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\n        color: 'white',\n        position: 'relative',\n        overflow: 'hidden',\n        boxShadow: theme.shadows[loading ? 0 : 2],\n        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n        '&:hover': {\n          boxShadow: theme.shadows[4],\n          transform: 'translateY(-4px)'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: isMobile ? 2 : 3,\n          height: '100%',\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'relative',\n            zIndex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              delay: 0.2,\n              type: \"spring\",\n              stiffness: 120\n            },\n            children: /*#__PURE__*/React.cloneElement(icon, {\n              sx: {\n                fontSize: isMobile ? 32 : 48,\n                opacity: 0.9,\n                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"h5\" : \"h4\",\n              component: \"div\",\n              sx: {\n                fontWeight: 700,\n                lineHeight: 1.2,\n                mb: 0.5,\n                textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\n              },\n              children: loading ? /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0\n                },\n                animate: {\n                  opacity: 1\n                },\n                transition: {\n                  duration: 0.5\n                },\n                children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: isMobile ? 20 : 24,\n                  color: \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 10\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3,\n                  duration: 0.5\n                },\n                children: value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"body2\" : \"body1\",\n              sx: {\n                opacity: 0.9,\n                fontWeight: 500,\n                letterSpacing: '0.5px',\n                textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\n              },\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                opacity: 0.8,\n                fontWeight: 400,\n                textShadow: '0px 1px 2px rgba(0,0,0,0.2)',\n                display: 'block'\n              },\n              children: subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            scale: 0.5\n          },\n          animate: {\n            opacity: 0.15,\n            scale: 2\n          },\n          transition: {\n            delay: 0.4,\n            duration: 0.8\n          },\n          sx: {\n            position: 'absolute',\n            right: -20,\n            bottom: -20,\n            filter: 'blur(2px)'\n          },\n          children: /*#__PURE__*/React.cloneElement(icon, {\n            sx: {\n              fontSize: isMobile ? 100 : 140\n            }\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n\n// Memoized color functions to prevent recalculation\n_s(StatCard, \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = StatCard;\nconst getStatusColor = (status, theme) => {\n  const statusColors = {\n    'New': theme.palette.info.main,\n    'Assigned': theme.palette.warning.main,\n    'In Progress': theme.palette.warning.dark,\n    'Resolved': theme.palette.success.main,\n    'Rejected': theme.palette.error.main\n  };\n  return statusColors[status] || theme.palette.grey[500];\n};\nconst getPriorityColor = (priority, theme) => {\n  const priorityColors = {\n    'Low': theme.palette.success.main,\n    'Medium': theme.palette.warning.main,\n    'High': theme.palette.error.main,\n    'Critical': theme.palette.error.dark\n  };\n  return priorityColors[priority] || theme.palette.grey[500];\n};\n\n// Optimized timestamp formatting function\nconst formatTimestamp = timestamp => {\n  if (!timestamp) return 'N/A';\n  try {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = Math.abs(now - date) / (1000 * 60 * 60);\n\n    // If less than 24 hours, show time\n    if (diffInHours < 24) {\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    }\n    // If less than 7 days, show day and time\n    else if (diffInHours < 168) {\n      return date.toLocaleDateString('en-US', {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    }\n    // Otherwise show full date and time\n    else {\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    }\n  } catch (error) {\n    console.error('Error formatting timestamp:', error);\n    return 'Invalid date';\n  }\n};\nconst ActivityItem = /*#__PURE__*/_s2(/*#__PURE__*/React.memo(_c2 = _s2(({\n  activity,\n  index\n}) => {\n  _s2();\n  const theme = useTheme();\n\n  // Use activityTimestamp if available, otherwise fall back to submissionDate\n  // The backend already handles this logic correctly\n  const displayTimestamp = activity.activityTimestamp || activity.submissionDate;\n\n  // Memoize formatted timestamp\n  const formattedTimestamp = useMemo(() => formatTimestamp(displayTimestamp), [displayTimestamp]);\n\n  // Memoize colors\n  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);\n  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      x: -20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    transition: {\n      delay: Math.min(index * 0.05, 0.3),\n      // Reduced delay for better performance\n      duration: 0.3,\n      // Reduced duration\n      ease: \"easeOut\"\n    },\n    children: /*#__PURE__*/_jsxDEV(ListItem, {\n      sx: {\n        bgcolor: 'background.paper',\n        borderRadius: 2,\n        mb: 1,\n        boxShadow: 1,\n        '&:hover': {\n          bgcolor: 'action.hover',\n          transform: 'translateX(4px)',\n          transition: 'transform 0.15s ease' // Faster transition\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(StatusIcon, {\n          sx: {\n            color: statusColor\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            mb: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 500,\n              flex: 1\n            },\n            children: [\"#\", activity.ComplaintNumber, \" - \", activity.description]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this), activity.Priority && /*#__PURE__*/_jsxDEV(Chip, {\n            label: activity.Priority,\n            size: \"small\",\n            sx: {\n              bgcolor: `${priorityColor}15`,\n              color: priorityColor,\n              fontWeight: 500,\n              fontSize: '0.7rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this),\n        secondary: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 0.5\n          },\n          children: [activity.activityDetails && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 0.5\n            },\n            children: activity.activityDetails\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              label: activity.Status,\n              size: \"small\",\n              sx: {\n                bgcolor: `${statusColor}15`,\n                color: statusColor,\n                fontWeight: 500\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), activity.Category && /*#__PURE__*/_jsxDEV(Chip, {\n              label: activity.Category,\n              size: \"small\",\n              variant: \"outlined\",\n              sx: {\n                fontSize: '0.7rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: formattedTimestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 256,\n    columnNumber: 5\n  }, this);\n}, \"0XhO5jXSGOZ751H0Lnx1g3U0Ar4=\", false, function () {\n  return [useTheme];\n})), \"0XhO5jXSGOZ751H0Lnx1g3U0Ar4=\", false, function () {\n  return [useTheme];\n});\n_c3 = ActivityItem;\nfunction Dashboard() {\n  _s3();\n  const [stats, setStats] = useState(null);\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n      const [statsResponse, activitiesResponse] = await Promise.all([axios.get('/api/dashboard/stats'), axios.get('/api/dashboard/recent-activities')]);\n      setStats(statsResponse.data);\n      setActivities(activitiesResponse.data);\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching dashboard data:', err);\n      setError('Failed to load dashboard data. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n  const statCards = useMemo(() => [{\n    title: 'Total Complaints',\n    value: (stats === null || stats === void 0 ? void 0 : stats.totalComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(ComplaintsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 13\n    }, this),\n    color: 'primary'\n  }, {\n    title: 'Resolved',\n    value: (stats === null || stats === void 0 ? void 0 : stats.resolvedComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(ResolvedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 13\n    }, this),\n    color: 'success'\n  }, {\n    title: 'Pending',\n    value: (stats === null || stats === void 0 ? void 0 : stats.pendingComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(PendingIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 13\n    }, this),\n    color: 'warning'\n  }, {\n    title: 'High Priority',\n    value: (stats === null || stats === void 0 ? void 0 : stats.highPriorityComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(HighPriorityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 13\n    }, this),\n    color: 'error'\n  }], [stats]);\n  const monthlyStatCards = useMemo(() => {\n    if (!(stats !== null && stats !== void 0 && stats.monthlyStats)) return [];\n    return [{\n      title: 'This Month',\n      value: stats.monthlyStats.totalMonthlyComplaints || 0,\n      icon: /*#__PURE__*/_jsxDEV(ComplaintsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 15\n      }, this),\n      color: 'info',\n      subtitle: 'New complaints'\n    }, {\n      title: 'Resolution Rate',\n      value: `${stats.monthlyStats.resolutionRate || 0}%`,\n      icon: /*#__PURE__*/_jsxDEV(ResolvedIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 15\n      }, this),\n      color: 'success',\n      subtitle: 'Monthly average'\n    }, {\n      title: 'Response Time',\n      value: `${stats.monthlyStats.responseEfficiency || 0}%`,\n      icon: /*#__PURE__*/_jsxDEV(TimeIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 15\n      }, this),\n      color: 'warning',\n      subtitle: 'Within 24h'\n    }, {\n      title: 'Avg Resolution',\n      value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,\n      icon: /*#__PURE__*/_jsxDEV(ResolutionIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 15\n      }, this),\n      color: 'primary',\n      subtitle: 'Hours to resolve'\n    }];\n  }, [stats]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: {\n        xs: 2,\n        sm: 3\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      sx: {\n        mb: 4,\n        fontWeight: 600\n      },\n      children: \"Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      action: /*#__PURE__*/_jsxDEV(motion.div, {\n        whileHover: {\n          scale: 1.05\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          size: \"small\",\n          onClick: fetchDashboardData,\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 13\n      }, this),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [statCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          ...card,\n          loading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 13\n        }, this)\n      }, card.title, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 11\n      }, this)), monthlyStatCards.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.2,\n            duration: 0.3\n          },\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            overflow: 'visible',\n            position: 'relative',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(255,255,255,0.1)',\n              backdropFilter: 'blur(10px)',\n              borderRadius: 'inherit',\n              zIndex: 0\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              position: 'relative',\n              zIndex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 3,\n                fontWeight: 600,\n                color: 'white'\n              },\n              children: \"\\uD83D\\uDCCA Monthly Performance Insights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: monthlyStatCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(MonthlyStatCard, {\n                  ...card,\n                  loading: loading,\n                  index: index\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 23\n                }, this)\n              }, card.title, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.4\n          },\n          sx: {\n            overflow: 'visible',\n            height: '100%',\n            minHeight: 400\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600\n              },\n              children: \"Recent Activities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                p: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this) : activities.length > 0 ? /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: /*#__PURE__*/_jsxDEV(List, {\n                children: activities.map((activity, index) => /*#__PURE__*/_jsxDEV(ActivityItem, {\n                  activity: activity,\n                  index: index\n                }, activity.ComplaintId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                py: 4,\n                color: 'text.secondary'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                children: \"No recent activities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 431,\n    columnNumber: 5\n  }, this);\n}\n_s3(Dashboard, \"NT4e200PVgwxttbR+vOjVkpb2PA=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c4 = Dashboard;\nexport default Dashboard;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"StatCard\");\n$RefreshReg$(_c2, \"ActivityItem$React.memo\");\n$RefreshReg$(_c3, \"ActivityItem\");\n$RefreshReg$(_c4, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "CircularProgress", "<PERSON><PERSON>", "useTheme", "List", "ListItem", "ListItemText", "ListItemIcon", "Divider", "Paper", "Chip", "LinearProgress", "useMediaQuery", "<PERSON><PERSON>", "Assignment", "ComplaintsIcon", "CheckCircle", "ResolvedIcon", "Pending", "PendingIcon", "Error", "HighPriorityIcon", "FiberManualRecord", "StatusIcon", "Schedule", "TimeIcon", "Speed", "EfficiencyIcon", "Timeline", "TrendIcon", "AccessTime", "ResolutionIcon", "motion", "AnimatePresence", "axios", "format", "formatDistanceToNow", "jsxDEV", "_jsxDEV", "StatCard", "title", "value", "icon", "color", "loading", "subtitle", "_s", "theme", "isMobile", "breakpoints", "down", "div", "initial", "opacity", "y", "animate", "transition", "type", "stiffness", "damping", "duration", "whileHover", "scale", "whileTap", "children", "sx", "height", "background", "palette", "main", "dark", "position", "overflow", "boxShadow", "shadows", "transform", "p", "display", "flexDirection", "justifyContent", "zIndex", "alignItems", "gap", "delay", "cloneElement", "fontSize", "filter", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "fontWeight", "lineHeight", "mb", "textShadow", "size", "letterSpacing", "right", "bottom", "_c", "getStatusColor", "status", "statusColors", "info", "warning", "success", "error", "grey", "getPriorityColor", "priority", "priorityColors", "formatTimestamp", "timestamp", "date", "Date", "now", "diffInHours", "Math", "abs", "toLocaleTimeString", "hour", "minute", "hour12", "toLocaleDateString", "weekday", "month", "day", "year", "console", "ActivityItem", "_s2", "memo", "_c2", "activity", "index", "displayTimestamp", "activityTimestamp", "submissionDate", "formattedTimestamp", "statusColor", "Status", "priorityColor", "Priority", "x", "min", "ease", "bgcolor", "borderRadius", "primary", "flex", "ComplaintNumber", "description", "label", "secondary", "mt", "activityDetails", "flexWrap", "Category", "_c3", "Dashboard", "_s3", "stats", "setStats", "activities", "setActivities", "setLoading", "setError", "fetchDashboardData", "statsResponse", "activitiesResponse", "Promise", "all", "get", "data", "err", "statCards", "totalComplaints", "resolvedComplaints", "pendingComplaints", "highPriorityComplaints", "monthlyStatCards", "monthlyStats", "totalMonthlyComplaints", "resolutionRate", "responseEfficiency", "round", "avgResolutionHours", "xs", "sm", "severity", "action", "onClick", "container", "spacing", "map", "card", "item", "md", "length", "content", "top", "left", "<PERSON><PERSON>ilter", "MonthlyStatCard", "minHeight", "ComplaintId", "textAlign", "py", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport {\r\n  Box,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  CircularProgress,\r\n  Alert,\r\n  useTheme,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Divider,\r\n  Paper,\r\n  Chip,\r\n  LinearProgress,\r\n  useMediaQuery,\r\n  Button,\r\n} from '@mui/material';\r\nimport {\r\n  Assignment as ComplaintsIcon,\r\n  CheckCircle as ResolvedIcon,\r\n  Pending as PendingIcon,\r\n  Error as HighPriorityIcon,\r\n  FiberManualRecord as StatusIcon,\r\n  Schedule as TimeIcon,\r\n  Speed as EfficiencyIcon,\r\n  Timeline as TrendIcon,\r\n  AccessTime as ResolutionIcon,\r\n} from '@mui/icons-material';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport axios from '../utils/axiosConfig';\r\nimport { format, formatDistanceToNow } from 'date-fns';\r\n\r\nconst StatCard = ({ title, value, icon, color, loading, subtitle }) => {\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n  \r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ \r\n        type: \"spring\",\r\n        stiffness: 100,\r\n        damping: 15,\r\n        duration: 0.6 \r\n      }}\r\n      whileHover={{ \r\n        scale: 1.02,\r\n        transition: { duration: 0.2 }\r\n      }}\r\n      whileTap={{ scale: 0.98 }}\r\n    >\r\n      <Card\r\n        sx={{\r\n          height: '100%',\r\n          background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\r\n          color: 'white',\r\n          position: 'relative',\r\n          overflow: 'hidden',\r\n          boxShadow: theme.shadows[loading ? 0 : 2],\r\n          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\r\n          '&:hover': {\r\n            boxShadow: theme.shadows[4],\r\n            transform: 'translateY(-4px)',\r\n          },\r\n        }}\r\n      >\r\n        <CardContent sx={{ \r\n          p: isMobile ? 2 : 3,\r\n          height: '100%',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'space-between'\r\n        }}>\r\n          <Box sx={{ \r\n            position: 'relative', \r\n            zIndex: 1,\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 2\r\n          }}>\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 120 }}\r\n            >\r\n              {React.cloneElement(icon, { \r\n                sx: { \r\n                  fontSize: isMobile ? 32 : 48,\r\n                  opacity: 0.9,\r\n                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\r\n                } \r\n              })}\r\n            </motion.div>\r\n            <Box>\r\n              <Typography \r\n                variant={isMobile ? \"h5\" : \"h4\"} \r\n                component=\"div\" \r\n                sx={{ \r\n                  fontWeight: 700,\r\n                  lineHeight: 1.2,\r\n                  mb: 0.5,\r\n                  textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {loading ? (\r\n                  <motion.div\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    transition={{ duration: 0.5 }}\r\n                  >\r\n                    <CircularProgress size={isMobile ? 20 : 24} color=\"inherit\" />\r\n                  </motion.div>\r\n                ) : (\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.3, duration: 0.5 }}\r\n                  >\r\n                    {value}\r\n                  </motion.div>\r\n                )}\r\n              </Typography>\r\n              <Typography\r\n                variant={isMobile ? \"body2\" : \"body1\"}\r\n                sx={{\r\n                  opacity: 0.9,\r\n                  fontWeight: 500,\r\n                  letterSpacing: '0.5px',\r\n                  textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {title}\r\n              </Typography>\r\n              {subtitle && (\r\n                <Typography\r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    opacity: 0.8,\r\n                    fontWeight: 400,\r\n                    textShadow: '0px 1px 2px rgba(0,0,0,0.2)',\r\n                    display: 'block'\r\n                  }}\r\n                >\r\n                  {subtitle}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n          </Box>\r\n          <Box\r\n            component={motion.div}\r\n            initial={{ opacity: 0, scale: 0.5 }}\r\n            animate={{ opacity: 0.15, scale: 2 }}\r\n            transition={{ delay: 0.4, duration: 0.8 }}\r\n            sx={{\r\n              position: 'absolute',\r\n              right: -20,\r\n              bottom: -20,\r\n              filter: 'blur(2px)'\r\n            }}\r\n          >\r\n            {React.cloneElement(icon, { \r\n              sx: { fontSize: isMobile ? 100 : 140 }\r\n            })}\r\n          </Box>\r\n        </CardContent>\r\n      </Card>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\n// Memoized color functions to prevent recalculation\r\nconst getStatusColor = (status, theme) => {\r\n  const statusColors = {\r\n    'New': theme.palette.info.main,\r\n    'Assigned': theme.palette.warning.main,\r\n    'In Progress': theme.palette.warning.dark,\r\n    'Resolved': theme.palette.success.main,\r\n    'Rejected': theme.palette.error.main,\r\n  };\r\n  return statusColors[status] || theme.palette.grey[500];\r\n};\r\n\r\nconst getPriorityColor = (priority, theme) => {\r\n  const priorityColors = {\r\n    'Low': theme.palette.success.main,\r\n    'Medium': theme.palette.warning.main,\r\n    'High': theme.palette.error.main,\r\n    'Critical': theme.palette.error.dark,\r\n  };\r\n  return priorityColors[priority] || theme.palette.grey[500];\r\n};\r\n\r\n// Optimized timestamp formatting function\r\nconst formatTimestamp = (timestamp) => {\r\n  if (!timestamp) return 'N/A';\r\n\r\n  try {\r\n    const date = new Date(timestamp);\r\n    const now = new Date();\r\n    const diffInHours = Math.abs(now - date) / (1000 * 60 * 60);\r\n\r\n    // If less than 24 hours, show time\r\n    if (diffInHours < 24) {\r\n      return date.toLocaleTimeString('en-US', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: true\r\n      });\r\n    }\r\n    // If less than 7 days, show day and time\r\n    else if (diffInHours < 168) {\r\n      return date.toLocaleDateString('en-US', {\r\n        weekday: 'short',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: true\r\n      });\r\n    }\r\n    // Otherwise show full date and time\r\n    else {\r\n      return date.toLocaleDateString('en-US', {\r\n        month: 'short',\r\n        day: 'numeric',\r\n        year: 'numeric',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: true\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error('Error formatting timestamp:', error);\r\n    return 'Invalid date';\r\n  }\r\n};\r\n\r\nconst ActivityItem = React.memo(({ activity, index }) => {\r\n  const theme = useTheme();\r\n\r\n  // Use activityTimestamp if available, otherwise fall back to submissionDate\r\n  // The backend already handles this logic correctly\r\n  const displayTimestamp = activity.activityTimestamp || activity.submissionDate;\r\n\r\n  // Memoize formatted timestamp\r\n  const formattedTimestamp = useMemo(() => formatTimestamp(displayTimestamp), [displayTimestamp]);\r\n\r\n  // Memoize colors\r\n  const statusColor = useMemo(() => getStatusColor(activity.Status, theme), [activity.Status, theme]);\r\n  const priorityColor = useMemo(() => getPriorityColor(activity.Priority, theme), [activity.Priority, theme]);\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, x: -20 }}\r\n      animate={{ opacity: 1, x: 0 }}\r\n      transition={{\r\n        delay: Math.min(index * 0.05, 0.3), // Reduced delay for better performance\r\n        duration: 0.3, // Reduced duration\r\n        ease: \"easeOut\"\r\n      }}\r\n    >\r\n      <ListItem\r\n        sx={{\r\n          bgcolor: 'background.paper',\r\n          borderRadius: 2,\r\n          mb: 1,\r\n          boxShadow: 1,\r\n          '&:hover': {\r\n            bgcolor: 'action.hover',\r\n            transform: 'translateX(4px)',\r\n            transition: 'transform 0.15s ease', // Faster transition\r\n          },\r\n        }}\r\n      >\r\n        <ListItemIcon>\r\n          <StatusIcon sx={{ color: statusColor }} />\r\n        </ListItemIcon>\r\n        <ListItemText\r\n          primary={\r\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>\r\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, flex: 1 }}>\r\n                #{activity.ComplaintNumber} - {activity.description}\r\n              </Typography>\r\n              {activity.Priority && (\r\n                <Chip\r\n                  label={activity.Priority}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${priorityColor}15`,\r\n                    color: priorityColor,\r\n                    fontWeight: 500,\r\n                    fontSize: '0.7rem'\r\n                  }}\r\n                />\r\n              )}\r\n            </Box>\r\n          }\r\n          secondary={\r\n            <Box sx={{ mt: 0.5 }}>\r\n              {activity.activityDetails && (\r\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 0.5 }}>\r\n                  {activity.activityDetails}\r\n                </Typography>\r\n              )}\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>\r\n                <Chip\r\n                  label={activity.Status}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${statusColor}15`,\r\n                    color: statusColor,\r\n                    fontWeight: 500\r\n                  }}\r\n                />\r\n                {activity.Category && (\r\n                  <Chip\r\n                    label={activity.Category}\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ fontSize: '0.7rem' }}\r\n                  />\r\n                )}\r\n                <Typography variant=\"caption\" color=\"text.secondary\">\r\n                  {formattedTimestamp}\r\n                </Typography>\r\n              </Box>\r\n            </Box>\r\n          }\r\n        />\r\n      </ListItem>\r\n    </motion.div>\r\n  );\r\n});\r\n\r\nfunction Dashboard() {\r\n  const [stats, setStats] = useState(null);\r\n  const [activities, setActivities] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\n  const fetchDashboardData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      const [statsResponse, activitiesResponse] = await Promise.all([\r\n        axios.get('/api/dashboard/stats'),\r\n        axios.get('/api/dashboard/recent-activities')\r\n      ]);\r\n      setStats(statsResponse.data);\r\n      setActivities(activitiesResponse.data);\r\n      setError(null);\r\n    } catch (err) {\r\n      console.error('Error fetching dashboard data:', err);\r\n      setError('Failed to load dashboard data. Please try again later.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n  }, [fetchDashboardData]);\r\n\r\n  const statCards = useMemo(() => [\r\n    {\r\n      title: 'Total Complaints',\r\n      value: stats?.totalComplaints || 0,\r\n      icon: <ComplaintsIcon />,\r\n      color: 'primary'\r\n    },\r\n    {\r\n      title: 'Resolved',\r\n      value: stats?.resolvedComplaints || 0,\r\n      icon: <ResolvedIcon />,\r\n      color: 'success'\r\n    },\r\n    {\r\n      title: 'Pending',\r\n      value: stats?.pendingComplaints || 0,\r\n      icon: <PendingIcon />,\r\n      color: 'warning'\r\n    },\r\n    {\r\n      title: 'High Priority',\r\n      value: stats?.highPriorityComplaints || 0,\r\n      icon: <HighPriorityIcon />,\r\n      color: 'error'\r\n    }\r\n  ], [stats]);\r\n\r\n  const monthlyStatCards = useMemo(() => {\r\n    if (!stats?.monthlyStats) return [];\r\n\r\n    return [\r\n      {\r\n        title: 'This Month',\r\n        value: stats.monthlyStats.totalMonthlyComplaints || 0,\r\n        icon: <ComplaintsIcon />,\r\n        color: 'info',\r\n        subtitle: 'New complaints'\r\n      },\r\n      {\r\n        title: 'Resolution Rate',\r\n        value: `${stats.monthlyStats.resolutionRate || 0}%`,\r\n        icon: <ResolvedIcon />,\r\n        color: 'success',\r\n        subtitle: 'Monthly average'\r\n      },\r\n      {\r\n        title: 'Response Time',\r\n        value: `${stats.monthlyStats.responseEfficiency || 0}%`,\r\n        icon: <TimeIcon />,\r\n        color: 'warning',\r\n        subtitle: 'Within 24h'\r\n      },\r\n      {\r\n        title: 'Avg Resolution',\r\n        value: `${Math.round(stats.monthlyStats.avgResolutionHours || 0)}h`,\r\n        icon: <ResolutionIcon />,\r\n        color: 'primary',\r\n        subtitle: 'Hours to resolve'\r\n      }\r\n    ];\r\n  }, [stats]);\r\n\r\n  return (\r\n    <Box sx={{ p: { xs: 2, sm: 3 } }}>\r\n      <Typography variant=\"h4\" sx={{ mb: 4, fontWeight: 600 }}>\r\n        Dashboard\r\n      </Typography>\r\n\r\n      {error && (\r\n        <Alert \r\n          severity=\"error\" \r\n          sx={{ mb: 3 }}\r\n          action={\r\n            <motion.div whileHover={{ scale: 1.05 }}>\r\n              <Button color=\"inherit\" size=\"small\" onClick={fetchDashboardData}>\r\n                Retry\r\n              </Button>\r\n            </motion.div>\r\n          }\r\n        >\r\n          {error}\r\n        </Alert>\r\n      )}\r\n\r\n      <Grid container spacing={3}>\r\n        {statCards.map((card, index) => (\r\n          <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n            <StatCard {...card} loading={loading} />\r\n          </Grid>\r\n        ))}\r\n\r\n        {/* Monthly Statistics Section */}\r\n        {monthlyStatCards.length > 0 && (\r\n          <Grid item xs={12}>\r\n            <Card\r\n              component={motion.div}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.2, duration: 0.3 }}\r\n              sx={{\r\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n                color: 'white',\r\n                overflow: 'visible',\r\n                position: 'relative',\r\n                '&::before': {\r\n                  content: '\"\"',\r\n                  position: 'absolute',\r\n                  top: 0,\r\n                  left: 0,\r\n                  right: 0,\r\n                  bottom: 0,\r\n                  background: 'rgba(255,255,255,0.1)',\r\n                  backdropFilter: 'blur(10px)',\r\n                  borderRadius: 'inherit',\r\n                  zIndex: 0\r\n                }\r\n              }}\r\n            >\r\n              <CardContent sx={{ position: 'relative', zIndex: 1 }}>\r\n                <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600, color: 'white' }}>\r\n                  📊 Monthly Performance Insights\r\n                </Typography>\r\n                <Grid container spacing={3}>\r\n                  {monthlyStatCards.map((card, index) => (\r\n                    <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n                      <MonthlyStatCard {...card} loading={loading} index={index} />\r\n                    </Grid>\r\n                  ))}\r\n                </Grid>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n        )}\r\n\r\n        <Grid item xs={12}>\r\n          <Card\r\n            component={motion.div}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4 }}\r\n            sx={{ \r\n              overflow: 'visible',\r\n              height: '100%',\r\n              minHeight: 400\r\n            }}\r\n          >\r\n            <CardContent>\r\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\r\n                Recent Activities\r\n              </Typography>\r\n              {loading ? (\r\n                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>\r\n                  <CircularProgress />\r\n                </Box>\r\n              ) : activities.length > 0 ? (\r\n                <AnimatePresence>\r\n                  <List>\r\n                    {activities.map((activity, index) => (\r\n                      <ActivityItem \r\n                        key={activity.ComplaintId} \r\n                        activity={activity} \r\n                        index={index}\r\n                      />\r\n                    ))}\r\n                  </List>\r\n                </AnimatePresence>\r\n              ) : (\r\n                <Box \r\n                  sx={{ \r\n                    textAlign: 'center', \r\n                    py: 4,\r\n                    color: 'text.secondary'\r\n                  }}\r\n                >\r\n                  <Typography>No recent activities</Typography>\r\n                </Box>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default Dashboard; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,cAAc,EACdC,aAAa,EACbC,MAAM,QACD,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,YAAY,EAC3BC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,gBAAgB,EACzBC,iBAAiB,IAAIC,UAAU,EAC/BC,QAAQ,IAAIC,QAAQ,EACpBC,KAAK,IAAIC,cAAc,EACvBC,QAAQ,IAAIC,SAAS,EACrBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrE,MAAMC,KAAK,GAAG5C,QAAQ,CAAC,CAAC;EACxB,MAAM6C,QAAQ,GAAGpC,aAAa,CAACmC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,oBACEZ,OAAA,CAACN,MAAM,CAACmB,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MACVC,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAE,GAAG;MACdC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE;IACZ,CAAE;IACFC,UAAU,EAAE;MACVC,KAAK,EAAE,IAAI;MACXN,UAAU,EAAE;QAAEI,QAAQ,EAAE;MAAI;IAC9B,CAAE;IACFG,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAAAE,QAAA,eAE1B1B,OAAA,CAACxC,IAAI;MACHmE,EAAE,EAAE;QACFC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,2BAA2BpB,KAAK,CAACqB,OAAO,CAACzB,KAAK,CAAC,CAAC0B,IAAI,QAAQtB,KAAK,CAACqB,OAAO,CAACzB,KAAK,CAAC,CAAC2B,IAAI,QAAQ;QACzG3B,KAAK,EAAE,OAAO;QACd4B,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE1B,KAAK,CAAC2B,OAAO,CAAC9B,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;QACzCY,UAAU,EAAE,uCAAuC;QACnD,SAAS,EAAE;UACTiB,SAAS,EAAE1B,KAAK,CAAC2B,OAAO,CAAC,CAAC,CAAC;UAC3BC,SAAS,EAAE;QACb;MACF,CAAE;MAAAX,QAAA,eAEF1B,OAAA,CAACvC,WAAW;QAACkE,EAAE,EAAE;UACfW,CAAC,EAAE5B,QAAQ,GAAG,CAAC,GAAG,CAAC;UACnBkB,MAAM,EAAE,MAAM;UACdW,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,cAAc,EAAE;QAClB,CAAE;QAAAf,QAAA,gBACA1B,OAAA,CAAC1C,GAAG;UAACqE,EAAE,EAAE;YACPM,QAAQ,EAAE,UAAU;YACpBS,MAAM,EAAE,CAAC;YACTH,OAAO,EAAE,MAAM;YACfI,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE;UACP,CAAE;UAAAlB,QAAA,gBACA1B,OAAA,CAACN,MAAM,CAACmB,GAAG;YACTC,OAAO,EAAE;cAAEU,KAAK,EAAE;YAAE,CAAE;YACtBP,OAAO,EAAE;cAAEO,KAAK,EAAE;YAAE,CAAE;YACtBN,UAAU,EAAE;cAAE2B,KAAK,EAAE,GAAG;cAAE1B,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAAAM,QAAA,eAE1DzE,KAAK,CAAC6F,YAAY,CAAC1C,IAAI,EAAE;cACxBuB,EAAE,EAAE;gBACFoB,QAAQ,EAAErC,QAAQ,GAAG,EAAE,GAAG,EAAE;gBAC5BK,OAAO,EAAE,GAAG;gBACZiC,MAAM,EAAE;cACV;YACF,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACbpD,OAAA,CAAC1C,GAAG;YAAAoE,QAAA,gBACF1B,OAAA,CAACtC,UAAU;cACT2F,OAAO,EAAE3C,QAAQ,GAAG,IAAI,GAAG,IAAK;cAChC4C,SAAS,EAAC,KAAK;cACf3B,EAAE,EAAE;gBACF4B,UAAU,EAAE,GAAG;gBACfC,UAAU,EAAE,GAAG;gBACfC,EAAE,EAAE,GAAG;gBACPC,UAAU,EAAE;cACd,CAAE;cAAAhC,QAAA,EAEDpB,OAAO,gBACNN,OAAA,CAACN,MAAM,CAACmB,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE;gBAAE,CAAE;gBACxBE,OAAO,EAAE;kBAAEF,OAAO,EAAE;gBAAE,CAAE;gBACxBG,UAAU,EAAE;kBAAEI,QAAQ,EAAE;gBAAI,CAAE;gBAAAI,QAAA,eAE9B1B,OAAA,CAACrC,gBAAgB;kBAACgG,IAAI,EAAEjD,QAAQ,GAAG,EAAE,GAAG,EAAG;kBAACL,KAAK,EAAC;gBAAS;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,gBAEbpD,OAAA,CAACN,MAAM,CAACmB,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,UAAU,EAAE;kBAAE2B,KAAK,EAAE,GAAG;kBAAEvB,QAAQ,EAAE;gBAAI,CAAE;gBAAAI,QAAA,EAEzCvB;cAAK;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eACbpD,OAAA,CAACtC,UAAU;cACT2F,OAAO,EAAE3C,QAAQ,GAAG,OAAO,GAAG,OAAQ;cACtCiB,EAAE,EAAE;gBACFZ,OAAO,EAAE,GAAG;gBACZwC,UAAU,EAAE,GAAG;gBACfK,aAAa,EAAE,OAAO;gBACtBF,UAAU,EAAE;cACd,CAAE;cAAAhC,QAAA,EAEDxB;YAAK;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACZ7C,QAAQ,iBACPP,OAAA,CAACtC,UAAU;cACT2F,OAAO,EAAC,SAAS;cACjB1B,EAAE,EAAE;gBACFZ,OAAO,EAAE,GAAG;gBACZwC,UAAU,EAAE,GAAG;gBACfG,UAAU,EAAE,6BAA6B;gBACzCnB,OAAO,EAAE;cACX,CAAE;cAAAb,QAAA,EAEDnB;YAAQ;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpD,OAAA,CAAC1C,GAAG;UACFgG,SAAS,EAAE5D,MAAM,CAACmB,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,KAAK,EAAE;UAAI,CAAE;UACpCP,OAAO,EAAE;YAAEF,OAAO,EAAE,IAAI;YAAES,KAAK,EAAE;UAAE,CAAE;UACrCN,UAAU,EAAE;YAAE2B,KAAK,EAAE,GAAG;YAAEvB,QAAQ,EAAE;UAAI,CAAE;UAC1CK,EAAE,EAAE;YACFM,QAAQ,EAAE,UAAU;YACpB4B,KAAK,EAAE,CAAC,EAAE;YACVC,MAAM,EAAE,CAAC,EAAE;YACXd,MAAM,EAAE;UACV,CAAE;UAAAtB,QAAA,eAEDzE,KAAK,CAAC6F,YAAY,CAAC1C,IAAI,EAAE;YACxBuB,EAAE,EAAE;cAAEoB,QAAQ,EAAErC,QAAQ,GAAG,GAAG,GAAG;YAAI;UACvC,CAAC;QAAC;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEjB,CAAC;;AAED;AAAA5C,EAAA,CA3IMP,QAAQ;EAAA,QACEpC,QAAQ,EACLS,aAAa;AAAA;AAAAyF,EAAA,GAF1B9D,QAAQ;AA4Id,MAAM+D,cAAc,GAAGA,CAACC,MAAM,EAAExD,KAAK,KAAK;EACxC,MAAMyD,YAAY,GAAG;IACnB,KAAK,EAAEzD,KAAK,CAACqB,OAAO,CAACqC,IAAI,CAACpC,IAAI;IAC9B,UAAU,EAAEtB,KAAK,CAACqB,OAAO,CAACsC,OAAO,CAACrC,IAAI;IACtC,aAAa,EAAEtB,KAAK,CAACqB,OAAO,CAACsC,OAAO,CAACpC,IAAI;IACzC,UAAU,EAAEvB,KAAK,CAACqB,OAAO,CAACuC,OAAO,CAACtC,IAAI;IACtC,UAAU,EAAEtB,KAAK,CAACqB,OAAO,CAACwC,KAAK,CAACvC;EAClC,CAAC;EACD,OAAOmC,YAAY,CAACD,MAAM,CAAC,IAAIxD,KAAK,CAACqB,OAAO,CAACyC,IAAI,CAAC,GAAG,CAAC;AACxD,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAACC,QAAQ,EAAEhE,KAAK,KAAK;EAC5C,MAAMiE,cAAc,GAAG;IACrB,KAAK,EAAEjE,KAAK,CAACqB,OAAO,CAACuC,OAAO,CAACtC,IAAI;IACjC,QAAQ,EAAEtB,KAAK,CAACqB,OAAO,CAACsC,OAAO,CAACrC,IAAI;IACpC,MAAM,EAAEtB,KAAK,CAACqB,OAAO,CAACwC,KAAK,CAACvC,IAAI;IAChC,UAAU,EAAEtB,KAAK,CAACqB,OAAO,CAACwC,KAAK,CAACtC;EAClC,CAAC;EACD,OAAO0C,cAAc,CAACD,QAAQ,CAAC,IAAIhE,KAAK,CAACqB,OAAO,CAACyC,IAAI,CAAC,GAAG,CAAC;AAC5D,CAAC;;AAED;AACA,MAAMI,eAAe,GAAIC,SAAS,IAAK;EACrC,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;EAE5B,IAAI;IACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAChC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;;IAE3D;IACA,IAAIG,WAAW,GAAG,EAAE,EAAE;MACpB,OAAOH,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IAAIN,WAAW,GAAG,GAAG,EAAE;MAC1B,OAAOH,IAAI,CAACU,kBAAkB,CAAC,OAAO,EAAE;QACtCC,OAAO,EAAE,OAAO;QAChBJ,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACA;IAAA,KACK;MACH,OAAOT,IAAI,CAACU,kBAAkB,CAAC,OAAO,EAAE;QACtCE,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfP,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;IACdsB,OAAO,CAACtB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,cAAc;EACvB;AACF,CAAC;AAED,MAAMuB,YAAY,gBAAAC,GAAA,cAAG7I,KAAK,CAAC8I,IAAI,CAAAC,GAAA,GAAAF,GAAA,CAAC,CAAC;EAAEG,QAAQ;EAAEC;AAAM,CAAC,KAAK;EAAAJ,GAAA;EACvD,MAAMrF,KAAK,GAAG5C,QAAQ,CAAC,CAAC;;EAExB;EACA;EACA,MAAMsI,gBAAgB,GAAGF,QAAQ,CAACG,iBAAiB,IAAIH,QAAQ,CAACI,cAAc;;EAE9E;EACA,MAAMC,kBAAkB,GAAGjJ,OAAO,CAAC,MAAMsH,eAAe,CAACwB,gBAAgB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAE/F;EACA,MAAMI,WAAW,GAAGlJ,OAAO,CAAC,MAAM2G,cAAc,CAACiC,QAAQ,CAACO,MAAM,EAAE/F,KAAK,CAAC,EAAE,CAACwF,QAAQ,CAACO,MAAM,EAAE/F,KAAK,CAAC,CAAC;EACnG,MAAMgG,aAAa,GAAGpJ,OAAO,CAAC,MAAMmH,gBAAgB,CAACyB,QAAQ,CAACS,QAAQ,EAAEjG,KAAK,CAAC,EAAE,CAACwF,QAAQ,CAACS,QAAQ,EAAEjG,KAAK,CAAC,CAAC;EAE3G,oBACET,OAAA,CAACN,MAAM,CAACmB,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAE4F,CAAC,EAAE,CAAC;IAAG,CAAE;IAChC1F,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAE4F,CAAC,EAAE;IAAE,CAAE;IAC9BzF,UAAU,EAAE;MACV2B,KAAK,EAAEoC,IAAI,CAAC2B,GAAG,CAACV,KAAK,GAAG,IAAI,EAAE,GAAG,CAAC;MAAE;MACpC5E,QAAQ,EAAE,GAAG;MAAE;MACfuF,IAAI,EAAE;IACR,CAAE;IAAAnF,QAAA,eAEF1B,OAAA,CAACjC,QAAQ;MACP4D,EAAE,EAAE;QACFmF,OAAO,EAAE,kBAAkB;QAC3BC,YAAY,EAAE,CAAC;QACftD,EAAE,EAAE,CAAC;QACLtB,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE;UACT2E,OAAO,EAAE,cAAc;UACvBzE,SAAS,EAAE,iBAAiB;UAC5BnB,UAAU,EAAE,sBAAsB,CAAE;QACtC;MACF,CAAE;MAAAQ,QAAA,gBAEF1B,OAAA,CAAC/B,YAAY;QAAAyD,QAAA,eACX1B,OAAA,CAACf,UAAU;UAAC0C,EAAE,EAAE;YAAEtB,KAAK,EAAEkG;UAAY;QAAE;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACfpD,OAAA,CAAChC,YAAY;QACXgJ,OAAO,eACLhH,OAAA,CAAC1C,GAAG;UAACqE,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEI,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE,CAAC;YAAEa,EAAE,EAAE;UAAI,CAAE;UAAA/B,QAAA,gBAClE1B,OAAA,CAACtC,UAAU;YAAC2F,OAAO,EAAC,WAAW;YAAC1B,EAAE,EAAE;cAAE4B,UAAU,EAAE,GAAG;cAAE0D,IAAI,EAAE;YAAE,CAAE;YAAAvF,QAAA,GAAC,GAC/D,EAACuE,QAAQ,CAACiB,eAAe,EAAC,KAAG,EAACjB,QAAQ,CAACkB,WAAW;UAAA;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EACZ6C,QAAQ,CAACS,QAAQ,iBAChB1G,OAAA,CAAC5B,IAAI;YACHgJ,KAAK,EAAEnB,QAAQ,CAACS,QAAS;YACzB/C,IAAI,EAAC,OAAO;YACZhC,EAAE,EAAE;cACFmF,OAAO,EAAE,GAAGL,aAAa,IAAI;cAC7BpG,KAAK,EAAEoG,aAAa;cACpBlD,UAAU,EAAE,GAAG;cACfR,QAAQ,EAAE;YACZ;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACDiE,SAAS,eACPrH,OAAA,CAAC1C,GAAG;UAACqE,EAAE,EAAE;YAAE2F,EAAE,EAAE;UAAI,CAAE;UAAA5F,QAAA,GAClBuE,QAAQ,CAACsB,eAAe,iBACvBvH,OAAA,CAACtC,UAAU;YAAC2F,OAAO,EAAC,OAAO;YAAChD,KAAK,EAAC,gBAAgB;YAACsB,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAI,CAAE;YAAA/B,QAAA,EAChEuE,QAAQ,CAACsB;UAAe;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACb,eACDpD,OAAA,CAAC1C,GAAG;YAACqE,EAAE,EAAE;cAAEY,OAAO,EAAE,MAAM;cAAEI,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE,CAAC;cAAE4E,QAAQ,EAAE;YAAO,CAAE;YAAA9F,QAAA,gBAC3E1B,OAAA,CAAC5B,IAAI;cACHgJ,KAAK,EAAEnB,QAAQ,CAACO,MAAO;cACvB7C,IAAI,EAAC,OAAO;cACZhC,EAAE,EAAE;gBACFmF,OAAO,EAAE,GAAGP,WAAW,IAAI;gBAC3BlG,KAAK,EAAEkG,WAAW;gBAClBhD,UAAU,EAAE;cACd;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACD6C,QAAQ,CAACwB,QAAQ,iBAChBzH,OAAA,CAAC5B,IAAI;cACHgJ,KAAK,EAAEnB,QAAQ,CAACwB,QAAS;cACzB9D,IAAI,EAAC,OAAO;cACZN,OAAO,EAAC,UAAU;cAClB1B,EAAE,EAAE;gBAAEoB,QAAQ,EAAE;cAAS;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACF,eACDpD,OAAA,CAACtC,UAAU;cAAC2F,OAAO,EAAC,SAAS;cAAChD,KAAK,EAAC,gBAAgB;cAAAqB,QAAA,EACjD4E;YAAkB;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEjB,CAAC;EAAA,QA9FevF,QAAQ;AAAA,EA8FvB,CAAC;EAAA,QA9FcA,QAAQ;AAAA,EA8FtB;AAAC6J,GAAA,GA/FG7B,YAAY;AAiGlB,SAAS8B,SAASA,CAAA,EAAG;EAAAC,GAAA;EACnB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5K,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6K,UAAU,EAAEC,aAAa,CAAC,GAAG9K,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoD,OAAO,EAAE2H,UAAU,CAAC,GAAG/K,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoH,KAAK,EAAE4D,QAAQ,CAAC,GAAGhL,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAMuD,KAAK,GAAG5C,QAAQ,CAAC,CAAC;EACxB,MAAM6C,QAAQ,GAAGpC,aAAa,CAACmC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,MAAMuH,kBAAkB,GAAG/K,WAAW,CAAC,YAAY;IACjD,IAAI;MACF6K,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACG,aAAa,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5D3I,KAAK,CAAC4I,GAAG,CAAC,sBAAsB,CAAC,EACjC5I,KAAK,CAAC4I,GAAG,CAAC,kCAAkC,CAAC,CAC9C,CAAC;MACFV,QAAQ,CAACM,aAAa,CAACK,IAAI,CAAC;MAC5BT,aAAa,CAACK,kBAAkB,CAACI,IAAI,CAAC;MACtCP,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZ9C,OAAO,CAACtB,KAAK,CAAC,gCAAgC,EAAEoE,GAAG,CAAC;MACpDR,QAAQ,CAAC,wDAAwD,CAAC;IACpE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN9K,SAAS,CAAC,MAAM;IACdgL,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,MAAMQ,SAAS,GAAGtL,OAAO,CAAC,MAAM,CAC9B;IACE6C,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,CAAA0H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEe,eAAe,KAAI,CAAC;IAClCxI,IAAI,eAAEJ,OAAA,CAACvB,cAAc;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxB/C,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CAAA0H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,kBAAkB,KAAI,CAAC;IACrCzI,IAAI,eAAEJ,OAAA,CAACrB,YAAY;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtB/C,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAA0H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,iBAAiB,KAAI,CAAC;IACpC1I,IAAI,eAAEJ,OAAA,CAACnB,WAAW;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrB/C,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,CAAA0H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,sBAAsB,KAAI,CAAC;IACzC3I,IAAI,eAAEJ,OAAA,CAACjB,gBAAgB;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1B/C,KAAK,EAAE;EACT,CAAC,CACF,EAAE,CAACwH,KAAK,CAAC,CAAC;EAEX,MAAMmB,gBAAgB,GAAG3L,OAAO,CAAC,MAAM;IACrC,IAAI,EAACwK,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEoB,YAAY,GAAE,OAAO,EAAE;IAEnC,OAAO,CACL;MACE/I,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE0H,KAAK,CAACoB,YAAY,CAACC,sBAAsB,IAAI,CAAC;MACrD9I,IAAI,eAAEJ,OAAA,CAACvB,cAAc;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxB/C,KAAK,EAAE,MAAM;MACbE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,GAAG0H,KAAK,CAACoB,YAAY,CAACE,cAAc,IAAI,CAAC,GAAG;MACnD/I,IAAI,eAAEJ,OAAA,CAACrB,YAAY;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtB/C,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,GAAG0H,KAAK,CAACoB,YAAY,CAACG,kBAAkB,IAAI,CAAC,GAAG;MACvDhJ,IAAI,eAAEJ,OAAA,CAACb,QAAQ;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClB/C,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,GAAG8E,IAAI,CAACoE,KAAK,CAACxB,KAAK,CAACoB,YAAY,CAACK,kBAAkB,IAAI,CAAC,CAAC,GAAG;MACnElJ,IAAI,eAAEJ,OAAA,CAACP,cAAc;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxB/C,KAAK,EAAE,SAAS;MAChBE,QAAQ,EAAE;IACZ,CAAC,CACF;EACH,CAAC,EAAE,CAACsH,KAAK,CAAC,CAAC;EAEX,oBACE7H,OAAA,CAAC1C,GAAG;IAACqE,EAAE,EAAE;MAAEW,CAAC,EAAE;QAAEiH,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAAE,CAAE;IAAA9H,QAAA,gBAC/B1B,OAAA,CAACtC,UAAU;MAAC2F,OAAO,EAAC,IAAI;MAAC1B,EAAE,EAAE;QAAE8B,EAAE,EAAE,CAAC;QAAEF,UAAU,EAAE;MAAI,CAAE;MAAA7B,QAAA,EAAC;IAEzD;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZkB,KAAK,iBACJtE,OAAA,CAACpC,KAAK;MACJ6L,QAAQ,EAAC,OAAO;MAChB9H,EAAE,EAAE;QAAE8B,EAAE,EAAE;MAAE,CAAE;MACdiG,MAAM,eACJ1J,OAAA,CAACN,MAAM,CAACmB,GAAG;QAACU,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAAAE,QAAA,eACtC1B,OAAA,CAACzB,MAAM;UAAC8B,KAAK,EAAC,SAAS;UAACsD,IAAI,EAAC,OAAO;UAACgG,OAAO,EAAExB,kBAAmB;UAAAzG,QAAA,EAAC;QAElE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb;MAAA1B,QAAA,EAEA4C;IAAK;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAEDpD,OAAA,CAACzC,IAAI;MAACqM,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnI,QAAA,GACxBiH,SAAS,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAE7D,KAAK,kBACzBlG,OAAA,CAACzC,IAAI;QAACyM,IAAI;QAACT,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACS,EAAE,EAAE,CAAE;QAAAvI,QAAA,eAC9B1B,OAAA,CAACC,QAAQ;UAAA,GAAK8J,IAAI;UAAEzJ,OAAO,EAAEA;QAAQ;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GADJ2G,IAAI,CAAC7J,KAAK;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE1C,CACP,CAAC,EAGD4F,gBAAgB,CAACkB,MAAM,GAAG,CAAC,iBAC1BlK,OAAA,CAACzC,IAAI;QAACyM,IAAI;QAACT,EAAE,EAAE,EAAG;QAAA7H,QAAA,eAChB1B,OAAA,CAACxC,IAAI;UACH8F,SAAS,EAAE5D,MAAM,CAACmB,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAE2B,KAAK,EAAE,GAAG;YAAEvB,QAAQ,EAAE;UAAI,CAAE;UAC1CK,EAAE,EAAE;YACFE,UAAU,EAAE,mDAAmD;YAC/DxB,KAAK,EAAE,OAAO;YACd6B,QAAQ,EAAE,SAAS;YACnBD,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE;cACXkI,OAAO,EAAE,IAAI;cACblI,QAAQ,EAAE,UAAU;cACpBmI,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPxG,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTjC,UAAU,EAAE,uBAAuB;cACnCyI,cAAc,EAAE,YAAY;cAC5BvD,YAAY,EAAE,SAAS;cACvBrE,MAAM,EAAE;YACV;UACF,CAAE;UAAAhB,QAAA,eAEF1B,OAAA,CAACvC,WAAW;YAACkE,EAAE,EAAE;cAAEM,QAAQ,EAAE,UAAU;cAAES,MAAM,EAAE;YAAE,CAAE;YAAAhB,QAAA,gBACnD1B,OAAA,CAACtC,UAAU;cAAC2F,OAAO,EAAC,IAAI;cAAC1B,EAAE,EAAE;gBAAE8B,EAAE,EAAE,CAAC;gBAAEF,UAAU,EAAE,GAAG;gBAAElD,KAAK,EAAE;cAAQ,CAAE;cAAAqB,QAAA,EAAC;YAEzE;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpD,OAAA,CAACzC,IAAI;cAACqM,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnI,QAAA,EACxBsH,gBAAgB,CAACc,GAAG,CAAC,CAACC,IAAI,EAAE7D,KAAK,kBAChClG,OAAA,CAACzC,IAAI;gBAACyM,IAAI;gBAACT,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAACS,EAAE,EAAE,CAAE;gBAAAvI,QAAA,eAC9B1B,OAAA,CAACuK,eAAe;kBAAA,GAAKR,IAAI;kBAAEzJ,OAAO,EAAEA,OAAQ;kBAAC4F,KAAK,EAAEA;gBAAM;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC,GADzB2G,IAAI,CAAC7J,KAAK;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE1C,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,eAEDpD,OAAA,CAACzC,IAAI;QAACyM,IAAI;QAACT,EAAE,EAAE,EAAG;QAAA7H,QAAA,eAChB1B,OAAA,CAACxC,IAAI;UACH8F,SAAS,EAAE5D,MAAM,CAACmB,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAE2B,KAAK,EAAE;UAAI,CAAE;UAC3BlB,EAAE,EAAE;YACFO,QAAQ,EAAE,SAAS;YACnBN,MAAM,EAAE,MAAM;YACd4I,SAAS,EAAE;UACb,CAAE;UAAA9I,QAAA,eAEF1B,OAAA,CAACvC,WAAW;YAAAiE,QAAA,gBACV1B,OAAA,CAACtC,UAAU;cAAC2F,OAAO,EAAC,IAAI;cAAC1B,EAAE,EAAE;gBAAE8B,EAAE,EAAE,CAAC;gBAAEF,UAAU,EAAE;cAAI,CAAE;cAAA7B,QAAA,EAAC;YAEzD;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ9C,OAAO,gBACNN,OAAA,CAAC1C,GAAG;cAACqE,EAAE,EAAE;gBAAEY,OAAO,EAAE,MAAM;gBAAEE,cAAc,EAAE,QAAQ;gBAAEH,CAAC,EAAE;cAAE,CAAE;cAAAZ,QAAA,eAC3D1B,OAAA,CAACrC,gBAAgB;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,GACJ2E,UAAU,CAACmC,MAAM,GAAG,CAAC,gBACvBlK,OAAA,CAACL,eAAe;cAAA+B,QAAA,eACd1B,OAAA,CAAClC,IAAI;gBAAA4D,QAAA,EACFqG,UAAU,CAAC+B,GAAG,CAAC,CAAC7D,QAAQ,EAAEC,KAAK,kBAC9BlG,OAAA,CAAC6F,YAAY;kBAEXI,QAAQ,EAAEA,QAAS;kBACnBC,KAAK,EAAEA;gBAAM,GAFRD,QAAQ,CAACwE,WAAW;kBAAAxH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAG1B,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,gBAElBpD,OAAA,CAAC1C,GAAG;cACFqE,EAAE,EAAE;gBACF+I,SAAS,EAAE,QAAQ;gBACnBC,EAAE,EAAE,CAAC;gBACLtK,KAAK,EAAE;cACT,CAAE;cAAAqB,QAAA,eAEF1B,OAAA,CAACtC,UAAU;gBAAAgE,QAAA,EAAC;cAAoB;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACwE,GAAA,CArNQD,SAAS;EAAA,QAKF9J,QAAQ,EACLS,aAAa;AAAA;AAAAsM,GAAA,GANvBjD,SAAS;AAuNlB,eAAeA,SAAS;AAAC,IAAA5D,EAAA,EAAAiC,GAAA,EAAA0B,GAAA,EAAAkD,GAAA;AAAAC,YAAA,CAAA9G,EAAA;AAAA8G,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}