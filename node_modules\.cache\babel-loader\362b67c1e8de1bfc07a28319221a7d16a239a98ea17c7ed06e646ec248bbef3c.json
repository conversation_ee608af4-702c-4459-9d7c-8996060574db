{"ast": null, "code": "import * as React from 'react';\nimport clsx from 'clsx';\nimport Fade from '@mui/material/Fade';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/material';\nimport { TransitionGroup } from 'react-transition-group';\nimport { getPickersFadeTransitionGroupUtilityClass } from './pickersFadeTransitionGroupClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getPickersFadeTransitionGroupUtilityClass, classes);\n};\nconst animationDuration = 500;\nconst PickersFadeTransitionGroupRoot = styled(TransitionGroup, {\n  name: 'MuiPickersFadeTransitionGroup',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})({\n  display: 'block',\n  position: 'relative'\n});\n/**\n * @ignore - do not document.\n */\n\nexport function PickersFadeTransitionGroup(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersFadeTransitionGroup'\n  });\n  const {\n    children,\n    className,\n    reduceAnimations,\n    transKey\n  } = props;\n  const classes = useUtilityClasses(props);\n  if (reduceAnimations) {\n    return children;\n  }\n  return /*#__PURE__*/_jsx(PickersFadeTransitionGroupRoot, {\n    className: clsx(classes.root, className),\n    children: /*#__PURE__*/_jsx(Fade, {\n      appear: false,\n      mountOnEnter: true,\n      unmountOnExit: true,\n      timeout: {\n        appear: animationDuration,\n        enter: animationDuration / 2,\n        exit: 0\n      },\n      children: children\n    }, transKey)\n  });\n}", "map": {"version": 3, "names": ["React", "clsx", "Fade", "styled", "useThemeProps", "unstable_composeClasses", "composeClasses", "TransitionGroup", "getPickersFadeTransitionGroupUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "animationDuration", "PickersFadeTransitionGroupRoot", "name", "slot", "overridesResolver", "_", "styles", "display", "position", "PickersFadeTransitionGroup", "inProps", "props", "children", "className", "reduceAnimations", "transKey", "appear", "mountOnEnter", "unmountOnExit", "timeout", "enter", "exit"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/CalendarPicker/PickersFadeTransitionGroup.js"], "sourcesContent": ["import * as React from 'react';\nimport clsx from 'clsx';\nimport Fade from '@mui/material/Fade';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/material';\nimport { TransitionGroup } from 'react-transition-group';\nimport { getPickersFadeTransitionGroupUtilityClass } from './pickersFadeTransitionGroupClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getPickersFadeTransitionGroupUtilityClass, classes);\n};\n\nconst animationDuration = 500;\nconst PickersFadeTransitionGroupRoot = styled(TransitionGroup, {\n  name: 'MuiPickersFadeTransitionGroup',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})({\n  display: 'block',\n  position: 'relative'\n});\n/**\n * @ignore - do not document.\n */\n\nexport function PickersFadeTransitionGroup(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersFadeTransitionGroup'\n  });\n  const {\n    children,\n    className,\n    reduceAnimations,\n    transKey\n  } = props;\n  const classes = useUtilityClasses(props);\n\n  if (reduceAnimations) {\n    return children;\n  }\n\n  return /*#__PURE__*/_jsx(PickersFadeTransitionGroupRoot, {\n    className: clsx(classes.root, className),\n    children: /*#__PURE__*/_jsx(Fade, {\n      appear: false,\n      mountOnEnter: true,\n      unmountOnExit: true,\n      timeout: {\n        appear: animationDuration,\n        enter: animationDuration / 2,\n        exit: 0\n      },\n      children: children\n    }, transKey)\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,eAAe;AACzE,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,yCAAyC,QAAQ,qCAAqC;AAC/F,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAE/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOT,cAAc,CAACQ,KAAK,EAAEN,yCAAyC,EAAEK,OAAO,CAAC;AAClF,CAAC;AAED,MAAMG,iBAAiB,GAAG,GAAG;AAC7B,MAAMC,8BAA8B,GAAGd,MAAM,CAACI,eAAe,EAAE;EAC7DW,IAAI,EAAE,+BAA+B;EACrCC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC3C,CAAC,CAAC,CAAC;EACDQ,OAAO,EAAE,OAAO;EAChBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF;AACA;AACA;;AAEA,OAAO,SAASC,0BAA0BA,CAACC,OAAO,EAAE;EAClD,MAAMC,KAAK,GAAGvB,aAAa,CAAC;IAC1BuB,KAAK,EAAED,OAAO;IACdR,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJU,QAAQ;IACRC,SAAS;IACTC,gBAAgB;IAChBC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMd,OAAO,GAAGF,iBAAiB,CAACgB,KAAK,CAAC;EAExC,IAAIG,gBAAgB,EAAE;IACpB,OAAOF,QAAQ;EACjB;EAEA,OAAO,aAAalB,IAAI,CAACO,8BAA8B,EAAE;IACvDY,SAAS,EAAE5B,IAAI,CAACY,OAAO,CAACE,IAAI,EAAEc,SAAS,CAAC;IACxCD,QAAQ,EAAE,aAAalB,IAAI,CAACR,IAAI,EAAE;MAChC8B,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBC,OAAO,EAAE;QACPH,MAAM,EAAEhB,iBAAiB;QACzBoB,KAAK,EAAEpB,iBAAiB,GAAG,CAAC;QAC5BqB,IAAI,EAAE;MACR,CAAC;MACDT,QAAQ,EAAEA;IACZ,CAAC,EAAEG,QAAQ;EACb,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}