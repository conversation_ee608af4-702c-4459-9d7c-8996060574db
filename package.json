{"name": "internal-complaints-portal", "version": "1.0.0", "description": "Internal Complaints Portal - A comprehensive system for managing internal complaints with React frontend and Node.js backend", "main": "server.js", "homepage": ".", "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject", "server": "node server.js", "dev": "nodemon server.js", "serve-build": "node serve-build.js", "prod": "npm run build && concurrently \"npm run server\" \"npm run serve-build\"", "debug": "node --inspect server.js", "start-dev": "node start.js", "start-prod": "start.bat", "tunnel": "tunnel.bat"}, "dependencies": {"@date-io/date-fns": "^2.17.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/lab": "^5.0.0-alpha.155", "@mui/material": "^5.14.20", "@mui/x-date-pickers": "^5.0.20", "ajv": "8.17.1", "ajv-keywords": "5.1.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "buffer": "^6.0.3", "concurrently": "^8.2.2", "cors": "^2.8.5", "crypto-browserify": "^3.12.0", "date-fns": "^2.30.0", "dotenv": "^16.5.0", "express": "^4.18.2", "framer-motion": "^10.16.5", "http-proxy-middleware": "^2.0.6", "jsonwebtoken": "^9.0.2", "mssql": "^10.0.1", "multer": "^2.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stream-browserify": "^3.0.0", "ws": "^8.18.2", "xlsx": "^0.18.5"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "nodemon": "^3.0.2", "react-app-rewired": "^2.2.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"extends": ["react-app"]}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["complaints", "portal", "internal", "management", "react", "nodejs", "express", "mssql"], "author": "Internal Development Team", "license": "UNLICENSED", "private": true}