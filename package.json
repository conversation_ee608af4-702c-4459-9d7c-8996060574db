{
  "name": "internal-complaints-portal",
  "version": "1.0.0",
  "description": "Internal Complaints Portal",
  "main": "server.js",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "server": "node server.js",
    "dev": "nodemon server.js",
    "serve-build": "node serve-build.js",
    "prod": "npm run build && concurrently \"npm run server\" \"npm run serve-build\"",
    "debug": "node --inspect server.js"
  },
  "dependencies": {
    "@date-io/date-fns": "^2.17.0",
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@mui/icons-material": "^5.14.19",
    "@mui/lab": "^5.0.0-alpha.155",
    "@mui/material": "^5.14.20",
    "@mui/x-date-pickers": "^5.0.20",
    "ajv": "8.17.1",
    "ajv-keywords": "5.1.0",
    "axios": "^1.6.2",
    "bcryptjs": "^2.4.3",
    "cors": "^2.8.5",
    "date-fns": "^2.30.0",
    "dotenv": "^16.5.0",
    "express": "^4.18.2",
    "framer-motion": "^10.16.5",
    "jsonwebtoken": "^9.0.2",
    "mssql": "^10.0.1",
    "multer": "^2.0.0",
    "path-browserify": "^1.0.1",
    "os-browserify": "^0.3.0",
    "crypto-browserify": "^3.12.0",
    "stream-browserify": "^3.0.0",
    "buffer": "^6.0.3",
    "http-proxy-middleware": "^2.0.6",
    "concurrently": "^8.2.2",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.20.1",
    "react-scripts": "5.0.1"
  },
  "devDependencies": {
    "eslint": "^8.55.0",
    "eslint-config-react-app": "^7.0.1",
    "nodemon": "^3.0.2"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "eslintConfig": {
    "extends": [
      "react-app"
    ]
  }
}{
  "name": "internal-complaints-portal",
  "version": "1.0.0",
  "description": "Internal Complaints Portal",
  "main": "server.js",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "server": "node server.js",
    "dev": "nodemon server.js",
    "serve-build": "node serve-build.js",
    "prod": "npm run build && concurrently \"npm run server\" \"npm run serve-build\"",
    "debug": "node --inspect server.js"
  },
  "dependencies": {
    "@date-io/date-fns": "^2.17.0",
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@mui/icons-material": "^5.14.19",
    "@mui/lab": "^5.0.0-alpha.155",
    "@mui/material": "^5.14.20",
    "@mui/x-date-pickers": "^5.0.20",
    "ajv": "8.17.1",
    "ajv-keywords": "5.1.0",
    "axios": "^1.6.2",
    "bcryptjs": "^2.4.3",
    "cors": "^2.8.5",
    "date-fns": "^2.30.0",
    "dotenv": "^16.5.0",
    "express": "^4.18.2",
    "framer-motion": "^10.16.5",
    "jsonwebtoken": "^9.0.2",
    "mssql": "^10.0.1",
    "multer": "^2.0.0",
    "path-browserify": "^1.0.1",
    "os-browserify": "^0.3.0",
    "crypto-browserify": "^3.12.0",
    "stream-browserify": "^3.0.0",
    "buffer": "^6.0.3",
    "http-proxy-middleware": "^2.0.6",
    "concurrently": "^8.2.2",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.20.1",
    "react-scripts": "5.0.1"
  },
  "devDependencies": {
    "eslint": "^8.55.0",
    "eslint-config-react-app": "^7.0.1",
    "nodemon": "^3.0.2"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "eslintConfig": {
    "extends": [
      "react-app"
    ]
  }
}{
  "name": "internal-complaints-portal",
  "version": "1.0.0",
  "description": "Internal Complaints Portal",
  "main": "server.js",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "server": "node server.js",
    "dev": "nodemon server.js",
    "serve-build": "node serve-build.js",
    "prod": "npm run build && concurrently \"npm run server\" \"npm run serve-build\"",
    "debug": "node --inspect server.js"
  },
  "dependencies": {
    "@date-io/date-fns": "^2.17.0",
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@mui/icons-material": "^5.14.19",
    "@mui/lab": "^5.0.0-alpha.155",
    "@mui/material": "^5.14.20",
    "@mui/x-date-pickers": "^5.0.20",
    "ajv": "8.17.1",
    "ajv-keywords": "5.1.0",
    "axios": "^1.6.2",
    "bcryptjs": "^2.4.3",
    "cors": "^2.8.5",
    "date-fns": "^2.30.0",
    "dotenv": "^16.5.0",
    "express": "^4.18.2",
    "framer-motion": "^10.16.5",
    "jsonwebtoken": "^9.0.2",
    "mssql": "^10.0.1",
    "multer": "^2.0.0",
    "path-browserify": "^1.0.1",
    "os-browserify": "^0.3.0",
    "crypto-browserify": "^3.12.0",
    "stream-browserify": "^3.0.0",
    "buffer": "^6.0.3",
    "http-proxy-middleware": "^2.0.6",
    "concurrently": "^8.2.2",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.20.1",
    "react-scripts": "5.0.1"
  },
  "devDependencies": {
    "eslint": "^8.55.0",
    "eslint-config-react-app": "^7.0.1",
    "nodemon": "^3.0.2"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "eslintConfig": {
    "extends": [
      "react-app"
    ]
  }
}{
  "name": "internal-complaints-portal",
  "version": "1.0.0",
  "description": "Internal Complaints Portal",
  "main": "server.js",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "server": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "@date-io/date-fns": "^2.17.0",
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@mui/icons-material": "^5.14.19",
    "@mui/lab": "^5.0.0-alpha.155",
    "@mui/material": "^5.14.20",
    "@mui/x-date-pickers": "^5.0.20",
    "ajv": "8.17.1",
    "ajv-keywords": "5.1.0",
    "axios": "^1.6.2",
    "bcryptjs": "^2.4.3",
    "cors": "^2.8.5",
    "date-fns": "^2.30.0",
    "dotenv": "^16.5.0",
    "express": "^4.18.2",
    "framer-motion": "^10.16.5",
    "jsonwebtoken": "^9.0.2",
    "mssql": "^10.0.1",
    "multer": "^2.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.20.1",
    "react-scripts": "5.0.1"
  },
  "devDependencies": {
    "eslint": "^8.55.0",
    "eslint-config-react-app": "^7.0.1",
    "nodemon": "^3.0.2"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "eslintConfig": {
    "extends": [
      "react-app"
    ]
  }
}
