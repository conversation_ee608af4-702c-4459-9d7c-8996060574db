{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ownerState\"];\nimport * as React from 'react';\nexport function prepareForSlot(Component) {\n  return /*#__PURE__*/React.forwardRef(function Slot(props, ref) {\n    const other = _objectWithoutPropertiesLoose(props, _excluded);\n    return /*#__PURE__*/React.createElement(Component, _extends({}, other, {\n      ref\n    }));\n  });\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "prepareForSlot", "Component", "forwardRef", "Slot", "props", "ref", "other", "createElement"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/utils/prepareForSlot.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ownerState\"];\nimport * as React from 'react';\nexport function prepareForSlot(Component) {\n  return /*#__PURE__*/React.forwardRef(function Slot(props, ref) {\n    const other = _objectWithoutPropertiesLoose(props, _excluded);\n    return /*#__PURE__*/React.createElement(Component, _extends({}, other, {\n      ref\n    }));\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,YAAY,CAAC;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,SAASC,cAAcA,CAACC,SAAS,EAAE;EACxC,OAAO,aAAaF,KAAK,CAACG,UAAU,CAAC,SAASC,IAAIA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC7D,MAAMC,KAAK,GAAGT,6BAA6B,CAACO,KAAK,EAAEN,SAAS,CAAC;IAC7D,OAAO,aAAaC,KAAK,CAACQ,aAAa,CAACN,SAAS,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,EAAE;MACrED;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}