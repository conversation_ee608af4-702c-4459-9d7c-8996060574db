{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Snackbar,Alert,Slide}from'@mui/material';import{jsx as _jsx}from\"react/jsx-runtime\";function SlideTransition(props){return/*#__PURE__*/_jsx(Slide,_objectSpread(_objectSpread({},props),{},{direction:\"down\"}));}const PermissionNotification=()=>{const[open,setOpen]=useState(false);const[message,setMessage]=useState('');useEffect(()=>{// Create a global function to show permission update notifications\nwindow.showPermissionUpdateNotification=msg=>{setMessage(msg);setOpen(true);};// Cleanup\nreturn()=>{delete window.showPermissionUpdateNotification;};},[]);const handleClose=(event,reason)=>{if(reason==='clickaway'){return;}setOpen(false);};return/*#__PURE__*/_jsx(Snackbar,{open:open,autoHideDuration:5000,onClose:handleClose,anchorOrigin:{vertical:'top',horizontal:'center'},TransitionComponent:SlideTransition,sx:{mt:8}// Add margin top to avoid overlapping with app bar\n,children:/*#__PURE__*/_jsx(Alert,{onClose:handleClose,severity:\"success\",variant:\"filled\",sx:{width:'100%'},children:message})});};export default PermissionNotification;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Snackbar", "<PERSON><PERSON>", "Slide", "jsx", "_jsx", "SlideTransition", "props", "_objectSpread", "direction", "PermissionNotification", "open", "<PERSON><PERSON><PERSON>", "message", "setMessage", "window", "showPermissionUpdateNotification", "msg", "handleClose", "event", "reason", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "TransitionComponent", "sx", "mt", "children", "severity", "variant", "width"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/components/PermissionNotification.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n    Snackbar,\n    Alert,\n    Slide\n} from '@mui/material';\n\nfunction SlideTransition(props) {\n    return <Slide {...props} direction=\"down\" />;\n}\n\nconst PermissionNotification = () => {\n    const [open, setOpen] = useState(false);\n    const [message, setMessage] = useState('');\n\n    useEffect(() => {\n        // Create a global function to show permission update notifications\n        window.showPermissionUpdateNotification = (msg) => {\n            setMessage(msg);\n            setOpen(true);\n        };\n\n        // Cleanup\n        return () => {\n            delete window.showPermissionUpdateNotification;\n        };\n    }, []);\n\n    const handleClose = (event, reason) => {\n        if (reason === 'clickaway') {\n            return;\n        }\n        setOpen(false);\n    };\n\n    return (\n        <Snackbar\n            open={open}\n            autoHideDuration={5000}\n            onClose={handleClose}\n            anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\n            TransitionComponent={SlideTransition}\n            sx={{ mt: 8 }} // Add margin top to avoid overlapping with app bar\n        >\n            <Alert \n                onClose={handleClose} \n                severity=\"success\" \n                variant=\"filled\"\n                sx={{ width: '100%' }}\n            >\n                {message}\n            </Alert>\n        </Snackbar>\n    );\n};\n\nexport default PermissionNotification;\n"], "mappings": "0JAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACIC,QAAQ,CACRC,KAAK,CACLC,KAAK,KACF,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEvB,QAAS,CAAAC,eAAeA,CAACC,KAAK,CAAE,CAC5B,mBAAOF,IAAA,CAACF,KAAK,CAAAK,aAAA,CAAAA,aAAA,IAAKD,KAAK,MAAEE,SAAS,CAAC,MAAM,EAAE,CAAC,CAChD,CAEA,KAAM,CAAAC,sBAAsB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CACvC,KAAM,CAACc,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAE1CC,SAAS,CAAC,IAAM,CACZ;AACAe,MAAM,CAACC,gCAAgC,CAAIC,GAAG,EAAK,CAC/CH,UAAU,CAACG,GAAG,CAAC,CACfL,OAAO,CAAC,IAAI,CAAC,CACjB,CAAC,CAED;AACA,MAAO,IAAM,CACT,MAAO,CAAAG,MAAM,CAACC,gCAAgC,CAClD,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAE,WAAW,CAAGA,CAACC,KAAK,CAAEC,MAAM,GAAK,CACnC,GAAIA,MAAM,GAAK,WAAW,CAAE,CACxB,OACJ,CACAR,OAAO,CAAC,KAAK,CAAC,CAClB,CAAC,CAED,mBACIP,IAAA,CAACJ,QAAQ,EACLU,IAAI,CAAEA,IAAK,CACXU,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAEJ,WAAY,CACrBK,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAE,CACxDC,mBAAmB,CAAEpB,eAAgB,CACrCqB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAG;AAAA,CAAAC,QAAA,cAEfxB,IAAA,CAACH,KAAK,EACFoB,OAAO,CAAEJ,WAAY,CACrBY,QAAQ,CAAC,SAAS,CAClBC,OAAO,CAAC,QAAQ,CAChBJ,EAAE,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAH,QAAA,CAErBhB,OAAO,CACL,CAAC,CACF,CAAC,CAEnB,CAAC,CAED,cAAe,CAAAH,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}