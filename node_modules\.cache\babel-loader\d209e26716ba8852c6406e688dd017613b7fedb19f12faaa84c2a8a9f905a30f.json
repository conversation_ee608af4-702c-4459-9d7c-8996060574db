{"ast": null, "code": "'use strict';\n\nexport default typeof FormData !== 'undefined' ? FormData : null;", "map": {"version": 3, "names": ["FormData"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/axios/lib/platform/browser/classes/FormData.js"], "sourcesContent": ["'use strict';\n\nexport default typeof FormData !== 'undefined' ? FormData : null;\n"], "mappings": "AAAA,YAAY;;AAEZ,eAAe,OAAOA,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}