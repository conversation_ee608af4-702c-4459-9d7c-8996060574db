<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Mobile Login Test - Internal Complaints Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px; /* Prevent zoom on iOS */
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            transform: none;
        }
        
        .error {
            background: #fee;
            color: #c33;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #fcc;
        }
        
        .success {
            background: #efe;
            color: #3c3;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #cfc;
        }
        
        .debug {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .debug h4 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">Mobile Login Test</h1>
        
        <div id="message"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="empCode">Employee Code:</label>
                <input type="text" id="empCode" name="empCode" value="EMP-M" autocomplete="username" autocapitalize="none" autocorrect="off" spellcheck="false">
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="qwerty" autocomplete="current-password">
            </div>
            
            <button type="submit" class="btn" id="loginBtn">Login</button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Logging in...</p>
        </div>
        
        <div class="debug" id="debug">
            <h4>Debug Information:</h4>
            <div id="debugContent"></div>
        </div>
    </div>

    <script>
        const form = document.getElementById('loginForm');
        const messageDiv = document.getElementById('message');
        const loadingDiv = document.getElementById('loading');
        const debugContent = document.getElementById('debugContent');
        const loginBtn = document.getElementById('loginBtn');
        
        function addDebugInfo(info) {
            const timestamp = new Date().toLocaleTimeString();
            debugContent.innerHTML += `<div>[${timestamp}] ${info}</div>`;
            debugContent.scrollTop = debugContent.scrollHeight;
        }
        
        function showMessage(message, type = 'error') {
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function clearMessage() {
            messageDiv.innerHTML = '';
        }
        
        // Add initial debug info
        addDebugInfo(`User Agent: ${navigator.userAgent}`);
        addDebugInfo(`Screen: ${screen.width}x${screen.height}`);
        addDebugInfo(`Viewport: ${window.innerWidth}x${window.innerHeight}`);
        addDebugInfo(`Location: ${window.location.href}`);
        addDebugInfo(`Network: ${navigator.onLine ? 'Online' : 'Offline'}`);
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const empCode = document.getElementById('empCode').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!empCode || !password) {
                showMessage('Please enter both employee code and password');
                return;
            }
            
            clearMessage();
            loadingDiv.style.display = 'block';
            loginBtn.disabled = true;
            
            addDebugInfo(`Attempting login with: ${empCode}`);
            
            try {
                // Determine the API URL
                const apiUrl = window.location.hostname === 'localhost' 
                    ? 'http://localhost:1976/api/auth/login'
                    : `http://${window.location.hostname}:1976/api/auth/login`;
                
                addDebugInfo(`API URL: ${apiUrl}`);
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({ empCode, password }),
                    mode: 'cors',
                    credentials: 'omit'
                });
                
                addDebugInfo(`Response status: ${response.status}`);
                addDebugInfo(`Response headers: ${JSON.stringify([...response.headers.entries()])}`);
                
                const data = await response.json();
                addDebugInfo(`Response data: ${JSON.stringify(data, null, 2)}`);
                
                if (response.ok && data.token) {
                    showMessage('Login successful! Redirecting...', 'success');
                    addDebugInfo('Login successful, storing token...');
                    
                    // Store token
                    localStorage.setItem('token', data.token);
                    
                    // Redirect to main app
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1500);
                } else {
                    showMessage(data.message || 'Login failed');
                    addDebugInfo(`Login failed: ${data.message || 'Unknown error'}`);
                }
                
            } catch (error) {
                addDebugInfo(`Error: ${error.message}`);
                addDebugInfo(`Error stack: ${error.stack}`);
                
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    showMessage('Network error: Cannot connect to server. Please check your internet connection.');
                } else {
                    showMessage(`Error: ${error.message}`);
                }
            } finally {
                loadingDiv.style.display = 'none';
                loginBtn.disabled = false;
            }
        });
        
        // Test network connectivity
        async function testConnectivity() {
            try {
                const testUrl = window.location.hostname === 'localhost' 
                    ? 'http://localhost:1976/api/debug/database-check'
                    : `http://${window.location.hostname}:1976/api/debug/database-check`;
                
                addDebugInfo(`Testing connectivity to: ${testUrl}`);
                
                const response = await fetch(testUrl, {
                    method: 'GET',
                    mode: 'cors',
                    credentials: 'omit'
                });
                
                if (response.ok) {
                    addDebugInfo('✅ Server connectivity test passed');
                } else {
                    addDebugInfo(`❌ Server connectivity test failed: ${response.status}`);
                }
            } catch (error) {
                addDebugInfo(`❌ Server connectivity test error: ${error.message}`);
            }
        }
        
        // Run connectivity test on load
        testConnectivity();
    </script>
</body>
</html>
