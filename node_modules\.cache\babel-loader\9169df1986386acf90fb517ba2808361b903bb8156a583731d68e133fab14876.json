{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport const getPickersFadeTransitionGroupUtilityClass = slot => generateUtilityClass('MuiPickersFadeTransitionGroup', slot);\nexport const pickersFadeTransitionGroupClasses = generateUtilityClasses('MuiPickersFadeTransitionGroup', ['root']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getPickersFadeTransitionGroupUtilityClass", "slot", "pickersFadeTransitionGroupClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/CalendarPicker/pickersFadeTransitionGroupClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport const getPickersFadeTransitionGroupUtilityClass = slot => generateUtilityClass('MuiPickersFadeTransitionGroup', slot);\nexport const pickersFadeTransitionGroupClasses = generateUtilityClasses('MuiPickersFadeTransitionGroup', ['root']);"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,eAAe;AAC5E,OAAO,MAAMC,yCAAyC,GAAGC,IAAI,IAAIH,oBAAoB,CAAC,+BAA+B,EAAEG,IAAI,CAAC;AAC5H,OAAO,MAAMC,iCAAiC,GAAGH,sBAAsB,CAAC,+BAA+B,EAAE,CAAC,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}