{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getFormControlUtilityClass(slot) {\n  return generateUtilityClass('MuiFormControl', slot);\n}\nexport const formControlClasses = generateUtilityClasses('MuiFormControl', ['root', 'disabled', 'error', 'filled', 'focused', 'required']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getFormControlUtilityClass", "slot", "formControlClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/FormControl/formControlClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getFormControlUtilityClass(slot) {\n  return generateUtilityClass('MuiFormControl', slot);\n}\nexport const formControlClasses = generateUtilityClasses('MuiFormControl', ['root', 'disabled', 'error', 'filled', 'focused', 'required']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,OAAO,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}