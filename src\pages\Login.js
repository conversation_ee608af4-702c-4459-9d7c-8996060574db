import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  useTheme,
  useMediaQuery,
  CircularProgress,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Login as LoginIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';

function Login() {
  const [empCode, setEmpCode] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login, user } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Trim whitespace from inputs
    const trimmedEmpCode = empCode.trim();
    const trimmedPassword = password.trim();

    if (!trimmedEmpCode || !trimmedPassword) {
      setError('Please enter both employee code and password');
      return;
    }

    setError('');
    setLoading(true);

    try {
      console.log('Attempting login with:', { empCode: trimmedEmpCode });
      console.log('User Agent:', navigator.userAgent);
      console.log('Screen:', `${screen.width}x${screen.height}`);
      console.log('Network:', navigator.onLine ? 'Online' : 'Offline');

      const result = await login(trimmedEmpCode, trimmedPassword);

      if (result.success) {
        console.log("Login successful, navigating...");

        // Navigate to dashboard immediately
        navigate('/dashboard', { replace: true });

        // For mobile devices, ensure proper state update
        if (window.innerWidth <= 768 || /Mobi|Android/i.test(navigator.userAgent)) {
          console.log('Mobile device detected, ensuring proper navigation...');
          // Small delay to ensure navigation completes
          setTimeout(() => {
            if (window.location.pathname === '/login') {
              console.log('Navigation failed, forcing redirect...');
              window.location.href = '/dashboard';
            }
          }, 500);
        }
      } else {
        console.error('Login failed:', result.message);
        setError(result.message || 'Invalid employee code or password');
      }
    } catch (err) {
      console.error('Login error:', err);

      // More specific error messages based on mobile test results
      if (!navigator.onLine) {
        setError('No internet connection. Please check your network and try again.');
      } else if (err.code === 'NETWORK_ERROR' || err.message.includes('Network Error') || err.name === 'TypeError') {
        setError('Network connection error. Please check your internet connection and try again.');
      } else if (err.response?.status === 401) {
        setError('Invalid employee code or password');
      } else if (err.response?.status >= 500) {
        setError('Server error. Please try again later.');
      } else if (err.response?.status === 0 || !err.response) {
        setError('Cannot connect to server. Please check if the server is running.');
      } else {
        setError(err.response?.data?.message || 'Login failed. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: theme.palette.grey[100],
        p: { xs: 2, sm: 4 }
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        style={{ width: '100%', maxWidth: 400 }}
      >
        <Card
          elevation={8}
          sx={{
            borderRadius: 2,
            overflow: 'hidden',
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
          }}
        >
          <CardContent sx={{ p: { xs: 3, sm: 4 } }}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 600,
                  color: theme.palette.primary.main,
                  fontSize: { xs: '1.75rem', sm: '2rem' },
                  mb: 1
                }}
              >
                Welcome Back
              </Typography>
              <Typography
                variant="body1"
                color="textSecondary"
                sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}
              >
                Sign in to continue to your account
              </Typography>
            </Box>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              </motion.div>
            )}

            <form onSubmit={handleSubmit} noValidate>
              <TextField
                fullWidth
                label="Employee Code"
                variant="outlined"
                value={empCode}
                onChange={(e) => setEmpCode(e.target.value)}
                disabled={loading}
                autoComplete="username"
                autoCapitalize="none"
                autoCorrect="off"
                spellCheck="false"
                inputMode="text"
                sx={{ mb: 2 }}
                InputProps={{
                  sx: { borderRadius: 1.5 }
                }}
                inputProps={{
                  style: {
                    fontSize: isMobile ? '16px' : '14px', // Prevent zoom on iOS
                  }
                }}
              />

              <TextField
                fullWidth
                label="Password"
                type={showPassword ? 'text' : 'password'}
                variant="outlined"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
                autoComplete="current-password"
                autoCapitalize="none"
                autoCorrect="off"
                spellCheck="false"
                sx={{ mb: 3 }}
                InputProps={{
                  sx: { borderRadius: 1.5 },
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                        tabIndex={-1}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
                inputProps={{
                  style: {
                    fontSize: isMobile ? '16px' : '14px', // Prevent zoom on iOS
                  }
                }}
              />

              <Button
                fullWidth
                type="submit"
                variant="contained"
                size="large"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <LoginIcon />}
                sx={{
                  borderRadius: 1.5,
                  py: 1.5,
                  textTransform: 'none',
                  fontSize: '1rem'
                }}
              >
                {loading ? 'Signing in...' : 'Sign In'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  );
}

export default Login; 