import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  useTheme,
  useMediaQuery,
  CircularProgress,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Login as LoginIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';

function Login() {
  const [empCode, setEmpCode] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const { login, user } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Trim whitespace from inputs
    const trimmedEmpCode = empCode.trim();
    const trimmedPassword = password.trim();

    if (!trimmedEmpCode || !trimmedPassword) {
      setError('Please enter both employee code and password');
      return;
    }

    setError('');
    setSuccess('');
    setLoading(true);

    try {
      console.log('Attempting login with:', { empCode: trimmedEmpCode });
      console.log('User Agent:', navigator.userAgent);
      console.log('Screen:', `${window.screen.width}x${window.screen.height}`);
      console.log('Network:', navigator.onLine ? 'Online' : 'Offline');

      const result = await login(trimmedEmpCode, trimmedPassword);

      if (result.success) {
        console.log("Login successful, navigating...");

        // Show success message
        setSuccess('Successfully logged in! Redirecting to dashboard...');

        // Small delay to show success message before navigation
        setTimeout(() => {
          navigate('/dashboard', { replace: true });

          // For mobile devices, ensure proper state update
          if (window.innerWidth <= 768 || /Mobi|Android/i.test(navigator.userAgent)) {
            console.log('Mobile device detected, ensuring proper navigation...');
            // Small delay to ensure navigation completes
            setTimeout(() => {
              if (window.location.pathname === '/login') {
                console.log('Navigation failed, forcing redirect...');
                window.location.href = '/dashboard';
              }
            }, 500);
          }
        }, 1000);
      } else {
        console.error('Login failed:', result.message);
        setError(result.message || 'Invalid employee code or password');
      }
    } catch (err) {
      console.error('Login error:', err);

      // Clear any success message
      setSuccess('');

      // More specific error messages based on mobile test results
      if (!navigator.onLine) {
        setError('No internet connection. Please check your network and try again.');
      } else if (err.code === 'NETWORK_ERROR' || err.message.includes('Network Error') || err.name === 'TypeError') {
        setError('Network connection error. Please check your internet connection and try again.');
      } else if (err.response?.status === 401) {
        setError('Invalid employee code or password. Please check your credentials and try again.');
      } else if (err.response?.status >= 500) {
        setError('Server error. Please try again later.');
      } else if (err.response?.status === 0 || !err.response) {
        setError('Cannot connect to server. Please check if the server is running.');
      } else {
        // Check if the error message indicates wrong credentials
        const errorMessage = err.response?.data?.message || 'Login failed. Please try again.';
        if (errorMessage.toLowerCase().includes('invalid') || errorMessage.toLowerCase().includes('password') || errorMessage.toLowerCase().includes('credentials')) {
          setError('Incorrect employee code or password. Please check your credentials and try again.');
        } else {
          setError(errorMessage);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        position: 'relative',
        p: { xs: 2, sm: 4 },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          opacity: 0.3,
          zIndex: 0
        }
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 30, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{
          duration: 0.6,
          type: "spring",
          stiffness: 100,
          damping: 15
        }}
        style={{
          width: '100%',
          maxWidth: 420,
          position: 'relative',
          zIndex: 1
        }}
      >
        <Card
          elevation={24}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
          }}
        >
          <CardContent sx={{ p: { xs: 4, sm: 5 } }}>
            <Box sx={{ textAlign: 'center', mb: 5 }}>
              {/* Logo/Icon */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 24px auto',
                    boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
                  }}
                >
                  <LoginIcon sx={{ fontSize: 40, color: 'white' }} />
                </Box>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              >
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 700,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontSize: { xs: '1.75rem', sm: '2.25rem' },
                    mb: 1,
                    letterSpacing: '-0.02em'
                  }}
                >
                  Internal Complaints
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 500,
                    color: theme.palette.text.primary,
                    fontSize: { xs: '1rem', sm: '1.125rem' },
                    mb: 1
                  }}
                >
                  Welcome Back
                </Typography>
                <Typography
                  variant="body1"
                  color="textSecondary"
                  sx={{
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    opacity: 0.8
                  }}
                >
                  Sign in to access your dashboard
                </Typography>
              </motion.div>
            </Box>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.3, type: "spring" }}
              >
                <Alert
                  severity="error"
                  sx={{
                    mb: 3,
                    borderRadius: 2,
                    '& .MuiAlert-icon': {
                      fontSize: '1.25rem'
                    }
                  }}
                >
                  {error}
                </Alert>
              </motion.div>
            )}

            {success && (
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.3, type: "spring" }}
              >
                <Alert
                  severity="success"
                  sx={{
                    mb: 3,
                    borderRadius: 2,
                    '& .MuiAlert-icon': {
                      fontSize: '1.25rem'
                    }
                  }}
                >
                  {success}
                </Alert>
              </motion.div>
            )}

            <motion.form
              onSubmit={handleSubmit}
              noValidate
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <TextField
                fullWidth
                label="Employee Code"
                variant="outlined"
                value={empCode}
                onChange={(e) => setEmpCode(e.target.value)}
                disabled={loading}
                autoComplete="username"
                autoCapitalize="none"
                autoCorrect="off"
                spellCheck="false"
                inputMode="text"
                sx={{
                  mb: 3,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.15)',
                    },
                    '&.Mui-focused': {
                      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.25)',
                    }
                  }
                }}
                inputProps={{
                  style: {
                    fontSize: isMobile ? '16px' : '14px', // Prevent zoom on iOS
                  }
                }}
              />

              <TextField
                fullWidth
                label="Password"
                type={showPassword ? 'text' : 'password'}
                variant="outlined"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
                autoComplete="current-password"
                autoCapitalize="none"
                autoCorrect="off"
                spellCheck="false"
                sx={{
                  mb: 4,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.15)',
                    },
                    '&.Mui-focused': {
                      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.25)',
                    }
                  }
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                        tabIndex={-1}
                        sx={{
                          color: 'text.secondary',
                          '&:hover': {
                            color: 'primary.main'
                          }
                        }}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
                inputProps={{
                  style: {
                    fontSize: isMobile ? '16px' : '14px', // Prevent zoom on iOS
                  }
                }}
              />

              <Button
                fullWidth
                type="submit"
                variant="contained"
                size="large"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <LoginIcon />}
                sx={{
                  borderRadius: 2,
                  py: 2,
                  textTransform: 'none',
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                    boxShadow: '0 12px 40px rgba(102, 126, 234, 0.4)',
                    transform: 'translateY(-2px)',
                  },
                  '&:disabled': {
                    background: 'rgba(0, 0, 0, 0.12)',
                    boxShadow: 'none',
                    transform: 'none',
                  }
                }}
              >
                {loading ? 'Signing in...' : 'Sign In'}
              </Button>
            </motion.form>
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  );
}

export default Login; 