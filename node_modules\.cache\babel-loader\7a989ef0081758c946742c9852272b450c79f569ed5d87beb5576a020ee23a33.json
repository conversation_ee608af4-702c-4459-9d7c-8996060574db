{"ast": null, "code": "import * as React from 'react';\nimport { useValidation } from './useValidation';\nimport { useLocalizationContext } from '../useUtils';\nimport { parseNonNullablePickerDate } from '../../utils/date-utils';\nexport const validateDate = _ref => {\n  let {\n    props,\n    value,\n    adapter\n  } = _ref;\n  const now = adapter.utils.date();\n  const date = adapter.utils.date(value);\n  const minDate = parseNonNullablePickerDate(adapter.utils, props.minDate, adapter.defaultDates.minDate);\n  const maxDate = parseNonNullablePickerDate(adapter.utils, props.maxDate, adapter.defaultDates.maxDate);\n  if (date === null) {\n    return null;\n  }\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(props.shouldDisableDate && props.shouldDisableDate(date)):\n      return 'shouldDisableDate';\n    case Boolean(props.disableFuture && adapter.utils.isAfterDay(date, now)):\n      return 'disableFuture';\n    case Boolean(props.disablePast && adapter.utils.isBeforeDay(date, now)):\n      return 'disablePast';\n    case Boolean(minDate && adapter.utils.isBeforeDay(date, minDate)):\n      return 'minDate';\n    case Boolean(maxDate && adapter.utils.isAfterDay(date, maxDate)):\n      return 'maxDate';\n    default:\n      return null;\n  }\n};\nexport const useIsDayDisabled = _ref2 => {\n  let {\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast\n  } = _ref2;\n  const adapter = useLocalizationContext();\n  return React.useCallback(day => validateDate({\n    adapter,\n    value: day,\n    props: {\n      shouldDisableDate,\n      minDate,\n      maxDate,\n      disableFuture,\n      disablePast\n    }\n  }) !== null, [adapter, shouldDisableDate, minDate, maxDate, disableFuture, disablePast]);\n};\nexport const isSameDateError = (a, b) => a === b;\nexport const useDateValidation = props => useValidation(props, validateDate, isSameDateError);", "map": {"version": 3, "names": ["React", "useValidation", "useLocalizationContext", "parseNonNullablePickerDate", "validateDate", "_ref", "props", "value", "adapter", "now", "utils", "date", "minDate", "defaultDates", "maxDate", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "shouldDisableDate", "disableFuture", "isAfterDay", "disablePast", "isBeforeDay", "useIsDayDisabled", "_ref2", "useCallback", "day", "isSameDateError", "a", "b", "useDateValidation"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/hooks/validation/useDateValidation.js"], "sourcesContent": ["import * as React from 'react';\nimport { useValidation } from './useValidation';\nimport { useLocalizationContext } from '../useUtils';\nimport { parseNonNullablePickerDate } from '../../utils/date-utils';\nexport const validateDate = ({\n  props,\n  value,\n  adapter\n}) => {\n  const now = adapter.utils.date();\n  const date = adapter.utils.date(value);\n  const minDate = parseNonNullablePickerDate(adapter.utils, props.minDate, adapter.defaultDates.minDate);\n  const maxDate = parseNonNullablePickerDate(adapter.utils, props.maxDate, adapter.defaultDates.maxDate);\n\n  if (date === null) {\n    return null;\n  }\n\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n\n    case Boolean(props.shouldDisableDate && props.shouldDisableDate(date)):\n      return 'shouldDisableDate';\n\n    case Boolean(props.disableFuture && adapter.utils.isAfterDay(date, now)):\n      return 'disableFuture';\n\n    case Boolean(props.disablePast && adapter.utils.isBeforeDay(date, now)):\n      return 'disablePast';\n\n    case Boolean(minDate && adapter.utils.isBeforeDay(date, minDate)):\n      return 'minDate';\n\n    case Boolean(maxDate && adapter.utils.isAfterDay(date, maxDate)):\n      return 'maxDate';\n\n    default:\n      return null;\n  }\n};\nexport const useIsDayDisabled = ({\n  shouldDisableDate,\n  minDate,\n  maxDate,\n  disableFuture,\n  disablePast\n}) => {\n  const adapter = useLocalizationContext();\n  return React.useCallback(day => validateDate({\n    adapter,\n    value: day,\n    props: {\n      shouldDisableDate,\n      minDate,\n      maxDate,\n      disableFuture,\n      disablePast\n    }\n  }) !== null, [adapter, shouldDisableDate, minDate, maxDate, disableFuture, disablePast]);\n};\nexport const isSameDateError = (a, b) => a === b;\nexport const useDateValidation = props => useValidation(props, validateDate, isSameDateError);"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,sBAAsB,QAAQ,aAAa;AACpD,SAASC,0BAA0B,QAAQ,wBAAwB;AACnE,OAAO,MAAMC,YAAY,GAAGC,IAAA,IAItB;EAAA,IAJuB;IAC3BC,KAAK;IACLC,KAAK;IACLC;EACF,CAAC,GAAAH,IAAA;EACC,MAAMI,GAAG,GAAGD,OAAO,CAACE,KAAK,CAACC,IAAI,CAAC,CAAC;EAChC,MAAMA,IAAI,GAAGH,OAAO,CAACE,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC;EACtC,MAAMK,OAAO,GAAGT,0BAA0B,CAACK,OAAO,CAACE,KAAK,EAAEJ,KAAK,CAACM,OAAO,EAAEJ,OAAO,CAACK,YAAY,CAACD,OAAO,CAAC;EACtG,MAAME,OAAO,GAAGX,0BAA0B,CAACK,OAAO,CAACE,KAAK,EAAEJ,KAAK,CAACQ,OAAO,EAAEN,OAAO,CAACK,YAAY,CAACC,OAAO,CAAC;EAEtG,IAAIH,IAAI,KAAK,IAAI,EAAE;IACjB,OAAO,IAAI;EACb;EAEA,QAAQ,IAAI;IACV,KAAK,CAACH,OAAO,CAACE,KAAK,CAACK,OAAO,CAACR,KAAK,CAAC;MAChC,OAAO,aAAa;IAEtB,KAAKS,OAAO,CAACV,KAAK,CAACW,iBAAiB,IAAIX,KAAK,CAACW,iBAAiB,CAACN,IAAI,CAAC,CAAC;MACpE,OAAO,mBAAmB;IAE5B,KAAKK,OAAO,CAACV,KAAK,CAACY,aAAa,IAAIV,OAAO,CAACE,KAAK,CAACS,UAAU,CAACR,IAAI,EAAEF,GAAG,CAAC,CAAC;MACtE,OAAO,eAAe;IAExB,KAAKO,OAAO,CAACV,KAAK,CAACc,WAAW,IAAIZ,OAAO,CAACE,KAAK,CAACW,WAAW,CAACV,IAAI,EAAEF,GAAG,CAAC,CAAC;MACrE,OAAO,aAAa;IAEtB,KAAKO,OAAO,CAACJ,OAAO,IAAIJ,OAAO,CAACE,KAAK,CAACW,WAAW,CAACV,IAAI,EAAEC,OAAO,CAAC,CAAC;MAC/D,OAAO,SAAS;IAElB,KAAKI,OAAO,CAACF,OAAO,IAAIN,OAAO,CAACE,KAAK,CAACS,UAAU,CAACR,IAAI,EAAEG,OAAO,CAAC,CAAC;MAC9D,OAAO,SAAS;IAElB;MACE,OAAO,IAAI;EACf;AACF,CAAC;AACD,OAAO,MAAMQ,gBAAgB,GAAGC,KAAA,IAM1B;EAAA,IAN2B;IAC/BN,iBAAiB;IACjBL,OAAO;IACPE,OAAO;IACPI,aAAa;IACbE;EACF,CAAC,GAAAG,KAAA;EACC,MAAMf,OAAO,GAAGN,sBAAsB,CAAC,CAAC;EACxC,OAAOF,KAAK,CAACwB,WAAW,CAACC,GAAG,IAAIrB,YAAY,CAAC;IAC3CI,OAAO;IACPD,KAAK,EAAEkB,GAAG;IACVnB,KAAK,EAAE;MACLW,iBAAiB;MACjBL,OAAO;MACPE,OAAO;MACPI,aAAa;MACbE;IACF;EACF,CAAC,CAAC,KAAK,IAAI,EAAE,CAACZ,OAAO,EAAES,iBAAiB,EAAEL,OAAO,EAAEE,OAAO,EAAEI,aAAa,EAAEE,WAAW,CAAC,CAAC;AAC1F,CAAC;AACD,OAAO,MAAMM,eAAe,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC;AAChD,OAAO,MAAMC,iBAAiB,GAAGvB,KAAK,IAAIL,aAAa,CAACK,KAAK,EAAEF,YAAY,EAAEsB,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}