{"ast": null, "code": "import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport getUTCISOWeek from \"../getUTCISOWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nexport default function setUTCISOWeek(dirtyDate, dirtyISOWeek) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var isoWeek = toInteger(dirtyISOWeek);\n  var diff = getUTCISOWeek(date) - isoWeek;\n  date.setUTCDate(date.getUTCDate() - diff * 7);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "getUTCISOWeek", "requiredArgs", "setUTCISOWeek", "dirtyDate", "dirtyISOWeek", "arguments", "date", "isoWeek", "diff", "setUTCDate", "getUTCDate"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/date-fns/esm/_lib/setUTCISOWeek/index.js"], "sourcesContent": ["import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport getUTCISOWeek from \"../getUTCISOWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nexport default function setUTCISOWeek(dirtyDate, dirtyISOWeek) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var isoWeek = toInteger(dirtyISOWeek);\n  var diff = getUTCISOWeek(date) - isoWeek;\n  date.setUTCDate(date.getUTCDate() - diff * 7);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC7DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGP,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAII,OAAO,GAAGT,SAAS,CAACM,YAAY,CAAC;EACrC,IAAII,IAAI,GAAGR,aAAa,CAACM,IAAI,CAAC,GAAGC,OAAO;EACxCD,IAAI,CAACG,UAAU,CAACH,IAAI,CAACI,UAAU,CAAC,CAAC,GAAGF,IAAI,GAAG,CAAC,CAAC;EAC7C,OAAOF,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}