{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRifm } from 'rifm';\nimport { useUtils } from './useUtils';\nimport { maskedDateFormatter, getDisplayDate, checkMaskIsValidForCurrentFormat, getMaskFromCurrentFormat } from '../utils/text-field-helper';\nexport const useMaskedInput = _ref => {\n  let {\n    acceptRegex = /[\\d]/gi,\n    disabled,\n    disableMaskedInput,\n    ignoreInvalidInputs,\n    inputFormat,\n    inputProps,\n    label,\n    mask,\n    onChange,\n    rawValue,\n    readOnly,\n    rifmFormatter,\n    TextFieldProps,\n    validationError\n  } = _ref;\n  const utils = useUtils();\n  const formatHelperText = utils.getFormatHelperText(inputFormat);\n  const {\n    shouldUseMaskedInput,\n    maskToUse\n  } = React.useMemo(() => {\n    // formatting of dates is a quite slow thing, so do not make useless .format calls\n    if (disableMaskedInput) {\n      return {\n        shouldUseMaskedInput: false,\n        maskToUse: ''\n      };\n    }\n    const computedMaskToUse = getMaskFromCurrentFormat(mask, inputFormat, acceptRegex, utils);\n    return {\n      shouldUseMaskedInput: checkMaskIsValidForCurrentFormat(computedMaskToUse, inputFormat, acceptRegex, utils),\n      maskToUse: computedMaskToUse\n    };\n  }, [acceptRegex, disableMaskedInput, inputFormat, mask, utils]);\n  const formatter = React.useMemo(() => shouldUseMaskedInput && maskToUse ? maskedDateFormatter(maskToUse, acceptRegex) : st => st, [acceptRegex, maskToUse, shouldUseMaskedInput]); // TODO: Implement with controlled vs uncontrolled `rawValue`\n\n  const parsedValue = rawValue === null ? null : utils.date(rawValue); // Track the value of the input\n\n  const [innerInputValue, setInnerInputValue] = React.useState(parsedValue); // control the input text\n\n  const [innerDisplayedInputValue, setInnerDisplayedInputValue] = React.useState(getDisplayDate(utils, rawValue, inputFormat)); // Inspired from autocomplete: https://github.com/mui/material-ui/blob/2c89d036dc2e16f100528f161600dffc83241768/packages/mui-base/src/AutocompleteUnstyled/useAutocomplete.js#L185:L201\n\n  const prevRawValue = React.useRef();\n  const prevLocale = React.useRef(utils.locale);\n  const prevInputFormat = React.useRef(inputFormat);\n  React.useEffect(() => {\n    const rawValueHasChanged = rawValue !== prevRawValue.current;\n    const localeHasChanged = utils.locale !== prevLocale.current;\n    const inputFormatHasChanged = inputFormat !== prevInputFormat.current;\n    prevRawValue.current = rawValue;\n    prevLocale.current = utils.locale;\n    prevInputFormat.current = inputFormat;\n    if (!rawValueHasChanged && !localeHasChanged && !inputFormatHasChanged) {\n      return;\n    }\n    const newParsedValue = rawValue === null ? null : utils.date(rawValue);\n    const isAcceptedValue = rawValue === null || utils.isValid(newParsedValue);\n    let innerEqualsParsed = innerInputValue === null && newParsedValue === null; // equal by being both null\n\n    if (innerInputValue !== null && newParsedValue !== null) {\n      const areEqual = utils.isEqual(innerInputValue, newParsedValue);\n      if (areEqual) {\n        innerEqualsParsed = true;\n      } else {\n        const diff = Math.abs(utils.getDiff(innerInputValue, newParsedValue)); // diff in ms\n\n        innerEqualsParsed = diff === 0 ? areEqual // if no diff, use equal to test the time-zone\n        : diff < 1000; // accept a difference bellow 1s\n      }\n    }\n    if (!localeHasChanged && !inputFormatHasChanged && (!isAcceptedValue || innerEqualsParsed)) {\n      return;\n    } // When dev set a new valid value, we trust them\n\n    const newDisplayDate = getDisplayDate(utils, rawValue, inputFormat);\n    setInnerInputValue(newParsedValue);\n    setInnerDisplayedInputValue(newDisplayDate);\n  }, [utils, rawValue, inputFormat, innerInputValue]);\n  const handleChange = text => {\n    const finalString = text === '' || text === mask ? '' : text;\n    setInnerDisplayedInputValue(finalString);\n    const date = finalString === null ? null : utils.parse(finalString, inputFormat);\n    if (ignoreInvalidInputs && !utils.isValid(date)) {\n      return;\n    }\n    setInnerInputValue(date);\n    onChange(date, finalString || undefined);\n  };\n  const rifmProps = useRifm({\n    value: innerDisplayedInputValue,\n    onChange: handleChange,\n    format: rifmFormatter || formatter\n  });\n  const inputStateArgs = shouldUseMaskedInput ? rifmProps : {\n    value: innerDisplayedInputValue,\n    onChange: event => {\n      handleChange(event.currentTarget.value);\n    }\n  };\n  return _extends({\n    label,\n    disabled,\n    error: validationError,\n    inputProps: _extends({}, inputStateArgs, {\n      disabled,\n      placeholder: formatHelperText,\n      readOnly,\n      type: shouldUseMaskedInput ? 'tel' : 'text'\n    }, inputProps)\n  }, TextFieldProps);\n};", "map": {"version": 3, "names": ["_extends", "React", "useRifm", "useUtils", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDisplayDate", "checkMaskIsValidForCurrentFormat", "getMaskFromCurrentFormat", "useMaskedInput", "_ref", "acceptRegex", "disabled", "disableMaskedInput", "ignoreInvalidInputs", "inputFormat", "inputProps", "label", "mask", "onChange", "rawValue", "readOnly", "rifmFormatter", "TextFieldProps", "validationError", "utils", "formatHelperText", "getFormatHelperText", "shouldUseMaskedInput", "maskToUse", "useMemo", "computedMaskToUse", "formatter", "st", "parsedValue", "date", "innerInputValue", "setInnerInputValue", "useState", "innerDisplayedInputValue", "setInnerDisplayedInputValue", "prevRawValue", "useRef", "prevLocale", "locale", "prevInputFormat", "useEffect", "rawValueHasChanged", "current", "localeHasChanged", "inputFormatHasChanged", "newParsedValue", "isAcceptedValue", "<PERSON><PERSON><PERSON><PERSON>", "innerEqualsParsed", "areEqual", "isEqual", "diff", "Math", "abs", "getDiff", "newDisplayDate", "handleChange", "text", "finalString", "parse", "undefined", "rifmProps", "value", "format", "inputStateArgs", "event", "currentTarget", "error", "placeholder", "type"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/hooks/useMaskedInput.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRifm } from 'rifm';\nimport { useUtils } from './useUtils';\nimport { maskedDateFormatter, getDisplayDate, checkMaskIsValidForCurrentFormat, getMaskFromCurrentFormat } from '../utils/text-field-helper';\nexport const useMaskedInput = ({\n  acceptRegex = /[\\d]/gi,\n  disabled,\n  disableMaskedInput,\n  ignoreInvalidInputs,\n  inputFormat,\n  inputProps,\n  label,\n  mask,\n  onChange,\n  rawValue,\n  readOnly,\n  rifmFormatter,\n  TextFieldProps,\n  validationError\n}) => {\n  const utils = useUtils();\n  const formatHelperText = utils.getFormatHelperText(inputFormat);\n  const {\n    shouldUseMaskedInput,\n    maskToUse\n  } = React.useMemo(() => {\n    // formatting of dates is a quite slow thing, so do not make useless .format calls\n    if (disableMaskedInput) {\n      return {\n        shouldUseMaskedInput: false,\n        maskToUse: ''\n      };\n    }\n\n    const computedMaskToUse = getMaskFromCurrentFormat(mask, inputFormat, acceptRegex, utils);\n    return {\n      shouldUseMaskedInput: checkMaskIsValidForCurrentFormat(computedMaskToUse, inputFormat, acceptRegex, utils),\n      maskToUse: computedMaskToUse\n    };\n  }, [acceptRegex, disableMaskedInput, inputFormat, mask, utils]);\n  const formatter = React.useMemo(() => shouldUseMaskedInput && maskToUse ? maskedDateFormatter(maskToUse, acceptRegex) : st => st, [acceptRegex, maskToUse, shouldUseMaskedInput]); // TODO: Implement with controlled vs uncontrolled `rawValue`\n\n  const parsedValue = rawValue === null ? null : utils.date(rawValue); // Track the value of the input\n\n  const [innerInputValue, setInnerInputValue] = React.useState(parsedValue); // control the input text\n\n  const [innerDisplayedInputValue, setInnerDisplayedInputValue] = React.useState(getDisplayDate(utils, rawValue, inputFormat)); // Inspired from autocomplete: https://github.com/mui/material-ui/blob/2c89d036dc2e16f100528f161600dffc83241768/packages/mui-base/src/AutocompleteUnstyled/useAutocomplete.js#L185:L201\n\n  const prevRawValue = React.useRef();\n  const prevLocale = React.useRef(utils.locale);\n  const prevInputFormat = React.useRef(inputFormat);\n  React.useEffect(() => {\n    const rawValueHasChanged = rawValue !== prevRawValue.current;\n    const localeHasChanged = utils.locale !== prevLocale.current;\n    const inputFormatHasChanged = inputFormat !== prevInputFormat.current;\n    prevRawValue.current = rawValue;\n    prevLocale.current = utils.locale;\n    prevInputFormat.current = inputFormat;\n\n    if (!rawValueHasChanged && !localeHasChanged && !inputFormatHasChanged) {\n      return;\n    }\n\n    const newParsedValue = rawValue === null ? null : utils.date(rawValue);\n    const isAcceptedValue = rawValue === null || utils.isValid(newParsedValue);\n    let innerEqualsParsed = innerInputValue === null && newParsedValue === null; // equal by being both null\n\n    if (innerInputValue !== null && newParsedValue !== null) {\n      const areEqual = utils.isEqual(innerInputValue, newParsedValue);\n\n      if (areEqual) {\n        innerEqualsParsed = true;\n      } else {\n        const diff = Math.abs(utils.getDiff(innerInputValue, newParsedValue)); // diff in ms\n\n        innerEqualsParsed = diff === 0 ? areEqual // if no diff, use equal to test the time-zone\n        : diff < 1000; // accept a difference bellow 1s\n      }\n    }\n\n    if (!localeHasChanged && !inputFormatHasChanged && (!isAcceptedValue || innerEqualsParsed)) {\n      return;\n    } // When dev set a new valid value, we trust them\n\n\n    const newDisplayDate = getDisplayDate(utils, rawValue, inputFormat);\n    setInnerInputValue(newParsedValue);\n    setInnerDisplayedInputValue(newDisplayDate);\n  }, [utils, rawValue, inputFormat, innerInputValue]);\n\n  const handleChange = text => {\n    const finalString = text === '' || text === mask ? '' : text;\n    setInnerDisplayedInputValue(finalString);\n    const date = finalString === null ? null : utils.parse(finalString, inputFormat);\n\n    if (ignoreInvalidInputs && !utils.isValid(date)) {\n      return;\n    }\n\n    setInnerInputValue(date);\n    onChange(date, finalString || undefined);\n  };\n\n  const rifmProps = useRifm({\n    value: innerDisplayedInputValue,\n    onChange: handleChange,\n    format: rifmFormatter || formatter\n  });\n  const inputStateArgs = shouldUseMaskedInput ? rifmProps : {\n    value: innerDisplayedInputValue,\n    onChange: event => {\n      handleChange(event.currentTarget.value);\n    }\n  };\n  return _extends({\n    label,\n    disabled,\n    error: validationError,\n    inputProps: _extends({}, inputStateArgs, {\n      disabled,\n      placeholder: formatHelperText,\n      readOnly,\n      type: shouldUseMaskedInput ? 'tel' : 'text'\n    }, inputProps)\n  }, TextFieldProps);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,mBAAmB,EAAEC,cAAc,EAAEC,gCAAgC,EAAEC,wBAAwB,QAAQ,4BAA4B;AAC5I,OAAO,MAAMC,cAAc,GAAGC,IAAA,IAexB;EAAA,IAfyB;IAC7BC,WAAW,GAAG,QAAQ;IACtBC,QAAQ;IACRC,kBAAkB;IAClBC,mBAAmB;IACnBC,WAAW;IACXC,UAAU;IACVC,KAAK;IACLC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,aAAa;IACbC,cAAc;IACdC;EACF,CAAC,GAAAd,IAAA;EACC,MAAMe,KAAK,GAAGrB,QAAQ,CAAC,CAAC;EACxB,MAAMsB,gBAAgB,GAAGD,KAAK,CAACE,mBAAmB,CAACZ,WAAW,CAAC;EAC/D,MAAM;IACJa,oBAAoB;IACpBC;EACF,CAAC,GAAG3B,KAAK,CAAC4B,OAAO,CAAC,MAAM;IACtB;IACA,IAAIjB,kBAAkB,EAAE;MACtB,OAAO;QACLe,oBAAoB,EAAE,KAAK;QAC3BC,SAAS,EAAE;MACb,CAAC;IACH;IAEA,MAAME,iBAAiB,GAAGvB,wBAAwB,CAACU,IAAI,EAAEH,WAAW,EAAEJ,WAAW,EAAEc,KAAK,CAAC;IACzF,OAAO;MACLG,oBAAoB,EAAErB,gCAAgC,CAACwB,iBAAiB,EAAEhB,WAAW,EAAEJ,WAAW,EAAEc,KAAK,CAAC;MAC1GI,SAAS,EAAEE;IACb,CAAC;EACH,CAAC,EAAE,CAACpB,WAAW,EAAEE,kBAAkB,EAAEE,WAAW,EAAEG,IAAI,EAAEO,KAAK,CAAC,CAAC;EAC/D,MAAMO,SAAS,GAAG9B,KAAK,CAAC4B,OAAO,CAAC,MAAMF,oBAAoB,IAAIC,SAAS,GAAGxB,mBAAmB,CAACwB,SAAS,EAAElB,WAAW,CAAC,GAAGsB,EAAE,IAAIA,EAAE,EAAE,CAACtB,WAAW,EAAEkB,SAAS,EAAED,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAEnL,MAAMM,WAAW,GAAGd,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAGK,KAAK,CAACU,IAAI,CAACf,QAAQ,CAAC,CAAC,CAAC;;EAErE,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,KAAK,CAACoC,QAAQ,CAACJ,WAAW,CAAC,CAAC,CAAC;;EAE3E,MAAM,CAACK,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGtC,KAAK,CAACoC,QAAQ,CAAChC,cAAc,CAACmB,KAAK,EAAEL,QAAQ,EAAEL,WAAW,CAAC,CAAC,CAAC,CAAC;;EAE9H,MAAM0B,YAAY,GAAGvC,KAAK,CAACwC,MAAM,CAAC,CAAC;EACnC,MAAMC,UAAU,GAAGzC,KAAK,CAACwC,MAAM,CAACjB,KAAK,CAACmB,MAAM,CAAC;EAC7C,MAAMC,eAAe,GAAG3C,KAAK,CAACwC,MAAM,CAAC3B,WAAW,CAAC;EACjDb,KAAK,CAAC4C,SAAS,CAAC,MAAM;IACpB,MAAMC,kBAAkB,GAAG3B,QAAQ,KAAKqB,YAAY,CAACO,OAAO;IAC5D,MAAMC,gBAAgB,GAAGxB,KAAK,CAACmB,MAAM,KAAKD,UAAU,CAACK,OAAO;IAC5D,MAAME,qBAAqB,GAAGnC,WAAW,KAAK8B,eAAe,CAACG,OAAO;IACrEP,YAAY,CAACO,OAAO,GAAG5B,QAAQ;IAC/BuB,UAAU,CAACK,OAAO,GAAGvB,KAAK,CAACmB,MAAM;IACjCC,eAAe,CAACG,OAAO,GAAGjC,WAAW;IAErC,IAAI,CAACgC,kBAAkB,IAAI,CAACE,gBAAgB,IAAI,CAACC,qBAAqB,EAAE;MACtE;IACF;IAEA,MAAMC,cAAc,GAAG/B,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAGK,KAAK,CAACU,IAAI,CAACf,QAAQ,CAAC;IACtE,MAAMgC,eAAe,GAAGhC,QAAQ,KAAK,IAAI,IAAIK,KAAK,CAAC4B,OAAO,CAACF,cAAc,CAAC;IAC1E,IAAIG,iBAAiB,GAAGlB,eAAe,KAAK,IAAI,IAAIe,cAAc,KAAK,IAAI,CAAC,CAAC;;IAE7E,IAAIf,eAAe,KAAK,IAAI,IAAIe,cAAc,KAAK,IAAI,EAAE;MACvD,MAAMI,QAAQ,GAAG9B,KAAK,CAAC+B,OAAO,CAACpB,eAAe,EAAEe,cAAc,CAAC;MAE/D,IAAII,QAAQ,EAAE;QACZD,iBAAiB,GAAG,IAAI;MAC1B,CAAC,MAAM;QACL,MAAMG,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAClC,KAAK,CAACmC,OAAO,CAACxB,eAAe,EAAEe,cAAc,CAAC,CAAC,CAAC,CAAC;;QAEvEG,iBAAiB,GAAGG,IAAI,KAAK,CAAC,GAAGF,QAAQ,CAAC;QAAA,EACxCE,IAAI,GAAG,IAAI,CAAC,CAAC;MACjB;IACF;IAEA,IAAI,CAACR,gBAAgB,IAAI,CAACC,qBAAqB,KAAK,CAACE,eAAe,IAAIE,iBAAiB,CAAC,EAAE;MAC1F;IACF,CAAC,CAAC;;IAGF,MAAMO,cAAc,GAAGvD,cAAc,CAACmB,KAAK,EAAEL,QAAQ,EAAEL,WAAW,CAAC;IACnEsB,kBAAkB,CAACc,cAAc,CAAC;IAClCX,2BAA2B,CAACqB,cAAc,CAAC;EAC7C,CAAC,EAAE,CAACpC,KAAK,EAAEL,QAAQ,EAAEL,WAAW,EAAEqB,eAAe,CAAC,CAAC;EAEnD,MAAM0B,YAAY,GAAGC,IAAI,IAAI;IAC3B,MAAMC,WAAW,GAAGD,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK7C,IAAI,GAAG,EAAE,GAAG6C,IAAI;IAC5DvB,2BAA2B,CAACwB,WAAW,CAAC;IACxC,MAAM7B,IAAI,GAAG6B,WAAW,KAAK,IAAI,GAAG,IAAI,GAAGvC,KAAK,CAACwC,KAAK,CAACD,WAAW,EAAEjD,WAAW,CAAC;IAEhF,IAAID,mBAAmB,IAAI,CAACW,KAAK,CAAC4B,OAAO,CAAClB,IAAI,CAAC,EAAE;MAC/C;IACF;IAEAE,kBAAkB,CAACF,IAAI,CAAC;IACxBhB,QAAQ,CAACgB,IAAI,EAAE6B,WAAW,IAAIE,SAAS,CAAC;EAC1C,CAAC;EAED,MAAMC,SAAS,GAAGhE,OAAO,CAAC;IACxBiE,KAAK,EAAE7B,wBAAwB;IAC/BpB,QAAQ,EAAE2C,YAAY;IACtBO,MAAM,EAAE/C,aAAa,IAAIU;EAC3B,CAAC,CAAC;EACF,MAAMsC,cAAc,GAAG1C,oBAAoB,GAAGuC,SAAS,GAAG;IACxDC,KAAK,EAAE7B,wBAAwB;IAC/BpB,QAAQ,EAAEoD,KAAK,IAAI;MACjBT,YAAY,CAACS,KAAK,CAACC,aAAa,CAACJ,KAAK,CAAC;IACzC;EACF,CAAC;EACD,OAAOnE,QAAQ,CAAC;IACdgB,KAAK;IACLL,QAAQ;IACR6D,KAAK,EAAEjD,eAAe;IACtBR,UAAU,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEqE,cAAc,EAAE;MACvC1D,QAAQ;MACR8D,WAAW,EAAEhD,gBAAgB;MAC7BL,QAAQ;MACRsD,IAAI,EAAE/C,oBAAoB,GAAG,KAAK,GAAG;IACvC,CAAC,EAAEZ,UAAU;EACf,CAAC,EAAEO,cAAc,CAAC;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}