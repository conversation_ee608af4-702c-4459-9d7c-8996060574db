{"ast": null, "code": "import axios from 'axios';\n\n// Get base URL from environment or use window.location.origin as fallback\nconst getBaseUrl = () => {\n  if (process.env.REACT_APP_API_URL) return process.env.REACT_APP_API_URL;\n\n  // In development, use the proxy setup (React dev server will proxy /api to backend)\n  if (process.env.NODE_ENV === 'development') {\n    // Use relative URLs so the proxy can handle routing\n    return '';\n  }\n\n  // In production, determine backend URL based on current location\n  const currentHost = window.location.hostname;\n  const currentPort = window.location.port;\n\n  // If accessing via localhost, use localhost backend\n  if (currentHost === 'localhost' || currentHost === '127.0.0.1') {\n    return 'http://localhost:1976';\n  }\n\n  // Otherwise, use the same hostname with backend port\n  return `http://${currentHost}:1976`;\n};\nconst instance = axios.create({\n  baseURL: getBaseUrl(),\n  timeout: 45000,\n  // Increased timeout for mobile networks\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json'\n  },\n  // Better mobile network handling\n  validateStatus: function (status) {\n    return status >= 200 && status < 300; // default\n  },\n  // Retry configuration for mobile networks\n  retry: 3,\n  retryDelay: 1000\n});\n\n// Request interceptor\ninstance.interceptors.request.use(config => {\n  var _config$method;\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  console.log(`API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n  return config;\n}, error => {\n  console.error('Request error:', error);\n  return Promise.reject(error);\n});\n\n// Response interceptor with retry logic\ninstance.interceptors.response.use(response => {\n  console.log(`API Response: ${response.status} for ${response.config.url}`);\n  return response;\n}, async error => {\n  var _error$response;\n  const config = error.config;\n  console.error('Response error:', error.response || error);\n\n  // Retry logic for network errors (especially useful for mobile)\n  if ((!error.response || error.response.status >= 500) && config && !config.__isRetryRequest) {\n    config.__retryCount = config.__retryCount || 0;\n    if (config.__retryCount < (config.retry || 3)) {\n      config.__retryCount++;\n      config.__isRetryRequest = true;\n      console.log(`Retrying request (${config.__retryCount}/${config.retry || 3}): ${config.url}`);\n\n      // Wait before retrying\n      await new Promise(resolve => setTimeout(resolve, config.retryDelay || 1000));\n      return instance(config);\n    }\n  }\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    console.log('Authentication error, redirecting to login');\n    // Clear token and redirect to login on unauthorized\n    localStorage.removeItem('token');\n    delete instance.defaults.headers.common['Authorization'];\n\n    // Use a slight delay to ensure console logs are visible\n    setTimeout(() => {\n      window.location.href = '/login';\n    }, 100);\n  }\n  return Promise.reject(error);\n});\n\n// Expose the current baseURL for debugging\nconsole.log(`API base URL: ${instance.defaults.baseURL}`);\nexport default instance;", "map": {"version": 3, "names": ["axios", "getBaseUrl", "process", "env", "REACT_APP_API_URL", "NODE_ENV", "currentHost", "window", "location", "hostname", "currentPort", "port", "instance", "create", "baseURL", "timeout", "headers", "validateStatus", "status", "retry", "retry<PERSON><PERSON><PERSON>", "interceptors", "request", "use", "config", "_config$method", "token", "localStorage", "getItem", "Authorization", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "_error$response", "__isRetryRequest", "__retryCount", "resolve", "setTimeout", "removeItem", "defaults", "common", "href"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/utils/axiosConfig.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Get base URL from environment or use window.location.origin as fallback\r\nconst getBaseUrl = () => {\r\n    if (process.env.REACT_APP_API_URL) return process.env.REACT_APP_API_URL;\r\n\r\n    // In development, use the proxy setup (React dev server will proxy /api to backend)\r\n    if (process.env.NODE_ENV === 'development') {\r\n        // Use relative URLs so the proxy can handle routing\r\n        return '';\r\n    }\r\n\r\n    // In production, determine backend URL based on current location\r\n    const currentHost = window.location.hostname;\r\n    const currentPort = window.location.port;\r\n\r\n    // If accessing via localhost, use localhost backend\r\n    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {\r\n        return 'http://localhost:1976';\r\n    }\r\n\r\n    // Otherwise, use the same hostname with backend port\r\n    return `http://${currentHost}:1976`;\r\n};\r\n\r\nconst instance = axios.create({\r\n    baseURL: getBaseUrl(),\r\n    timeout: 45000, // Increased timeout for mobile networks\r\n    headers: {\r\n        'Content-Type': 'application/json',\r\n        'Accept': 'application/json',\r\n    },\r\n    // Better mobile network handling\r\n    validateStatus: function (status) {\r\n        return status >= 200 && status < 300; // default\r\n    },\r\n    // Retry configuration for mobile networks\r\n    retry: 3,\r\n    retryDelay: 1000,\r\n});\r\n\r\n// Request interceptor\r\ninstance.interceptors.request.use(\r\n    (config) => {\r\n        const token = localStorage.getItem('token');\r\n        if (token) {\r\n            config.headers.Authorization = `Bearer ${token}`;\r\n        }\r\n\r\n        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\r\n        return config;\r\n    },\r\n    (error) => {\r\n        console.error('Request error:', error);\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Response interceptor with retry logic\r\ninstance.interceptors.response.use(\r\n    (response) => {\r\n        console.log(`API Response: ${response.status} for ${response.config.url}`);\r\n        return response;\r\n    },\r\n    async (error) => {\r\n        const config = error.config;\r\n\r\n        console.error('Response error:', error.response || error);\r\n\r\n        // Retry logic for network errors (especially useful for mobile)\r\n        if ((!error.response || error.response.status >= 500) && config && !config.__isRetryRequest) {\r\n            config.__retryCount = config.__retryCount || 0;\r\n\r\n            if (config.__retryCount < (config.retry || 3)) {\r\n                config.__retryCount++;\r\n                config.__isRetryRequest = true;\r\n\r\n                console.log(`Retrying request (${config.__retryCount}/${config.retry || 3}): ${config.url}`);\r\n\r\n                // Wait before retrying\r\n                await new Promise(resolve => setTimeout(resolve, config.retryDelay || 1000));\r\n\r\n                return instance(config);\r\n            }\r\n        }\r\n\r\n        if (error.response?.status === 401) {\r\n            console.log('Authentication error, redirecting to login');\r\n            // Clear token and redirect to login on unauthorized\r\n            localStorage.removeItem('token');\r\n            delete instance.defaults.headers.common['Authorization'];\r\n\r\n            // Use a slight delay to ensure console logs are visible\r\n            setTimeout(() => {\r\n                window.location.href = '/login';\r\n            }, 100);\r\n        }\r\n\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Expose the current baseURL for debugging\r\nconsole.log(`API base URL: ${instance.defaults.baseURL}`);\r\n\r\nexport default instance; "], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACrB,IAAIC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAE,OAAOF,OAAO,CAACC,GAAG,CAACC,iBAAiB;;EAEvE;EACA,IAAIF,OAAO,CAACC,GAAG,CAACE,QAAQ,KAAK,aAAa,EAAE;IACxC;IACA,OAAO,EAAE;EACb;;EAEA;EACA,MAAMC,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAC5C,MAAMC,WAAW,GAAGH,MAAM,CAACC,QAAQ,CAACG,IAAI;;EAExC;EACA,IAAIL,WAAW,KAAK,WAAW,IAAIA,WAAW,KAAK,WAAW,EAAE;IAC5D,OAAO,uBAAuB;EAClC;;EAEA;EACA,OAAO,UAAUA,WAAW,OAAO;AACvC,CAAC;AAED,MAAMM,QAAQ,GAAGZ,KAAK,CAACa,MAAM,CAAC;EAC1BC,OAAO,EAAEb,UAAU,CAAC,CAAC;EACrBc,OAAO,EAAE,KAAK;EAAE;EAChBC,OAAO,EAAE;IACL,cAAc,EAAE,kBAAkB;IAClC,QAAQ,EAAE;EACd,CAAC;EACD;EACAC,cAAc,EAAE,SAAAA,CAAUC,MAAM,EAAE;IAC9B,OAAOA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,CAAC,CAAC;EAC1C,CAAC;EACD;EACAC,KAAK,EAAE,CAAC;EACRC,UAAU,EAAE;AAChB,CAAC,CAAC;;AAEF;AACAR,QAAQ,CAACS,YAAY,CAACC,OAAO,CAACC,GAAG,CAC5BC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACR,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACPF,MAAM,CAACR,OAAO,CAACa,aAAa,GAAG,UAAUH,KAAK,EAAE;EACpD;EAEAI,OAAO,CAACC,GAAG,CAAC,iBAAAN,cAAA,GAAgBD,MAAM,CAACQ,MAAM,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,WAAW,CAAC,CAAC,IAAIT,MAAM,CAACU,GAAG,EAAE,CAAC;EACzE,OAAOV,MAAM;AACjB,CAAC,EACAW,KAAK,IAAK;EACPL,OAAO,CAACK,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACtC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;;AAED;AACAvB,QAAQ,CAACS,YAAY,CAACiB,QAAQ,CAACf,GAAG,CAC7Be,QAAQ,IAAK;EACVR,OAAO,CAACC,GAAG,CAAC,iBAAiBO,QAAQ,CAACpB,MAAM,QAAQoB,QAAQ,CAACd,MAAM,CAACU,GAAG,EAAE,CAAC;EAC1E,OAAOI,QAAQ;AACnB,CAAC,EACD,MAAOH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACb,MAAMf,MAAM,GAAGW,KAAK,CAACX,MAAM;EAE3BM,OAAO,CAACK,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAAC;;EAEzD;EACA,IAAI,CAAC,CAACA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACpB,MAAM,IAAI,GAAG,KAAKM,MAAM,IAAI,CAACA,MAAM,CAACgB,gBAAgB,EAAE;IACzFhB,MAAM,CAACiB,YAAY,GAAGjB,MAAM,CAACiB,YAAY,IAAI,CAAC;IAE9C,IAAIjB,MAAM,CAACiB,YAAY,IAAIjB,MAAM,CAACL,KAAK,IAAI,CAAC,CAAC,EAAE;MAC3CK,MAAM,CAACiB,YAAY,EAAE;MACrBjB,MAAM,CAACgB,gBAAgB,GAAG,IAAI;MAE9BV,OAAO,CAACC,GAAG,CAAC,qBAAqBP,MAAM,CAACiB,YAAY,IAAIjB,MAAM,CAACL,KAAK,IAAI,CAAC,MAAMK,MAAM,CAACU,GAAG,EAAE,CAAC;;MAE5F;MACA,MAAM,IAAIE,OAAO,CAACM,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAElB,MAAM,CAACJ,UAAU,IAAI,IAAI,CAAC,CAAC;MAE5E,OAAOR,QAAQ,CAACY,MAAM,CAAC;IAC3B;EACJ;EAEA,IAAI,EAAAe,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBrB,MAAM,MAAK,GAAG,EAAE;IAChCY,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzD;IACAJ,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOhC,QAAQ,CAACiC,QAAQ,CAAC7B,OAAO,CAAC8B,MAAM,CAAC,eAAe,CAAC;;IAExD;IACAH,UAAU,CAAC,MAAM;MACbpC,MAAM,CAACC,QAAQ,CAACuC,IAAI,GAAG,QAAQ;IACnC,CAAC,EAAE,GAAG,CAAC;EACX;EAEA,OAAOX,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;;AAED;AACAL,OAAO,CAACC,GAAG,CAAC,iBAAiBnB,QAAQ,CAACiC,QAAQ,CAAC/B,OAAO,EAAE,CAAC;AAEzD,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}