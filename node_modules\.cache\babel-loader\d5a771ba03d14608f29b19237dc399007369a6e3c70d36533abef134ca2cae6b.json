{"ast": null, "code": "import * as React from 'react';\nexport const useFocusManagement = _ref => {\n  let {\n    autoFocus,\n    openView\n  } = _ref;\n  const [focusedView, setFocusedView] = React.useState(autoFocus ? openView : null);\n  const setFocusedViewCallback = React.useCallback(view => newHasFocus => {\n    if (newHasFocus) {\n      setFocusedView(view);\n    } else {\n      setFocusedView(prevFocusedView => view === prevFocusedView ? null : prevFocusedView);\n    }\n  }, []);\n  return {\n    focusedView,\n    setFocusedView: setFocusedViewCallback\n  };\n};", "map": {"version": 3, "names": ["React", "useFocusManagement", "_ref", "autoFocus", "openView", "focused<PERSON>iew", "setFocusedView", "useState", "setFocused<PERSON>iewCallback", "useCallback", "view", "newHasFocus", "prevFocusedView"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/components/CalendarOrClockPicker/useFocusManagement.js"], "sourcesContent": ["import * as React from 'react';\nexport const useFocusManagement = ({\n  autoFocus,\n  openView\n}) => {\n  const [focusedView, setFocusedView] = React.useState(autoFocus ? openView : null);\n  const setFocusedViewCallback = React.useCallback(view => newHasFocus => {\n    if (newHasFocus) {\n      setFocusedView(view);\n    } else {\n      setFocusedView(prevFocusedView => view === prevFocusedView ? null : prevFocusedView);\n    }\n  }, []);\n  return {\n    focusedView,\n    setFocusedView: setFocusedViewCallback\n  };\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,kBAAkB,GAAGC,IAAA,IAG5B;EAAA,IAH6B;IACjCC,SAAS;IACTC;EACF,CAAC,GAAAF,IAAA;EACC,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGN,KAAK,CAACO,QAAQ,CAACJ,SAAS,GAAGC,QAAQ,GAAG,IAAI,CAAC;EACjF,MAAMI,sBAAsB,GAAGR,KAAK,CAACS,WAAW,CAACC,IAAI,IAAIC,WAAW,IAAI;IACtE,IAAIA,WAAW,EAAE;MACfL,cAAc,CAACI,IAAI,CAAC;IACtB,CAAC,MAAM;MACLJ,cAAc,CAACM,eAAe,IAAIF,IAAI,KAAKE,eAAe,GAAG,IAAI,GAAGA,eAAe,CAAC;IACtF;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO;IACLP,WAAW;IACXC,cAAc,EAAEE;EAClB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}