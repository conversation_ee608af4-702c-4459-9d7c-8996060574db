{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nexport const CompoundComponentContext = /*#__PURE__*/React.createContext(null);\nCompoundComponentContext.displayName = 'CompoundComponentContext';\n/**\n * Sorts the subitems by their position in the DOM.\n */\nfunction sortSubitems(subitems) {\n  const subitemsArray = Array.from(subitems.keys()).map(key => {\n    const subitem = subitems.get(key);\n    return {\n      key,\n      subitem\n    };\n  });\n  subitemsArray.sort((a, b) => {\n    const aNode = a.subitem.ref.current;\n    const bNode = b.subitem.ref.current;\n    if (aNode === null || bNode === null || aNode === bNode) {\n      return 0;\n    }\n\n    // eslint-disable-next-line no-bitwise\n    return aNode.compareDocumentPosition(bNode) & Node.DOCUMENT_POSITION_PRECEDING ? 1 : -1;\n  });\n  return new Map(subitemsArray.map(item => [item.key, item.subitem]));\n}\n\n/**\n * Provides a way for a component to know about its children.\n *\n * Child components register themselves with the `useCompoundItem` hook, passing in arbitrary metadata to the parent.\n *\n * This is a more powerful altervantive to `children` traversal, as child components don't have to be placed\n * directly inside the parent component. They can be anywhere in the tree (and even rendered by other components).\n *\n * The downside is that this doesn't work with SSR as it relies on the useEffect hook.\n *\n * @ignore - internal hook.\n */\nexport function useCompoundParent() {\n  const [subitems, setSubitems] = React.useState(new Map());\n  const subitemKeys = React.useRef(new Set());\n  const deregisterItem = React.useCallback(function deregisterItem(id) {\n    subitemKeys.current.delete(id);\n    setSubitems(previousState => {\n      const newState = new Map(previousState);\n      newState.delete(id);\n      return newState;\n    });\n  }, []);\n  const registerItem = React.useCallback(function registerItem(id, item) {\n    let providedOrGeneratedId;\n    if (typeof id === 'function') {\n      providedOrGeneratedId = id(subitemKeys.current);\n    } else {\n      providedOrGeneratedId = id;\n    }\n    subitemKeys.current.add(providedOrGeneratedId);\n    setSubitems(previousState => {\n      const newState = new Map(previousState);\n      newState.set(providedOrGeneratedId, item);\n      return newState;\n    });\n    return {\n      id: providedOrGeneratedId,\n      deregister: () => deregisterItem(providedOrGeneratedId)\n    };\n  }, [deregisterItem]);\n  const sortedSubitems = React.useMemo(() => sortSubitems(subitems), [subitems]);\n  const getItemIndex = React.useCallback(function getItemIndex(id) {\n    return Array.from(sortedSubitems.keys()).indexOf(id);\n  }, [sortedSubitems]);\n  const contextValue = React.useMemo(() => ({\n    getItemIndex,\n    registerItem,\n    totalSubitemCount: subitems.size\n  }), [getItemIndex, registerItem, subitems.size]);\n  return {\n    contextValue,\n    subitems: sortedSubitems\n  };\n}", "map": {"version": 3, "names": ["React", "CompoundComponentContext", "createContext", "displayName", "sortSubitems", "subitems", "subitemsArray", "Array", "from", "keys", "map", "key", "subitem", "get", "sort", "a", "b", "aNode", "ref", "current", "bNode", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_PRECEDING", "Map", "item", "useCompoundParent", "setSubitems", "useState", "subitemKeys", "useRef", "Set", "deregisterItem", "useCallback", "id", "delete", "previousState", "newState", "registerItem", "providedOrGeneratedId", "add", "set", "deregister", "sortedSubitems", "useMemo", "getItemIndex", "indexOf", "contextValue", "totalSubitemCount", "size"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/useCompound/useCompoundParent.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nexport const CompoundComponentContext = /*#__PURE__*/React.createContext(null);\nCompoundComponentContext.displayName = 'CompoundComponentContext';\n/**\n * Sorts the subitems by their position in the DOM.\n */\nfunction sortSubitems(subitems) {\n  const subitemsArray = Array.from(subitems.keys()).map(key => {\n    const subitem = subitems.get(key);\n    return {\n      key,\n      subitem\n    };\n  });\n  subitemsArray.sort((a, b) => {\n    const aNode = a.subitem.ref.current;\n    const bNode = b.subitem.ref.current;\n    if (aNode === null || bNode === null || aNode === bNode) {\n      return 0;\n    }\n\n    // eslint-disable-next-line no-bitwise\n    return aNode.compareDocumentPosition(bNode) & Node.DOCUMENT_POSITION_PRECEDING ? 1 : -1;\n  });\n  return new Map(subitemsArray.map(item => [item.key, item.subitem]));\n}\n\n/**\n * Provides a way for a component to know about its children.\n *\n * Child components register themselves with the `useCompoundItem` hook, passing in arbitrary metadata to the parent.\n *\n * This is a more powerful altervantive to `children` traversal, as child components don't have to be placed\n * directly inside the parent component. They can be anywhere in the tree (and even rendered by other components).\n *\n * The downside is that this doesn't work with SSR as it relies on the useEffect hook.\n *\n * @ignore - internal hook.\n */\nexport function useCompoundParent() {\n  const [subitems, setSubitems] = React.useState(new Map());\n  const subitemKeys = React.useRef(new Set());\n  const deregisterItem = React.useCallback(function deregisterItem(id) {\n    subitemKeys.current.delete(id);\n    setSubitems(previousState => {\n      const newState = new Map(previousState);\n      newState.delete(id);\n      return newState;\n    });\n  }, []);\n  const registerItem = React.useCallback(function registerItem(id, item) {\n    let providedOrGeneratedId;\n    if (typeof id === 'function') {\n      providedOrGeneratedId = id(subitemKeys.current);\n    } else {\n      providedOrGeneratedId = id;\n    }\n    subitemKeys.current.add(providedOrGeneratedId);\n    setSubitems(previousState => {\n      const newState = new Map(previousState);\n      newState.set(providedOrGeneratedId, item);\n      return newState;\n    });\n    return {\n      id: providedOrGeneratedId,\n      deregister: () => deregisterItem(providedOrGeneratedId)\n    };\n  }, [deregisterItem]);\n  const sortedSubitems = React.useMemo(() => sortSubitems(subitems), [subitems]);\n  const getItemIndex = React.useCallback(function getItemIndex(id) {\n    return Array.from(sortedSubitems.keys()).indexOf(id);\n  }, [sortedSubitems]);\n  const contextValue = React.useMemo(() => ({\n    getItemIndex,\n    registerItem,\n    totalSubitemCount: subitems.size\n  }), [getItemIndex, registerItem, subitems.size]);\n  return {\n    contextValue,\n    subitems: sortedSubitems\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,wBAAwB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC9ED,wBAAwB,CAACE,WAAW,GAAG,0BAA0B;AACjE;AACA;AACA;AACA,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC9B,MAAMC,aAAa,GAAGC,KAAK,CAACC,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAACC,GAAG,IAAI;IAC3D,MAAMC,OAAO,GAAGP,QAAQ,CAACQ,GAAG,CAACF,GAAG,CAAC;IACjC,OAAO;MACLA,GAAG;MACHC;IACF,CAAC;EACH,CAAC,CAAC;EACFN,aAAa,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC3B,MAAMC,KAAK,GAAGF,CAAC,CAACH,OAAO,CAACM,GAAG,CAACC,OAAO;IACnC,MAAMC,KAAK,GAAGJ,CAAC,CAACJ,OAAO,CAACM,GAAG,CAACC,OAAO;IACnC,IAAIF,KAAK,KAAK,IAAI,IAAIG,KAAK,KAAK,IAAI,IAAIH,KAAK,KAAKG,KAAK,EAAE;MACvD,OAAO,CAAC;IACV;;IAEA;IACA,OAAOH,KAAK,CAACI,uBAAuB,CAACD,KAAK,CAAC,GAAGE,IAAI,CAACC,2BAA2B,GAAG,CAAC,GAAG,CAAC,CAAC;EACzF,CAAC,CAAC;EACF,OAAO,IAAIC,GAAG,CAAClB,aAAa,CAACI,GAAG,CAACe,IAAI,IAAI,CAACA,IAAI,CAACd,GAAG,EAAEc,IAAI,CAACb,OAAO,CAAC,CAAC,CAAC;AACrE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASc,iBAAiBA,CAAA,EAAG;EAClC,MAAM,CAACrB,QAAQ,EAAEsB,WAAW,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC,IAAIJ,GAAG,CAAC,CAAC,CAAC;EACzD,MAAMK,WAAW,GAAG7B,KAAK,CAAC8B,MAAM,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;EAC3C,MAAMC,cAAc,GAAGhC,KAAK,CAACiC,WAAW,CAAC,SAASD,cAAcA,CAACE,EAAE,EAAE;IACnEL,WAAW,CAACV,OAAO,CAACgB,MAAM,CAACD,EAAE,CAAC;IAC9BP,WAAW,CAACS,aAAa,IAAI;MAC3B,MAAMC,QAAQ,GAAG,IAAIb,GAAG,CAACY,aAAa,CAAC;MACvCC,QAAQ,CAACF,MAAM,CAACD,EAAE,CAAC;MACnB,OAAOG,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,YAAY,GAAGtC,KAAK,CAACiC,WAAW,CAAC,SAASK,YAAYA,CAACJ,EAAE,EAAET,IAAI,EAAE;IACrE,IAAIc,qBAAqB;IACzB,IAAI,OAAOL,EAAE,KAAK,UAAU,EAAE;MAC5BK,qBAAqB,GAAGL,EAAE,CAACL,WAAW,CAACV,OAAO,CAAC;IACjD,CAAC,MAAM;MACLoB,qBAAqB,GAAGL,EAAE;IAC5B;IACAL,WAAW,CAACV,OAAO,CAACqB,GAAG,CAACD,qBAAqB,CAAC;IAC9CZ,WAAW,CAACS,aAAa,IAAI;MAC3B,MAAMC,QAAQ,GAAG,IAAIb,GAAG,CAACY,aAAa,CAAC;MACvCC,QAAQ,CAACI,GAAG,CAACF,qBAAqB,EAAEd,IAAI,CAAC;MACzC,OAAOY,QAAQ;IACjB,CAAC,CAAC;IACF,OAAO;MACLH,EAAE,EAAEK,qBAAqB;MACzBG,UAAU,EAAEA,CAAA,KAAMV,cAAc,CAACO,qBAAqB;IACxD,CAAC;EACH,CAAC,EAAE,CAACP,cAAc,CAAC,CAAC;EACpB,MAAMW,cAAc,GAAG3C,KAAK,CAAC4C,OAAO,CAAC,MAAMxC,YAAY,CAACC,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC9E,MAAMwC,YAAY,GAAG7C,KAAK,CAACiC,WAAW,CAAC,SAASY,YAAYA,CAACX,EAAE,EAAE;IAC/D,OAAO3B,KAAK,CAACC,IAAI,CAACmC,cAAc,CAAClC,IAAI,CAAC,CAAC,CAAC,CAACqC,OAAO,CAACZ,EAAE,CAAC;EACtD,CAAC,EAAE,CAACS,cAAc,CAAC,CAAC;EACpB,MAAMI,YAAY,GAAG/C,KAAK,CAAC4C,OAAO,CAAC,OAAO;IACxCC,YAAY;IACZP,YAAY;IACZU,iBAAiB,EAAE3C,QAAQ,CAAC4C;EAC9B,CAAC,CAAC,EAAE,CAACJ,YAAY,EAAEP,YAAY,EAAEjC,QAAQ,CAAC4C,IAAI,CAAC,CAAC;EAChD,OAAO;IACLF,YAAY;IACZ1C,QAAQ,EAAEsC;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}