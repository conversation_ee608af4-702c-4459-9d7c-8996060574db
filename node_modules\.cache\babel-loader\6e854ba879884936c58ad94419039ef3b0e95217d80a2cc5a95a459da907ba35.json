{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useIsDayDisabled } from '../internals/hooks/validation/useDateValidation';\nimport { useUtils, useNow } from '../internals/hooks/useUtils';\nexport const createCalendarStateReducer = (reduceAnimations, disableSwitchToMonthOnDayFocus, utils) => (state, action) => {\n  switch (action.type) {\n    case 'changeMonth':\n      return _extends({}, state, {\n        slideDirection: action.direction,\n        currentMonth: action.newMonth,\n        isMonthSwitchingAnimating: !reduceAnimations\n      });\n    case 'finishMonthSwitchingAnimation':\n      return _extends({}, state, {\n        isMonthSwitchingAnimating: false\n      });\n    case 'changeFocusedDay':\n      {\n        if (state.focusedDay != null && action.focusedDay != null && utils.isSameDay(action.focusedDay, state.focusedDay)) {\n          return state;\n        }\n        const needMonthSwitch = action.focusedDay != null && !disableSwitchToMonthOnDayFocus && !utils.isSameMonth(state.currentMonth, action.focusedDay);\n        return _extends({}, state, {\n          focusedDay: action.focusedDay,\n          isMonthSwitchingAnimating: needMonthSwitch && !reduceAnimations && !action.withoutMonthSwitchingAnimation,\n          currentMonth: needMonthSwitch ? utils.startOfMonth(action.focusedDay) : state.currentMonth,\n          slideDirection: action.focusedDay != null && utils.isAfterDay(action.focusedDay, state.currentMonth) ? 'left' : 'right'\n        });\n      }\n    default:\n      throw new Error('missing support');\n  }\n};\nexport const useCalendarState = _ref2 => {\n  let {\n    date,\n    defaultCalendarMonth,\n    disableFuture,\n    disablePast,\n    disableSwitchToMonthOnDayFocus = false,\n    maxDate,\n    minDate,\n    onMonthChange,\n    reduceAnimations,\n    shouldDisableDate\n  } = _ref2;\n  var _ref;\n  const now = useNow();\n  const utils = useUtils();\n  const reducerFn = React.useRef(createCalendarStateReducer(Boolean(reduceAnimations), disableSwitchToMonthOnDayFocus, utils)).current;\n  const [calendarState, dispatch] = React.useReducer(reducerFn, {\n    isMonthSwitchingAnimating: false,\n    focusedDay: date || now,\n    currentMonth: utils.startOfMonth((_ref = date != null ? date : defaultCalendarMonth) != null ? _ref : now),\n    slideDirection: 'left'\n  });\n  const handleChangeMonth = React.useCallback(payload => {\n    dispatch(_extends({\n      type: 'changeMonth'\n    }, payload));\n    if (onMonthChange) {\n      onMonthChange(payload.newMonth);\n    }\n  }, [onMonthChange]);\n  const changeMonth = React.useCallback(newDate => {\n    const newDateRequested = newDate != null ? newDate : now;\n    if (utils.isSameMonth(newDateRequested, calendarState.currentMonth)) {\n      return;\n    }\n    handleChangeMonth({\n      newMonth: utils.startOfMonth(newDateRequested),\n      direction: utils.isAfterDay(newDateRequested, calendarState.currentMonth) ? 'left' : 'right'\n    });\n  }, [calendarState.currentMonth, handleChangeMonth, now, utils]);\n  const isDateDisabled = useIsDayDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast\n  });\n  const onMonthSwitchingAnimationEnd = React.useCallback(() => {\n    dispatch({\n      type: 'finishMonthSwitchingAnimation'\n    });\n  }, []);\n  const changeFocusedDay = React.useCallback((newFocusedDate, withoutMonthSwitchingAnimation) => {\n    if (!isDateDisabled(newFocusedDate)) {\n      dispatch({\n        type: 'changeFocusedDay',\n        focusedDay: newFocusedDate,\n        withoutMonthSwitchingAnimation\n      });\n    }\n  }, [isDateDisabled]);\n  return {\n    calendarState,\n    changeMonth,\n    changeFocusedDay,\n    isDateDisabled,\n    onMonthSwitchingAnimationEnd,\n    handleChangeMonth\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useIsDayDisabled", "useUtils", "useNow", "createCalendarStateReducer", "reduceAnimations", "disableSwitchToMonthOnDayFocus", "utils", "state", "action", "type", "slideDirection", "direction", "currentMonth", "newMonth", "isMonthSwitchingAnimating", "focusedDay", "isSameDay", "needMonthSwitch", "isSameMonth", "withoutMonthSwitchingAnimation", "startOfMonth", "isAfterDay", "Error", "useCalendarState", "_ref2", "date", "defaultCalendarMonth", "disableFuture", "disablePast", "maxDate", "minDate", "onMonthChange", "shouldDisableDate", "_ref", "now", "reducerFn", "useRef", "Boolean", "current", "calendarState", "dispatch", "useReducer", "handleChangeMonth", "useCallback", "payload", "changeMonth", "newDate", "newDateRequested", "isDateDisabled", "onMonthSwitchingAnimationEnd", "changeFocusedDay", "newFocusedDate"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/CalendarPicker/useCalendarState.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useIsDayDisabled } from '../internals/hooks/validation/useDateValidation';\nimport { useUtils, useNow } from '../internals/hooks/useUtils';\nexport const createCalendarStateReducer = (reduceAnimations, disableSwitchToMonthOnDayFocus, utils) => (state, action) => {\n  switch (action.type) {\n    case 'changeMonth':\n      return _extends({}, state, {\n        slideDirection: action.direction,\n        currentMonth: action.newMonth,\n        isMonthSwitchingAnimating: !reduceAnimations\n      });\n\n    case 'finishMonthSwitchingAnimation':\n      return _extends({}, state, {\n        isMonthSwitchingAnimating: false\n      });\n\n    case 'changeFocusedDay':\n      {\n        if (state.focusedDay != null && action.focusedDay != null && utils.isSameDay(action.focusedDay, state.focusedDay)) {\n          return state;\n        }\n\n        const needMonthSwitch = action.focusedDay != null && !disableSwitchToMonthOnDayFocus && !utils.isSameMonth(state.currentMonth, action.focusedDay);\n        return _extends({}, state, {\n          focusedDay: action.focusedDay,\n          isMonthSwitchingAnimating: needMonthSwitch && !reduceAnimations && !action.withoutMonthSwitchingAnimation,\n          currentMonth: needMonthSwitch ? utils.startOfMonth(action.focusedDay) : state.currentMonth,\n          slideDirection: action.focusedDay != null && utils.isAfterDay(action.focusedDay, state.currentMonth) ? 'left' : 'right'\n        });\n      }\n\n    default:\n      throw new Error('missing support');\n  }\n};\nexport const useCalendarState = ({\n  date,\n  defaultCalendarMonth,\n  disableFuture,\n  disablePast,\n  disableSwitchToMonthOnDayFocus = false,\n  maxDate,\n  minDate,\n  onMonthChange,\n  reduceAnimations,\n  shouldDisableDate\n}) => {\n  var _ref;\n\n  const now = useNow();\n  const utils = useUtils();\n  const reducerFn = React.useRef(createCalendarStateReducer(Boolean(reduceAnimations), disableSwitchToMonthOnDayFocus, utils)).current;\n  const [calendarState, dispatch] = React.useReducer(reducerFn, {\n    isMonthSwitchingAnimating: false,\n    focusedDay: date || now,\n    currentMonth: utils.startOfMonth((_ref = date != null ? date : defaultCalendarMonth) != null ? _ref : now),\n    slideDirection: 'left'\n  });\n  const handleChangeMonth = React.useCallback(payload => {\n    dispatch(_extends({\n      type: 'changeMonth'\n    }, payload));\n\n    if (onMonthChange) {\n      onMonthChange(payload.newMonth);\n    }\n  }, [onMonthChange]);\n  const changeMonth = React.useCallback(newDate => {\n    const newDateRequested = newDate != null ? newDate : now;\n\n    if (utils.isSameMonth(newDateRequested, calendarState.currentMonth)) {\n      return;\n    }\n\n    handleChangeMonth({\n      newMonth: utils.startOfMonth(newDateRequested),\n      direction: utils.isAfterDay(newDateRequested, calendarState.currentMonth) ? 'left' : 'right'\n    });\n  }, [calendarState.currentMonth, handleChangeMonth, now, utils]);\n  const isDateDisabled = useIsDayDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast\n  });\n  const onMonthSwitchingAnimationEnd = React.useCallback(() => {\n    dispatch({\n      type: 'finishMonthSwitchingAnimation'\n    });\n  }, []);\n  const changeFocusedDay = React.useCallback((newFocusedDate, withoutMonthSwitchingAnimation) => {\n    if (!isDateDisabled(newFocusedDate)) {\n      dispatch({\n        type: 'changeFocusedDay',\n        focusedDay: newFocusedDate,\n        withoutMonthSwitchingAnimation\n      });\n    }\n  }, [isDateDisabled]);\n  return {\n    calendarState,\n    changeMonth,\n    changeFocusedDay,\n    isDateDisabled,\n    onMonthSwitchingAnimationEnd,\n    handleChangeMonth\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,iDAAiD;AAClF,SAASC,QAAQ,EAAEC,MAAM,QAAQ,6BAA6B;AAC9D,OAAO,MAAMC,0BAA0B,GAAGA,CAACC,gBAAgB,EAAEC,8BAA8B,EAAEC,KAAK,KAAK,CAACC,KAAK,EAAEC,MAAM,KAAK;EACxH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAChB,OAAOX,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,EAAE;QACzBG,cAAc,EAAEF,MAAM,CAACG,SAAS;QAChCC,YAAY,EAAEJ,MAAM,CAACK,QAAQ;QAC7BC,yBAAyB,EAAE,CAACV;MAC9B,CAAC,CAAC;IAEJ,KAAK,+BAA+B;MAClC,OAAON,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,EAAE;QACzBO,yBAAyB,EAAE;MAC7B,CAAC,CAAC;IAEJ,KAAK,kBAAkB;MACrB;QACE,IAAIP,KAAK,CAACQ,UAAU,IAAI,IAAI,IAAIP,MAAM,CAACO,UAAU,IAAI,IAAI,IAAIT,KAAK,CAACU,SAAS,CAACR,MAAM,CAACO,UAAU,EAAER,KAAK,CAACQ,UAAU,CAAC,EAAE;UACjH,OAAOR,KAAK;QACd;QAEA,MAAMU,eAAe,GAAGT,MAAM,CAACO,UAAU,IAAI,IAAI,IAAI,CAACV,8BAA8B,IAAI,CAACC,KAAK,CAACY,WAAW,CAACX,KAAK,CAACK,YAAY,EAAEJ,MAAM,CAACO,UAAU,CAAC;QACjJ,OAAOjB,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,EAAE;UACzBQ,UAAU,EAAEP,MAAM,CAACO,UAAU;UAC7BD,yBAAyB,EAAEG,eAAe,IAAI,CAACb,gBAAgB,IAAI,CAACI,MAAM,CAACW,8BAA8B;UACzGP,YAAY,EAAEK,eAAe,GAAGX,KAAK,CAACc,YAAY,CAACZ,MAAM,CAACO,UAAU,CAAC,GAAGR,KAAK,CAACK,YAAY;UAC1FF,cAAc,EAAEF,MAAM,CAACO,UAAU,IAAI,IAAI,IAAIT,KAAK,CAACe,UAAU,CAACb,MAAM,CAACO,UAAU,EAAER,KAAK,CAACK,YAAY,CAAC,GAAG,MAAM,GAAG;QAClH,CAAC,CAAC;MACJ;IAEF;MACE,MAAM,IAAIU,KAAK,CAAC,iBAAiB,CAAC;EACtC;AACF,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGC,KAAA,IAW1B;EAAA,IAX2B;IAC/BC,IAAI;IACJC,oBAAoB;IACpBC,aAAa;IACbC,WAAW;IACXvB,8BAA8B,GAAG,KAAK;IACtCwB,OAAO;IACPC,OAAO;IACPC,aAAa;IACb3B,gBAAgB;IAChB4B;EACF,CAAC,GAAAR,KAAA;EACC,IAAIS,IAAI;EAER,MAAMC,GAAG,GAAGhC,MAAM,CAAC,CAAC;EACpB,MAAMI,KAAK,GAAGL,QAAQ,CAAC,CAAC;EACxB,MAAMkC,SAAS,GAAGpC,KAAK,CAACqC,MAAM,CAACjC,0BAA0B,CAACkC,OAAO,CAACjC,gBAAgB,CAAC,EAAEC,8BAA8B,EAAEC,KAAK,CAAC,CAAC,CAACgC,OAAO;EACpI,MAAM,CAACC,aAAa,EAAEC,QAAQ,CAAC,GAAGzC,KAAK,CAAC0C,UAAU,CAACN,SAAS,EAAE;IAC5DrB,yBAAyB,EAAE,KAAK;IAChCC,UAAU,EAAEU,IAAI,IAAIS,GAAG;IACvBtB,YAAY,EAAEN,KAAK,CAACc,YAAY,CAAC,CAACa,IAAI,GAAGR,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAGC,oBAAoB,KAAK,IAAI,GAAGO,IAAI,GAAGC,GAAG,CAAC;IAC1GxB,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAMgC,iBAAiB,GAAG3C,KAAK,CAAC4C,WAAW,CAACC,OAAO,IAAI;IACrDJ,QAAQ,CAAC1C,QAAQ,CAAC;MAChBW,IAAI,EAAE;IACR,CAAC,EAAEmC,OAAO,CAAC,CAAC;IAEZ,IAAIb,aAAa,EAAE;MACjBA,aAAa,CAACa,OAAO,CAAC/B,QAAQ,CAAC;IACjC;EACF,CAAC,EAAE,CAACkB,aAAa,CAAC,CAAC;EACnB,MAAMc,WAAW,GAAG9C,KAAK,CAAC4C,WAAW,CAACG,OAAO,IAAI;IAC/C,MAAMC,gBAAgB,GAAGD,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAGZ,GAAG;IAExD,IAAI5B,KAAK,CAACY,WAAW,CAAC6B,gBAAgB,EAAER,aAAa,CAAC3B,YAAY,CAAC,EAAE;MACnE;IACF;IAEA8B,iBAAiB,CAAC;MAChB7B,QAAQ,EAAEP,KAAK,CAACc,YAAY,CAAC2B,gBAAgB,CAAC;MAC9CpC,SAAS,EAAEL,KAAK,CAACe,UAAU,CAAC0B,gBAAgB,EAAER,aAAa,CAAC3B,YAAY,CAAC,GAAG,MAAM,GAAG;IACvF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC2B,aAAa,CAAC3B,YAAY,EAAE8B,iBAAiB,EAAER,GAAG,EAAE5B,KAAK,CAAC,CAAC;EAC/D,MAAM0C,cAAc,GAAGhD,gBAAgB,CAAC;IACtCgC,iBAAiB;IACjBF,OAAO;IACPD,OAAO;IACPF,aAAa;IACbC;EACF,CAAC,CAAC;EACF,MAAMqB,4BAA4B,GAAGlD,KAAK,CAAC4C,WAAW,CAAC,MAAM;IAC3DH,QAAQ,CAAC;MACP/B,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAMyC,gBAAgB,GAAGnD,KAAK,CAAC4C,WAAW,CAAC,CAACQ,cAAc,EAAEhC,8BAA8B,KAAK;IAC7F,IAAI,CAAC6B,cAAc,CAACG,cAAc,CAAC,EAAE;MACnCX,QAAQ,CAAC;QACP/B,IAAI,EAAE,kBAAkB;QACxBM,UAAU,EAAEoC,cAAc;QAC1BhC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC6B,cAAc,CAAC,CAAC;EACpB,OAAO;IACLT,aAAa;IACbM,WAAW;IACXK,gBAAgB;IAChBF,cAAc;IACdC,4BAA4B;IAC5BP;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}