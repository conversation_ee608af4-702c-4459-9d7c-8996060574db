{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { elementAcceptingRef, exactProp, unstable_ownerDocument as ownerDocument, unstable_useForkRef as useForkRef, unstable_useEventCallback as useEventCallback } from '@mui/utils';\n\n// TODO: return `EventHandlerName extends `on${infer EventName}` ? Lowercase<EventName> : never` once generatePropTypes runs with TS 4.1\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction mapEventPropToEvent(eventProp) {\n  return eventProp.substring(2).toLowerCase();\n}\nfunction clickedRootScrollbar(event, doc) {\n  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;\n}\n/**\n * Listen for click events that occur somewhere in the document, outside of the element itself.\n * For instance, if you need to hide a menu when people click anywhere else on your page.\n *\n * Demos:\n *\n * - [Click-Away Listener](https://mui.com/base-ui/react-click-away-listener/)\n *\n * API:\n *\n * - [ClickAwayListener API](https://mui.com/base-ui/react-click-away-listener/components-api/#click-away-listener)\n */\nfunction ClickAwayListener(props) {\n  const {\n    children,\n    disableReactTree = false,\n    mouseEvent = 'onClick',\n    onClickAway,\n    touchEvent = 'onTouchEnd'\n  } = props;\n  const movedRef = React.useRef(false);\n  const nodeRef = React.useRef(null);\n  const activatedRef = React.useRef(false);\n  const syntheticEventRef = React.useRef(false);\n  React.useEffect(() => {\n    // Ensure that this component is not \"activated\" synchronously.\n    // https://github.com/facebook/react/issues/20074\n    setTimeout(() => {\n      activatedRef.current = true;\n    }, 0);\n    return () => {\n      activatedRef.current = false;\n    };\n  }, []);\n  const handleRef = useForkRef(\n  // @ts-expect-error TODO upstream fix\n  children.ref, nodeRef);\n\n  // The handler doesn't take event.defaultPrevented into account:\n  //\n  // event.preventDefault() is meant to stop default behaviors like\n  // clicking a checkbox to check it, hitting a button to submit a form,\n  // and hitting left arrow to move the cursor in a text input etc.\n  // Only special HTML elements have these default behaviors.\n  const handleClickAway = useEventCallback(event => {\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = syntheticEventRef.current;\n    syntheticEventRef.current = false;\n    const doc = ownerDocument(nodeRef.current);\n\n    // 1. IE11 support, which trigger the handleClickAway even after the unbind\n    // 2. The child might render null.\n    // 3. Behave like a blur listener.\n    if (!activatedRef.current || !nodeRef.current || 'clientX' in event && clickedRootScrollbar(event, doc)) {\n      return;\n    }\n\n    // Do not act if user performed touchmove\n    if (movedRef.current) {\n      movedRef.current = false;\n      return;\n    }\n    let insideDOM;\n\n    // If not enough, can use https://github.com/DieterHolvoet/event-propagation-path/blob/master/propagationPath.js\n    if (event.composedPath) {\n      insideDOM = event.composedPath().indexOf(nodeRef.current) > -1;\n    } else {\n      insideDOM = !doc.documentElement.contains(\n      // @ts-expect-error returns `false` as intended when not dispatched from a Node\n      event.target) || nodeRef.current.contains(\n      // @ts-expect-error returns `false` as intended when not dispatched from a Node\n      event.target);\n    }\n    if (!insideDOM && (disableReactTree || !insideReactTree)) {\n      onClickAway(event);\n    }\n  });\n\n  // Keep track of mouse/touch events that bubbled up through the portal.\n  const createHandleSynthetic = handlerName => event => {\n    syntheticEventRef.current = true;\n    const childrenPropsHandler = children.props[handlerName];\n    if (childrenPropsHandler) {\n      childrenPropsHandler(event);\n    }\n  };\n  const childrenProps = {\n    ref: handleRef\n  };\n  if (touchEvent !== false) {\n    childrenProps[touchEvent] = createHandleSynthetic(touchEvent);\n  }\n  React.useEffect(() => {\n    if (touchEvent !== false) {\n      const mappedTouchEvent = mapEventPropToEvent(touchEvent);\n      const doc = ownerDocument(nodeRef.current);\n      const handleTouchMove = () => {\n        movedRef.current = true;\n      };\n      doc.addEventListener(mappedTouchEvent, handleClickAway);\n      doc.addEventListener('touchmove', handleTouchMove);\n      return () => {\n        doc.removeEventListener(mappedTouchEvent, handleClickAway);\n        doc.removeEventListener('touchmove', handleTouchMove);\n      };\n    }\n    return undefined;\n  }, [handleClickAway, touchEvent]);\n  if (mouseEvent !== false) {\n    childrenProps[mouseEvent] = createHandleSynthetic(mouseEvent);\n  }\n  React.useEffect(() => {\n    if (mouseEvent !== false) {\n      const mappedMouseEvent = mapEventPropToEvent(mouseEvent);\n      const doc = ownerDocument(nodeRef.current);\n      doc.addEventListener(mappedMouseEvent, handleClickAway);\n      return () => {\n        doc.removeEventListener(mappedMouseEvent, handleClickAway);\n      };\n    }\n    return undefined;\n  }, [handleClickAway, mouseEvent]);\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: /*#__PURE__*/React.cloneElement(children, childrenProps)\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ClickAwayListener.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The wrapped element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * If `true`, the React tree is ignored and only the DOM tree is considered.\n   * This prop changes how portaled elements are handled.\n   * @default false\n   */\n  disableReactTree: PropTypes.bool,\n  /**\n   * The mouse event to listen to. You can disable the listener by providing `false`.\n   * @default 'onClick'\n   */\n  mouseEvent: PropTypes.oneOf(['onClick', 'onMouseDown', 'onMouseUp', 'onPointerDown', 'onPointerUp', false]),\n  /**\n   * Callback fired when a \"click away\" event is detected.\n   */\n  onClickAway: PropTypes.func.isRequired,\n  /**\n   * The touch event to listen to. You can disable the listener by providing `false`.\n   * @default 'onTouchEnd'\n   */\n  touchEvent: PropTypes.oneOf(['onTouchEnd', 'onTouchStart', false])\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  ClickAwayListener['propTypes' + ''] = exactProp(ClickAwayListener.propTypes);\n}\nexport { ClickAwayListener };", "map": {"version": 3, "names": ["React", "PropTypes", "elementAcceptingRef", "exactProp", "unstable_ownerDocument", "ownerDocument", "unstable_useForkRef", "useForkRef", "unstable_useEventCallback", "useEventCallback", "jsx", "_jsx", "mapEventPropToEvent", "eventProp", "substring", "toLowerCase", "clickedRootScrollbar", "event", "doc", "documentElement", "clientWidth", "clientX", "clientHeight", "clientY", "ClickAwayListener", "props", "children", "disableReactTree", "mouseEvent", "onClickAway", "touchEvent", "movedRef", "useRef", "nodeRef", "activatedRef", "syntheticEventRef", "useEffect", "setTimeout", "current", "handleRef", "ref", "handleClickAway", "insideReactTree", "insideDOM", "<PERSON><PERSON><PERSON>", "indexOf", "contains", "target", "createHandleSynthetic", "handler<PERSON>ame", "childrenPropsHandler", "childrenProps", "mappedTouchEvent", "handleTouchMove", "addEventListener", "removeEventListener", "undefined", "mappedMouseEvent", "Fragment", "cloneElement", "process", "env", "NODE_ENV", "propTypes", "isRequired", "bool", "oneOf", "func"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/ClickAwayListener/ClickAwayListener.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { elementAcceptingRef, exactProp, unstable_ownerDocument as ownerDocument, unstable_useForkRef as useForkRef, unstable_useEventCallback as useEventCallback } from '@mui/utils';\n\n// TODO: return `EventHandlerName extends `on${infer EventName}` ? Lowercase<EventName> : never` once generatePropTypes runs with TS 4.1\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction mapEventPropToEvent(eventProp) {\n  return eventProp.substring(2).toLowerCase();\n}\nfunction clickedRootScrollbar(event, doc) {\n  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;\n}\n/**\n * Listen for click events that occur somewhere in the document, outside of the element itself.\n * For instance, if you need to hide a menu when people click anywhere else on your page.\n *\n * Demos:\n *\n * - [Click-Away Listener](https://mui.com/base-ui/react-click-away-listener/)\n *\n * API:\n *\n * - [ClickAwayListener API](https://mui.com/base-ui/react-click-away-listener/components-api/#click-away-listener)\n */\nfunction ClickAwayListener(props) {\n  const {\n    children,\n    disableReactTree = false,\n    mouseEvent = 'onClick',\n    onClickAway,\n    touchEvent = 'onTouchEnd'\n  } = props;\n  const movedRef = React.useRef(false);\n  const nodeRef = React.useRef(null);\n  const activatedRef = React.useRef(false);\n  const syntheticEventRef = React.useRef(false);\n  React.useEffect(() => {\n    // Ensure that this component is not \"activated\" synchronously.\n    // https://github.com/facebook/react/issues/20074\n    setTimeout(() => {\n      activatedRef.current = true;\n    }, 0);\n    return () => {\n      activatedRef.current = false;\n    };\n  }, []);\n  const handleRef = useForkRef(\n  // @ts-expect-error TODO upstream fix\n  children.ref, nodeRef);\n\n  // The handler doesn't take event.defaultPrevented into account:\n  //\n  // event.preventDefault() is meant to stop default behaviors like\n  // clicking a checkbox to check it, hitting a button to submit a form,\n  // and hitting left arrow to move the cursor in a text input etc.\n  // Only special HTML elements have these default behaviors.\n  const handleClickAway = useEventCallback(event => {\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = syntheticEventRef.current;\n    syntheticEventRef.current = false;\n    const doc = ownerDocument(nodeRef.current);\n\n    // 1. IE11 support, which trigger the handleClickAway even after the unbind\n    // 2. The child might render null.\n    // 3. Behave like a blur listener.\n    if (!activatedRef.current || !nodeRef.current || 'clientX' in event && clickedRootScrollbar(event, doc)) {\n      return;\n    }\n\n    // Do not act if user performed touchmove\n    if (movedRef.current) {\n      movedRef.current = false;\n      return;\n    }\n    let insideDOM;\n\n    // If not enough, can use https://github.com/DieterHolvoet/event-propagation-path/blob/master/propagationPath.js\n    if (event.composedPath) {\n      insideDOM = event.composedPath().indexOf(nodeRef.current) > -1;\n    } else {\n      insideDOM = !doc.documentElement.contains(\n      // @ts-expect-error returns `false` as intended when not dispatched from a Node\n      event.target) || nodeRef.current.contains(\n      // @ts-expect-error returns `false` as intended when not dispatched from a Node\n      event.target);\n    }\n    if (!insideDOM && (disableReactTree || !insideReactTree)) {\n      onClickAway(event);\n    }\n  });\n\n  // Keep track of mouse/touch events that bubbled up through the portal.\n  const createHandleSynthetic = handlerName => event => {\n    syntheticEventRef.current = true;\n    const childrenPropsHandler = children.props[handlerName];\n    if (childrenPropsHandler) {\n      childrenPropsHandler(event);\n    }\n  };\n  const childrenProps = {\n    ref: handleRef\n  };\n  if (touchEvent !== false) {\n    childrenProps[touchEvent] = createHandleSynthetic(touchEvent);\n  }\n  React.useEffect(() => {\n    if (touchEvent !== false) {\n      const mappedTouchEvent = mapEventPropToEvent(touchEvent);\n      const doc = ownerDocument(nodeRef.current);\n      const handleTouchMove = () => {\n        movedRef.current = true;\n      };\n      doc.addEventListener(mappedTouchEvent, handleClickAway);\n      doc.addEventListener('touchmove', handleTouchMove);\n      return () => {\n        doc.removeEventListener(mappedTouchEvent, handleClickAway);\n        doc.removeEventListener('touchmove', handleTouchMove);\n      };\n    }\n    return undefined;\n  }, [handleClickAway, touchEvent]);\n  if (mouseEvent !== false) {\n    childrenProps[mouseEvent] = createHandleSynthetic(mouseEvent);\n  }\n  React.useEffect(() => {\n    if (mouseEvent !== false) {\n      const mappedMouseEvent = mapEventPropToEvent(mouseEvent);\n      const doc = ownerDocument(nodeRef.current);\n      doc.addEventListener(mappedMouseEvent, handleClickAway);\n      return () => {\n        doc.removeEventListener(mappedMouseEvent, handleClickAway);\n      };\n    }\n    return undefined;\n  }, [handleClickAway, mouseEvent]);\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: /*#__PURE__*/React.cloneElement(children, childrenProps)\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ClickAwayListener.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The wrapped element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * If `true`, the React tree is ignored and only the DOM tree is considered.\n   * This prop changes how portaled elements are handled.\n   * @default false\n   */\n  disableReactTree: PropTypes.bool,\n  /**\n   * The mouse event to listen to. You can disable the listener by providing `false`.\n   * @default 'onClick'\n   */\n  mouseEvent: PropTypes.oneOf(['onClick', 'onMouseDown', 'onMouseUp', 'onPointerDown', 'onPointerUp', false]),\n  /**\n   * Callback fired when a \"click away\" event is detected.\n   */\n  onClickAway: PropTypes.func.isRequired,\n  /**\n   * The touch event to listen to. You can disable the listener by providing `false`.\n   * @default 'onTouchEnd'\n   */\n  touchEvent: PropTypes.oneOf(['onTouchEnd', 'onTouchStart', false])\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  ClickAwayListener['propTypes' + ''] = exactProp(ClickAwayListener.propTypes);\n}\nexport { ClickAwayListener };"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,mBAAmB,EAAEC,SAAS,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;;AAEtL;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,mBAAmBA,CAACC,SAAS,EAAE;EACtC,OAAOA,SAAS,CAACC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AAC7C;AACA,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxC,OAAOA,GAAG,CAACC,eAAe,CAACC,WAAW,GAAGH,KAAK,CAACI,OAAO,IAAIH,GAAG,CAACC,eAAe,CAACG,YAAY,GAAGL,KAAK,CAACM,OAAO;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAChC,MAAM;IACJC,QAAQ;IACRC,gBAAgB,GAAG,KAAK;IACxBC,UAAU,GAAG,SAAS;IACtBC,WAAW;IACXC,UAAU,GAAG;EACf,CAAC,GAAGL,KAAK;EACT,MAAMM,QAAQ,GAAG/B,KAAK,CAACgC,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMC,OAAO,GAAGjC,KAAK,CAACgC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAME,YAAY,GAAGlC,KAAK,CAACgC,MAAM,CAAC,KAAK,CAAC;EACxC,MAAMG,iBAAiB,GAAGnC,KAAK,CAACgC,MAAM,CAAC,KAAK,CAAC;EAC7ChC,KAAK,CAACoC,SAAS,CAAC,MAAM;IACpB;IACA;IACAC,UAAU,CAAC,MAAM;MACfH,YAAY,CAACI,OAAO,GAAG,IAAI;IAC7B,CAAC,EAAE,CAAC,CAAC;IACL,OAAO,MAAM;MACXJ,YAAY,CAACI,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,SAAS,GAAGhC,UAAU;EAC5B;EACAmB,QAAQ,CAACc,GAAG,EAAEP,OAAO,CAAC;;EAEtB;EACA;EACA;EACA;EACA;EACA;EACA,MAAMQ,eAAe,GAAGhC,gBAAgB,CAACQ,KAAK,IAAI;IAChD;IACA;IACA,MAAMyB,eAAe,GAAGP,iBAAiB,CAACG,OAAO;IACjDH,iBAAiB,CAACG,OAAO,GAAG,KAAK;IACjC,MAAMpB,GAAG,GAAGb,aAAa,CAAC4B,OAAO,CAACK,OAAO,CAAC;;IAE1C;IACA;IACA;IACA,IAAI,CAACJ,YAAY,CAACI,OAAO,IAAI,CAACL,OAAO,CAACK,OAAO,IAAI,SAAS,IAAIrB,KAAK,IAAID,oBAAoB,CAACC,KAAK,EAAEC,GAAG,CAAC,EAAE;MACvG;IACF;;IAEA;IACA,IAAIa,QAAQ,CAACO,OAAO,EAAE;MACpBP,QAAQ,CAACO,OAAO,GAAG,KAAK;MACxB;IACF;IACA,IAAIK,SAAS;;IAEb;IACA,IAAI1B,KAAK,CAAC2B,YAAY,EAAE;MACtBD,SAAS,GAAG1B,KAAK,CAAC2B,YAAY,CAAC,CAAC,CAACC,OAAO,CAACZ,OAAO,CAACK,OAAO,CAAC,GAAG,CAAC,CAAC;IAChE,CAAC,MAAM;MACLK,SAAS,GAAG,CAACzB,GAAG,CAACC,eAAe,CAAC2B,QAAQ;MACzC;MACA7B,KAAK,CAAC8B,MAAM,CAAC,IAAId,OAAO,CAACK,OAAO,CAACQ,QAAQ;MACzC;MACA7B,KAAK,CAAC8B,MAAM,CAAC;IACf;IACA,IAAI,CAACJ,SAAS,KAAKhB,gBAAgB,IAAI,CAACe,eAAe,CAAC,EAAE;MACxDb,WAAW,CAACZ,KAAK,CAAC;IACpB;EACF,CAAC,CAAC;;EAEF;EACA,MAAM+B,qBAAqB,GAAGC,WAAW,IAAIhC,KAAK,IAAI;IACpDkB,iBAAiB,CAACG,OAAO,GAAG,IAAI;IAChC,MAAMY,oBAAoB,GAAGxB,QAAQ,CAACD,KAAK,CAACwB,WAAW,CAAC;IACxD,IAAIC,oBAAoB,EAAE;MACxBA,oBAAoB,CAACjC,KAAK,CAAC;IAC7B;EACF,CAAC;EACD,MAAMkC,aAAa,GAAG;IACpBX,GAAG,EAAED;EACP,CAAC;EACD,IAAIT,UAAU,KAAK,KAAK,EAAE;IACxBqB,aAAa,CAACrB,UAAU,CAAC,GAAGkB,qBAAqB,CAAClB,UAAU,CAAC;EAC/D;EACA9B,KAAK,CAACoC,SAAS,CAAC,MAAM;IACpB,IAAIN,UAAU,KAAK,KAAK,EAAE;MACxB,MAAMsB,gBAAgB,GAAGxC,mBAAmB,CAACkB,UAAU,CAAC;MACxD,MAAMZ,GAAG,GAAGb,aAAa,CAAC4B,OAAO,CAACK,OAAO,CAAC;MAC1C,MAAMe,eAAe,GAAGA,CAAA,KAAM;QAC5BtB,QAAQ,CAACO,OAAO,GAAG,IAAI;MACzB,CAAC;MACDpB,GAAG,CAACoC,gBAAgB,CAACF,gBAAgB,EAAEX,eAAe,CAAC;MACvDvB,GAAG,CAACoC,gBAAgB,CAAC,WAAW,EAAED,eAAe,CAAC;MAClD,OAAO,MAAM;QACXnC,GAAG,CAACqC,mBAAmB,CAACH,gBAAgB,EAAEX,eAAe,CAAC;QAC1DvB,GAAG,CAACqC,mBAAmB,CAAC,WAAW,EAAEF,eAAe,CAAC;MACvD,CAAC;IACH;IACA,OAAOG,SAAS;EAClB,CAAC,EAAE,CAACf,eAAe,EAAEX,UAAU,CAAC,CAAC;EACjC,IAAIF,UAAU,KAAK,KAAK,EAAE;IACxBuB,aAAa,CAACvB,UAAU,CAAC,GAAGoB,qBAAqB,CAACpB,UAAU,CAAC;EAC/D;EACA5B,KAAK,CAACoC,SAAS,CAAC,MAAM;IACpB,IAAIR,UAAU,KAAK,KAAK,EAAE;MACxB,MAAM6B,gBAAgB,GAAG7C,mBAAmB,CAACgB,UAAU,CAAC;MACxD,MAAMV,GAAG,GAAGb,aAAa,CAAC4B,OAAO,CAACK,OAAO,CAAC;MAC1CpB,GAAG,CAACoC,gBAAgB,CAACG,gBAAgB,EAAEhB,eAAe,CAAC;MACvD,OAAO,MAAM;QACXvB,GAAG,CAACqC,mBAAmB,CAACE,gBAAgB,EAAEhB,eAAe,CAAC;MAC5D,CAAC;IACH;IACA,OAAOe,SAAS;EAClB,CAAC,EAAE,CAACf,eAAe,EAAEb,UAAU,CAAC,CAAC;EACjC,OAAO,aAAajB,IAAI,CAACX,KAAK,CAAC0D,QAAQ,EAAE;IACvChC,QAAQ,EAAE,aAAa1B,KAAK,CAAC2D,YAAY,CAACjC,QAAQ,EAAEyB,aAAa;EACnE,CAAC,CAAC;AACJ;AACAS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtC,iBAAiB,CAACuC,SAAS,CAAC,yBAAyB;EAC3F;EACA;EACA;EACA;EACA;AACF;AACA;EACErC,QAAQ,EAAExB,mBAAmB,CAAC8D,UAAU;EACxC;AACF;AACA;AACA;AACA;EACErC,gBAAgB,EAAE1B,SAAS,CAACgE,IAAI;EAChC;AACF;AACA;AACA;EACErC,UAAU,EAAE3B,SAAS,CAACiE,KAAK,CAAC,CAAC,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;EAC3G;AACF;AACA;EACErC,WAAW,EAAE5B,SAAS,CAACkE,IAAI,CAACH,UAAU;EACtC;AACF;AACA;AACA;EACElC,UAAU,EAAE7B,SAAS,CAACiE,KAAK,CAAC,CAAC,YAAY,EAAE,cAAc,EAAE,KAAK,CAAC;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACAtC,iBAAiB,CAAC,WAAW,GAAG,EAAE,CAAC,GAAGrB,SAAS,CAACqB,iBAAiB,CAACuC,SAAS,CAAC;AAC9E;AACA,SAASvC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}