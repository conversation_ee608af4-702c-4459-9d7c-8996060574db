{"ast": null, "code": "import axios from 'axios';\n\n// Get base URL from environment or use window.location.origin as fallback\nconst getBaseUrl = () => {\n  if (process.env.REACT_APP_API_URL) return process.env.REACT_APP_API_URL;\n\n  // When running in production, we want to use the same origin\n  // This helps when deploying behind proxies or accessing externally\n  if (process.env.NODE_ENV === 'production') {\n    return window.location.origin;\n  }\n\n  // Default development server\n  return 'http://localhost:1976';\n};\nconst instance = axios.create({\n  baseURL: getBaseUrl(),\n  timeout: 30000,\n  // Increased timeout\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor\ninstance.interceptors.request.use(config => {\n  var _config$method;\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  console.log(`API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n  return config;\n}, error => {\n  console.error('Request error:', error);\n  return Promise.reject(error);\n});\n\n// Response interceptor\ninstance.interceptors.response.use(response => {\n  console.log(`API Response: ${response.status} for ${response.config.url}`);\n  return response;\n}, error => {\n  var _error$response;\n  console.error('Response error:', error.response || error);\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    console.log('Authentication error, redirecting to login');\n    // Clear token and redirect to login on unauthorized\n    localStorage.removeItem('token');\n    delete instance.defaults.headers.common['Authorization'];\n\n    // Use a slight delay to ensure console logs are visible\n    setTimeout(() => {\n      window.location.href = '/login';\n    }, 100);\n  }\n  return Promise.reject(error);\n});\n\n// Expose the current baseURL for debugging\nconsole.log(`API base URL: ${instance.defaults.baseURL}`);\nexport default instance;", "map": {"version": 3, "names": ["axios", "getBaseUrl", "process", "env", "REACT_APP_API_URL", "NODE_ENV", "window", "location", "origin", "instance", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "_config$method", "token", "localStorage", "getItem", "Authorization", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "_error$response", "removeItem", "defaults", "common", "setTimeout", "href"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/utils/axiosConfig.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Get base URL from environment or use window.location.origin as fallback\r\nconst getBaseUrl = () => {\r\n    if (process.env.REACT_APP_API_URL) return process.env.REACT_APP_API_URL;\r\n\r\n    // When running in production, we want to use the same origin\r\n    // This helps when deploying behind proxies or accessing externally\r\n    if (process.env.NODE_ENV === 'production') {\r\n        return window.location.origin;\r\n    }\r\n\r\n    // Default development server\r\n    return 'http://localhost:1976';\r\n};\r\n\r\nconst instance = axios.create({\r\n    baseURL: getBaseUrl(),\r\n    timeout: 30000, // Increased timeout\r\n    headers: {\r\n        'Content-Type': 'application/json',\r\n    }\r\n});\r\n\r\n// Request interceptor\r\ninstance.interceptors.request.use(\r\n    (config) => {\r\n        const token = localStorage.getItem('token');\r\n        if (token) {\r\n            config.headers.Authorization = `Bearer ${token}`;\r\n        }\r\n\r\n        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\r\n        return config;\r\n    },\r\n    (error) => {\r\n        console.error('Request error:', error);\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Response interceptor\r\ninstance.interceptors.response.use(\r\n    (response) => {\r\n        console.log(`API Response: ${response.status} for ${response.config.url}`);\r\n        return response;\r\n    },\r\n    (error) => {\r\n        console.error('Response error:', error.response || error);\r\n\r\n        if (error.response?.status === 401) {\r\n            console.log('Authentication error, redirecting to login');\r\n            // Clear token and redirect to login on unauthorized\r\n            localStorage.removeItem('token');\r\n            delete instance.defaults.headers.common['Authorization'];\r\n\r\n            // Use a slight delay to ensure console logs are visible\r\n            setTimeout(() => {\r\n            window.location.href = '/login';\r\n            }, 100);\r\n        }\r\n\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Expose the current baseURL for debugging\r\nconsole.log(`API base URL: ${instance.defaults.baseURL}`);\r\n\r\nexport default instance; "], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACrB,IAAIC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAE,OAAOF,OAAO,CAACC,GAAG,CAACC,iBAAiB;;EAEvE;EACA;EACA,IAAIF,OAAO,CAACC,GAAG,CAACE,QAAQ,KAAK,YAAY,EAAE;IACvC,OAAOC,MAAM,CAACC,QAAQ,CAACC,MAAM;EACjC;;EAEA;EACA,OAAO,uBAAuB;AAClC,CAAC;AAED,MAAMC,QAAQ,GAAGT,KAAK,CAACU,MAAM,CAAC;EAC1BC,OAAO,EAAEV,UAAU,CAAC,CAAC;EACrBW,OAAO,EAAE,KAAK;EAAE;EAChBC,OAAO,EAAE;IACL,cAAc,EAAE;EACpB;AACJ,CAAC,CAAC;;AAEF;AACAJ,QAAQ,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC5BC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACR,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACPF,MAAM,CAACJ,OAAO,CAACS,aAAa,GAAG,UAAUH,KAAK,EAAE;EACpD;EAEAI,OAAO,CAACC,GAAG,CAAC,iBAAAN,cAAA,GAAgBD,MAAM,CAACQ,MAAM,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,WAAW,CAAC,CAAC,IAAIT,MAAM,CAACU,GAAG,EAAE,CAAC;EACzE,OAAOV,MAAM;AACjB,CAAC,EACAW,KAAK,IAAK;EACPL,OAAO,CAACK,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACtC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;;AAED;AACAnB,QAAQ,CAACK,YAAY,CAACiB,QAAQ,CAACf,GAAG,CAC7Be,QAAQ,IAAK;EACVR,OAAO,CAACC,GAAG,CAAC,iBAAiBO,QAAQ,CAACC,MAAM,QAAQD,QAAQ,CAACd,MAAM,CAACU,GAAG,EAAE,CAAC;EAC1E,OAAOI,QAAQ;AACnB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAK,eAAA;EACPV,OAAO,CAACK,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAAC;EAEzD,IAAI,EAAAK,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBD,MAAM,MAAK,GAAG,EAAE;IAChCT,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzD;IACAJ,YAAY,CAACc,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOzB,QAAQ,CAAC0B,QAAQ,CAACtB,OAAO,CAACuB,MAAM,CAAC,eAAe,CAAC;;IAExD;IACAC,UAAU,CAAC,MAAM;MACjB/B,MAAM,CAACC,QAAQ,CAAC+B,IAAI,GAAG,QAAQ;IAC/B,CAAC,EAAE,GAAG,CAAC;EACX;EAEA,OAAOT,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;;AAED;AACAL,OAAO,CAACC,GAAG,CAAC,iBAAiBf,QAAQ,CAAC0B,QAAQ,CAACxB,OAAO,EAAE,CAAC;AAEzD,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}