{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useEventCallback } from '@mui/material/utils';\nimport { onSpaceOrEnter } from '../utils/utils';\nimport { useLocaleText, useUtils } from '../hooks/useUtils';\nimport { getDisplayDate } from '../utils/text-field-helper';\n// TODO: why is this called \"Pure*\" when it's not memoized? Does \"Pure\" mean \"readonly\"?\nexport const PureDateInput = /*#__PURE__*/React.forwardRef(function PureDateInput(props, ref) {\n  const {\n    disabled,\n    getOpenDialogAriaText: getOpenDialogAriaTextProp,\n    inputFormat,\n    InputProps,\n    inputRef,\n    label,\n    openPicker: onOpen,\n    rawValue,\n    renderInput,\n    TextFieldProps = {},\n    validationError,\n    className\n  } = props;\n  const localeText = useLocaleText(); // The prop can not be deprecated\n  // Default is \"Choose date, ...\", but time pickers override it with \"Choose time, ...\"\n\n  const getOpenDialogAriaText = getOpenDialogAriaTextProp != null ? getOpenDialogAriaTextProp : localeText.openDatePickerDialogue;\n  const utils = useUtils();\n  const PureDateInputProps = React.useMemo(() => _extends({}, InputProps, {\n    readOnly: true\n  }), [InputProps]);\n  const inputValue = getDisplayDate(utils, rawValue, inputFormat);\n  const handleOnClick = useEventCallback(event => {\n    event.stopPropagation();\n    onOpen();\n  });\n  return renderInput(_extends({\n    label,\n    disabled,\n    ref,\n    inputRef,\n    error: validationError,\n    InputProps: PureDateInputProps,\n    className\n  }, !props.readOnly && !props.disabled && {\n    onClick: handleOnClick\n  }, {\n    inputProps: _extends({\n      disabled,\n      readOnly: true,\n      'aria-readonly': true,\n      'aria-label': getOpenDialogAriaText(rawValue, utils),\n      value: inputValue\n    }, !props.readOnly && {\n      onClick: handleOnClick\n    }, {\n      onKeyDown: onSpaceOrEnter(onOpen)\n    })\n  }, TextFieldProps));\n});", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "onSpaceOrEnter", "useLocaleText", "useUtils", "getDisplayDate", "PureDateInput", "forwardRef", "props", "ref", "disabled", "getOpenDialogAriaText", "getOpenDialogAriaTextProp", "inputFormat", "InputProps", "inputRef", "label", "openPicker", "onOpen", "rawValue", "renderInput", "TextFieldProps", "validationError", "className", "localeText", "openDatePickerDialogue", "utils", "PureDateInputProps", "useMemo", "readOnly", "inputValue", "handleOnClick", "event", "stopPropagation", "error", "onClick", "inputProps", "value", "onKeyDown"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/components/PureDateInput.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useEventCallback } from '@mui/material/utils';\nimport { onSpaceOrEnter } from '../utils/utils';\nimport { useLocaleText, useUtils } from '../hooks/useUtils';\nimport { getDisplayDate } from '../utils/text-field-helper';\n// TODO: why is this called \"Pure*\" when it's not memoized? Does \"Pure\" mean \"readonly\"?\nexport const PureDateInput = /*#__PURE__*/React.forwardRef(function PureDateInput(props, ref) {\n  const {\n    disabled,\n    getOpenDialogAriaText: getOpenDialogAriaTextProp,\n    inputFormat,\n    InputProps,\n    inputRef,\n    label,\n    openPicker: onOpen,\n    rawValue,\n    renderInput,\n    TextFieldProps = {},\n    validationError,\n    className\n  } = props;\n  const localeText = useLocaleText(); // The prop can not be deprecated\n  // Default is \"Choose date, ...\", but time pickers override it with \"Choose time, ...\"\n\n  const getOpenDialogAriaText = getOpenDialogAriaTextProp != null ? getOpenDialogAriaTextProp : localeText.openDatePickerDialogue;\n  const utils = useUtils();\n  const PureDateInputProps = React.useMemo(() => _extends({}, InputProps, {\n    readOnly: true\n  }), [InputProps]);\n  const inputValue = getDisplayDate(utils, rawValue, inputFormat);\n  const handleOnClick = useEventCallback(event => {\n    event.stopPropagation();\n    onOpen();\n  });\n  return renderInput(_extends({\n    label,\n    disabled,\n    ref,\n    inputRef,\n    error: validationError,\n    InputProps: PureDateInputProps,\n    className\n  }, !props.readOnly && !props.disabled && {\n    onClick: handleOnClick\n  }, {\n    inputProps: _extends({\n      disabled,\n      readOnly: true,\n      'aria-readonly': true,\n      'aria-label': getOpenDialogAriaText(rawValue, utils),\n      value: inputValue\n    }, !props.readOnly && {\n      onClick: handleOnClick\n    }, {\n      onKeyDown: onSpaceOrEnter(onOpen)\n    })\n  }, TextFieldProps));\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,aAAa,EAAEC,QAAQ,QAAQ,mBAAmB;AAC3D,SAASC,cAAc,QAAQ,4BAA4B;AAC3D;AACA,OAAO,MAAMC,aAAa,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,SAASD,aAAaA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC5F,MAAM;IACJC,QAAQ;IACRC,qBAAqB,EAAEC,yBAAyB;IAChDC,WAAW;IACXC,UAAU;IACVC,QAAQ;IACRC,KAAK;IACLC,UAAU,EAAEC,MAAM;IAClBC,QAAQ;IACRC,WAAW;IACXC,cAAc,GAAG,CAAC,CAAC;IACnBC,eAAe;IACfC;EACF,CAAC,GAAGf,KAAK;EACT,MAAMgB,UAAU,GAAGrB,aAAa,CAAC,CAAC,CAAC,CAAC;EACpC;;EAEA,MAAMQ,qBAAqB,GAAGC,yBAAyB,IAAI,IAAI,GAAGA,yBAAyB,GAAGY,UAAU,CAACC,sBAAsB;EAC/H,MAAMC,KAAK,GAAGtB,QAAQ,CAAC,CAAC;EACxB,MAAMuB,kBAAkB,GAAG3B,KAAK,CAAC4B,OAAO,CAAC,MAAM7B,QAAQ,CAAC,CAAC,CAAC,EAAEe,UAAU,EAAE;IACtEe,QAAQ,EAAE;EACZ,CAAC,CAAC,EAAE,CAACf,UAAU,CAAC,CAAC;EACjB,MAAMgB,UAAU,GAAGzB,cAAc,CAACqB,KAAK,EAAEP,QAAQ,EAAEN,WAAW,CAAC;EAC/D,MAAMkB,aAAa,GAAG9B,gBAAgB,CAAC+B,KAAK,IAAI;IAC9CA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvBf,MAAM,CAAC,CAAC;EACV,CAAC,CAAC;EACF,OAAOE,WAAW,CAACrB,QAAQ,CAAC;IAC1BiB,KAAK;IACLN,QAAQ;IACRD,GAAG;IACHM,QAAQ;IACRmB,KAAK,EAAEZ,eAAe;IACtBR,UAAU,EAAEa,kBAAkB;IAC9BJ;EACF,CAAC,EAAE,CAACf,KAAK,CAACqB,QAAQ,IAAI,CAACrB,KAAK,CAACE,QAAQ,IAAI;IACvCyB,OAAO,EAAEJ;EACX,CAAC,EAAE;IACDK,UAAU,EAAErC,QAAQ,CAAC;MACnBW,QAAQ;MACRmB,QAAQ,EAAE,IAAI;MACd,eAAe,EAAE,IAAI;MACrB,YAAY,EAAElB,qBAAqB,CAACQ,QAAQ,EAAEO,KAAK,CAAC;MACpDW,KAAK,EAAEP;IACT,CAAC,EAAE,CAACtB,KAAK,CAACqB,QAAQ,IAAI;MACpBM,OAAO,EAAEJ;IACX,CAAC,EAAE;MACDO,SAAS,EAAEpC,cAAc,CAACgB,MAAM;IAClC,CAAC;EACH,CAAC,EAAEG,cAAc,CAAC,CAAC;AACrB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}