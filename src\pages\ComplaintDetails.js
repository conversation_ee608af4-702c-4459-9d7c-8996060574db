import React, { useState, useEffect, useCallback, Suspense } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  TextField,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  IconButton,
  Divider,
  Paper,
  Grid,
  Skeleton,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  AttachFile as AttachFileIcon,
  InsertDriveFile as FileIcon,
  Download as DownloadIcon,
  Description as DescriptionIcon,
  Assignment as AssignmentIcon,
  ArrowBack as ArrowBackIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { format, parseISO, parse } from 'date-fns';
import axios from '../utils/axiosConfig';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { motion, AnimatePresence } from 'framer-motion';
import Timeline from '@mui/lab/Timeline';
import TimelineItem from '@mui/lab/TimelineItem';
import TimelineSeparator from '@mui/lab/TimelineSeparator';
import TimelineConnector from '@mui/lab/TimelineConnector';
import TimelineContent from '@mui/lab/TimelineContent';
import TimelineDot from '@mui/lab/TimelineDot';
import TimelineOppositeContent from '@mui/lab/TimelineOppositeContent';

const statusOptions = ['New', 'Assigned', 'In Progress', 'Resolved', 'Rejected'];

// Loading skeleton component
const DetailsSkeleton = () => (
  <Box sx={{ width: '100%', p: 3 }}>
    <Skeleton variant="text" width="40%" height={40} sx={{ mb: 2 }} />
    <Grid container spacing={3}>
      <Grid item xs={12} md={8}>
        <Paper sx={{ p: 3 }}>
          <Skeleton variant="rectangular" height={200} sx={{ mb: 2 }} />
          <Skeleton variant="text" width="60%" />
          <Skeleton variant="text" width="40%" />
          <Skeleton variant="text" width="70%" />
        </Paper>
      </Grid>
      <Grid item xs={12} md={4}>
        <Paper sx={{ p: 3 }}>
          <Skeleton variant="text" width="80%" />
          <Skeleton variant="rectangular" height={300} />
        </Paper>
      </Grid>
    </Grid>
  </Box>
);

// Animation variants
const pageTransition = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};

const cardTransition = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  transition: { duration: 0.3 }
};

// Helper functions for status and priority colors
const getStatusColor = (status) => {
  if (!status) return 'default';

  // Normalize the status string
  const normalizedStatus = status.toString().trim();

  const statusMap = {
    'New': 'info',
    'Assigned': 'warning',
    'In Progress': 'primary',
    'Resolved': 'success',
    'Rejected': 'error',
    'Unknown': 'default'
  };

  return statusMap[normalizedStatus] || 'default';
};

const getPriorityColor = (priority) => {
  if (!priority) return 'default';

  // Normalize the priority string
  const normalizedPriority = priority.toString().trim();

  const priorityMap = {
    'Low': 'success',
    'Medium': 'warning',
    'High': 'error',
    'Critical': 'error'
  };

  return priorityMap[normalizedPriority] || 'default';
};

const formatFileSize = (bytes) => {
  if (!bytes) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

function ComplaintDetails() {
  const theme = useTheme();
  const { id } = useParams();
  const navigate = useNavigate();
  const [complaint, setComplaint] = useState(null);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [downloadingAttachment, setDownloadingAttachment] = useState(null);
  const [employees, setEmployees] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [assignmentNotes, setAssignmentNotes] = useState('');
  const [dueDate, setDueDate] = useState(new Date());
  const [statusComments, setStatusComments] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [statusHistory, setStatusHistory] = useState([]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [loadingTimeout, setLoadingTimeout] = useState(null);
  const [userPermissions, setUserPermissions] = useState({
    CanAssign: false,
    CanUpdateStatus: false,
    CanViewDashboard: false
  });

  const fetchComplaintDetails = useCallback(async () => {
    let timeoutId;  // Declare timeout variable
    try {
      setLoading(true);
      setError(null);

      // Set a timeout to show a message if loading takes too long
      timeoutId = setTimeout(() => {
        setLoadingTimeout(true);
      }, 5000);

      // Fetch all data in parallel
      const [complaintResponse, departmentsResponse, employeesResponse, permissionsResponse] = await Promise.all([
        axios.get(`/api/complaints/${id}`),
        axios.get(`/api/departments`),
        axios.get(`/api/employees`),
        axios.get(`/api/user/permissions`)
      ]);

      clearTimeout(timeoutId);  // Use the correct variable
      setLoadingTimeout(false);

      if (complaintResponse.data) {
        setComplaint(complaintResponse.data);
        setStatusHistory(complaintResponse.data.statusHistory || []);
        setDepartments(departmentsResponse.data || []);
        setEmployees(employeesResponse.data || []);
        setUserPermissions(permissionsResponse.data || {
          CanAssign: false,
          CanUpdateStatus: false,
          CanViewDashboard: false
        });
        setError(null);
      } else {
        setError('No complaint data found');
      }
    } catch (error) {
      if (timeoutId) clearTimeout(timeoutId);  // Clear timeout on error
      setLoadingTimeout(false);
      console.error('Error fetching complaint details:', error);
      if (error.response?.status === 401) {
        navigate('/login');
      } else if (error.response?.status === 404) {
        setError('Complaint not found');
      } else {
        setError(error.response?.data?.message || 'Failed to fetch complaint details. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  }, [id, navigate]);

  useEffect(() => {
    fetchComplaintDetails();

    // Cleanup function
    return () => {
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
    };
  }, [fetchComplaintDetails]);

  const handleStatusUpdate = async () => {
    try {
      setError(null);
      setSuccessMessage('');

      const response = await axios.post(`/api/complaints/${id}/status`, {
        newStatusId: getStatusId(newStatus),
        comments: statusComments
      });

      console.log('Status update response:', response.data);

      if (response.data.error) {
        setErrorMessage(response.data.message || 'Failed to update status');
      } else {
        setSuccessMessage('Status updated successfully');
        setStatusDialogOpen(false);
        fetchComplaintDetails(); // Refresh complaint details
      }
    } catch (err) {
      console.error('Error updating status:', err);
      setErrorMessage(err.response?.data?.message || 'Failed to update status. Please try again.');
    }
  };

  // Helper function to convert status text to ID
  const getStatusId = (status) => {
    const statusMap = {
      'New': 1,
      'Assigned': 2,
      'In Progress': 3,
      'Resolved': 4,
      'Rejected': 5
    };
    return statusMap[status] || 1;
  };

  const handleDownload = async (attachmentId, fileName) => {
    try {
      setDownloadingAttachment(attachmentId);
      const response = await axios.get(
        `/api/complaints/attachments/${attachmentId}/download`,
        {
          responseType: 'blob'
        }
      );

      // Get the filename from the Content-Disposition header if available
      const contentDisposition = response.headers['content-disposition'];
      const downloadFileName = contentDisposition
        ? decodeURIComponent(contentDisposition.split('filename=')[1].replace(/"/g, ''))
        : fileName;

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', downloadFileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      setError(null);
    } catch (error) {
      console.error('Error downloading attachment:', error);
      setError('Failed to download attachment. Please try again.');
    } finally {
      setDownloadingAttachment(null);
    }
  };

  const handleView = (attachmentId, fileName) => {
    const viewUrl = `/api/complaints/attachments/${attachmentId}/view`;
    window.open(viewUrl, '_blank');
  };

  const handleAssign = async () => {
    try {
      if (!selectedEmployee) {
        setErrorMessage('Please select an employee to assign');
        return;
      }

      const response = await axios.post(
        `/api/complaints/${id}/assign`,
        {
          empCode: selectedEmployee
        }
      );

      if (response.data.error === false) {
        setSuccessMessage('Complaint assigned successfully');
        setAssignDialogOpen(false);
        fetchComplaintDetails();
        setSelectedDepartment('');
        setSelectedEmployee('');
        setAssignmentNotes('');
      } else {
        setErrorMessage(response.data.message || 'Failed to assign complaint');
      }
    } catch (error) {
      console.error('Error assigning complaint:', error);
      setErrorMessage(error.response?.data?.message || 'Failed to assign complaint');
      // Keep the dialog open when there's an error
    }
  };

  const handleOpenAssignDialog = () => {
    setAssignDialogOpen(true);
  };

  const formatDateTime = (dateString) => {
    try {
      if (!dateString) return 'N/A';

      // SQL datetime format (YYYY-MM-DD HH:MI:SS)
      const date = parse(dateString, 'yyyy-MM-dd HH:mm:ss', new Date());

      if (isNaN(date.getTime())) {
        console.warn('Invalid date:', dateString);
        return 'N/A';
      }

      // Use the same format as "Last Updated" - PPpp gives "Jun 9, 2025, 11:27:36 AM"
      return format(date, 'PPpp');
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'N/A';
    }
  };

  const renderStatusTimeline = () => {
    if (!statusHistory?.length) {
      return (
        <Typography color="textSecondary" sx={{ p: 2, textAlign: 'center' }}>
          No status updates available
        </Typography>
      );
    }

    return (
      <Timeline>
        {statusHistory.map((status, index) => (
          <TimelineItem key={index}>
            <TimelineOppositeContent color="text.secondary" sx={{ flex: 0.5 }}>
              {formatDateTime(status?.timestamp)}
            </TimelineOppositeContent>
            <TimelineSeparator>
              <TimelineDot color={getStatusColor(status?.toStatus)} />
              {index < statusHistory.length - 1 && <TimelineConnector />}
            </TimelineSeparator>
            <TimelineContent>
              <Typography variant="body2" component="span">
                {status?.fromStatus} → {status?.toStatus}
              </Typography>
              <Typography variant="caption" display="block" color="text.secondary">
                By: {status?.updatedBy} ({status?.updatedByDepartment})
              </Typography>
              {status?.comments && (
                <Typography variant="caption" display="block" sx={{ mt: 0.5 }}>
                  {status.comments}
                </Typography>
              )}
            </TimelineContent>
          </TimelineItem>
        ))}
      </Timeline>
    );
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <CircularProgress />
        {loadingTimeout && (
          <Typography sx={{ mt: 2, color: 'text.secondary' }}>
            This is taking longer than usual. Please wait...
          </Typography>
        )}
      </Box>
    );
  }

  if (error) {
    return (
      <motion.div
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageTransition}
      >
        <Box sx={{ p: 3 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/complaints')}
            sx={{ mb: 2 }}
          >
            Back to Complaints
          </Button>
          <Alert
            severity="error"
            action={
              <Button color="inherit" size="small" onClick={fetchComplaintDetails}>
                Retry
              </Button>
            }
          >
            {error}
          </Alert>
        </Box>
      </motion.div>
    );
  }

  if (!complaint) {
    return (
      <motion.div
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageTransition}
      >
        <Box sx={{ p: 3 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/complaints')}
            sx={{ mb: 2 }}
          >
            Back to Complaints
          </Button>
          <Alert severity="info">No complaint found with ID: {id}</Alert>
        </Box>
      </motion.div>
    );
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageTransition}
      >
        <Box sx={{ width: '100%', p: { xs: 2, sm: 3 } }}>
          <Typography
            variant={isMobile ? "h5" : "h4"}
            gutterBottom
            sx={{ mb: { xs: 2, sm: 3 } }}
            component={motion.h1}
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            Complaint Details
          </Typography>

          <Grid container spacing={{ xs: 2, sm: 3 }}>
            <Grid item xs={12} md={8}>
              <motion.div variants={cardTransition}>
                <Paper
                  sx={{
                    p: { xs: 2, sm: 3 },
                    boxShadow: theme.shadows[3],
                    transition: 'box-shadow 0.3s ease-in-out',
                    '&:hover': {
                      boxShadow: theme.shadows[6]
                    }
                  }}
                >
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: { xs: 2, sm: 3 }
                  }}>
                    {/* Success Message */}
                    {successMessage && (
                      <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccessMessage('')}>
                        {successMessage}
                      </Alert>
                    )}

                    {/* Error Message */}
                    {errorMessage && (
                      <Alert severity="error" sx={{ mb: 2 }} onClose={() => setErrorMessage('')}>
                        {errorMessage}
                      </Alert>
                    )}

                    <Card>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                          <Box>
                            <Typography variant="h5" gutterBottom>
                              Complaint #{complaint.complaintNumber}
                            </Typography>
                            <Chip
                              label={complaint.status || 'Unknown'}
                              color={getStatusColor(complaint.status)}
                              variant={getStatusColor(complaint.status) === 'default' ? 'outlined' : 'filled'}
                              sx={{ mr: 1 }}
                            />
                            <Chip
                              label={complaint.priority || 'Not Set'}
                              color={getPriorityColor(complaint.priority)}
                              variant={getPriorityColor(complaint.priority) === 'default' ? 'outlined' : 'filled'}
                            />
                          </Box>
                          <Box>
                            {userPermissions.CanAssign && (
                              <Button
                                variant="outlined"
                                color="primary"
                                onClick={handleOpenAssignDialog}
                                disabled={loading}
                                startIcon={<AssignmentIcon />}
                                sx={{ mr: 1 }}
                              >
                                Assign
                              </Button>
                            )}
                            {userPermissions.CanUpdateStatus && (
                              <Button
                                variant="contained"
                                color="primary"
                                onClick={() => setStatusDialogOpen(true)}
                                disabled={loading}
                              >
                                Update Status
                              </Button>
                            )}
                          </Box>
                        </Box>

                        <Divider sx={{ my: 2 }} />

                        <Typography variant="h6" gutterBottom>
                          Details
                        </Typography>

                        <Box sx={{ display: 'grid', gap: 2, gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' } }}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">
                              Title
                            </Typography>
                            <Typography variant="body1" paragraph>
                              {complaint.title}
                            </Typography>

                            <Typography variant="subtitle2" color="text.secondary">
                              Description
                            </Typography>
                            <Typography variant="body1" paragraph>
                              {complaint.description}
                            </Typography>

                            <Typography variant="subtitle2" color="text.secondary">
                              Category
                            </Typography>
                            <Typography variant="body1" paragraph>
                              {complaint.category || 'N/A'}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">
                              Submitted By
                            </Typography>
                            <Typography variant="body1" paragraph>
                              {complaint.submittedByName} ({complaint.submittedByCode})
                              <br />
                              Department: {complaint.submittedByDepartment}
                            </Typography>

                            <Typography variant="subtitle2" color="text.secondary">
                              Submission Date
                            </Typography>
                            <Typography variant="body1" paragraph>
                              {complaint.submissionDate ? format(new Date(complaint.submissionDate.replace(' ', 'T')), 'PPpp') : 'N/A'}
                            </Typography>

                            {complaint.assignedToName && (
                              <>
                                <Typography variant="subtitle2" color="text.secondary">
                                  Assigned To
                                </Typography>
                                <Typography variant="body1" paragraph>
                                  {complaint.assignedToName} ({complaint.assignedToCode})
                                  <br />
                                  Department: {complaint.assignedToDepartment}
                                </Typography>
                              </>
                            )}

                            {complaint.lastUpdateDate && (
                              <>
                                <Typography variant="subtitle2" color="text.secondary">
                                  Last Updated
                                </Typography>
                                <Typography variant="body1" paragraph>
                                  {format(new Date(complaint.lastUpdateDate.replace(' ', 'T')), 'PPpp')}
                                </Typography>
                              </>
                            )}
                          </Box>
                        </Box>

                        <Divider sx={{ my: 2 }} />

                        {/* Attachments Section */}
                        {complaint.attachments && complaint.attachments.length > 0 && (
                          <>
                            <Typography variant="h6" gutterBottom>
                              Attachments
                            </Typography>
                            <List>
                              {complaint.attachments.map((attachment) => (
                                <ListItem
                                  key={attachment.id}
                                  secondaryAction={
                                    <IconButton
                                      edge="end"
                                      onClick={() => handleDownload(attachment.id, attachment.name)}
                                      disabled={downloadingAttachment === attachment.id}
                                    >
                                      {downloadingAttachment === attachment.id ? (
                                        <CircularProgress size={24} />
                                      ) : (
                                        <DownloadIcon />
                                      )}
                                    </IconButton>
                                  }
                                >
                                  <ListItemIcon>
                                    <DescriptionIcon />
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={attachment.name}
                                    secondary={`Uploaded on ${format(new Date(attachment.uploadDate), 'PPpp')} • ${formatFileSize(attachment.size)}`}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </>
                        )}

                        <Divider sx={{ my: 2 }} />

                        {/* Status History Section */}
                        {complaint.statusHistory && complaint.statusHistory.length > 0 && (
                          <>
                            <Typography variant="h6" gutterBottom>
                              Status History
                            </Typography>
                            <List>
                              {complaint.statusHistory.map((history, index) => (
                                <ListItem key={index}>
                                  <ListItemText
                                    primary={`${history.fromStatus} → ${history.toStatus}`}
                                    secondary={
                                      <>
                                        Changed by {history.updatedBy} on {formatDateTime(history.timestamp)}
                                        {history.comments && (
                                          <Typography component="div" variant="body2" color="text.secondary">
                                            Comments: {history.comments}
                                          </Typography>
                                        )}
                                      </>
                                    }
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </>
                        )}
                      </CardContent>
                    </Card>
                  </Box>
                </Paper>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div
                variants={cardTransition}
                transition={{ delay: 0.2 }}
              >
                <Paper
                  sx={{
                    p: 3,
                    height: '100%',
                    boxShadow: theme.shadows[3],
                    transition: 'box-shadow 0.3s ease-in-out',
                    '&:hover': {
                      boxShadow: theme.shadows[6]
                    }
                  }}
                >
                  <Typography variant="h6" gutterBottom>
                    Status Timeline
                  </Typography>
                  {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    renderStatusTimeline()
                  )}
                </Paper>
              </motion.div>
            </Grid>
          </Grid>

          {/* Assign Dialog */}
          <Dialog
            open={assignDialogOpen}
            onClose={() => setAssignDialogOpen(false)}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>Assign Complaint</DialogTitle>
            <DialogContent>
              <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
                {errorMessage && (
                  <Alert severity="error" onClose={() => setErrorMessage('')}>
                    {errorMessage}
                  </Alert>
                )}

                <FormControl fullWidth>
                  <InputLabel>Assign To</InputLabel>
                  <Select
                    value={selectedEmployee}
                    onChange={(e) => setSelectedEmployee(e.target.value)}
                    label="Assign To"
                  >
                    {employees.map((emp) => (
                      <MenuItem key={emp.EmpCode} value={emp.EmpCode}>
                        {emp.EmpName} ({emp.DeptName})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Assignment Notes"
                  value={assignmentNotes}
                  onChange={(e) => setAssignmentNotes(e.target.value)}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setAssignDialogOpen(false)}>Cancel</Button>
              <Button onClick={handleAssign} variant="contained" color="primary">
                Assign
              </Button>
            </DialogActions>
          </Dialog>

          {/* Status Update Dialog */}
          <Dialog
            open={statusDialogOpen}
            onClose={() => setStatusDialogOpen(false)}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>Update Complaint Status</DialogTitle>
            <DialogContent>
              <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
                {errorMessage && (
                  <Alert
                    severity="error"
                    onClose={() => setErrorMessage('')}
                  >
                    {errorMessage}
                  </Alert>
                )}

                <TextField
                  select
                  fullWidth
                  label="New Status"
                  value={newStatus}
                  onChange={(e) => setNewStatus(e.target.value)}
                >
                  <MenuItem value="New">New</MenuItem>
                  <MenuItem value="Assigned">Assigned</MenuItem>
                  <MenuItem value="In Progress">In Progress</MenuItem>
                  <MenuItem value="Resolved">Resolved</MenuItem>
                  <MenuItem value="Rejected">Rejected</MenuItem>
                </TextField>

                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Comments"
                  value={statusComments}
                  onChange={(e) => setStatusComments(e.target.value)}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
              <Button
                onClick={handleStatusUpdate}
                variant="contained"
                color="primary"
                disabled={!newStatus}
              >
                Update
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </motion.div>
    </AnimatePresence>
  );
}

export default ComplaintDetails;