import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    But<PERSON>
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';

const PermissionUpdateNotification = () => {
    const { refreshUserPermissions } = useAuth();
    const [showNotification, setShowNotification] = useState(false);
    const [lastPermissionCheck, setLastPermissionCheck] = useState(Date.now());

    // Check for permission updates every 30 seconds
    useEffect(() => {
        const checkForUpdates = async () => {
            try {
                // This is a simple check - in a real app you might want to implement
                // a more sophisticated notification system using WebSockets or Server-Sent Events
                const response = await fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    // You could implement a timestamp-based check here
                    // For now, we'll just provide the manual refresh option
                }
            } catch (error) {
                console.error('Error checking for permission updates:', error);
            }
        };

        const interval = setInterval(checkForUpdates, 30000); // Check every 30 seconds
        return () => clearInterval(interval);
    }, []);

    const handleRefreshPermissions = async () => {
        const success = await refreshUserPermissions();
        if (success) {
            setShowNotification(false);
            // You could show a success message here
        }
    };

    return (
        <Snackbar
            open={showNotification}
            onClose={() => setShowNotification(false)}
            anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            autoHideDuration={null} // Don't auto-hide
        >
            <Alert 
                severity="info" 
                action={
                    <Button 
                        color="inherit" 
                        size="small" 
                        onClick={handleRefreshPermissions}
                    >
                        Refresh
                    </Button>
                }
                onClose={() => setShowNotification(false)}
            >
                Your permissions may have been updated. Click "Refresh" to apply changes.
            </Alert>
        </Snackbar>
    );
};

export default PermissionUpdateNotification;
