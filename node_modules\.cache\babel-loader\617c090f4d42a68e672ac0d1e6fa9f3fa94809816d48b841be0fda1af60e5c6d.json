{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onChange\", \"maxRows\", \"minRows\", \"style\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport * as ReactDOM from 'react-dom';\nimport { unstable_debounce as debounce, unstable_useForkRef as useForkRef, unstable_useEnhancedEffect as useEnhancedEffect, unstable_ownerWindow as ownerWindow } from '@mui/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction getStyleValue(value) {\n  return parseInt(value, 10) || 0;\n}\nconst styles = {\n  shadow: {\n    // Visibility needed to hide the extra text area on iPads\n    visibility: 'hidden',\n    // Remove from the content flow\n    position: 'absolute',\n    // Ignore the scrollbar width\n    overflow: 'hidden',\n    height: 0,\n    top: 0,\n    left: 0,\n    // Create a new layer, increase the isolation of the computed values\n    transform: 'translateZ(0)'\n  }\n};\nfunction isEmpty(obj) {\n  return obj === undefined || obj === null || Object.keys(obj).length === 0 || obj.outerHeightStyle === 0 && !obj.overflow;\n}\n\n/**\n *\n * Demos:\n *\n * - [Textarea Autosize](https://mui.com/base-ui/react-textarea-autosize/)\n * - [Textarea Autosize](https://mui.com/material-ui/react-textarea-autosize/)\n *\n * API:\n *\n * - [TextareaAutosize API](https://mui.com/base-ui/react-textarea-autosize/components-api/#textarea-autosize)\n */\nconst TextareaAutosize = /*#__PURE__*/React.forwardRef(function TextareaAutosize(props, forwardedRef) {\n  const {\n      onChange,\n      maxRows,\n      minRows = 1,\n      style,\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    current: isControlled\n  } = React.useRef(value != null);\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(forwardedRef, inputRef);\n  const shadowRef = React.useRef(null);\n  const renders = React.useRef(0);\n  const [state, setState] = React.useState({\n    outerHeightStyle: 0\n  });\n  const getUpdatedState = React.useCallback(() => {\n    const input = inputRef.current;\n    const containerWindow = ownerWindow(input);\n    const computedStyle = containerWindow.getComputedStyle(input);\n\n    // If input's width is shrunk and it's not visible, don't sync height.\n    if (computedStyle.width === '0px') {\n      return {\n        outerHeightStyle: 0\n      };\n    }\n    const inputShallow = shadowRef.current;\n    inputShallow.style.width = computedStyle.width;\n    inputShallow.value = input.value || props.placeholder || 'x';\n    if (inputShallow.value.slice(-1) === '\\n') {\n      // Certain fonts which overflow the line height will cause the textarea\n      // to report a different scrollHeight depending on whether the last line\n      // is empty. Make it non-empty to avoid this issue.\n      inputShallow.value += ' ';\n    }\n    const boxSizing = computedStyle.boxSizing;\n    const padding = getStyleValue(computedStyle.paddingBottom) + getStyleValue(computedStyle.paddingTop);\n    const border = getStyleValue(computedStyle.borderBottomWidth) + getStyleValue(computedStyle.borderTopWidth);\n\n    // The height of the inner content\n    const innerHeight = inputShallow.scrollHeight;\n\n    // Measure height of a textarea with a single row\n    inputShallow.value = 'x';\n    const singleRowHeight = inputShallow.scrollHeight;\n\n    // The height of the outer content\n    let outerHeight = innerHeight;\n    if (minRows) {\n      outerHeight = Math.max(Number(minRows) * singleRowHeight, outerHeight);\n    }\n    if (maxRows) {\n      outerHeight = Math.min(Number(maxRows) * singleRowHeight, outerHeight);\n    }\n    outerHeight = Math.max(outerHeight, singleRowHeight);\n\n    // Take the box sizing into account for applying this value as a style.\n    const outerHeightStyle = outerHeight + (boxSizing === 'border-box' ? padding + border : 0);\n    const overflow = Math.abs(outerHeight - innerHeight) <= 1;\n    return {\n      outerHeightStyle,\n      overflow\n    };\n  }, [maxRows, minRows, props.placeholder]);\n  const updateState = (prevState, newState) => {\n    const {\n      outerHeightStyle,\n      overflow\n    } = newState;\n    // Need a large enough difference to update the height.\n    // This prevents infinite rendering loop.\n    if (renders.current < 20 && (outerHeightStyle > 0 && Math.abs((prevState.outerHeightStyle || 0) - outerHeightStyle) > 1 || prevState.overflow !== overflow)) {\n      renders.current += 1;\n      return {\n        overflow,\n        outerHeightStyle\n      };\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (renders.current === 20) {\n        console.error(['MUI: Too many re-renders. The layout is unstable.', 'TextareaAutosize limits the number of renders to prevent an infinite loop.'].join('\\n'));\n      }\n    }\n    return prevState;\n  };\n  const syncHeight = React.useCallback(() => {\n    const newState = getUpdatedState();\n    if (isEmpty(newState)) {\n      return;\n    }\n    setState(prevState => updateState(prevState, newState));\n  }, [getUpdatedState]);\n  useEnhancedEffect(() => {\n    const syncHeightWithFlushSync = () => {\n      const newState = getUpdatedState();\n      if (isEmpty(newState)) {\n        return;\n      }\n\n      // In React 18, state updates in a ResizeObserver's callback are happening after\n      // the paint, this leads to an infinite rendering.\n      //\n      // Using flushSync ensures that the states is updated before the next pain.\n      // Related issue - https://github.com/facebook/react/issues/24331\n      ReactDOM.flushSync(() => {\n        setState(prevState => updateState(prevState, newState));\n      });\n    };\n    const handleResize = () => {\n      renders.current = 0;\n      syncHeightWithFlushSync();\n    };\n    // Workaround a \"ResizeObserver loop completed with undelivered notifications\" error\n    // in test.\n    // Note that we might need to use this logic in production per https://github.com/WICG/resize-observer/issues/38\n    // Also see https://github.com/mui/mui-x/issues/8733\n    let rAF;\n    const rAFHandleResize = () => {\n      cancelAnimationFrame(rAF);\n      rAF = requestAnimationFrame(() => {\n        handleResize();\n      });\n    };\n    const debounceHandleResize = debounce(handleResize);\n    const input = inputRef.current;\n    const containerWindow = ownerWindow(input);\n    containerWindow.addEventListener('resize', debounceHandleResize);\n    let resizeObserver;\n    if (typeof ResizeObserver !== 'undefined') {\n      resizeObserver = new ResizeObserver(process.env.NODE_ENV === 'test' ? rAFHandleResize : handleResize);\n      resizeObserver.observe(input);\n    }\n    return () => {\n      debounceHandleResize.clear();\n      cancelAnimationFrame(rAF);\n      containerWindow.removeEventListener('resize', debounceHandleResize);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n    };\n  }, [getUpdatedState]);\n  useEnhancedEffect(() => {\n    syncHeight();\n  });\n  React.useEffect(() => {\n    renders.current = 0;\n  }, [value]);\n  const handleChange = event => {\n    renders.current = 0;\n    if (!isControlled) {\n      syncHeight();\n    }\n    if (onChange) {\n      onChange(event);\n    }\n  };\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(\"textarea\", _extends({\n      value: value,\n      onChange: handleChange,\n      ref: handleRef\n      // Apply the rows prop to get a \"correct\" first SSR paint\n      ,\n\n      rows: minRows,\n      style: _extends({\n        height: state.outerHeightStyle,\n        // Need a large enough difference to allow scrolling.\n        // This prevents infinite rendering loop.\n        overflow: state.overflow ? 'hidden' : undefined\n      }, style)\n    }, other)), /*#__PURE__*/_jsx(\"textarea\", {\n      \"aria-hidden\": true,\n      className: props.className,\n      readOnly: true,\n      ref: shadowRef,\n      tabIndex: -1,\n      style: _extends({}, styles.shadow, style, {\n        paddingTop: 0,\n        paddingBottom: 0\n      })\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TextareaAutosize.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Maximum number of rows to display.\n   */\n  maxRows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * Minimum number of rows to display.\n   * @default 1\n   */\n  minRows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  placeholder: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * @ignore\n   */\n  value: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.number, PropTypes.string])\n} : void 0;\nexport { TextareaAutosize };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "ReactDOM", "unstable_debounce", "debounce", "unstable_useForkRef", "useForkRef", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_ownerW<PERSON>ow", "ownerWindow", "jsx", "_jsx", "jsxs", "_jsxs", "getStyleValue", "value", "parseInt", "styles", "shadow", "visibility", "position", "overflow", "height", "top", "left", "transform", "isEmpty", "obj", "undefined", "Object", "keys", "length", "outerHeightStyle", "TextareaAutosize", "forwardRef", "props", "forwardedRef", "onChange", "maxRows", "minRows", "style", "other", "current", "isControlled", "useRef", "inputRef", "handleRef", "shadowRef", "renders", "state", "setState", "useState", "getUpdatedState", "useCallback", "input", "containerWindow", "computedStyle", "getComputedStyle", "width", "inputShallow", "placeholder", "slice", "boxSizing", "padding", "paddingBottom", "paddingTop", "border", "borderBottomWidth", "borderTopWidth", "innerHeight", "scrollHeight", "singleRowHeight", "outerHeight", "Math", "max", "Number", "min", "abs", "updateState", "prevState", "newState", "process", "env", "NODE_ENV", "console", "error", "join", "syncHeight", "syncHeightWithFlushSync", "flushSync", "handleResize", "rAF", "rAFHandleResize", "cancelAnimationFrame", "requestAnimationFrame", "debounceHandleResize", "addEventListener", "resizeObserver", "ResizeObserver", "observe", "clear", "removeEventListener", "disconnect", "useEffect", "handleChange", "event", "Fragment", "children", "ref", "rows", "className", "readOnly", "tabIndex", "propTypes", "string", "oneOfType", "number", "func", "object", "arrayOf"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/TextareaAutosize/TextareaAutosize.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onChange\", \"maxRows\", \"minRows\", \"style\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport * as ReactDOM from 'react-dom';\nimport { unstable_debounce as debounce, unstable_useForkRef as useForkRef, unstable_useEnhancedEffect as useEnhancedEffect, unstable_ownerWindow as ownerWindow } from '@mui/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction getStyleValue(value) {\n  return parseInt(value, 10) || 0;\n}\nconst styles = {\n  shadow: {\n    // Visibility needed to hide the extra text area on iPads\n    visibility: 'hidden',\n    // Remove from the content flow\n    position: 'absolute',\n    // Ignore the scrollbar width\n    overflow: 'hidden',\n    height: 0,\n    top: 0,\n    left: 0,\n    // Create a new layer, increase the isolation of the computed values\n    transform: 'translateZ(0)'\n  }\n};\nfunction isEmpty(obj) {\n  return obj === undefined || obj === null || Object.keys(obj).length === 0 || obj.outerHeightStyle === 0 && !obj.overflow;\n}\n\n/**\n *\n * Demos:\n *\n * - [Textarea Autosize](https://mui.com/base-ui/react-textarea-autosize/)\n * - [Textarea Autosize](https://mui.com/material-ui/react-textarea-autosize/)\n *\n * API:\n *\n * - [TextareaAutosize API](https://mui.com/base-ui/react-textarea-autosize/components-api/#textarea-autosize)\n */\nconst TextareaAutosize = /*#__PURE__*/React.forwardRef(function TextareaAutosize(props, forwardedRef) {\n  const {\n      onChange,\n      maxRows,\n      minRows = 1,\n      style,\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    current: isControlled\n  } = React.useRef(value != null);\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(forwardedRef, inputRef);\n  const shadowRef = React.useRef(null);\n  const renders = React.useRef(0);\n  const [state, setState] = React.useState({\n    outerHeightStyle: 0\n  });\n  const getUpdatedState = React.useCallback(() => {\n    const input = inputRef.current;\n    const containerWindow = ownerWindow(input);\n    const computedStyle = containerWindow.getComputedStyle(input);\n\n    // If input's width is shrunk and it's not visible, don't sync height.\n    if (computedStyle.width === '0px') {\n      return {\n        outerHeightStyle: 0\n      };\n    }\n    const inputShallow = shadowRef.current;\n    inputShallow.style.width = computedStyle.width;\n    inputShallow.value = input.value || props.placeholder || 'x';\n    if (inputShallow.value.slice(-1) === '\\n') {\n      // Certain fonts which overflow the line height will cause the textarea\n      // to report a different scrollHeight depending on whether the last line\n      // is empty. Make it non-empty to avoid this issue.\n      inputShallow.value += ' ';\n    }\n    const boxSizing = computedStyle.boxSizing;\n    const padding = getStyleValue(computedStyle.paddingBottom) + getStyleValue(computedStyle.paddingTop);\n    const border = getStyleValue(computedStyle.borderBottomWidth) + getStyleValue(computedStyle.borderTopWidth);\n\n    // The height of the inner content\n    const innerHeight = inputShallow.scrollHeight;\n\n    // Measure height of a textarea with a single row\n    inputShallow.value = 'x';\n    const singleRowHeight = inputShallow.scrollHeight;\n\n    // The height of the outer content\n    let outerHeight = innerHeight;\n    if (minRows) {\n      outerHeight = Math.max(Number(minRows) * singleRowHeight, outerHeight);\n    }\n    if (maxRows) {\n      outerHeight = Math.min(Number(maxRows) * singleRowHeight, outerHeight);\n    }\n    outerHeight = Math.max(outerHeight, singleRowHeight);\n\n    // Take the box sizing into account for applying this value as a style.\n    const outerHeightStyle = outerHeight + (boxSizing === 'border-box' ? padding + border : 0);\n    const overflow = Math.abs(outerHeight - innerHeight) <= 1;\n    return {\n      outerHeightStyle,\n      overflow\n    };\n  }, [maxRows, minRows, props.placeholder]);\n  const updateState = (prevState, newState) => {\n    const {\n      outerHeightStyle,\n      overflow\n    } = newState;\n    // Need a large enough difference to update the height.\n    // This prevents infinite rendering loop.\n    if (renders.current < 20 && (outerHeightStyle > 0 && Math.abs((prevState.outerHeightStyle || 0) - outerHeightStyle) > 1 || prevState.overflow !== overflow)) {\n      renders.current += 1;\n      return {\n        overflow,\n        outerHeightStyle\n      };\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (renders.current === 20) {\n        console.error(['MUI: Too many re-renders. The layout is unstable.', 'TextareaAutosize limits the number of renders to prevent an infinite loop.'].join('\\n'));\n      }\n    }\n    return prevState;\n  };\n  const syncHeight = React.useCallback(() => {\n    const newState = getUpdatedState();\n    if (isEmpty(newState)) {\n      return;\n    }\n    setState(prevState => updateState(prevState, newState));\n  }, [getUpdatedState]);\n  useEnhancedEffect(() => {\n    const syncHeightWithFlushSync = () => {\n      const newState = getUpdatedState();\n      if (isEmpty(newState)) {\n        return;\n      }\n\n      // In React 18, state updates in a ResizeObserver's callback are happening after\n      // the paint, this leads to an infinite rendering.\n      //\n      // Using flushSync ensures that the states is updated before the next pain.\n      // Related issue - https://github.com/facebook/react/issues/24331\n      ReactDOM.flushSync(() => {\n        setState(prevState => updateState(prevState, newState));\n      });\n    };\n    const handleResize = () => {\n      renders.current = 0;\n      syncHeightWithFlushSync();\n    };\n    // Workaround a \"ResizeObserver loop completed with undelivered notifications\" error\n    // in test.\n    // Note that we might need to use this logic in production per https://github.com/WICG/resize-observer/issues/38\n    // Also see https://github.com/mui/mui-x/issues/8733\n    let rAF;\n    const rAFHandleResize = () => {\n      cancelAnimationFrame(rAF);\n      rAF = requestAnimationFrame(() => {\n        handleResize();\n      });\n    };\n    const debounceHandleResize = debounce(handleResize);\n    const input = inputRef.current;\n    const containerWindow = ownerWindow(input);\n    containerWindow.addEventListener('resize', debounceHandleResize);\n    let resizeObserver;\n    if (typeof ResizeObserver !== 'undefined') {\n      resizeObserver = new ResizeObserver(process.env.NODE_ENV === 'test' ? rAFHandleResize : handleResize);\n      resizeObserver.observe(input);\n    }\n    return () => {\n      debounceHandleResize.clear();\n      cancelAnimationFrame(rAF);\n      containerWindow.removeEventListener('resize', debounceHandleResize);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n    };\n  }, [getUpdatedState]);\n  useEnhancedEffect(() => {\n    syncHeight();\n  });\n  React.useEffect(() => {\n    renders.current = 0;\n  }, [value]);\n  const handleChange = event => {\n    renders.current = 0;\n    if (!isControlled) {\n      syncHeight();\n    }\n    if (onChange) {\n      onChange(event);\n    }\n  };\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(\"textarea\", _extends({\n      value: value,\n      onChange: handleChange,\n      ref: handleRef\n      // Apply the rows prop to get a \"correct\" first SSR paint\n      ,\n      rows: minRows,\n      style: _extends({\n        height: state.outerHeightStyle,\n        // Need a large enough difference to allow scrolling.\n        // This prevents infinite rendering loop.\n        overflow: state.overflow ? 'hidden' : undefined\n      }, style)\n    }, other)), /*#__PURE__*/_jsx(\"textarea\", {\n      \"aria-hidden\": true,\n      className: props.className,\n      readOnly: true,\n      ref: shadowRef,\n      tabIndex: -1,\n      style: _extends({}, styles.shadow, style, {\n        paddingTop: 0,\n        paddingBottom: 0\n      })\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TextareaAutosize.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Maximum number of rows to display.\n   */\n  maxRows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * Minimum number of rows to display.\n   * @default 1\n   */\n  minRows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  placeholder: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * @ignore\n   */\n  value: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.number, PropTypes.string])\n} : void 0;\nexport { TextareaAutosize };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC;AACtE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,SAASC,iBAAiB,IAAIC,QAAQ,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,oBAAoB,IAAIC,WAAW,QAAQ,YAAY;AACnL,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOC,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC;AACjC;AACA,MAAME,MAAM,GAAG;EACbC,MAAM,EAAE;IACN;IACAC,UAAU,EAAE,QAAQ;IACpB;IACAC,QAAQ,EAAE,UAAU;IACpB;IACAC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,CAAC;IACTC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACP;IACAC,SAAS,EAAE;EACb;AACF,CAAC;AACD,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,OAAOA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI,IAAIE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAACI,MAAM,KAAK,CAAC,IAAIJ,GAAG,CAACK,gBAAgB,KAAK,CAAC,IAAI,CAACL,GAAG,CAACN,QAAQ;AAC1H;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,gBAAgB,GAAG,aAAalC,KAAK,CAACmC,UAAU,CAAC,SAASD,gBAAgBA,CAACE,KAAK,EAAEC,YAAY,EAAE;EACpG,MAAM;MACFC,QAAQ;MACRC,OAAO;MACPC,OAAO,GAAG,CAAC;MACXC,KAAK;MACLzB;IACF,CAAC,GAAGoB,KAAK;IACTM,KAAK,GAAG5C,6BAA6B,CAACsC,KAAK,EAAErC,SAAS,CAAC;EACzD,MAAM;IACJ4C,OAAO,EAAEC;EACX,CAAC,GAAG5C,KAAK,CAAC6C,MAAM,CAAC7B,KAAK,IAAI,IAAI,CAAC;EAC/B,MAAM8B,QAAQ,GAAG9C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAME,SAAS,GAAGzC,UAAU,CAAC+B,YAAY,EAAES,QAAQ,CAAC;EACpD,MAAME,SAAS,GAAGhD,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMI,OAAO,GAAGjD,KAAK,CAAC6C,MAAM,CAAC,CAAC,CAAC;EAC/B,MAAM,CAACK,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,KAAK,CAACoD,QAAQ,CAAC;IACvCnB,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF,MAAMoB,eAAe,GAAGrD,KAAK,CAACsD,WAAW,CAAC,MAAM;IAC9C,MAAMC,KAAK,GAAGT,QAAQ,CAACH,OAAO;IAC9B,MAAMa,eAAe,GAAG9C,WAAW,CAAC6C,KAAK,CAAC;IAC1C,MAAME,aAAa,GAAGD,eAAe,CAACE,gBAAgB,CAACH,KAAK,CAAC;;IAE7D;IACA,IAAIE,aAAa,CAACE,KAAK,KAAK,KAAK,EAAE;MACjC,OAAO;QACL1B,gBAAgB,EAAE;MACpB,CAAC;IACH;IACA,MAAM2B,YAAY,GAAGZ,SAAS,CAACL,OAAO;IACtCiB,YAAY,CAACnB,KAAK,CAACkB,KAAK,GAAGF,aAAa,CAACE,KAAK;IAC9CC,YAAY,CAAC5C,KAAK,GAAGuC,KAAK,CAACvC,KAAK,IAAIoB,KAAK,CAACyB,WAAW,IAAI,GAAG;IAC5D,IAAID,YAAY,CAAC5C,KAAK,CAAC8C,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MACzC;MACA;MACA;MACAF,YAAY,CAAC5C,KAAK,IAAI,GAAG;IAC3B;IACA,MAAM+C,SAAS,GAAGN,aAAa,CAACM,SAAS;IACzC,MAAMC,OAAO,GAAGjD,aAAa,CAAC0C,aAAa,CAACQ,aAAa,CAAC,GAAGlD,aAAa,CAAC0C,aAAa,CAACS,UAAU,CAAC;IACpG,MAAMC,MAAM,GAAGpD,aAAa,CAAC0C,aAAa,CAACW,iBAAiB,CAAC,GAAGrD,aAAa,CAAC0C,aAAa,CAACY,cAAc,CAAC;;IAE3G;IACA,MAAMC,WAAW,GAAGV,YAAY,CAACW,YAAY;;IAE7C;IACAX,YAAY,CAAC5C,KAAK,GAAG,GAAG;IACxB,MAAMwD,eAAe,GAAGZ,YAAY,CAACW,YAAY;;IAEjD;IACA,IAAIE,WAAW,GAAGH,WAAW;IAC7B,IAAI9B,OAAO,EAAE;MACXiC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACpC,OAAO,CAAC,GAAGgC,eAAe,EAAEC,WAAW,CAAC;IACxE;IACA,IAAIlC,OAAO,EAAE;MACXkC,WAAW,GAAGC,IAAI,CAACG,GAAG,CAACD,MAAM,CAACrC,OAAO,CAAC,GAAGiC,eAAe,EAAEC,WAAW,CAAC;IACxE;IACAA,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACF,WAAW,EAAED,eAAe,CAAC;;IAEpD;IACA,MAAMvC,gBAAgB,GAAGwC,WAAW,IAAIV,SAAS,KAAK,YAAY,GAAGC,OAAO,GAAGG,MAAM,GAAG,CAAC,CAAC;IAC1F,MAAM7C,QAAQ,GAAGoD,IAAI,CAACI,GAAG,CAACL,WAAW,GAAGH,WAAW,CAAC,IAAI,CAAC;IACzD,OAAO;MACLrC,gBAAgB;MAChBX;IACF,CAAC;EACH,CAAC,EAAE,CAACiB,OAAO,EAAEC,OAAO,EAAEJ,KAAK,CAACyB,WAAW,CAAC,CAAC;EACzC,MAAMkB,WAAW,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;IAC3C,MAAM;MACJhD,gBAAgB;MAChBX;IACF,CAAC,GAAG2D,QAAQ;IACZ;IACA;IACA,IAAIhC,OAAO,CAACN,OAAO,GAAG,EAAE,KAAKV,gBAAgB,GAAG,CAAC,IAAIyC,IAAI,CAACI,GAAG,CAAC,CAACE,SAAS,CAAC/C,gBAAgB,IAAI,CAAC,IAAIA,gBAAgB,CAAC,GAAG,CAAC,IAAI+C,SAAS,CAAC1D,QAAQ,KAAKA,QAAQ,CAAC,EAAE;MAC3J2B,OAAO,CAACN,OAAO,IAAI,CAAC;MACpB,OAAO;QACLrB,QAAQ;QACRW;MACF,CAAC;IACH;IACA,IAAIiD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAInC,OAAO,CAACN,OAAO,KAAK,EAAE,EAAE;QAC1B0C,OAAO,CAACC,KAAK,CAAC,CAAC,mDAAmD,EAAE,4EAA4E,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/J;IACF;IACA,OAAOP,SAAS;EAClB,CAAC;EACD,MAAMQ,UAAU,GAAGxF,KAAK,CAACsD,WAAW,CAAC,MAAM;IACzC,MAAM2B,QAAQ,GAAG5B,eAAe,CAAC,CAAC;IAClC,IAAI1B,OAAO,CAACsD,QAAQ,CAAC,EAAE;MACrB;IACF;IACA9B,QAAQ,CAAC6B,SAAS,IAAID,WAAW,CAACC,SAAS,EAAEC,QAAQ,CAAC,CAAC;EACzD,CAAC,EAAE,CAAC5B,eAAe,CAAC,CAAC;EACrB7C,iBAAiB,CAAC,MAAM;IACtB,MAAMiF,uBAAuB,GAAGA,CAAA,KAAM;MACpC,MAAMR,QAAQ,GAAG5B,eAAe,CAAC,CAAC;MAClC,IAAI1B,OAAO,CAACsD,QAAQ,CAAC,EAAE;QACrB;MACF;;MAEA;MACA;MACA;MACA;MACA;MACA/E,QAAQ,CAACwF,SAAS,CAAC,MAAM;QACvBvC,QAAQ,CAAC6B,SAAS,IAAID,WAAW,CAACC,SAAS,EAAEC,QAAQ,CAAC,CAAC;MACzD,CAAC,CAAC;IACJ,CAAC;IACD,MAAMU,YAAY,GAAGA,CAAA,KAAM;MACzB1C,OAAO,CAACN,OAAO,GAAG,CAAC;MACnB8C,uBAAuB,CAAC,CAAC;IAC3B,CAAC;IACD;IACA;IACA;IACA;IACA,IAAIG,GAAG;IACP,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5BC,oBAAoB,CAACF,GAAG,CAAC;MACzBA,GAAG,GAAGG,qBAAqB,CAAC,MAAM;QAChCJ,YAAY,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ,CAAC;IACD,MAAMK,oBAAoB,GAAG5F,QAAQ,CAACuF,YAAY,CAAC;IACnD,MAAMpC,KAAK,GAAGT,QAAQ,CAACH,OAAO;IAC9B,MAAMa,eAAe,GAAG9C,WAAW,CAAC6C,KAAK,CAAC;IAC1CC,eAAe,CAACyC,gBAAgB,CAAC,QAAQ,EAAED,oBAAoB,CAAC;IAChE,IAAIE,cAAc;IAClB,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;MACzCD,cAAc,GAAG,IAAIC,cAAc,CAACjB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,GAAGS,eAAe,GAAGF,YAAY,CAAC;MACrGO,cAAc,CAACE,OAAO,CAAC7C,KAAK,CAAC;IAC/B;IACA,OAAO,MAAM;MACXyC,oBAAoB,CAACK,KAAK,CAAC,CAAC;MAC5BP,oBAAoB,CAACF,GAAG,CAAC;MACzBpC,eAAe,CAAC8C,mBAAmB,CAAC,QAAQ,EAAEN,oBAAoB,CAAC;MACnE,IAAIE,cAAc,EAAE;QAClBA,cAAc,CAACK,UAAU,CAAC,CAAC;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAAClD,eAAe,CAAC,CAAC;EACrB7C,iBAAiB,CAAC,MAAM;IACtBgF,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;EACFxF,KAAK,CAACwG,SAAS,CAAC,MAAM;IACpBvD,OAAO,CAACN,OAAO,GAAG,CAAC;EACrB,CAAC,EAAE,CAAC3B,KAAK,CAAC,CAAC;EACX,MAAMyF,YAAY,GAAGC,KAAK,IAAI;IAC5BzD,OAAO,CAACN,OAAO,GAAG,CAAC;IACnB,IAAI,CAACC,YAAY,EAAE;MACjB4C,UAAU,CAAC,CAAC;IACd;IACA,IAAIlD,QAAQ,EAAE;MACZA,QAAQ,CAACoE,KAAK,CAAC;IACjB;EACF,CAAC;EACD,OAAO,aAAa5F,KAAK,CAACd,KAAK,CAAC2G,QAAQ,EAAE;IACxCC,QAAQ,EAAE,CAAC,aAAahG,IAAI,CAAC,UAAU,EAAEf,QAAQ,CAAC;MAChDmB,KAAK,EAAEA,KAAK;MACZsB,QAAQ,EAAEmE,YAAY;MACtBI,GAAG,EAAE9D;MACL;MAAA;;MAEA+D,IAAI,EAAEtE,OAAO;MACbC,KAAK,EAAE5C,QAAQ,CAAC;QACd0B,MAAM,EAAE2B,KAAK,CAACjB,gBAAgB;QAC9B;QACA;QACAX,QAAQ,EAAE4B,KAAK,CAAC5B,QAAQ,GAAG,QAAQ,GAAGO;MACxC,CAAC,EAAEY,KAAK;IACV,CAAC,EAAEC,KAAK,CAAC,CAAC,EAAE,aAAa9B,IAAI,CAAC,UAAU,EAAE;MACxC,aAAa,EAAE,IAAI;MACnBmG,SAAS,EAAE3E,KAAK,CAAC2E,SAAS;MAC1BC,QAAQ,EAAE,IAAI;MACdH,GAAG,EAAE7D,SAAS;MACdiE,QAAQ,EAAE,CAAC,CAAC;MACZxE,KAAK,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAEqB,MAAM,CAACC,MAAM,EAAEsB,KAAK,EAAE;QACxCyB,UAAU,EAAE,CAAC;QACbD,aAAa,EAAE;MACjB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlD,gBAAgB,CAACgF,SAAS,CAAC,yBAAyB;EAC1F;EACA;EACA;EACA;EACA;AACF;AACA;EACEH,SAAS,EAAE9G,SAAS,CAACkH,MAAM;EAC3B;AACF;AACA;EACE5E,OAAO,EAAEtC,SAAS,CAACmH,SAAS,CAAC,CAACnH,SAAS,CAACoH,MAAM,EAAEpH,SAAS,CAACkH,MAAM,CAAC,CAAC;EAClE;AACF;AACA;AACA;EACE3E,OAAO,EAAEvC,SAAS,CAACmH,SAAS,CAAC,CAACnH,SAAS,CAACoH,MAAM,EAAEpH,SAAS,CAACkH,MAAM,CAAC,CAAC;EAClE;AACF;AACA;EACE7E,QAAQ,EAAErC,SAAS,CAACqH,IAAI;EACxB;AACF;AACA;EACEzD,WAAW,EAAE5D,SAAS,CAACkH,MAAM;EAC7B;AACF;AACA;EACE1E,KAAK,EAAExC,SAAS,CAACsH,MAAM;EACvB;AACF;AACA;EACEvG,KAAK,EAAEf,SAAS,CAACmH,SAAS,CAAC,CAACnH,SAAS,CAACuH,OAAO,CAACvH,SAAS,CAACkH,MAAM,CAAC,EAAElH,SAAS,CAACoH,MAAM,EAAEpH,SAAS,CAACkH,MAAM,CAAC;AACtG,CAAC,GAAG,KAAK,CAAC;AACV,SAASjF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}