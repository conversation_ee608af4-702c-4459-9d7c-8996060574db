{"ast": null, "code": "'use client';\n\nexport * from './MenuItem';\nexport * from './MenuItem.types';\nexport * from './menuItemClasses';", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/MenuItem/index.js"], "sourcesContent": ["'use client';\n\nexport * from './MenuItem';\nexport * from './MenuItem.types';\nexport * from './menuItemClasses';"], "mappings": "AAAA,YAAY;;AAEZ,cAAc,YAAY;AAC1B,cAAc,kBAAkB;AAChC,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}