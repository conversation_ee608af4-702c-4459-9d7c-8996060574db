{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getClockPickerUtilityClass(slot) {\n  return generateUtilityClass('MuiClockPicker', slot);\n}\nexport const clockPickerClasses = generateUtilityClasses('MuiClockPicker', ['root', 'arrowSwitcher']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getClockPickerUtilityClass", "slot", "clockPickerClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/ClockPicker/clockPickerClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getClockPickerUtilityClass(slot) {\n  return generateUtilityClass('MuiClockPicker', slot);\n}\nexport const clockPickerClasses = generateUtilityClasses('MuiClockPicker', ['root', 'arrowSwitcher']);"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,eAAe;AAC5E,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,OAAO,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}