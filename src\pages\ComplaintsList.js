import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Chip,
  MenuItem,
  FormControl,
  Select,
  InputAdornment,
  Fab,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Toolbar,
  Paper,
  useTheme,
  useMediaQuery,
  Grid,
  Collapse,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  DateRange as DateRangeIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';
import axios from '../utils/axiosConfig';
import { useAuth } from '../contexts/AuthContext';

const tableRowVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

function ComplaintsList() {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();

  const [complaints, setComplaints] = useState([]);
  const [metadata, setMetadata] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('all');
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const fetchComplaints = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();

      if (fromDate) {
        params.append('startDate', fromDate);
      }
      if (toDate) {
        params.append('endDate', toDate);
      }

      const url = `/api/complaints${params.toString() ? `?${params.toString()}` : ''}`;
      console.log('Fetching complaints with URL:', url);

      const response = await axios.get(url);
      console.log('Complaints data:', response.data); // For debugging

      setComplaints(response.data.complaints || []);
      setMetadata(response.data.metadata || {});
    } catch (error) {
      console.error('Error fetching complaints:', error);
      setError(error.response?.data?.message || 'Failed to fetch complaints');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComplaints();
  }, []);

  // Refetch when date filters change
  useEffect(() => {
    if (fromDate || toDate) {
      fetchComplaints();
    }
  }, [fromDate, toDate]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleClearFilters = () => {
    setFromDate('');
    setToDate('');
    setSearchTerm('');
    setStatusFilter('all');
    setPriorityFilter('all');
  };

  const handleApplyDateFilter = () => {
    fetchComplaints();
  };

  const formatDateForInput = (date) => {
    if (!date) return '';
    return new Date(date).toISOString().split('T')[0];
  };

  const filterComplaints = (complaints) => {
    return complaints.filter(complaint => {
      // Filter by tab
      if (activeTab === 'my' && complaint.RelationType !== 'My Complaint') return false;
      if (activeTab === 'assigned' && complaint.RelationType !== 'Assigned to Me') return false;

      // Filter by search term
      if (searchTerm && !complaint.Title?.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !complaint.ComplaintNumber?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      // Filter by status
      if (statusFilter !== 'all' && complaint.Status !== statusFilter) return false;

      // Filter by priority
      if (priorityFilter !== 'all' && complaint.Priority !== priorityFilter) return false;

      return true;
    });
  };

  const getStatusColor = (status) => {
    const colors = {
      'New': 'info',
      'Assigned': 'warning',
      'In Progress': 'primary',
      'Resolved': 'success',
      'Rejected': 'default'
    };
    return colors[status] || 'default';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'Low': 'success',
      'Medium': 'warning',
      'High': 'error',
      'Critical': 'error'
    };
    return colors[priority] || 'default';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  const filteredComplaints = filterComplaints(complaints);

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ p: { xs: 2, sm: 3 } }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant={isMobile ? "h5" : "h4"} component="h1">
              Complaints
            </Typography>
            <Button
              startIcon={<RefreshIcon />}
              onClick={fetchComplaints}
              disabled={loading}
            >
              Refresh
            </Button>
          </Box>

          {error && (
            <Alert 
              severity="error" 
              sx={{ mb: 2 }}
              action={
                <Button color="inherit" size="small" onClick={fetchComplaints}>
                  Retry
                </Button>
              }
            >
              {error}
            </Alert>
          )}

          <Card sx={{ mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant={isMobile ? "scrollable" : "standard"}
              scrollButtons={isMobile ? "auto" : false}
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              <Tab 
                label={`All ${metadata?.isAdmin ? `(${metadata?.totalComplaints || 0})` : ''}`} 
                value="all"
                disabled={!metadata?.isAdmin}
              />
              <Tab 
                label={`My Complaints (${metadata?.myComplaints || 0})`} 
                value="my"
              />
              <Tab 
                label={`Assigned to Me (${metadata?.assignedToMe || 0})`} 
                value="assigned"
              />
            </Tabs>

            <Toolbar sx={{ p: 2, gap: 2, flexWrap: 'wrap' }}>
              <TextField
                placeholder="Search complaints..."
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ flexGrow: 1, minWidth: 200 }}
              />

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="New">New</MenuItem>
                  <MenuItem value="Assigned">Assigned</MenuItem>
                  <MenuItem value="In Progress">In Progress</MenuItem>
                  <MenuItem value="Resolved">Resolved</MenuItem>
                  <MenuItem value="Rejected">Rejected</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  label="Priority"
                >
                  <MenuItem value="all">All Priority</MenuItem>
                  <MenuItem value="Low">Low</MenuItem>
                  <MenuItem value="Medium">Medium</MenuItem>
                  <MenuItem value="High">High</MenuItem>
                  <MenuItem value="Critical">Critical</MenuItem>
                </Select>
              </FormControl>

              <Button
                startIcon={<FilterIcon />}
                onClick={() => setShowFilters(!showFilters)}
                variant={showFilters ? "contained" : "outlined"}
                size="small"
                sx={{ minWidth: 'auto' }}
              >
                {isMobile ? '' : 'Date Filter'}
              </Button>
            </Toolbar>

            {/* Date Range Filter Section */}
            <Collapse in={showFilters}>
              <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider', bgcolor: 'grey.50' }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={12} md={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <DateRangeIcon color="primary" />
                      <Typography variant="subtitle2" color="primary" fontWeight={600}>
                        Date Range Filter
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      label="From Date"
                      type="date"
                      size="small"
                      fullWidth
                      value={fromDate}
                      onChange={(e) => setFromDate(e.target.value)}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      inputProps={{
                        max: toDate || undefined
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      label="To Date"
                      type="date"
                      size="small"
                      fullWidth
                      value={toDate}
                      onChange={(e) => setToDate(e.target.value)}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      inputProps={{
                        min: fromDate || undefined
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={12} md={4}>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Button
                        variant="contained"
                        size="small"
                        onClick={handleApplyDateFilter}
                        disabled={!fromDate && !toDate}
                        startIcon={<SearchIcon />}
                      >
                        Apply Filter
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={handleClearFilters}
                        startIcon={<ClearIcon />}
                      >
                        Clear All
                      </Button>
                    </Box>
                  </Grid>
                </Grid>

                {/* Quick Date Presets */}
                <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Typography variant="caption" color="textSecondary" sx={{ mr: 1, alignSelf: 'center' }}>
                    Quick filters:
                  </Typography>
                  <Button
                    size="small"
                    variant="text"
                    onClick={() => {
                      const today = new Date();
                      setFromDate(formatDateForInput(today));
                      setToDate(formatDateForInput(today));
                    }}
                  >
                    Today
                  </Button>
                  <Button
                    size="small"
                    variant="text"
                    onClick={() => {
                      const today = new Date();
                      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                      setFromDate(formatDateForInput(lastWeek));
                      setToDate(formatDateForInput(today));
                    }}
                  >
                    Last 7 Days
                  </Button>
                  <Button
                    size="small"
                    variant="text"
                    onClick={() => {
                      const today = new Date();
                      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
                      setFromDate(formatDateForInput(lastMonth));
                      setToDate(formatDateForInput(today));
                    }}
                  >
                    Last 30 Days
                  </Button>
                  <Button
                    size="small"
                    variant="text"
                    onClick={() => {
                      const today = new Date();
                      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                      setFromDate(formatDateForInput(firstDay));
                      setToDate(formatDateForInput(today));
                    }}
                  >
                    This Month
                  </Button>
                </Box>
              </Box>
            </Collapse>
          </Card>

          {/* Mobile View - Cards */}
          {isMobile ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {filteredComplaints.length > 0 ? (
                filteredComplaints.map((complaint, index) => (
                  <motion.div
                    key={complaint.ComplaintId}
                    variants={tableRowVariants}
                    initial="hidden"
                    animate="visible"
                    transition={{ delay: index * 0.05 }}
                  >
                    <Card
                      sx={{
                        cursor: 'pointer',
                        '&:hover': {
                          boxShadow: 3,
                        },
                      }}
                      onClick={() => navigate(`/complaints/${complaint.ComplaintId}`)}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
                            {complaint.ComplaintNumber}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Chip
                              label={complaint.Status}
                              color={getStatusColor(complaint.Status)}
                              size="small"
                            />
                            <Chip
                              label={complaint.Priority}
                              color={getPriorityColor(complaint.Priority)}
                              size="small"
                            />
                          </Box>
                        </Box>

                        <Typography variant="body1" sx={{ mb: 2, fontWeight: 500 }}>
                          {complaint.Title}
                        </Typography>

                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <Box>
                            <Typography variant="caption" color="textSecondary">
                              Submitted by:
                            </Typography>
                            <Typography variant="body2">
                              {complaint.SubmittedByName}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {complaint.SubmittedByDepartment}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography variant="caption" color="textSecondary">
                              Submitted on:
                            </Typography>
                            <Typography variant="body2">
                              {format(new Date(complaint.SubmissionDate), 'MMM dd, yyyy')}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography variant="caption" color="textSecondary">
                              Assigned to:
                            </Typography>
                            {complaint.AssignedToName ? (
                              <>
                                <Typography variant="body2">
                                  {complaint.AssignedToName}
                                </Typography>
                                <Typography variant="caption" color="textSecondary">
                                  {complaint.AssignedToDepartment}
                                </Typography>
                              </>
                            ) : (
                              <Typography variant="body2" color="textSecondary">
                                Not Assigned
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))
              ) : (
                <Card>
                  <CardContent>
                    <Typography color="textSecondary" align="center">
                      No complaints found
                    </Typography>
                  </CardContent>
                </Card>
              )}
            </Box>
          ) : (
            /* Desktop View - Table */
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Complaint #</TableCell>
                    <TableCell>Title</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Submitted By</TableCell>
                    <TableCell>Submitted On</TableCell>
                    <TableCell>Assigned To</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredComplaints.length > 0 ? (
                    filteredComplaints.map((complaint, index) => (
                      <motion.tr
                        key={complaint.ComplaintId}
                        variants={tableRowVariants}
                        initial="hidden"
                        animate="visible"
                        transition={{ delay: index * 0.05 }}
                        component={TableRow}
                        sx={{
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: theme.palette.action.hover,
                          },
                        }}
                        onClick={() => navigate(`/complaints/${complaint.ComplaintId}`)}
                      >
                        <TableCell>{complaint.ComplaintNumber}</TableCell>
                        <TableCell>{complaint.Title}</TableCell>
                        <TableCell>
                          <Chip
                            label={complaint.Status}
                            color={getStatusColor(complaint.Status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={complaint.Priority}
                            color={getPriorityColor(complaint.Priority)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {complaint.SubmittedByName}
                          <Typography variant="caption" display="block" color="textSecondary">
                            {complaint.SubmittedByDepartment}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {format(new Date(complaint.SubmissionDate), 'MMM dd, yyyy')}
                        </TableCell>
                        <TableCell>
                          {complaint.AssignedToName ? (
                            <>
                              {complaint.AssignedToName}
                              <Typography variant="caption" display="block" color="textSecondary">
                                {complaint.AssignedToDepartment}
                              </Typography>
                            </>
                          ) : (
                            <Typography variant="caption" color="textSecondary">
                              Not Assigned
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/complaints/${complaint.ComplaintId}`);
                            }}
                          >
                            <VisibilityIcon />
                          </IconButton>
                        </TableCell>
                      </motion.tr>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} align="center">
                        <Typography color="textSecondary">
                          No complaints found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          <Fab
            color="primary"
            aria-label="add complaint"
            sx={{
              position: 'fixed',
              bottom: 16,
              right: 16,
              zIndex: 1000
            }}
            onClick={() => navigate('/complaints/new')}
          >
            <AddIcon />
          </Fab>
        </Box>
      </motion.div>
    </AnimatePresence>
  );
}

export default ComplaintsList; 