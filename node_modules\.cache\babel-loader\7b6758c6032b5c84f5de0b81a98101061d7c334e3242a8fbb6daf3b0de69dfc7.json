{"ast": null, "code": "import React from'react';import{Routes,Route,Navigate}from'react-router-dom';import{ThemeProvider,CssBaseline}from'@mui/material';import{useAuth}from'./contexts/AuthContext';import{SocketProvider}from'./contexts/SocketContext';import{LocalizationProvider}from'@mui/x-date-pickers/LocalizationProvider';import{AdapterDateFns}from'@mui/x-date-pickers/AdapterDateFns';import theme from'./theme';// Pages\nimport Login from'./pages/Login';import Dashboard from'./pages/Dashboard';import ComplaintsList from'./pages/ComplaintsList';import ComplaintDetails from'./pages/ComplaintDetails';import AuthorityManagement from'./pages/AuthorityManagement';import ChangePassword from'./pages/ChangePassword';import Layout from'./components/Layout';import NewComplaint from'./pages/NewComplaint';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){var _user$permissions;const{user,loading,initialized}=useAuth();// Show nothing while auth is initializing\nif(!initialized){return null;}// Show loading state\nif(loading){return null;// Or a loading spinner\n}// Check if user should see dashboard\nconst canViewDashboard=(user===null||user===void 0?void 0:user.isAdmin)||(user===null||user===void 0?void 0:(_user$permissions=user.permissions)===null||_user$permissions===void 0?void 0:_user$permissions.canViewDashboard)===true;return/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(LocalizationProvider,{dateAdapter:AdapterDateFns,children:/*#__PURE__*/_jsx(SocketProvider,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),user?/*#__PURE__*/_jsxs(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Layout,{}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:canViewDashboard?/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true}):/*#__PURE__*/_jsx(Navigate,{to:\"/complaints\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"dashboard\",element:canViewDashboard?/*#__PURE__*/_jsx(Dashboard,{}):/*#__PURE__*/_jsx(Navigate,{to:\"/complaints\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"complaints\",element:/*#__PURE__*/_jsx(ComplaintsList,{})}),/*#__PURE__*/_jsx(Route,{path:\"complaints/new\",element:/*#__PURE__*/_jsx(NewComplaint,{})}),/*#__PURE__*/_jsx(Route,{path:\"complaints/:id\",element:/*#__PURE__*/_jsx(ComplaintDetails,{})}),/*#__PURE__*/_jsx(Route,{path:\"authority-management\",element:user.isAdmin?/*#__PURE__*/_jsx(AuthorityManagement,{}):/*#__PURE__*/_jsx(Navigate,{to:\"/complaints\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"change-password\",element:/*#__PURE__*/_jsx(ChangePassword,{})})]}):/*#__PURE__*/// If not authenticated, redirect to login\n_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:user?canViewDashboard?\"/dashboard\":\"/complaints\":\"/login\",replace:true})})]})})})]});}export default App;", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "ThemeProvider", "CssBaseline", "useAuth", "SocketProvider", "LocalizationProvider", "AdapterDateFns", "theme", "<PERSON><PERSON>", "Dashboard", "ComplaintsList", "ComplaintDetails", "AuthorityManagement", "ChangePassword", "Layout", "NewComplaint", "jsx", "_jsx", "jsxs", "_jsxs", "App", "_user$permissions", "user", "loading", "initialized", "canViewDashboard", "isAdmin", "permissions", "children", "dateAdapter", "path", "element", "index", "to", "replace"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { Routes, Route, Navigate } from 'react-router-dom';\r\nimport { ThemeProvider, CssBaseline } from '@mui/material';\r\nimport { useAuth } from './contexts/AuthContext';\r\nimport { SocketProvider } from './contexts/SocketContext';\r\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\r\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\r\nimport theme from './theme';\r\n\r\n// Pages\r\nimport Login from './pages/Login';\r\nimport Dashboard from './pages/Dashboard';\r\nimport ComplaintsList from './pages/ComplaintsList';\r\nimport ComplaintDetails from './pages/ComplaintDetails';\r\nimport AuthorityManagement from './pages/AuthorityManagement';\r\nimport ChangePassword from './pages/ChangePassword';\r\nimport Layout from './components/Layout';\r\nimport NewComplaint from './pages/NewComplaint';\r\n\r\nfunction App() {\r\n  const { user, loading, initialized } = useAuth();\r\n\r\n  // Show nothing while auth is initializing\r\n  if (!initialized) {\r\n    return null;\r\n  }\r\n\r\n  // Show loading state\r\n  if (loading) {\r\n    return null; // Or a loading spinner\r\n  }\r\n\r\n  // Check if user should see dashboard\r\n  const canViewDashboard = user?.isAdmin || (user?.permissions?.canViewDashboard === true);\r\n\r\n  return (\r\n    <ThemeProvider theme={theme}>\r\n      <CssBaseline />\r\n      <LocalizationProvider dateAdapter={AdapterDateFns}>\r\n        <SocketProvider>\r\n          <Routes>\r\n          {/* Public route - Login */}\r\n          <Route\r\n            path=\"/login\"\r\n            element={<Login />}\r\n          />\r\n\r\n          {/* Protected routes - Must be authenticated */}\r\n          {user ? (\r\n            <Route path=\"/\" element={<Layout />}>\r\n              <Route \r\n                index \r\n                element={\r\n                  canViewDashboard ?\r\n                    <Navigate to=\"/dashboard\" replace /> : \r\n                    <Navigate to=\"/complaints\" replace />\r\n                } \r\n              />\r\n              <Route \r\n                path=\"dashboard\" \r\n                element={\r\n                  canViewDashboard ?\r\n                    <Dashboard /> : \r\n                    <Navigate to=\"/complaints\" replace />\r\n                } \r\n              />\r\n              <Route path=\"complaints\" element={<ComplaintsList />} />\r\n              <Route path=\"complaints/new\" element={<NewComplaint />} />\r\n              <Route path=\"complaints/:id\" element={<ComplaintDetails />} />\r\n              <Route \r\n                path=\"authority-management\" \r\n                element={\r\n                  user.isAdmin ? \r\n                    <AuthorityManagement /> : \r\n                    <Navigate to=\"/complaints\" replace />\r\n                } \r\n              />\r\n              <Route path=\"change-password\" element={<ChangePassword />} />\r\n            </Route>\r\n          ) : (\r\n            // If not authenticated, redirect to login\r\n            <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\r\n          )}\r\n\r\n          {/* Catch all route - redirect to login if not authenticated, otherwise to dashboard/complaints */}\r\n          <Route \r\n            path=\"*\" \r\n            element={\r\n              <Navigate \r\n                to={\r\n                  user ? \r\n                    (canViewDashboard ? \"/dashboard\" : \"/complaints\") :\r\n                    \"/login\"\r\n                } \r\n                replace \r\n              />\r\n            } \r\n          />\r\n          </Routes>\r\n        </SocketProvider>\r\n      </LocalizationProvider>\r\n    </ThemeProvider>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CAC1D,OAASC,aAAa,CAAEC,WAAW,KAAQ,eAAe,CAC1D,OAASC,OAAO,KAAQ,wBAAwB,CAChD,OAASC,cAAc,KAAQ,0BAA0B,CACzD,OAASC,oBAAoB,KAAQ,0CAA0C,CAC/E,OAASC,cAAc,KAAQ,oCAAoC,CACnE,MAAO,CAAAC,KAAK,KAAM,SAAS,CAE3B;AACA,MAAO,CAAAC,KAAK,KAAM,eAAe,CACjC,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,mBAAmB,KAAM,6BAA6B,CAC7D,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,QAAS,CAAAC,GAAGA,CAAA,CAAG,KAAAC,iBAAA,CACb,KAAM,CAAEC,IAAI,CAAEC,OAAO,CAAEC,WAAY,CAAC,CAAGrB,OAAO,CAAC,CAAC,CAEhD;AACA,GAAI,CAACqB,WAAW,CAAE,CAChB,MAAO,KAAI,CACb,CAEA;AACA,GAAID,OAAO,CAAE,CACX,MAAO,KAAI,CAAE;AACf,CAEA;AACA,KAAM,CAAAE,gBAAgB,CAAG,CAAAH,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEI,OAAO,GAAK,CAAAJ,IAAI,SAAJA,IAAI,kBAAAD,iBAAA,CAAJC,IAAI,CAAEK,WAAW,UAAAN,iBAAA,iBAAjBA,iBAAA,CAAmBI,gBAAgB,IAAK,IAAK,CAExF,mBACEN,KAAA,CAAClB,aAAa,EAACM,KAAK,CAAEA,KAAM,CAAAqB,QAAA,eAC1BX,IAAA,CAACf,WAAW,GAAE,CAAC,cACfe,IAAA,CAACZ,oBAAoB,EAACwB,WAAW,CAAEvB,cAAe,CAAAsB,QAAA,cAChDX,IAAA,CAACb,cAAc,EAAAwB,QAAA,cACbT,KAAA,CAACrB,MAAM,EAAA8B,QAAA,eAEPX,IAAA,CAAClB,KAAK,EACJ+B,IAAI,CAAC,QAAQ,CACbC,OAAO,cAAEd,IAAA,CAACT,KAAK,GAAE,CAAE,CACpB,CAAC,CAGDc,IAAI,cACHH,KAAA,CAACpB,KAAK,EAAC+B,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEd,IAAA,CAACH,MAAM,GAAE,CAAE,CAAAc,QAAA,eAClCX,IAAA,CAAClB,KAAK,EACJiC,KAAK,MACLD,OAAO,CACLN,gBAAgB,cACdR,IAAA,CAACjB,QAAQ,EAACiC,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAC,cACpCjB,IAAA,CAACjB,QAAQ,EAACiC,EAAE,CAAC,aAAa,CAACC,OAAO,MAAE,CACvC,CACF,CAAC,cACFjB,IAAA,CAAClB,KAAK,EACJ+B,IAAI,CAAC,WAAW,CAChBC,OAAO,CACLN,gBAAgB,cACdR,IAAA,CAACR,SAAS,GAAE,CAAC,cACbQ,IAAA,CAACjB,QAAQ,EAACiC,EAAE,CAAC,aAAa,CAACC,OAAO,MAAE,CACvC,CACF,CAAC,cACFjB,IAAA,CAAClB,KAAK,EAAC+B,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEd,IAAA,CAACP,cAAc,GAAE,CAAE,CAAE,CAAC,cACxDO,IAAA,CAAClB,KAAK,EAAC+B,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEd,IAAA,CAACF,YAAY,GAAE,CAAE,CAAE,CAAC,cAC1DE,IAAA,CAAClB,KAAK,EAAC+B,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEd,IAAA,CAACN,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAC9DM,IAAA,CAAClB,KAAK,EACJ+B,IAAI,CAAC,sBAAsB,CAC3BC,OAAO,CACLT,IAAI,CAACI,OAAO,cACVT,IAAA,CAACL,mBAAmB,GAAE,CAAC,cACvBK,IAAA,CAACjB,QAAQ,EAACiC,EAAE,CAAC,aAAa,CAACC,OAAO,MAAE,CACvC,CACF,CAAC,cACFjB,IAAA,CAAClB,KAAK,EAAC+B,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEd,IAAA,CAACJ,cAAc,GAAE,CAAE,CAAE,CAAC,EACxD,CAAC,cAER;AACAI,IAAA,CAAClB,KAAK,EAAC+B,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEd,IAAA,CAACjB,QAAQ,EAACiC,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAE,CAAE,CAC7D,cAGDjB,IAAA,CAAClB,KAAK,EACJ+B,IAAI,CAAC,GAAG,CACRC,OAAO,cACLd,IAAA,CAACjB,QAAQ,EACPiC,EAAE,CACAX,IAAI,CACDG,gBAAgB,CAAG,YAAY,CAAG,aAAa,CAChD,QACH,CACDS,OAAO,MACR,CACF,CACF,CAAC,EACM,CAAC,CACK,CAAC,CACG,CAAC,EACV,CAAC,CAEpB,CAEA,cAAe,CAAAd,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}