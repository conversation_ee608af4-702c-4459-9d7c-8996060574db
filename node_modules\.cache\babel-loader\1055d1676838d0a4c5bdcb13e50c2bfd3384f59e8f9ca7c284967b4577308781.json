{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getMenuButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuButton', slot);\n}\nexport const menuButtonClasses = generateUtilityClasses('MuiMenuButton', ['root', 'active', 'disabled', 'expanded']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMenuButtonUtilityClass", "slot", "menuButtonClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/MenuButton/menuButtonClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getMenuButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuButton', slot);\n}\nexport const menuButtonClasses = generateUtilityClasses('MuiMenuButton', ['root', 'active', 'disabled', 'expanded']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOH,oBAAoB,CAAC,eAAe,EAAEG,IAAI,CAAC;AACpD;AACA,OAAO,MAAMC,iBAAiB,GAAGH,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}