import axios from 'axios';

// Get base URL from environment or use window.location.origin as fallback
const getBaseUrl = () => {
    if (process.env.REACT_APP_API_URL) return process.env.REACT_APP_API_URL;

    // In development, use the proxy setup (React dev server will proxy /api to backend)
    if (process.env.NODE_ENV === 'development') {
        // Use relative URLs so the proxy can handle routing
        return '';
    }

    // In production, determine backend URL based on current location
    const currentHost = window.location.hostname;
    const currentPort = window.location.port;

    // If accessing via localhost, use localhost backend
    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
        return 'http://localhost:1976';
    }

    // Otherwise, use the same hostname with backend port
    return `http://${currentHost}:1976`;
};

const instance = axios.create({
    baseURL: getBaseUrl(),
    timeout: 30000, // Increased timeout
    headers: {
        'Content-Type': 'application/json',
    }
});

// Request interceptor
instance.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
    },
    (error) => {
        console.error('Request error:', error);
        return Promise.reject(error);
    }
);

// Response interceptor
instance.interceptors.response.use(
    (response) => {
        console.log(`API Response: ${response.status} for ${response.config.url}`);
        return response;
    },
    (error) => {
        console.error('Response error:', error.response || error);

        if (error.response?.status === 401) {
            console.log('Authentication error, redirecting to login');
            // Clear token and redirect to login on unauthorized
            localStorage.removeItem('token');
            delete instance.defaults.headers.common['Authorization'];

            // Use a slight delay to ensure console logs are visible
            setTimeout(() => {
            window.location.href = '/login';
            }, 100);
        }

        return Promise.reject(error);
    }
);

// Expose the current baseURL for debugging
console.log(`API base URL: ${instance.defaults.baseURL}`);

export default instance; 