{"ast": null, "code": "export const getDisplayDate = (utils, rawValue, inputFormat) => {\n  const date = utils.date(rawValue);\n  const isEmpty = rawValue === null;\n  if (isEmpty) {\n    return '';\n  }\n  return utils.isValid(date) ? utils.formatByString(\n  // TODO: should `isValid` narrow `TDate | null` to `NonNullable<TDate>`?\n  // Either we allow `TDate | null` to be valid and guard against calling `formatByString` with `null`.\n  // Or we ensure `formatByString` is callable with `null`.\n  date, inputFormat) : '';\n};\nconst MASK_USER_INPUT_SYMBOL = '_';\nconst staticDateWith2DigitTokens = '2019-11-21T22:30:00.000';\nconst staticDateWith1DigitTokens = '2019-01-01T09:00:00.000';\nexport function getMaskFromCurrentFormat(mask, format, acceptRegex, utils) {\n  if (mask) {\n    return mask;\n  }\n  const formattedDateWith1Digit = utils.formatByString(utils.date(staticDateWith1DigitTokens), format);\n  const inferredFormatPatternWith1Digits = formattedDateWith1Digit.replace(acceptRegex, MASK_USER_INPUT_SYMBOL);\n  const inferredFormatPatternWith2Digits = utils.formatByString(utils.date(staticDateWith2DigitTokens), format).replace(acceptRegex, '_');\n  if (inferredFormatPatternWith1Digits === inferredFormatPatternWith2Digits) {\n    return inferredFormatPatternWith1Digits;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    console.warn([\"Mask does not support numbers with variable length such as 'M'.\", \"Either use numbers with fix length or disable mask feature with 'disableMaskedInput' prop\", \"Falling down to uncontrolled no-mask input.\"].join('\\n'));\n  }\n  return '';\n}\nexport function checkMaskIsValidForCurrentFormat(mask, format, acceptRegex, utils) {\n  if (!mask) {\n    return false;\n  }\n  const formattedDateWith1Digit = utils.formatByString(utils.date(staticDateWith1DigitTokens), format);\n  const inferredFormatPatternWith1Digits = formattedDateWith1Digit.replace(acceptRegex, MASK_USER_INPUT_SYMBOL);\n  const inferredFormatPatternWith2Digits = utils.formatByString(utils.date(staticDateWith2DigitTokens), format).replace(acceptRegex, '_');\n  const isMaskValid = inferredFormatPatternWith2Digits === inferredFormatPatternWith1Digits && mask === inferredFormatPatternWith2Digits;\n  if (!isMaskValid && utils.lib !== 'luxon' && process.env.NODE_ENV !== 'production') {\n    if (format.includes('MMM')) {\n      console.warn([\"Mask does not support literals such as 'MMM'.\", \"Either use numbers with fix length or disable mask feature with 'disableMaskedInput' prop\", \"Falling down to uncontrolled no-mask input.\"].join('\\n'));\n    } else if (inferredFormatPatternWith2Digits && inferredFormatPatternWith2Digits !== inferredFormatPatternWith1Digits) {\n      console.warn([\"Mask does not support numbers with variable length such as 'M'.\", \"Either use numbers with fix length or disable mask feature with 'disableMaskedInput' prop\", \"Falling down to uncontrolled no-mask input.\"].join('\\n'));\n    } else if (mask) {\n      console.warn([\"The mask \\\"\".concat(mask, \"\\\" you passed is not valid for the format used \").concat(format, \".\"), \"Falling down to uncontrolled no-mask input.\"].join('\\n'));\n    }\n  }\n  return isMaskValid;\n}\nexport const maskedDateFormatter = (mask, acceptRegexp) => value => {\n  let outputCharIndex = 0;\n  return value.split('').map((char, inputCharIndex) => {\n    acceptRegexp.lastIndex = 0;\n    if (outputCharIndex > mask.length - 1) {\n      return '';\n    }\n    const maskChar = mask[outputCharIndex];\n    const nextMaskChar = mask[outputCharIndex + 1];\n    const acceptedChar = acceptRegexp.test(char) ? char : '';\n    const formattedChar = maskChar === MASK_USER_INPUT_SYMBOL ? acceptedChar : maskChar + acceptedChar;\n    outputCharIndex += formattedChar.length;\n    const isLastCharacter = inputCharIndex === value.length - 1;\n    if (isLastCharacter && nextMaskChar && nextMaskChar !== MASK_USER_INPUT_SYMBOL) {\n      // when cursor at the end of mask part (e.g. month) prerender next symbol \"21\" -> \"21/\"\n      return formattedChar ? formattedChar + nextMaskChar : '';\n    }\n    return formattedChar;\n  }).join('');\n};", "map": {"version": 3, "names": ["getDisplayDate", "utils", "rawValue", "inputFormat", "date", "isEmpty", "<PERSON><PERSON><PERSON><PERSON>", "formatByString", "MASK_USER_INPUT_SYMBOL", "staticDateWith2DigitTokens", "staticDateWith1DigitTokens", "getMaskFromCurrentFormat", "mask", "format", "acceptRegex", "formattedDateWith1Digit", "inferredFormatPatternWith1Digits", "replace", "inferredFormatPatternWith2Digits", "process", "env", "NODE_ENV", "console", "warn", "join", "checkMaskIsValidForCurrentFormat", "isMaskValid", "lib", "includes", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acceptRegexp", "value", "outputCharIndex", "split", "map", "char", "inputCharIndex", "lastIndex", "length", "maskChar", "nextMaskChar", "acceptedChar", "test", "formattedChar", "isLastCharacter"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/utils/text-field-helper.js"], "sourcesContent": ["export const getDisplayDate = (utils, rawValue, inputFormat) => {\n  const date = utils.date(rawValue);\n  const isEmpty = rawValue === null;\n\n  if (isEmpty) {\n    return '';\n  }\n\n  return utils.isValid(date) ? utils.formatByString( // TODO: should `isValid` narrow `TDate | null` to `NonNullable<TDate>`?\n  // Either we allow `TDate | null` to be valid and guard against calling `formatByString` with `null`.\n  // Or we ensure `formatByString` is callable with `null`.\n  date, inputFormat) : '';\n};\nconst MASK_USER_INPUT_SYMBOL = '_';\nconst staticDateWith2DigitTokens = '2019-11-21T22:30:00.000';\nconst staticDateWith1DigitTokens = '2019-01-01T09:00:00.000';\nexport function getMaskFromCurrentFormat(mask, format, acceptRegex, utils) {\n  if (mask) {\n    return mask;\n  }\n\n  const formattedDateWith1Digit = utils.formatByString(utils.date(staticDateWith1DigitTokens), format);\n  const inferredFormatPatternWith1Digits = formattedDateWith1Digit.replace(acceptRegex, MASK_USER_INPUT_SYMBOL);\n  const inferredFormatPatternWith2Digits = utils.formatByString(utils.date(staticDateWith2DigitTokens), format).replace(acceptRegex, '_');\n\n  if (inferredFormatPatternWith1Digits === inferredFormatPatternWith2Digits) {\n    return inferredFormatPatternWith1Digits;\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    console.warn([`Mask does not support numbers with variable length such as 'M'.`, `Either use numbers with fix length or disable mask feature with 'disableMaskedInput' prop`, `Falling down to uncontrolled no-mask input.`].join('\\n'));\n  }\n\n  return '';\n}\nexport function checkMaskIsValidForCurrentFormat(mask, format, acceptRegex, utils) {\n  if (!mask) {\n    return false;\n  }\n\n  const formattedDateWith1Digit = utils.formatByString(utils.date(staticDateWith1DigitTokens), format);\n  const inferredFormatPatternWith1Digits = formattedDateWith1Digit.replace(acceptRegex, MASK_USER_INPUT_SYMBOL);\n  const inferredFormatPatternWith2Digits = utils.formatByString(utils.date(staticDateWith2DigitTokens), format).replace(acceptRegex, '_');\n  const isMaskValid = inferredFormatPatternWith2Digits === inferredFormatPatternWith1Digits && mask === inferredFormatPatternWith2Digits;\n\n  if (!isMaskValid && utils.lib !== 'luxon' && process.env.NODE_ENV !== 'production') {\n    if (format.includes('MMM')) {\n      console.warn([`Mask does not support literals such as 'MMM'.`, `Either use numbers with fix length or disable mask feature with 'disableMaskedInput' prop`, `Falling down to uncontrolled no-mask input.`].join('\\n'));\n    } else if (inferredFormatPatternWith2Digits && inferredFormatPatternWith2Digits !== inferredFormatPatternWith1Digits) {\n      console.warn([`Mask does not support numbers with variable length such as 'M'.`, `Either use numbers with fix length or disable mask feature with 'disableMaskedInput' prop`, `Falling down to uncontrolled no-mask input.`].join('\\n'));\n    } else if (mask) {\n      console.warn([`The mask \"${mask}\" you passed is not valid for the format used ${format}.`, `Falling down to uncontrolled no-mask input.`].join('\\n'));\n    }\n  }\n\n  return isMaskValid;\n}\nexport const maskedDateFormatter = (mask, acceptRegexp) => value => {\n  let outputCharIndex = 0;\n  return value.split('').map((char, inputCharIndex) => {\n    acceptRegexp.lastIndex = 0;\n\n    if (outputCharIndex > mask.length - 1) {\n      return '';\n    }\n\n    const maskChar = mask[outputCharIndex];\n    const nextMaskChar = mask[outputCharIndex + 1];\n    const acceptedChar = acceptRegexp.test(char) ? char : '';\n    const formattedChar = maskChar === MASK_USER_INPUT_SYMBOL ? acceptedChar : maskChar + acceptedChar;\n    outputCharIndex += formattedChar.length;\n    const isLastCharacter = inputCharIndex === value.length - 1;\n\n    if (isLastCharacter && nextMaskChar && nextMaskChar !== MASK_USER_INPUT_SYMBOL) {\n      // when cursor at the end of mask part (e.g. month) prerender next symbol \"21\" -> \"21/\"\n      return formattedChar ? formattedChar + nextMaskChar : '';\n    }\n\n    return formattedChar;\n  }).join('');\n};"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,KAAK;EAC9D,MAAMC,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACF,QAAQ,CAAC;EACjC,MAAMG,OAAO,GAAGH,QAAQ,KAAK,IAAI;EAEjC,IAAIG,OAAO,EAAE;IACX,OAAO,EAAE;EACX;EAEA,OAAOJ,KAAK,CAACK,OAAO,CAACF,IAAI,CAAC,GAAGH,KAAK,CAACM,cAAc;EAAE;EACnD;EACA;EACAH,IAAI,EAAED,WAAW,CAAC,GAAG,EAAE;AACzB,CAAC;AACD,MAAMK,sBAAsB,GAAG,GAAG;AAClC,MAAMC,0BAA0B,GAAG,yBAAyB;AAC5D,MAAMC,0BAA0B,GAAG,yBAAyB;AAC5D,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEb,KAAK,EAAE;EACzE,IAAIW,IAAI,EAAE;IACR,OAAOA,IAAI;EACb;EAEA,MAAMG,uBAAuB,GAAGd,KAAK,CAACM,cAAc,CAACN,KAAK,CAACG,IAAI,CAACM,0BAA0B,CAAC,EAAEG,MAAM,CAAC;EACpG,MAAMG,gCAAgC,GAAGD,uBAAuB,CAACE,OAAO,CAACH,WAAW,EAAEN,sBAAsB,CAAC;EAC7G,MAAMU,gCAAgC,GAAGjB,KAAK,CAACM,cAAc,CAACN,KAAK,CAACG,IAAI,CAACK,0BAA0B,CAAC,EAAEI,MAAM,CAAC,CAACI,OAAO,CAACH,WAAW,EAAE,GAAG,CAAC;EAEvI,IAAIE,gCAAgC,KAAKE,gCAAgC,EAAE;IACzE,OAAOF,gCAAgC;EACzC;EAEA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCC,OAAO,CAACC,IAAI,CAAC,+MAA+M,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC1O;EAEA,OAAO,EAAE;AACX;AACA,OAAO,SAASC,gCAAgCA,CAACb,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEb,KAAK,EAAE;EACjF,IAAI,CAACW,IAAI,EAAE;IACT,OAAO,KAAK;EACd;EAEA,MAAMG,uBAAuB,GAAGd,KAAK,CAACM,cAAc,CAACN,KAAK,CAACG,IAAI,CAACM,0BAA0B,CAAC,EAAEG,MAAM,CAAC;EACpG,MAAMG,gCAAgC,GAAGD,uBAAuB,CAACE,OAAO,CAACH,WAAW,EAAEN,sBAAsB,CAAC;EAC7G,MAAMU,gCAAgC,GAAGjB,KAAK,CAACM,cAAc,CAACN,KAAK,CAACG,IAAI,CAACK,0BAA0B,CAAC,EAAEI,MAAM,CAAC,CAACI,OAAO,CAACH,WAAW,EAAE,GAAG,CAAC;EACvI,MAAMY,WAAW,GAAGR,gCAAgC,KAAKF,gCAAgC,IAAIJ,IAAI,KAAKM,gCAAgC;EAEtI,IAAI,CAACQ,WAAW,IAAIzB,KAAK,CAAC0B,GAAG,KAAK,OAAO,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IAClF,IAAIR,MAAM,CAACe,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC1BN,OAAO,CAACC,IAAI,CAAC,6LAA6L,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxN,CAAC,MAAM,IAAIN,gCAAgC,IAAIA,gCAAgC,KAAKF,gCAAgC,EAAE;MACpHM,OAAO,CAACC,IAAI,CAAC,+MAA+M,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1O,CAAC,MAAM,IAAIZ,IAAI,EAAE;MACfU,OAAO,CAACC,IAAI,CAAC,eAAAM,MAAA,CAAcjB,IAAI,qDAAAiB,MAAA,CAAiDhB,MAAM,sDAAmD,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC;IACvJ;EACF;EAEA,OAAOE,WAAW;AACpB;AACA,OAAO,MAAMI,mBAAmB,GAAGA,CAAClB,IAAI,EAAEmB,YAAY,KAAKC,KAAK,IAAI;EAClE,IAAIC,eAAe,GAAG,CAAC;EACvB,OAAOD,KAAK,CAACE,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,cAAc,KAAK;IACnDN,YAAY,CAACO,SAAS,GAAG,CAAC;IAE1B,IAAIL,eAAe,GAAGrB,IAAI,CAAC2B,MAAM,GAAG,CAAC,EAAE;MACrC,OAAO,EAAE;IACX;IAEA,MAAMC,QAAQ,GAAG5B,IAAI,CAACqB,eAAe,CAAC;IACtC,MAAMQ,YAAY,GAAG7B,IAAI,CAACqB,eAAe,GAAG,CAAC,CAAC;IAC9C,MAAMS,YAAY,GAAGX,YAAY,CAACY,IAAI,CAACP,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;IACxD,MAAMQ,aAAa,GAAGJ,QAAQ,KAAKhC,sBAAsB,GAAGkC,YAAY,GAAGF,QAAQ,GAAGE,YAAY;IAClGT,eAAe,IAAIW,aAAa,CAACL,MAAM;IACvC,MAAMM,eAAe,GAAGR,cAAc,KAAKL,KAAK,CAACO,MAAM,GAAG,CAAC;IAE3D,IAAIM,eAAe,IAAIJ,YAAY,IAAIA,YAAY,KAAKjC,sBAAsB,EAAE;MAC9E;MACA,OAAOoC,aAAa,GAAGA,aAAa,GAAGH,YAAY,GAAG,EAAE;IAC1D;IAEA,OAAOG,aAAa;EACtB,CAAC,CAAC,CAACpB,IAAI,CAAC,EAAE,CAAC;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}