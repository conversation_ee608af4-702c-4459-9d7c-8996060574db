{"ast": null, "code": "import * as React from 'react';\nimport { useControlled } from '@mui/material/utils';\nimport { arrayIncludes } from '../utils/utils';\nexport function useViews(_ref) {\n  let {\n    onChange,\n    onViewChange,\n    openTo,\n    view,\n    views\n  } = _ref;\n  var _views, _views2;\n  const [openView, setOpenView] = useControlled({\n    name: 'Picker',\n    state: 'view',\n    controlled: view,\n    default: openTo && arrayIncludes(views, openTo) ? openTo : views[0]\n  });\n  const previousView = (_views = views[views.indexOf(openView) - 1]) != null ? _views : null;\n  const nextView = (_views2 = views[views.indexOf(openView) + 1]) != null ? _views2 : null;\n  const changeView = React.useCallback(newView => {\n    setOpenView(newView);\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  }, [setOpenView, onViewChange]);\n  const openNext = React.useCallback(() => {\n    if (nextView) {\n      changeView(nextView);\n    }\n  }, [nextView, changeView]);\n  const handleChangeAndOpenNext = React.useCallback((date, currentViewSelectionState) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const globalSelectionState = isSelectionFinishedOnCurrentView && Boolean(nextView) ? 'partial' : currentViewSelectionState;\n    onChange(date, globalSelectionState);\n    if (isSelectionFinishedOnCurrentView) {\n      openNext();\n    }\n  }, [nextView, onChange, openNext]);\n  return {\n    handleChangeAndOpenNext,\n    nextView,\n    previousView,\n    openNext,\n    openView,\n    setOpenView: changeView\n  };\n}", "map": {"version": 3, "names": ["React", "useControlled", "arrayIncludes", "useViews", "_ref", "onChange", "onViewChange", "openTo", "view", "views", "_views", "_views2", "openView", "<PERSON><PERSON><PERSON><PERSON>", "name", "state", "controlled", "default", "previousView", "indexOf", "next<PERSON>iew", "changeView", "useCallback", "newView", "openNext", "handleChangeAndOpenNext", "date", "currentViewSelectionState", "isSelectionFinishedOnCurrentView", "globalSelectionState", "Boolean"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/hooks/useViews.js"], "sourcesContent": ["import * as React from 'react';\nimport { useControlled } from '@mui/material/utils';\nimport { arrayIncludes } from '../utils/utils';\nexport function useViews({\n  onChange,\n  onViewChange,\n  openTo,\n  view,\n  views\n}) {\n  var _views, _views2;\n\n  const [openView, setOpenView] = useControlled({\n    name: 'Picker',\n    state: 'view',\n    controlled: view,\n    default: openTo && arrayIncludes(views, openTo) ? openTo : views[0]\n  });\n  const previousView = (_views = views[views.indexOf(openView) - 1]) != null ? _views : null;\n  const nextView = (_views2 = views[views.indexOf(openView) + 1]) != null ? _views2 : null;\n  const changeView = React.useCallback(newView => {\n    setOpenView(newView);\n\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  }, [setOpenView, onViewChange]);\n  const openNext = React.useCallback(() => {\n    if (nextView) {\n      changeView(nextView);\n    }\n  }, [nextView, changeView]);\n  const handleChangeAndOpenNext = React.useCallback((date, currentViewSelectionState) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const globalSelectionState = isSelectionFinishedOnCurrentView && Boolean(nextView) ? 'partial' : currentViewSelectionState;\n    onChange(date, globalSelectionState);\n\n    if (isSelectionFinishedOnCurrentView) {\n      openNext();\n    }\n  }, [nextView, onChange, openNext]);\n  return {\n    handleChangeAndOpenNext,\n    nextView,\n    previousView,\n    openNext,\n    openView,\n    setOpenView: changeView\n  };\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAO,SAASC,QAAQA,CAAAC,IAAA,EAMrB;EAAA,IANsB;IACvBC,QAAQ;IACRC,YAAY;IACZC,MAAM;IACNC,IAAI;IACJC;EACF,CAAC,GAAAL,IAAA;EACC,IAAIM,MAAM,EAAEC,OAAO;EAEnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,aAAa,CAAC;IAC5Ca,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAER,IAAI;IAChBS,OAAO,EAAEV,MAAM,IAAIL,aAAa,CAACO,KAAK,EAAEF,MAAM,CAAC,GAAGA,MAAM,GAAGE,KAAK,CAAC,CAAC;EACpE,CAAC,CAAC;EACF,MAAMS,YAAY,GAAG,CAACR,MAAM,GAAGD,KAAK,CAACA,KAAK,CAACU,OAAO,CAACP,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,GAAGF,MAAM,GAAG,IAAI;EAC1F,MAAMU,QAAQ,GAAG,CAACT,OAAO,GAAGF,KAAK,CAACA,KAAK,CAACU,OAAO,CAACP,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,GAAGD,OAAO,GAAG,IAAI;EACxF,MAAMU,UAAU,GAAGrB,KAAK,CAACsB,WAAW,CAACC,OAAO,IAAI;IAC9CV,WAAW,CAACU,OAAO,CAAC;IAEpB,IAAIjB,YAAY,EAAE;MAChBA,YAAY,CAACiB,OAAO,CAAC;IACvB;EACF,CAAC,EAAE,CAACV,WAAW,EAAEP,YAAY,CAAC,CAAC;EAC/B,MAAMkB,QAAQ,GAAGxB,KAAK,CAACsB,WAAW,CAAC,MAAM;IACvC,IAAIF,QAAQ,EAAE;MACZC,UAAU,CAACD,QAAQ,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEC,UAAU,CAAC,CAAC;EAC1B,MAAMI,uBAAuB,GAAGzB,KAAK,CAACsB,WAAW,CAAC,CAACI,IAAI,EAAEC,yBAAyB,KAAK;IACrF,MAAMC,gCAAgC,GAAGD,yBAAyB,KAAK,QAAQ;IAC/E,MAAME,oBAAoB,GAAGD,gCAAgC,IAAIE,OAAO,CAACV,QAAQ,CAAC,GAAG,SAAS,GAAGO,yBAAyB;IAC1HtB,QAAQ,CAACqB,IAAI,EAAEG,oBAAoB,CAAC;IAEpC,IAAID,gCAAgC,EAAE;MACpCJ,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACJ,QAAQ,EAAEf,QAAQ,EAAEmB,QAAQ,CAAC,CAAC;EAClC,OAAO;IACLC,uBAAuB;IACvBL,QAAQ;IACRF,YAAY;IACZM,QAAQ;IACRZ,QAAQ;IACRC,WAAW,EAAEQ;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}