{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { ListContext } from '../useList/ListContext';\nimport { CompoundComponentContext } from '../useCompound';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Sets up the contexts for the underlying Option components.\n *\n * @ignore - do not document.\n */\nexport function SelectProvider(props) {\n  const {\n    value,\n    children\n  } = props;\n  const {\n    dispatch,\n    getItemIndex,\n    getItemState,\n    registerItem,\n    totalSubitemCount\n  } = value;\n  const listContextValue = React.useMemo(() => ({\n    dispatch,\n    getItemState,\n    getItemIndex\n  }), [dispatch, getItemIndex, getItemState]);\n  const compoundComponentContextValue = React.useMemo(() => ({\n    getItemIndex,\n    registerItem,\n    totalSubitemCount\n  }), [registerItem, getItemIndex, totalSubitemCount]);\n  return /*#__PURE__*/_jsx(CompoundComponentContext.Provider, {\n    value: compoundComponentContextValue,\n    children: /*#__PURE__*/_jsx(ListContext.Provider, {\n      value: listContextValue,\n      children: children\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "ListContext", "CompoundComponentContext", "jsx", "_jsx", "SelectProvider", "props", "value", "children", "dispatch", "getItemIndex", "getItemState", "registerItem", "totalSubitemCount", "listContextValue", "useMemo", "compoundComponentContextValue", "Provider"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/useSelect/SelectProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ListContext } from '../useList/ListContext';\nimport { CompoundComponentContext } from '../useCompound';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Sets up the contexts for the underlying Option components.\n *\n * @ignore - do not document.\n */\nexport function SelectProvider(props) {\n  const {\n    value,\n    children\n  } = props;\n  const {\n    dispatch,\n    getItemIndex,\n    getItemState,\n    registerItem,\n    totalSubitemCount\n  } = value;\n  const listContextValue = React.useMemo(() => ({\n    dispatch,\n    getItemState,\n    getItemIndex\n  }), [dispatch, getItemIndex, getItemState]);\n  const compoundComponentContextValue = React.useMemo(() => ({\n    getItemIndex,\n    registerItem,\n    totalSubitemCount\n  }), [registerItem, getItemIndex, totalSubitemCount]);\n  return /*#__PURE__*/_jsx(CompoundComponentContext.Provider, {\n    value: compoundComponentContextValue,\n    children: /*#__PURE__*/_jsx(ListContext.Provider, {\n      value: listContextValue,\n      children: children\n    })\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,wBAAwB,QAAQ,gBAAgB;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGF,KAAK;EACT,MAAM;IACJG,QAAQ;IACRC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC;EACF,CAAC,GAAGN,KAAK;EACT,MAAMO,gBAAgB,GAAGd,KAAK,CAACe,OAAO,CAAC,OAAO;IAC5CN,QAAQ;IACRE,YAAY;IACZD;EACF,CAAC,CAAC,EAAE,CAACD,QAAQ,EAAEC,YAAY,EAAEC,YAAY,CAAC,CAAC;EAC3C,MAAMK,6BAA6B,GAAGhB,KAAK,CAACe,OAAO,CAAC,OAAO;IACzDL,YAAY;IACZE,YAAY;IACZC;EACF,CAAC,CAAC,EAAE,CAACD,YAAY,EAAEF,YAAY,EAAEG,iBAAiB,CAAC,CAAC;EACpD,OAAO,aAAaT,IAAI,CAACF,wBAAwB,CAACe,QAAQ,EAAE;IAC1DV,KAAK,EAAES,6BAA6B;IACpCR,QAAQ,EAAE,aAAaJ,IAAI,CAACH,WAAW,CAACgB,QAAQ,EAAE;MAChDV,KAAK,EAAEO,gBAAgB;MACvBN,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}