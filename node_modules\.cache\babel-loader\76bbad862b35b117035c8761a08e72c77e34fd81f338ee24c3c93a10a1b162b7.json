{"ast": null, "code": "export default function buildLocalizeFn(args) {\n  return function (dirtyIndex, options) {\n    var context = options !== null && options !== void 0 && options.context ? String(options.context) : 'standalone';\n    var valuesArray;\n    if (context === 'formatting' && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(dirtyIndex) : dirtyIndex;\n    // @ts-ignore: For some reason TypeScript just don't want to match it, no matter how hard we try. I challenge you to try to remove it!\n    return valuesArray[index];\n  };\n}", "map": {"version": 3, "names": ["buildLocalizeFn", "args", "dirtyIndex", "options", "context", "String", "valuesArray", "formattingValues", "defaultWidth", "defaultFormattingWidth", "width", "_defaultWidth", "_width", "values", "index", "argument<PERSON>allback"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/date-fns/esm/locale/_lib/buildLocalizeFn/index.js"], "sourcesContent": ["export default function buildLocalizeFn(args) {\n  return function (dirtyIndex, options) {\n    var context = options !== null && options !== void 0 && options.context ? String(options.context) : 'standalone';\n    var valuesArray;\n    if (context === 'formatting' && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(dirtyIndex) : dirtyIndex;\n    // @ts-ignore: For some reason TypeScript just don't want to match it, no matter how hard we try. I challenge you to try to remove it!\n    return valuesArray[index];\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,eAAeA,CAACC,IAAI,EAAE;EAC5C,OAAO,UAAUC,UAAU,EAAEC,OAAO,EAAE;IACpC,IAAIC,OAAO,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,OAAO,GAAGC,MAAM,CAACF,OAAO,CAACC,OAAO,CAAC,GAAG,YAAY;IAChH,IAAIE,WAAW;IACf,IAAIF,OAAO,KAAK,YAAY,IAAIH,IAAI,CAACM,gBAAgB,EAAE;MACrD,IAAIC,YAAY,GAAGP,IAAI,CAACQ,sBAAsB,IAAIR,IAAI,CAACO,YAAY;MACnE,IAAIE,KAAK,GAAGP,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACO,KAAK,GAAGL,MAAM,CAACF,OAAO,CAACO,KAAK,CAAC,GAAGF,YAAY;MAC1GF,WAAW,GAAGL,IAAI,CAACM,gBAAgB,CAACG,KAAK,CAAC,IAAIT,IAAI,CAACM,gBAAgB,CAACC,YAAY,CAAC;IACnF,CAAC,MAAM;MACL,IAAIG,aAAa,GAAGV,IAAI,CAACO,YAAY;MACrC,IAAII,MAAM,GAAGT,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACO,KAAK,GAAGL,MAAM,CAACF,OAAO,CAACO,KAAK,CAAC,GAAGT,IAAI,CAACO,YAAY;MAChHF,WAAW,GAAGL,IAAI,CAACY,MAAM,CAACD,MAAM,CAAC,IAAIX,IAAI,CAACY,MAAM,CAACF,aAAa,CAAC;IACjE;IACA,IAAIG,KAAK,GAAGb,IAAI,CAACc,gBAAgB,GAAGd,IAAI,CAACc,gBAAgB,CAACb,UAAU,CAAC,GAAGA,UAAU;IAClF;IACA,OAAOI,WAAW,CAACQ,KAAK,CAAC;EAC3B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}