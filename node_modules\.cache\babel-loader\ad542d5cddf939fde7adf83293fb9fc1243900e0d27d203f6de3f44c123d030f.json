{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation, Outlet } from 'react-router-dom';\nimport { Box, Drawer, AppBar, Toolbar, List, Typography, Divider, IconButton, ListItem, ListItemIcon, ListItemText, ListItemButton, Button, useTheme, useMediaQuery, Tooltip } from '@mui/material';\nimport { Menu as MenuIcon, Dashboard as DashboardIcon, Assignment as ComplaintsIcon, Security as AuthorityIcon, Lock as PasswordIcon, Logout as LogoutIcon, Person as PersonIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst drawerWidth = 240;\nfunction Layout() {\n  _s();\n  var _user$permissions, _menuItems$find;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const [mobileOpen, setMobileOpen] = useState(false);\n  const {\n    user,\n    logout,\n    refreshUserPermissions\n  } = useAuth();\n  console.log('Layout - Current user:', user);\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n  const handleRefreshPermissions = async () => {\n    await refreshUserPermissions();\n    // Force a page refresh to update the menu items\n    window.location.reload();\n  };\n  const menuItems = [...(user !== null && user !== void 0 && (_user$permissions = user.permissions) !== null && _user$permissions !== void 0 && _user$permissions.canViewDashboard ? [{\n    text: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 34\n    }, this),\n    path: '/dashboard'\n  }] : []), {\n    text: 'Complaints',\n    icon: /*#__PURE__*/_jsxDEV(ComplaintsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 33\n    }, this),\n    path: '/complaints'\n  }, ...(user !== null && user !== void 0 && user.isAdmin ? [{\n    text: 'Authority Management',\n    icon: /*#__PURE__*/_jsxDEV(AuthorityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 45\n    }, this),\n    path: '/authority-management'\n  }] : []), {\n    text: 'Change Password',\n    icon: /*#__PURE__*/_jsxDEV(PasswordIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 38\n    }, this),\n    path: '/change-password'\n  }];\n  console.log('Layout - Menu items:', menuItems);\n  const drawer = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Toolbar, {\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        noWrap: true,\n        component: \"div\",\n        sx: {\n          fontWeight: 600\n        },\n        children: \"Internal Complaints\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      sx: {\n        flexGrow: 1\n      },\n      children: menuItems.map(item => /*#__PURE__*/_jsxDEV(ListItem, {\n        disablePadding: true,\n        children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n          selected: location.pathname === item.path,\n          onClick: () => {\n            navigate(item.path);\n            if (isMobile) setMobileOpen(false);\n          },\n          sx: {\n            borderRadius: 1,\n            mx: 1,\n            '&.Mui-selected': {\n              backgroundColor: theme.palette.primary.light,\n              color: theme.palette.primary.main,\n              '&:hover': {\n                backgroundColor: theme.palette.primary.light\n              },\n              '& .MuiListItemIcon-root': {\n                color: theme.palette.primary.main\n              }\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            sx: {\n              minWidth: 40\n            },\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: item.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this)\n      }, item.text, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n          sx: {\n            color: theme.palette.primary.main,\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: (user === null || user === void 0 ? void 0 : user.department) || 'Department'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Click to refresh your permissions if they were recently updated by an admin\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          color: \"secondary\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 24\n          }, this),\n          onClick: handleRefreshPermissions,\n          sx: {\n            textTransform: 'none',\n            borderRadius: 2,\n            mb: 1\n          },\n          children: \"Refresh Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        fullWidth: true,\n        variant: \"outlined\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 22\n        }, this),\n        onClick: handleLogout,\n        sx: {\n          textTransform: 'none',\n          borderRadius: 2\n        },\n        children: \"Logout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      minHeight: '100vh',\n      bgcolor: 'grey.50'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      elevation: 1,\n      sx: {\n        width: {\n          sm: `calc(100% - ${drawerWidth}px)`\n        },\n        ml: {\n          sm: `${drawerWidth}px`\n        },\n        bgcolor: 'background.paper',\n        borderBottom: `1px solid ${theme.palette.divider}`\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            \"aria-label\": \"open drawer\",\n            edge: \"start\",\n            onClick: handleDrawerToggle,\n            sx: {\n              mr: 2,\n              display: {\n                sm: 'none'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            noWrap: true,\n            component: \"div\",\n            sx: {\n              color: theme.palette.text.primary,\n              fontWeight: 500\n            },\n            children: ((_menuItems$find = menuItems.find(item => item.path === location.pathname)) === null || _menuItems$find === void 0 ? void 0 : _menuItems$find.text) || 'Internal Complaints Portal'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: {\n              xs: 'none',\n              sm: 'flex'\n            },\n            alignItems: 'center',\n            gap: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"primary\",\n            size: \"small\",\n            startIcon: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 26\n            }, this),\n            onClick: handleLogout,\n            sx: {\n              textTransform: 'none',\n              borderRadius: 2\n            },\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"nav\",\n      sx: {\n        width: {\n          sm: drawerWidth\n        },\n        flexShrink: {\n          sm: 0\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Drawer, {\n        variant: \"temporary\",\n        open: mobileOpen,\n        onClose: handleDrawerToggle,\n        ModalProps: {\n          keepMounted: true\n        },\n        sx: {\n          display: {\n            xs: 'block',\n            sm: 'none'\n          },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: drawerWidth,\n            borderRight: `1px solid ${theme.palette.divider}`\n          }\n        },\n        children: drawer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n        variant: \"permanent\",\n        sx: {\n          display: {\n            xs: 'none',\n            sm: 'block'\n          },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: drawerWidth,\n            borderRight: `1px solid ${theme.palette.divider}`,\n            bgcolor: 'background.paper'\n          }\n        },\n        children: drawer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3,\n        width: {\n          sm: `calc(100% - ${drawerWidth}px)`\n        },\n        minHeight: '100vh',\n        bgcolor: 'grey.50',\n        mt: {\n          xs: 7,\n          sm: 8\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this);\n}\n_s(Layout, \"lHAdEKIXeKn21kkpFDefoO701Qg=\", false, function () {\n  return [useNavigate, useLocation, useTheme, useMediaQuery, useAuth];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "Outlet", "Box", "Drawer", "AppBar", "<PERSON><PERSON><PERSON>", "List", "Typography", "Divider", "IconButton", "ListItem", "ListItemIcon", "ListItemText", "ListItemButton", "<PERSON><PERSON>", "useTheme", "useMediaQuery", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "MenuIcon", "Dashboard", "DashboardIcon", "Assignment", "ComplaintsIcon", "Security", "AuthorityIcon", "Lock", "PasswordIcon", "Logout", "LogoutIcon", "Person", "PersonIcon", "Refresh", "RefreshIcon", "useAuth", "jsxDEV", "_jsxDEV", "drawerWidth", "Layout", "_s", "_user$permissions", "_menuItems$find", "navigate", "location", "theme", "isMobile", "breakpoints", "down", "mobileOpen", "setMobileOpen", "user", "logout", "refreshUserPermissions", "console", "log", "handleDrawerToggle", "handleLogout", "handleRefreshPermissions", "window", "reload", "menuItems", "permissions", "canViewDashboard", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "isAdmin", "drawer", "sx", "height", "display", "flexDirection", "children", "variant", "noWrap", "component", "fontWeight", "flexGrow", "map", "item", "disablePadding", "selected", "pathname", "onClick", "borderRadius", "mx", "backgroundColor", "palette", "primary", "light", "color", "main", "min<PERSON><PERSON><PERSON>", "p", "alignItems", "mb", "mr", "name", "department", "title", "fullWidth", "startIcon", "textTransform", "minHeight", "bgcolor", "position", "elevation", "width", "sm", "ml", "borderBottom", "divider", "justifyContent", "edge", "find", "xs", "gap", "size", "flexShrink", "open", "onClose", "ModalProps", "keepMounted", "boxSizing", "borderRight", "mt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/components/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation, Outlet } from 'react-router-dom';\nimport {\n  Box,\n  Drawer,\n  AppBar,\n  Toolbar,\n  List,\n  Typography,\n  Divider,\n  IconButton,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  ListItemButton,\n  Button,\n  useTheme,\n  useMediaQuery,\n  Tooltip,\n} from '@mui/material';\nimport {\n  Menu as MenuIcon,\n  Dashboard as DashboardIcon,\n  Assignment as ComplaintsIcon,\n  Security as AuthorityIcon,\n  Lock as PasswordIcon,\n  Logout as LogoutIcon,\n  Person as PersonIcon,\n  Refresh as RefreshIcon,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst drawerWidth = 240;\n\nfunction Layout() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const [mobileOpen, setMobileOpen] = useState(false);\n  const { user, logout, refreshUserPermissions } = useAuth();\n  console.log('Layout - Current user:', user);\n\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  const handleRefreshPermissions = async () => {\n    await refreshUserPermissions();\n    // Force a page refresh to update the menu items\n    window.location.reload();\n  };\n\n  const menuItems = [\n    ...(user?.permissions?.canViewDashboard ? [\n      { text: 'Dashboard', icon: <DashboardIcon />, path: '/dashboard' }\n    ] : []),\n    { text: 'Complaints', icon: <ComplaintsIcon />, path: '/complaints' },\n    ...(user?.isAdmin ? [\n      { text: 'Authority Management', icon: <AuthorityIcon />, path: '/authority-management' }\n    ] : []),\n    { text: 'Change Password', icon: <PasswordIcon />, path: '/change-password' },\n  ];\n\n  console.log('Layout - Menu items:', menuItems);\n\n  const drawer = (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <Toolbar>\n        <Typography variant=\"h6\" noWrap component=\"div\" sx={{ fontWeight: 600 }}>\n          Internal Complaints\n        </Typography>\n      </Toolbar>\n      <Divider />\n      <List sx={{ flexGrow: 1 }}>\n        {menuItems.map((item) => (\n          <ListItem key={item.text} disablePadding>\n            <ListItemButton\n              selected={location.pathname === item.path}\n              onClick={() => {\n                navigate(item.path);\n                if (isMobile) setMobileOpen(false);\n              }}\n              sx={{\n                borderRadius: 1,\n                mx: 1,\n                '&.Mui-selected': {\n                  backgroundColor: theme.palette.primary.light,\n                  color: theme.palette.primary.main,\n                  '&:hover': {\n                    backgroundColor: theme.palette.primary.light,\n                  },\n                  '& .MuiListItemIcon-root': {\n                    color: theme.palette.primary.main,\n                  },\n                },\n              }}\n            >\n              <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>\n              <ListItemText primary={item.text} />\n            </ListItemButton>\n          </ListItem>\n        ))}\n      </List>\n      <Divider />\n      <Box sx={{ p: 2 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n          <PersonIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />\n          <Box>\n            <Typography variant=\"subtitle2\" sx={{ fontWeight: 500 }}>\n              {user?.name || 'User'}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {user?.department || 'Department'}\n            </Typography>\n          </Box>\n        </Box>\n        <Tooltip title=\"Click to refresh your permissions if they were recently updated by an admin\">\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            color=\"secondary\"\n            startIcon={<RefreshIcon />}\n            onClick={handleRefreshPermissions}\n            sx={{\n              textTransform: 'none',\n              borderRadius: 2,\n              mb: 1\n            }}\n          >\n            Refresh Permissions\n          </Button>\n        </Tooltip>\n        <Button\n          fullWidth\n          variant=\"outlined\"\n          color=\"primary\"\n          startIcon={<LogoutIcon />}\n          onClick={handleLogout}\n          sx={{\n            textTransform: 'none',\n            borderRadius: 2\n          }}\n        >\n          Logout\n        </Button>\n      </Box>\n    </Box>\n  );\n\n  return (\n    <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: 'grey.50' }}>\n      <AppBar\n        position=\"fixed\"\n        elevation={1}\n        sx={{\n          width: { sm: `calc(100% - ${drawerWidth}px)` },\n          ml: { sm: `${drawerWidth}px` },\n          bgcolor: 'background.paper',\n          borderBottom: `1px solid ${theme.palette.divider}`,\n        }}\n      >\n        <Toolbar sx={{ display: 'flex', justifyContent: 'space-between' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <IconButton\n              color=\"inherit\"\n              aria-label=\"open drawer\"\n              edge=\"start\"\n              onClick={handleDrawerToggle}\n              sx={{ mr: 2, display: { sm: 'none' } }}\n            >\n              <MenuIcon />\n            </IconButton>\n            <Typography \n              variant=\"h6\" \n              noWrap \n              component=\"div\"\n              sx={{ \n                color: theme.palette.text.primary,\n                fontWeight: 500\n              }}\n            >\n              {menuItems.find(item => item.path === location.pathname)?.text || 'Internal Complaints Portal'}\n            </Typography>\n          </Box>\n\n          <Box sx={{ display: { xs: 'none', sm: 'flex' }, alignItems: 'center', gap: 2 }}>\n            <Button\n              variant=\"outlined\"\n              color=\"primary\"\n              size=\"small\"\n              startIcon={<LogoutIcon />}\n              onClick={handleLogout}\n              sx={{ \n                textTransform: 'none',\n                borderRadius: 2\n              }}\n            >\n              Logout\n            </Button>\n          </Box>\n        </Toolbar>\n      </AppBar>\n\n      <Box\n        component=\"nav\"\n        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}\n      >\n        <Drawer\n          variant=\"temporary\"\n          open={mobileOpen}\n          onClose={handleDrawerToggle}\n          ModalProps={{\n            keepMounted: true,\n          }}\n          sx={{\n            display: { xs: 'block', sm: 'none' },\n            '& .MuiDrawer-paper': { \n              boxSizing: 'border-box', \n              width: drawerWidth,\n              borderRight: `1px solid ${theme.palette.divider}`,\n            },\n          }}\n        >\n          {drawer}\n        </Drawer>\n        <Drawer\n          variant=\"permanent\"\n          sx={{\n            display: { xs: 'none', sm: 'block' },\n            '& .MuiDrawer-paper': { \n              boxSizing: 'border-box', \n              width: drawerWidth,\n              borderRight: `1px solid ${theme.palette.divider}`,\n              bgcolor: 'background.paper',\n            },\n          }}\n        >\n          {drawer}\n        </Drawer>\n      </Box>\n\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: { sm: `calc(100% - ${drawerWidth}px)` },\n          minHeight: '100vh',\n          bgcolor: 'grey.50',\n          mt: { xs: 7, sm: 8 },\n        }}\n      >\n        <Outlet />\n      </Box>\n    </Box>\n  );\n}\n\nexport default Layout; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,EAAEC,MAAM,QAAQ,kBAAkB;AACnE,SACEC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,QAAQ,EACRC,aAAa,EACbC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,aAAa,EACzBC,IAAI,IAAIC,YAAY,EACpBC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,GAAG,GAAG;AAEvB,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,eAAA;EAChB,MAAMC,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,KAAK,GAAG7B,QAAQ,CAAC,CAAC;EACxB,MAAM8B,QAAQ,GAAG7B,aAAa,CAAC4B,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM;IAAEoD,IAAI;IAAEC,MAAM;IAAEC;EAAuB,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC1DmB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEJ,IAAI,CAAC;EAE3C,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/BN,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzBL,MAAM,CAAC,CAAC;IACRT,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMe,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,MAAML,sBAAsB,CAAC,CAAC;IAC9B;IACAM,MAAM,CAACf,QAAQ,CAACgB,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,SAAS,GAAG,CAChB,IAAIV,IAAI,aAAJA,IAAI,gBAAAV,iBAAA,GAAJU,IAAI,CAAEW,WAAW,cAAArB,iBAAA,eAAjBA,iBAAA,CAAmBsB,gBAAgB,GAAG,CACxC;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAE5B,OAAA,CAACf,aAAa;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAa,CAAC,CACnE,GAAG,EAAE,CAAC,EACP;IAAEN,IAAI,EAAE,YAAY;IAAEC,IAAI,eAAE5B,OAAA,CAACb,cAAc;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAc,CAAC,EACrE,IAAInB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoB,OAAO,GAAG,CAClB;IAAEP,IAAI,EAAE,sBAAsB;IAAEC,IAAI,eAAE5B,OAAA,CAACX,aAAa;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAwB,CAAC,CACzF,GAAG,EAAE,CAAC,EACP;IAAEN,IAAI,EAAE,iBAAiB;IAAEC,IAAI,eAAE5B,OAAA,CAACT,YAAY;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAmB,CAAC,CAC9E;EAEDhB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEM,SAAS,CAAC;EAE9C,MAAMW,MAAM,gBACVnC,OAAA,CAAClC,GAAG;IAACsE,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACpExC,OAAA,CAAC/B,OAAO;MAAAuE,QAAA,eACNxC,OAAA,CAAC7B,UAAU;QAACsE,OAAO,EAAC,IAAI;QAACC,MAAM;QAACC,SAAS,EAAC,KAAK;QAACP,EAAE,EAAE;UAAEQ,UAAU,EAAE;QAAI,CAAE;QAAAJ,QAAA,EAAC;MAEzE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACVhC,OAAA,CAAC5B,OAAO;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXhC,OAAA,CAAC9B,IAAI;MAACkE,EAAE,EAAE;QAAES,QAAQ,EAAE;MAAE,CAAE;MAAAL,QAAA,EACvBhB,SAAS,CAACsB,GAAG,CAAEC,IAAI,iBAClB/C,OAAA,CAAC1B,QAAQ;QAAiB0E,cAAc;QAAAR,QAAA,eACtCxC,OAAA,CAACvB,cAAc;UACbwE,QAAQ,EAAE1C,QAAQ,CAAC2C,QAAQ,KAAKH,IAAI,CAACd,IAAK;UAC1CkB,OAAO,EAAEA,CAAA,KAAM;YACb7C,QAAQ,CAACyC,IAAI,CAACd,IAAI,CAAC;YACnB,IAAIxB,QAAQ,EAAEI,aAAa,CAAC,KAAK,CAAC;UACpC,CAAE;UACFuB,EAAE,EAAE;YACFgB,YAAY,EAAE,CAAC;YACfC,EAAE,EAAE,CAAC;YACL,gBAAgB,EAAE;cAChBC,eAAe,EAAE9C,KAAK,CAAC+C,OAAO,CAACC,OAAO,CAACC,KAAK;cAC5CC,KAAK,EAAElD,KAAK,CAAC+C,OAAO,CAACC,OAAO,CAACG,IAAI;cACjC,SAAS,EAAE;gBACTL,eAAe,EAAE9C,KAAK,CAAC+C,OAAO,CAACC,OAAO,CAACC;cACzC,CAAC;cACD,yBAAyB,EAAE;gBACzBC,KAAK,EAAElD,KAAK,CAAC+C,OAAO,CAACC,OAAO,CAACG;cAC/B;YACF;UACF,CAAE;UAAAnB,QAAA,gBAEFxC,OAAA,CAACzB,YAAY;YAAC6D,EAAE,EAAE;cAAEwB,QAAQ,EAAE;YAAG,CAAE;YAAApB,QAAA,EAAEO,IAAI,CAACnB;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAC9DhC,OAAA,CAACxB,YAAY;YAACgF,OAAO,EAAET,IAAI,CAACpB;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC,GAxBJe,IAAI,CAACpB,IAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyBd,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACPhC,OAAA,CAAC5B,OAAO;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXhC,OAAA,CAAClC,GAAG;MAACsE,EAAE,EAAE;QAAEyB,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,gBAChBxC,OAAA,CAAClC,GAAG;QAACsE,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEwB,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBACxDxC,OAAA,CAACL,UAAU;UAACyC,EAAE,EAAE;YAAEsB,KAAK,EAAElD,KAAK,CAAC+C,OAAO,CAACC,OAAO,CAACG,IAAI;YAAEK,EAAE,EAAE;UAAE;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEhC,OAAA,CAAClC,GAAG;UAAA0E,QAAA,gBACFxC,OAAA,CAAC7B,UAAU;YAACsE,OAAO,EAAC,WAAW;YAACL,EAAE,EAAE;cAAEQ,UAAU,EAAE;YAAI,CAAE;YAAAJ,QAAA,EACrD,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,IAAI,KAAI;UAAM;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACbhC,OAAA,CAAC7B,UAAU;YAACsE,OAAO,EAAC,SAAS;YAACiB,KAAK,EAAC,gBAAgB;YAAAlB,QAAA,EACjD,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoD,UAAU,KAAI;UAAY;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNhC,OAAA,CAACnB,OAAO;QAACsF,KAAK,EAAC,6EAA6E;QAAA3B,QAAA,eAC1FxC,OAAA,CAACtB,MAAM;UACL0F,SAAS;UACT3B,OAAO,EAAC,UAAU;UAClBiB,KAAK,EAAC,WAAW;UACjBW,SAAS,eAAErE,OAAA,CAACH,WAAW;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BmB,OAAO,EAAE9B,wBAAyB;UAClCe,EAAE,EAAE;YACFkC,aAAa,EAAE,MAAM;YACrBlB,YAAY,EAAE,CAAC;YACfW,EAAE,EAAE;UACN,CAAE;UAAAvB,QAAA,EACH;QAED;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACVhC,OAAA,CAACtB,MAAM;QACL0F,SAAS;QACT3B,OAAO,EAAC,UAAU;QAClBiB,KAAK,EAAC,SAAS;QACfW,SAAS,eAAErE,OAAA,CAACP,UAAU;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1BmB,OAAO,EAAE/B,YAAa;QACtBgB,EAAE,EAAE;UACFkC,aAAa,EAAE,MAAM;UACrBlB,YAAY,EAAE;QAChB,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEhC,OAAA,CAAClC,GAAG;IAACsE,EAAE,EAAE;MAAEE,OAAO,EAAE,MAAM;MAAEiC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAhC,QAAA,gBACnExC,OAAA,CAAChC,MAAM;MACLyG,QAAQ,EAAC,OAAO;MAChBC,SAAS,EAAE,CAAE;MACbtC,EAAE,EAAE;QACFuC,KAAK,EAAE;UAAEC,EAAE,EAAE,eAAe3E,WAAW;QAAM,CAAC;QAC9C4E,EAAE,EAAE;UAAED,EAAE,EAAE,GAAG3E,WAAW;QAAK,CAAC;QAC9BuE,OAAO,EAAE,kBAAkB;QAC3BM,YAAY,EAAE,aAAatE,KAAK,CAAC+C,OAAO,CAACwB,OAAO;MAClD,CAAE;MAAAvC,QAAA,eAEFxC,OAAA,CAAC/B,OAAO;QAACmE,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAE0C,cAAc,EAAE;QAAgB,CAAE;QAAAxC,QAAA,gBAChExC,OAAA,CAAClC,GAAG;UAACsE,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEwB,UAAU,EAAE;UAAS,CAAE;UAAAtB,QAAA,gBACjDxC,OAAA,CAAC3B,UAAU;YACTqF,KAAK,EAAC,SAAS;YACf,cAAW,aAAa;YACxBuB,IAAI,EAAC,OAAO;YACZ9B,OAAO,EAAEhC,kBAAmB;YAC5BiB,EAAE,EAAE;cAAE4B,EAAE,EAAE,CAAC;cAAE1B,OAAO,EAAE;gBAAEsC,EAAE,EAAE;cAAO;YAAE,CAAE;YAAApC,QAAA,eAEvCxC,OAAA,CAACjB,QAAQ;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACbhC,OAAA,CAAC7B,UAAU;YACTsE,OAAO,EAAC,IAAI;YACZC,MAAM;YACNC,SAAS,EAAC,KAAK;YACfP,EAAE,EAAE;cACFsB,KAAK,EAAElD,KAAK,CAAC+C,OAAO,CAAC5B,IAAI,CAAC6B,OAAO;cACjCZ,UAAU,EAAE;YACd,CAAE;YAAAJ,QAAA,EAED,EAAAnC,eAAA,GAAAmB,SAAS,CAAC0D,IAAI,CAACnC,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK1B,QAAQ,CAAC2C,QAAQ,CAAC,cAAA7C,eAAA,uBAAvDA,eAAA,CAAyDsB,IAAI,KAAI;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENhC,OAAA,CAAClC,GAAG;UAACsE,EAAE,EAAE;YAAEE,OAAO,EAAE;cAAE6C,EAAE,EAAE,MAAM;cAAEP,EAAE,EAAE;YAAO,CAAC;YAAEd,UAAU,EAAE,QAAQ;YAAEsB,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,eAC7ExC,OAAA,CAACtB,MAAM;YACL+D,OAAO,EAAC,UAAU;YAClBiB,KAAK,EAAC,SAAS;YACf2B,IAAI,EAAC,OAAO;YACZhB,SAAS,eAAErE,OAAA,CAACP,UAAU;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BmB,OAAO,EAAE/B,YAAa;YACtBgB,EAAE,EAAE;cACFkC,aAAa,EAAE,MAAM;cACrBlB,YAAY,EAAE;YAChB,CAAE;YAAAZ,QAAA,EACH;UAED;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEThC,OAAA,CAAClC,GAAG;MACF6E,SAAS,EAAC,KAAK;MACfP,EAAE,EAAE;QAAEuC,KAAK,EAAE;UAAEC,EAAE,EAAE3E;QAAY,CAAC;QAAEqF,UAAU,EAAE;UAAEV,EAAE,EAAE;QAAE;MAAE,CAAE;MAAApC,QAAA,gBAE1DxC,OAAA,CAACjC,MAAM;QACL0E,OAAO,EAAC,WAAW;QACnB8C,IAAI,EAAE3E,UAAW;QACjB4E,OAAO,EAAErE,kBAAmB;QAC5BsE,UAAU,EAAE;UACVC,WAAW,EAAE;QACf,CAAE;QACFtD,EAAE,EAAE;UACFE,OAAO,EAAE;YAAE6C,EAAE,EAAE,OAAO;YAAEP,EAAE,EAAE;UAAO,CAAC;UACpC,oBAAoB,EAAE;YACpBe,SAAS,EAAE,YAAY;YACvBhB,KAAK,EAAE1E,WAAW;YAClB2F,WAAW,EAAE,aAAapF,KAAK,CAAC+C,OAAO,CAACwB,OAAO;UACjD;QACF,CAAE;QAAAvC,QAAA,EAEDL;MAAM;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACThC,OAAA,CAACjC,MAAM;QACL0E,OAAO,EAAC,WAAW;QACnBL,EAAE,EAAE;UACFE,OAAO,EAAE;YAAE6C,EAAE,EAAE,MAAM;YAAEP,EAAE,EAAE;UAAQ,CAAC;UACpC,oBAAoB,EAAE;YACpBe,SAAS,EAAE,YAAY;YACvBhB,KAAK,EAAE1E,WAAW;YAClB2F,WAAW,EAAE,aAAapF,KAAK,CAAC+C,OAAO,CAACwB,OAAO,EAAE;YACjDP,OAAO,EAAE;UACX;QACF,CAAE;QAAAhC,QAAA,EAEDL;MAAM;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENhC,OAAA,CAAClC,GAAG;MACF6E,SAAS,EAAC,MAAM;MAChBP,EAAE,EAAE;QACFS,QAAQ,EAAE,CAAC;QACXgB,CAAC,EAAE,CAAC;QACJc,KAAK,EAAE;UAAEC,EAAE,EAAE,eAAe3E,WAAW;QAAM,CAAC;QAC9CsE,SAAS,EAAE,OAAO;QAClBC,OAAO,EAAE,SAAS;QAClBqB,EAAE,EAAE;UAAEV,EAAE,EAAE,CAAC;UAAEP,EAAE,EAAE;QAAE;MACrB,CAAE;MAAApC,QAAA,eAEFxC,OAAA,CAACnC,MAAM;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC7B,EAAA,CApOQD,MAAM;EAAA,QACIvC,WAAW,EACXC,WAAW,EACde,QAAQ,EACLC,aAAa,EAEmBkB,OAAO;AAAA;AAAAgG,EAAA,GANjD5F,MAAM;AAsOf,eAAeA,MAAM;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}