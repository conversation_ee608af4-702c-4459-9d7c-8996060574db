import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { useAuth } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import theme from './theme';

// Pages
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import ComplaintsList from './pages/ComplaintsList';
import ComplaintDetails from './pages/ComplaintDetails';
import AuthorityManagement from './pages/AuthorityManagement';
import ChangePassword from './pages/ChangePassword';
import Layout from './components/Layout';
import NewComplaint from './pages/NewComplaint';

function App() {
  const { user, loading, initialized } = useAuth();

  // Show nothing while auth is initializing
  if (!initialized) {
    return null;
  }

  // Show loading state
  if (loading) {
    return null; // Or a loading spinner
  }

  // Check if user should see dashboard
  const canViewDashboard = user?.isAdmin || (user?.permissions?.canViewDashboard === true);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <SocketProvider>
          <Routes>
          {/* Public route - Login */}
          <Route
            path="/login"
            element={<Login />}
          />

          {/* Protected routes - Must be authenticated */}
          {user ? (
            <Route path="/" element={<Layout />}>
              <Route 
                index 
                element={
                  canViewDashboard ?
                    <Navigate to="/dashboard" replace /> : 
                    <Navigate to="/complaints" replace />
                } 
              />
              <Route 
                path="dashboard" 
                element={
                  canViewDashboard ?
                    <Dashboard /> : 
                    <Navigate to="/complaints" replace />
                } 
              />
              <Route path="complaints" element={<ComplaintsList />} />
              <Route path="complaints/new" element={<NewComplaint />} />
              <Route path="complaints/:id" element={<ComplaintDetails />} />
              <Route 
                path="authority-management" 
                element={
                  user.isAdmin ? 
                    <AuthorityManagement /> : 
                    <Navigate to="/complaints" replace />
                } 
              />
              <Route path="change-password" element={<ChangePassword />} />
            </Route>
          ) : (
            // If not authenticated, redirect to login
            <Route path="*" element={<Navigate to="/login" replace />} />
          )}

          {/* Catch all route - redirect to login if not authenticated, otherwise to dashboard/complaints */}
          <Route 
            path="*" 
            element={
              <Navigate 
                to={
                  user ? 
                    (canViewDashboard ? "/dashboard" : "/complaints") :
                    "/login"
                } 
                replace 
              />
            } 
          />
          </Routes>
        </SocketProvider>
      </LocalizationProvider>
    </ThemeProvider>
  );
}

export default App; 