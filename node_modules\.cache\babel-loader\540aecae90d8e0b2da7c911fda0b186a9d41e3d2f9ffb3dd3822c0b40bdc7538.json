{"ast": null, "code": "export const getMeridiem = (date, utils) => {\n  if (!date) {\n    return null;\n  }\n  return utils.getHours(date) >= 12 ? 'pm' : 'am';\n};\nexport const convertValueToMeridiem = (value, meridiem, ampm) => {\n  if (ampm) {\n    const currentMeridiem = value >= 12 ? 'pm' : 'am';\n    if (currentMeridiem !== meridiem) {\n      return meridiem === 'am' ? value - 12 : value + 12;\n    }\n  }\n  return value;\n};\nexport const convertToMeridiem = (time, meridiem, ampm, utils) => {\n  const newHoursAmount = convertValueToMeridiem(utils.getHours(time), meridiem, ampm);\n  return utils.setHours(time, newHoursAmount);\n};\nexport const getSecondsInDay = (date, utils) => {\n  return utils.getHours(date) * 3600 + utils.getMinutes(date) * 60 + utils.getSeconds(date);\n};\nexport const createIsAfterIgnoreDatePart = function () {\n  let disableIgnoringDatePartForTimeValidation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  let utils = arguments.length > 1 ? arguments[1] : undefined;\n  return (dateLeft, dateRight) => {\n    if (disableIgnoringDatePartForTimeValidation) {\n      return utils.isAfter(dateLeft, dateRight);\n    }\n    return getSecondsInDay(dateLeft, utils) > getSecondsInDay(dateRight, utils);\n  };\n};", "map": {"version": 3, "names": ["getMeridiem", "date", "utils", "getHours", "convertValueToMeridiem", "value", "meridiem", "ampm", "currentMeridiem", "convertToMeridiem", "time", "newHoursAmount", "setHours", "getSecondsInDay", "getMinutes", "getSeconds", "createIsAfterIgnoreDatePart", "disableIgnoringDatePartForTimeValidation", "arguments", "length", "undefined", "dateLeft", "dateRight", "isAfter"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/x-date-pickers/internals/utils/time-utils.js"], "sourcesContent": ["export const getMeridiem = (date, utils) => {\n  if (!date) {\n    return null;\n  }\n\n  return utils.getHours(date) >= 12 ? 'pm' : 'am';\n};\nexport const convertValueToMeridiem = (value, meridiem, ampm) => {\n  if (ampm) {\n    const currentMeridiem = value >= 12 ? 'pm' : 'am';\n\n    if (currentMeridiem !== meridiem) {\n      return meridiem === 'am' ? value - 12 : value + 12;\n    }\n  }\n\n  return value;\n};\nexport const convertToMeridiem = (time, meridiem, ampm, utils) => {\n  const newHoursAmount = convertValueToMeridiem(utils.getHours(time), meridiem, ampm);\n  return utils.setHours(time, newHoursAmount);\n};\nexport const getSecondsInDay = (date, utils) => {\n  return utils.getHours(date) * 3600 + utils.getMinutes(date) * 60 + utils.getSeconds(date);\n};\nexport const createIsAfterIgnoreDatePart = (disableIgnoringDatePartForTimeValidation = false, utils) => (dateLeft, dateRight) => {\n  if (disableIgnoringDatePartForTimeValidation) {\n    return utils.isAfter(dateLeft, dateRight);\n  }\n\n  return getSecondsInDay(dateLeft, utils) > getSecondsInDay(dateRight, utils);\n};"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;EAC1C,IAAI,CAACD,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EAEA,OAAOC,KAAK,CAACC,QAAQ,CAACF,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;AACjD,CAAC;AACD,OAAO,MAAMG,sBAAsB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,KAAK;EAC/D,IAAIA,IAAI,EAAE;IACR,MAAMC,eAAe,GAAGH,KAAK,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;IAEjD,IAAIG,eAAe,KAAKF,QAAQ,EAAE;MAChC,OAAOA,QAAQ,KAAK,IAAI,GAAGD,KAAK,GAAG,EAAE,GAAGA,KAAK,GAAG,EAAE;IACpD;EACF;EAEA,OAAOA,KAAK;AACd,CAAC;AACD,OAAO,MAAMI,iBAAiB,GAAGA,CAACC,IAAI,EAAEJ,QAAQ,EAAEC,IAAI,EAAEL,KAAK,KAAK;EAChE,MAAMS,cAAc,GAAGP,sBAAsB,CAACF,KAAK,CAACC,QAAQ,CAACO,IAAI,CAAC,EAAEJ,QAAQ,EAAEC,IAAI,CAAC;EACnF,OAAOL,KAAK,CAACU,QAAQ,CAACF,IAAI,EAAEC,cAAc,CAAC;AAC7C,CAAC;AACD,OAAO,MAAME,eAAe,GAAGA,CAACZ,IAAI,EAAEC,KAAK,KAAK;EAC9C,OAAOA,KAAK,CAACC,QAAQ,CAACF,IAAI,CAAC,GAAG,IAAI,GAAGC,KAAK,CAACY,UAAU,CAACb,IAAI,CAAC,GAAG,EAAE,GAAGC,KAAK,CAACa,UAAU,CAACd,IAAI,CAAC;AAC3F,CAAC;AACD,OAAO,MAAMe,2BAA2B,GAAG,SAAAA,CAAA;EAAA,IAACC,wCAAwC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IAAEhB,KAAK,GAAAgB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,OAAK,CAACC,QAAQ,EAAEC,SAAS,KAAK;IAC/H,IAAIL,wCAAwC,EAAE;MAC5C,OAAOf,KAAK,CAACqB,OAAO,CAACF,QAAQ,EAAEC,SAAS,CAAC;IAC3C;IAEA,OAAOT,eAAe,CAACQ,QAAQ,EAAEnB,KAAK,CAAC,GAAGW,eAAe,CAACS,SAAS,EAAEpB,KAAK,CAAC;EAC7E,CAAC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}