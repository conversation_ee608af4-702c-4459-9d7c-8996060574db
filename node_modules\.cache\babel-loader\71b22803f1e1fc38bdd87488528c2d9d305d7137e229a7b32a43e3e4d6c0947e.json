{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\INTERNAL COMPLAINTS\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, CircularProgress, Alert, useTheme, List, ListItem, ListItemText, ListItemIcon, Divider, Paper, Chip, LinearProgress, useMediaQuery, Button } from '@mui/material';\nimport { Assignment as ComplaintsIcon, CheckCircle as ResolvedIcon, Pending as PendingIcon, Error as HighPriorityIcon, FiberManualRecord as StatusIcon, Schedule as TimeIcon, Speed as EfficiencyIcon, Timeline as TrendIcon, AccessTime as ResolutionIcon } from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport axios from '../utils/axiosConfig';\nimport { format, formatDistanceToNow } from 'date-fns';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatCard = ({\n  title,\n  value,\n  icon,\n  color,\n  loading\n}) => {\n  _s();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      type: \"spring\",\n      stiffness: 100,\n      damping: 15,\n      duration: 0.6\n    },\n    whileHover: {\n      scale: 1.02,\n      transition: {\n        duration: 0.2\n      }\n    },\n    whileTap: {\n      scale: 0.98\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        height: '100%',\n        background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\n        color: 'white',\n        position: 'relative',\n        overflow: 'hidden',\n        boxShadow: theme.shadows[loading ? 0 : 2],\n        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n        '&:hover': {\n          boxShadow: theme.shadows[4],\n          transform: 'translateY(-4px)'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: isMobile ? 2 : 3,\n          height: '100%',\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'relative',\n            zIndex: 1,\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              delay: 0.2,\n              type: \"spring\",\n              stiffness: 120\n            },\n            children: /*#__PURE__*/React.cloneElement(icon, {\n              sx: {\n                fontSize: isMobile ? 32 : 48,\n                opacity: 0.9,\n                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"h5\" : \"h4\",\n              component: \"div\",\n              sx: {\n                fontWeight: 700,\n                lineHeight: 1.2,\n                mb: 0.5,\n                textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\n              },\n              children: loading ? /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0\n                },\n                animate: {\n                  opacity: 1\n                },\n                transition: {\n                  duration: 0.5\n                },\n                children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: isMobile ? 20 : 24,\n                  color: \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 10\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3,\n                  duration: 0.5\n                },\n                children: value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: isMobile ? \"body2\" : \"body1\",\n              sx: {\n                opacity: 0.9,\n                fontWeight: 500,\n                letterSpacing: '0.5px',\n                textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\n              },\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            scale: 0.5\n          },\n          animate: {\n            opacity: 0.15,\n            scale: 2\n          },\n          transition: {\n            delay: 0.4,\n            duration: 0.8\n          },\n          sx: {\n            position: 'absolute',\n            right: -20,\n            bottom: -20,\n            filter: 'blur(2px)'\n          },\n          children: /*#__PURE__*/React.cloneElement(icon, {\n            sx: {\n              fontSize: isMobile ? 100 : 140\n            }\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_s(StatCard, \"25T5RqnpHPZx1hYuwXS/vSFcc1w=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = StatCard;\nconst ActivityItem = /*#__PURE__*/_s2(/*#__PURE__*/React.memo(_c2 = _s2(({\n  activity,\n  index\n}) => {\n  _s2();\n  const theme = useTheme();\n  const getStatusColor = status => {\n    const statusColors = {\n      'New': theme.palette.info.main,\n      'Assigned': theme.palette.warning.main,\n      'In Progress': theme.palette.warning.dark,\n      'Resolved': theme.palette.success.main,\n      'Rejected': theme.palette.error.main\n    };\n    return statusColors[status] || theme.palette.grey[500];\n  };\n  const getPriorityColor = priority => {\n    const priorityColors = {\n      'Low': theme.palette.success.main,\n      'Medium': theme.palette.warning.main,\n      'High': theme.palette.error.main,\n      'Critical': theme.palette.error.dark\n    };\n    return priorityColors[priority] || theme.palette.grey[500];\n  };\n\n  // Use activityTimestamp if available, otherwise fall back to submissionDate\n  const displayTimestamp = activity.activityTimestamp || activity.submissionDate;\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      x: -20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    transition: {\n      delay: index * 0.1,\n      duration: 0.5,\n      ease: \"easeOut\"\n    },\n    children: /*#__PURE__*/_jsxDEV(ListItem, {\n      sx: {\n        bgcolor: 'background.paper',\n        borderRadius: 2,\n        mb: 1,\n        boxShadow: 1,\n        '&:hover': {\n          bgcolor: 'action.hover',\n          transform: 'translateX(4px)',\n          transition: 'all 0.2s ease'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(StatusIcon, {\n          sx: {\n            color: getStatusColor(activity.Status)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            mb: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 500,\n              flex: 1\n            },\n            children: [\"#\", activity.ComplaintNumber, \" - \", activity.description]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this), activity.Priority && /*#__PURE__*/_jsxDEV(Chip, {\n            label: activity.Priority,\n            size: \"small\",\n            sx: {\n              bgcolor: `${getPriorityColor(activity.Priority)}15`,\n              color: getPriorityColor(activity.Priority),\n              fontWeight: 500,\n              fontSize: '0.7rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this),\n        secondary: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 0.5\n          },\n          children: [activity.activityDetails && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 0.5\n            },\n            children: activity.activityDetails\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              label: activity.Status,\n              size: \"small\",\n              sx: {\n                bgcolor: `${getStatusColor(activity.Status)}15`,\n                color: getStatusColor(activity.Status),\n                fontWeight: 500\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), activity.Category && /*#__PURE__*/_jsxDEV(Chip, {\n              label: activity.Category,\n              size: \"small\",\n              variant: \"outlined\",\n              sx: {\n                fontSize: '0.7rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: formatDistanceToNow(new Date(displayTimestamp), {\n                addSuffix: true\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n}, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n})), \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c3 = ActivityItem;\nfunction Dashboard() {\n  _s3();\n  const [stats, setStats] = useState(null);\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n      const [statsResponse, activitiesResponse] = await Promise.all([axios.get('/api/dashboard/stats'), axios.get('/api/dashboard/recent-activities')]);\n      setStats(statsResponse.data);\n      setActivities(activitiesResponse.data);\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching dashboard data:', err);\n      setError('Failed to load dashboard data. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n  const statCards = useMemo(() => [{\n    title: 'Total Complaints',\n    value: (stats === null || stats === void 0 ? void 0 : stats.totalComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(ComplaintsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 13\n    }, this),\n    color: 'primary'\n  }, {\n    title: 'Resolved',\n    value: (stats === null || stats === void 0 ? void 0 : stats.resolvedComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(ResolvedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 13\n    }, this),\n    color: 'success'\n  }, {\n    title: 'Pending',\n    value: (stats === null || stats === void 0 ? void 0 : stats.pendingComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(PendingIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 13\n    }, this),\n    color: 'warning'\n  }, {\n    title: 'High Priority',\n    value: (stats === null || stats === void 0 ? void 0 : stats.highPriorityComplaints) || 0,\n    icon: /*#__PURE__*/_jsxDEV(HighPriorityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 13\n    }, this),\n    color: 'error'\n  }], [stats]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: {\n        xs: 2,\n        sm: 3\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      sx: {\n        mb: 4,\n        fontWeight: 600\n      },\n      children: \"Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      action: /*#__PURE__*/_jsxDEV(motion.div, {\n        whileHover: {\n          scale: 1.05\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          size: \"small\",\n          onClick: fetchDashboardData,\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 13\n      }, this),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [statCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          ...card,\n          loading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this)\n      }, card.title, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          component: motion.div,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.4\n          },\n          sx: {\n            overflow: 'visible',\n            height: '100%',\n            minHeight: 400\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600\n              },\n              children: \"Recent Activities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                p: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this) : activities.length > 0 ? /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: /*#__PURE__*/_jsxDEV(List, {\n                children: activities.map((activity, index) => /*#__PURE__*/_jsxDEV(ActivityItem, {\n                  activity: activity,\n                  index: index\n                }, activity.ComplaintId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                py: 4,\n                color: 'text.secondary'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                children: \"No recent activities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 331,\n    columnNumber: 5\n  }, this);\n}\n_s3(Dashboard, \"zh/e4781oolyfEHoG3Maj1lrLGw=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c4 = Dashboard;\nexport default Dashboard;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"StatCard\");\n$RefreshReg$(_c2, \"ActivityItem$React.memo\");\n$RefreshReg$(_c3, \"ActivityItem\");\n$RefreshReg$(_c4, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "CircularProgress", "<PERSON><PERSON>", "useTheme", "List", "ListItem", "ListItemText", "ListItemIcon", "Divider", "Paper", "Chip", "LinearProgress", "useMediaQuery", "<PERSON><PERSON>", "Assignment", "ComplaintsIcon", "CheckCircle", "ResolvedIcon", "Pending", "PendingIcon", "Error", "HighPriorityIcon", "FiberManualRecord", "StatusIcon", "Schedule", "TimeIcon", "Speed", "EfficiencyIcon", "Timeline", "TrendIcon", "AccessTime", "ResolutionIcon", "motion", "AnimatePresence", "axios", "format", "formatDistanceToNow", "jsxDEV", "_jsxDEV", "StatCard", "title", "value", "icon", "color", "loading", "_s", "theme", "isMobile", "breakpoints", "down", "div", "initial", "opacity", "y", "animate", "transition", "type", "stiffness", "damping", "duration", "whileHover", "scale", "whileTap", "children", "sx", "height", "background", "palette", "main", "dark", "position", "overflow", "boxShadow", "shadows", "transform", "p", "display", "flexDirection", "justifyContent", "zIndex", "alignItems", "gap", "delay", "cloneElement", "fontSize", "filter", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "fontWeight", "lineHeight", "mb", "textShadow", "size", "letterSpacing", "right", "bottom", "_c", "ActivityItem", "_s2", "memo", "_c2", "activity", "index", "getStatusColor", "status", "statusColors", "info", "warning", "success", "error", "grey", "getPriorityColor", "priority", "priorityColors", "displayTimestamp", "activityTimestamp", "submissionDate", "x", "ease", "bgcolor", "borderRadius", "Status", "primary", "flex", "ComplaintNumber", "description", "Priority", "label", "secondary", "mt", "activityDetails", "flexWrap", "Category", "Date", "addSuffix", "_c3", "Dashboard", "_s3", "stats", "setStats", "activities", "setActivities", "setLoading", "setError", "fetchDashboardData", "statsResponse", "activitiesResponse", "Promise", "all", "get", "data", "err", "console", "statCards", "totalComplaints", "resolvedComplaints", "pendingComplaints", "highPriorityComplaints", "xs", "sm", "severity", "action", "onClick", "container", "spacing", "map", "card", "item", "md", "minHeight", "length", "ComplaintId", "textAlign", "py", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport {\r\n  Box,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  CircularProgress,\r\n  Alert,\r\n  useTheme,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Divider,\r\n  Paper,\r\n  Chip,\r\n  LinearProgress,\r\n  useMediaQuery,\r\n  Button,\r\n} from '@mui/material';\r\nimport {\r\n  Assignment as ComplaintsIcon,\r\n  CheckCircle as ResolvedIcon,\r\n  Pending as PendingIcon,\r\n  Error as HighPriorityIcon,\r\n  FiberManualRecord as StatusIcon,\r\n  Schedule as TimeIcon,\r\n  Speed as EfficiencyIcon,\r\n  Timeline as TrendIcon,\r\n  AccessTime as ResolutionIcon,\r\n} from '@mui/icons-material';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport axios from '../utils/axiosConfig';\r\nimport { format, formatDistanceToNow } from 'date-fns';\r\n\r\nconst StatCard = ({ title, value, icon, color, loading }) => {\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n  \r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ \r\n        type: \"spring\",\r\n        stiffness: 100,\r\n        damping: 15,\r\n        duration: 0.6 \r\n      }}\r\n      whileHover={{ \r\n        scale: 1.02,\r\n        transition: { duration: 0.2 }\r\n      }}\r\n      whileTap={{ scale: 0.98 }}\r\n    >\r\n      <Card\r\n        sx={{\r\n          height: '100%',\r\n          background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,\r\n          color: 'white',\r\n          position: 'relative',\r\n          overflow: 'hidden',\r\n          boxShadow: theme.shadows[loading ? 0 : 2],\r\n          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\r\n          '&:hover': {\r\n            boxShadow: theme.shadows[4],\r\n            transform: 'translateY(-4px)',\r\n          },\r\n        }}\r\n      >\r\n        <CardContent sx={{ \r\n          p: isMobile ? 2 : 3,\r\n          height: '100%',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          justifyContent: 'space-between'\r\n        }}>\r\n          <Box sx={{ \r\n            position: 'relative', \r\n            zIndex: 1,\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 2\r\n          }}>\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 120 }}\r\n            >\r\n              {React.cloneElement(icon, { \r\n                sx: { \r\n                  fontSize: isMobile ? 32 : 48,\r\n                  opacity: 0.9,\r\n                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))'\r\n                } \r\n              })}\r\n            </motion.div>\r\n            <Box>\r\n              <Typography \r\n                variant={isMobile ? \"h5\" : \"h4\"} \r\n                component=\"div\" \r\n                sx={{ \r\n                  fontWeight: 700,\r\n                  lineHeight: 1.2,\r\n                  mb: 0.5,\r\n                  textShadow: '0px 2px 4px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {loading ? (\r\n                  <motion.div\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    transition={{ duration: 0.5 }}\r\n                  >\r\n                    <CircularProgress size={isMobile ? 20 : 24} color=\"inherit\" />\r\n                  </motion.div>\r\n                ) : (\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.3, duration: 0.5 }}\r\n                  >\r\n                    {value}\r\n                  </motion.div>\r\n                )}\r\n              </Typography>\r\n              <Typography \r\n                variant={isMobile ? \"body2\" : \"body1\"}\r\n                sx={{ \r\n                  opacity: 0.9,\r\n                  fontWeight: 500,\r\n                  letterSpacing: '0.5px',\r\n                  textShadow: '0px 1px 2px rgba(0,0,0,0.2)'\r\n                }}\r\n              >\r\n                {title}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n          <Box\r\n            component={motion.div}\r\n            initial={{ opacity: 0, scale: 0.5 }}\r\n            animate={{ opacity: 0.15, scale: 2 }}\r\n            transition={{ delay: 0.4, duration: 0.8 }}\r\n            sx={{\r\n              position: 'absolute',\r\n              right: -20,\r\n              bottom: -20,\r\n              filter: 'blur(2px)'\r\n            }}\r\n          >\r\n            {React.cloneElement(icon, { \r\n              sx: { fontSize: isMobile ? 100 : 140 }\r\n            })}\r\n          </Box>\r\n        </CardContent>\r\n      </Card>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nconst ActivityItem = React.memo(({ activity, index }) => {\r\n  const theme = useTheme();\r\n\r\n  const getStatusColor = (status) => {\r\n    const statusColors = {\r\n      'New': theme.palette.info.main,\r\n      'Assigned': theme.palette.warning.main,\r\n      'In Progress': theme.palette.warning.dark,\r\n      'Resolved': theme.palette.success.main,\r\n      'Rejected': theme.palette.error.main,\r\n    };\r\n    return statusColors[status] || theme.palette.grey[500];\r\n  };\r\n\r\n  const getPriorityColor = (priority) => {\r\n    const priorityColors = {\r\n      'Low': theme.palette.success.main,\r\n      'Medium': theme.palette.warning.main,\r\n      'High': theme.palette.error.main,\r\n      'Critical': theme.palette.error.dark,\r\n    };\r\n    return priorityColors[priority] || theme.palette.grey[500];\r\n  };\r\n\r\n  // Use activityTimestamp if available, otherwise fall back to submissionDate\r\n  const displayTimestamp = activity.activityTimestamp || activity.submissionDate;\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, x: -20 }}\r\n      animate={{ opacity: 1, x: 0 }}\r\n      transition={{\r\n        delay: index * 0.1,\r\n        duration: 0.5,\r\n        ease: \"easeOut\"\r\n      }}\r\n    >\r\n      <ListItem\r\n        sx={{\r\n          bgcolor: 'background.paper',\r\n          borderRadius: 2,\r\n          mb: 1,\r\n          boxShadow: 1,\r\n          '&:hover': {\r\n            bgcolor: 'action.hover',\r\n            transform: 'translateX(4px)',\r\n            transition: 'all 0.2s ease',\r\n          },\r\n        }}\r\n      >\r\n        <ListItemIcon>\r\n          <StatusIcon sx={{ color: getStatusColor(activity.Status) }} />\r\n        </ListItemIcon>\r\n        <ListItemText\r\n          primary={\r\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>\r\n              <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, flex: 1 }}>\r\n                #{activity.ComplaintNumber} - {activity.description}\r\n              </Typography>\r\n              {activity.Priority && (\r\n                <Chip\r\n                  label={activity.Priority}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${getPriorityColor(activity.Priority)}15`,\r\n                    color: getPriorityColor(activity.Priority),\r\n                    fontWeight: 500,\r\n                    fontSize: '0.7rem'\r\n                  }}\r\n                />\r\n              )}\r\n            </Box>\r\n          }\r\n          secondary={\r\n            <Box sx={{ mt: 0.5 }}>\r\n              {activity.activityDetails && (\r\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 0.5 }}>\r\n                  {activity.activityDetails}\r\n                </Typography>\r\n              )}\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>\r\n                <Chip\r\n                  label={activity.Status}\r\n                  size=\"small\"\r\n                  sx={{\r\n                    bgcolor: `${getStatusColor(activity.Status)}15`,\r\n                    color: getStatusColor(activity.Status),\r\n                    fontWeight: 500\r\n                  }}\r\n                />\r\n                {activity.Category && (\r\n                  <Chip\r\n                    label={activity.Category}\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ fontSize: '0.7rem' }}\r\n                  />\r\n                )}\r\n                <Typography variant=\"caption\" color=\"text.secondary\">\r\n                  {formatDistanceToNow(new Date(displayTimestamp), { addSuffix: true })}\r\n                </Typography>\r\n              </Box>\r\n            </Box>\r\n          }\r\n        />\r\n      </ListItem>\r\n    </motion.div>\r\n  );\r\n});\r\n\r\nfunction Dashboard() {\r\n  const [stats, setStats] = useState(null);\r\n  const [activities, setActivities] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n\r\n  const fetchDashboardData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      const [statsResponse, activitiesResponse] = await Promise.all([\r\n        axios.get('/api/dashboard/stats'),\r\n        axios.get('/api/dashboard/recent-activities')\r\n      ]);\r\n      setStats(statsResponse.data);\r\n      setActivities(activitiesResponse.data);\r\n      setError(null);\r\n    } catch (err) {\r\n      console.error('Error fetching dashboard data:', err);\r\n      setError('Failed to load dashboard data. Please try again later.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n  }, [fetchDashboardData]);\r\n\r\n  const statCards = useMemo(() => [\r\n    {\r\n      title: 'Total Complaints',\r\n      value: stats?.totalComplaints || 0,\r\n      icon: <ComplaintsIcon />,\r\n      color: 'primary'\r\n    },\r\n    {\r\n      title: 'Resolved',\r\n      value: stats?.resolvedComplaints || 0,\r\n      icon: <ResolvedIcon />,\r\n      color: 'success'\r\n    },\r\n    {\r\n      title: 'Pending',\r\n      value: stats?.pendingComplaints || 0,\r\n      icon: <PendingIcon />,\r\n      color: 'warning'\r\n    },\r\n    {\r\n      title: 'High Priority',\r\n      value: stats?.highPriorityComplaints || 0,\r\n      icon: <HighPriorityIcon />,\r\n      color: 'error'\r\n    }\r\n  ], [stats]);\r\n\r\n  return (\r\n    <Box sx={{ p: { xs: 2, sm: 3 } }}>\r\n      <Typography variant=\"h4\" sx={{ mb: 4, fontWeight: 600 }}>\r\n        Dashboard\r\n      </Typography>\r\n\r\n      {error && (\r\n        <Alert \r\n          severity=\"error\" \r\n          sx={{ mb: 3 }}\r\n          action={\r\n            <motion.div whileHover={{ scale: 1.05 }}>\r\n              <Button color=\"inherit\" size=\"small\" onClick={fetchDashboardData}>\r\n                Retry\r\n              </Button>\r\n            </motion.div>\r\n          }\r\n        >\r\n          {error}\r\n        </Alert>\r\n      )}\r\n\r\n      <Grid container spacing={3}>\r\n        {statCards.map((card, index) => (\r\n          <Grid item xs={12} sm={6} md={3} key={card.title}>\r\n            <StatCard {...card} loading={loading} />\r\n          </Grid>\r\n        ))}\r\n\r\n        <Grid item xs={12}>\r\n          <Card\r\n            component={motion.div}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4 }}\r\n            sx={{ \r\n              overflow: 'visible',\r\n              height: '100%',\r\n              minHeight: 400\r\n            }}\r\n          >\r\n            <CardContent>\r\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\r\n                Recent Activities\r\n              </Typography>\r\n              {loading ? (\r\n                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>\r\n                  <CircularProgress />\r\n                </Box>\r\n              ) : activities.length > 0 ? (\r\n                <AnimatePresence>\r\n                  <List>\r\n                    {activities.map((activity, index) => (\r\n                      <ActivityItem \r\n                        key={activity.ComplaintId} \r\n                        activity={activity} \r\n                        index={index}\r\n                      />\r\n                    ))}\r\n                  </List>\r\n                </AnimatePresence>\r\n              ) : (\r\n                <Box \r\n                  sx={{ \r\n                    textAlign: 'center', \r\n                    py: 4,\r\n                    color: 'text.secondary'\r\n                  }}\r\n                >\r\n                  <Typography>No recent activities</Typography>\r\n                </Box>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default Dashboard; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,cAAc,EACdC,aAAa,EACbC,MAAM,QACD,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,YAAY,EAC3BC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,gBAAgB,EACzBC,iBAAiB,IAAIC,UAAU,EAC/BC,QAAQ,IAAIC,QAAQ,EACpBC,KAAK,IAAIC,cAAc,EACvBC,QAAQ,IAAIC,SAAS,EACrBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,IAAI;EAAEC,KAAK;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAMC,KAAK,GAAG3C,QAAQ,CAAC,CAAC;EACxB,MAAM4C,QAAQ,GAAGnC,aAAa,CAACkC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,oBACEX,OAAA,CAACN,MAAM,CAACkB,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MACVC,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAE,GAAG;MACdC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE;IACZ,CAAE;IACFC,UAAU,EAAE;MACVC,KAAK,EAAE,IAAI;MACXN,UAAU,EAAE;QAAEI,QAAQ,EAAE;MAAI;IAC9B,CAAE;IACFG,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAAAE,QAAA,eAE1BzB,OAAA,CAACxC,IAAI;MACHkE,EAAE,EAAE;QACFC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,2BAA2BpB,KAAK,CAACqB,OAAO,CAACxB,KAAK,CAAC,CAACyB,IAAI,QAAQtB,KAAK,CAACqB,OAAO,CAACxB,KAAK,CAAC,CAAC0B,IAAI,QAAQ;QACzG1B,KAAK,EAAE,OAAO;QACd2B,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE1B,KAAK,CAAC2B,OAAO,CAAC7B,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;QACzCW,UAAU,EAAE,uCAAuC;QACnD,SAAS,EAAE;UACTiB,SAAS,EAAE1B,KAAK,CAAC2B,OAAO,CAAC,CAAC,CAAC;UAC3BC,SAAS,EAAE;QACb;MACF,CAAE;MAAAX,QAAA,eAEFzB,OAAA,CAACvC,WAAW;QAACiE,EAAE,EAAE;UACfW,CAAC,EAAE5B,QAAQ,GAAG,CAAC,GAAG,CAAC;UACnBkB,MAAM,EAAE,MAAM;UACdW,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,cAAc,EAAE;QAClB,CAAE;QAAAf,QAAA,gBACAzB,OAAA,CAAC1C,GAAG;UAACoE,EAAE,EAAE;YACPM,QAAQ,EAAE,UAAU;YACpBS,MAAM,EAAE,CAAC;YACTH,OAAO,EAAE,MAAM;YACfI,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE;UACP,CAAE;UAAAlB,QAAA,gBACAzB,OAAA,CAACN,MAAM,CAACkB,GAAG;YACTC,OAAO,EAAE;cAAEU,KAAK,EAAE;YAAE,CAAE;YACtBP,OAAO,EAAE;cAAEO,KAAK,EAAE;YAAE,CAAE;YACtBN,UAAU,EAAE;cAAE2B,KAAK,EAAE,GAAG;cAAE1B,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAAAM,QAAA,eAE1DxE,KAAK,CAAC4F,YAAY,CAACzC,IAAI,EAAE;cACxBsB,EAAE,EAAE;gBACFoB,QAAQ,EAAErC,QAAQ,GAAG,EAAE,GAAG,EAAE;gBAC5BK,OAAO,EAAE,GAAG;gBACZiC,MAAM,EAAE;cACV;YACF,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACbnD,OAAA,CAAC1C,GAAG;YAAAmE,QAAA,gBACFzB,OAAA,CAACtC,UAAU;cACT0F,OAAO,EAAE3C,QAAQ,GAAG,IAAI,GAAG,IAAK;cAChC4C,SAAS,EAAC,KAAK;cACf3B,EAAE,EAAE;gBACF4B,UAAU,EAAE,GAAG;gBACfC,UAAU,EAAE,GAAG;gBACfC,EAAE,EAAE,GAAG;gBACPC,UAAU,EAAE;cACd,CAAE;cAAAhC,QAAA,EAEDnB,OAAO,gBACNN,OAAA,CAACN,MAAM,CAACkB,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE;gBAAE,CAAE;gBACxBE,OAAO,EAAE;kBAAEF,OAAO,EAAE;gBAAE,CAAE;gBACxBG,UAAU,EAAE;kBAAEI,QAAQ,EAAE;gBAAI,CAAE;gBAAAI,QAAA,eAE9BzB,OAAA,CAACrC,gBAAgB;kBAAC+F,IAAI,EAAEjD,QAAQ,GAAG,EAAE,GAAG,EAAG;kBAACJ,KAAK,EAAC;gBAAS;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,gBAEbnD,OAAA,CAACN,MAAM,CAACkB,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,UAAU,EAAE;kBAAE2B,KAAK,EAAE,GAAG;kBAAEvB,QAAQ,EAAE;gBAAI,CAAE;gBAAAI,QAAA,EAEzCtB;cAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eACbnD,OAAA,CAACtC,UAAU;cACT0F,OAAO,EAAE3C,QAAQ,GAAG,OAAO,GAAG,OAAQ;cACtCiB,EAAE,EAAE;gBACFZ,OAAO,EAAE,GAAG;gBACZwC,UAAU,EAAE,GAAG;gBACfK,aAAa,EAAE,OAAO;gBACtBF,UAAU,EAAE;cACd,CAAE;cAAAhC,QAAA,EAEDvB;YAAK;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnD,OAAA,CAAC1C,GAAG;UACF+F,SAAS,EAAE3D,MAAM,CAACkB,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,KAAK,EAAE;UAAI,CAAE;UACpCP,OAAO,EAAE;YAAEF,OAAO,EAAE,IAAI;YAAES,KAAK,EAAE;UAAE,CAAE;UACrCN,UAAU,EAAE;YAAE2B,KAAK,EAAE,GAAG;YAAEvB,QAAQ,EAAE;UAAI,CAAE;UAC1CK,EAAE,EAAE;YACFM,QAAQ,EAAE,UAAU;YACpB4B,KAAK,EAAE,CAAC,EAAE;YACVC,MAAM,EAAE,CAAC,EAAE;YACXd,MAAM,EAAE;UACV,CAAE;UAAAtB,QAAA,eAEDxE,KAAK,CAAC4F,YAAY,CAACzC,IAAI,EAAE;YACxBsB,EAAE,EAAE;cAAEoB,QAAQ,EAAErC,QAAQ,GAAG,GAAG,GAAG;YAAI;UACvC,CAAC;QAAC;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEjB,CAAC;AAAC5C,EAAA,CA5HIN,QAAQ;EAAA,QACEpC,QAAQ,EACLS,aAAa;AAAA;AAAAwF,EAAA,GAF1B7D,QAAQ;AA8Hd,MAAM8D,YAAY,gBAAAC,GAAA,cAAG/G,KAAK,CAACgH,IAAI,CAAAC,GAAA,GAAAF,GAAA,CAAC,CAAC;EAAEG,QAAQ;EAAEC;AAAM,CAAC,KAAK;EAAAJ,GAAA;EACvD,MAAMxD,KAAK,GAAG3C,QAAQ,CAAC,CAAC;EAExB,MAAMwG,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,YAAY,GAAG;MACnB,KAAK,EAAE/D,KAAK,CAACqB,OAAO,CAAC2C,IAAI,CAAC1C,IAAI;MAC9B,UAAU,EAAEtB,KAAK,CAACqB,OAAO,CAAC4C,OAAO,CAAC3C,IAAI;MACtC,aAAa,EAAEtB,KAAK,CAACqB,OAAO,CAAC4C,OAAO,CAAC1C,IAAI;MACzC,UAAU,EAAEvB,KAAK,CAACqB,OAAO,CAAC6C,OAAO,CAAC5C,IAAI;MACtC,UAAU,EAAEtB,KAAK,CAACqB,OAAO,CAAC8C,KAAK,CAAC7C;IAClC,CAAC;IACD,OAAOyC,YAAY,CAACD,MAAM,CAAC,IAAI9D,KAAK,CAACqB,OAAO,CAAC+C,IAAI,CAAC,GAAG,CAAC;EACxD,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,MAAMC,cAAc,GAAG;MACrB,KAAK,EAAEvE,KAAK,CAACqB,OAAO,CAAC6C,OAAO,CAAC5C,IAAI;MACjC,QAAQ,EAAEtB,KAAK,CAACqB,OAAO,CAAC4C,OAAO,CAAC3C,IAAI;MACpC,MAAM,EAAEtB,KAAK,CAACqB,OAAO,CAAC8C,KAAK,CAAC7C,IAAI;MAChC,UAAU,EAAEtB,KAAK,CAACqB,OAAO,CAAC8C,KAAK,CAAC5C;IAClC,CAAC;IACD,OAAOgD,cAAc,CAACD,QAAQ,CAAC,IAAItE,KAAK,CAACqB,OAAO,CAAC+C,IAAI,CAAC,GAAG,CAAC;EAC5D,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGb,QAAQ,CAACc,iBAAiB,IAAId,QAAQ,CAACe,cAAc;EAE9E,oBACElF,OAAA,CAACN,MAAM,CAACkB,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEqE,CAAC,EAAE,CAAC;IAAG,CAAE;IAChCnE,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEqE,CAAC,EAAE;IAAE,CAAE;IAC9BlE,UAAU,EAAE;MACV2B,KAAK,EAAEwB,KAAK,GAAG,GAAG;MAClB/C,QAAQ,EAAE,GAAG;MACb+D,IAAI,EAAE;IACR,CAAE;IAAA3D,QAAA,eAEFzB,OAAA,CAACjC,QAAQ;MACP2D,EAAE,EAAE;QACF2D,OAAO,EAAE,kBAAkB;QAC3BC,YAAY,EAAE,CAAC;QACf9B,EAAE,EAAE,CAAC;QACLtB,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE;UACTmD,OAAO,EAAE,cAAc;UACvBjD,SAAS,EAAE,iBAAiB;UAC5BnB,UAAU,EAAE;QACd;MACF,CAAE;MAAAQ,QAAA,gBAEFzB,OAAA,CAAC/B,YAAY;QAAAwD,QAAA,eACXzB,OAAA,CAACf,UAAU;UAACyC,EAAE,EAAE;YAAErB,KAAK,EAAEgE,cAAc,CAACF,QAAQ,CAACoB,MAAM;UAAE;QAAE;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACfnD,OAAA,CAAChC,YAAY;QACXwH,OAAO,eACLxF,OAAA,CAAC1C,GAAG;UAACoE,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEI,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE,CAAC;YAAEa,EAAE,EAAE;UAAI,CAAE;UAAA/B,QAAA,gBAClEzB,OAAA,CAACtC,UAAU;YAAC0F,OAAO,EAAC,WAAW;YAAC1B,EAAE,EAAE;cAAE4B,UAAU,EAAE,GAAG;cAAEmC,IAAI,EAAE;YAAE,CAAE;YAAAhE,QAAA,GAAC,GAC/D,EAAC0C,QAAQ,CAACuB,eAAe,EAAC,KAAG,EAACvB,QAAQ,CAACwB,WAAW;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EACZgB,QAAQ,CAACyB,QAAQ,iBAChB5F,OAAA,CAAC5B,IAAI;YACHyH,KAAK,EAAE1B,QAAQ,CAACyB,QAAS;YACzBlC,IAAI,EAAC,OAAO;YACZhC,EAAE,EAAE;cACF2D,OAAO,EAAE,GAAGR,gBAAgB,CAACV,QAAQ,CAACyB,QAAQ,CAAC,IAAI;cACnDvF,KAAK,EAAEwE,gBAAgB,CAACV,QAAQ,CAACyB,QAAQ,CAAC;cAC1CtC,UAAU,EAAE,GAAG;cACfR,QAAQ,EAAE;YACZ;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACD2C,SAAS,eACP9F,OAAA,CAAC1C,GAAG;UAACoE,EAAE,EAAE;YAAEqE,EAAE,EAAE;UAAI,CAAE;UAAAtE,QAAA,GAClB0C,QAAQ,CAAC6B,eAAe,iBACvBhG,OAAA,CAACtC,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAAC/C,KAAK,EAAC,gBAAgB;YAACqB,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAI,CAAE;YAAA/B,QAAA,EAChE0C,QAAQ,CAAC6B;UAAe;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACb,eACDnD,OAAA,CAAC1C,GAAG;YAACoE,EAAE,EAAE;cAAEY,OAAO,EAAE,MAAM;cAAEI,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE,CAAC;cAAEsD,QAAQ,EAAE;YAAO,CAAE;YAAAxE,QAAA,gBAC3EzB,OAAA,CAAC5B,IAAI;cACHyH,KAAK,EAAE1B,QAAQ,CAACoB,MAAO;cACvB7B,IAAI,EAAC,OAAO;cACZhC,EAAE,EAAE;gBACF2D,OAAO,EAAE,GAAGhB,cAAc,CAACF,QAAQ,CAACoB,MAAM,CAAC,IAAI;gBAC/ClF,KAAK,EAAEgE,cAAc,CAACF,QAAQ,CAACoB,MAAM,CAAC;gBACtCjC,UAAU,EAAE;cACd;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACDgB,QAAQ,CAAC+B,QAAQ,iBAChBlG,OAAA,CAAC5B,IAAI;cACHyH,KAAK,EAAE1B,QAAQ,CAAC+B,QAAS;cACzBxC,IAAI,EAAC,OAAO;cACZN,OAAO,EAAC,UAAU;cAClB1B,EAAE,EAAE;gBAAEoB,QAAQ,EAAE;cAAS;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACF,eACDnD,OAAA,CAACtC,UAAU;cAAC0F,OAAO,EAAC,SAAS;cAAC/C,KAAK,EAAC,gBAAgB;cAAAoB,QAAA,EACjD3B,mBAAmB,CAAC,IAAIqG,IAAI,CAACnB,gBAAgB,CAAC,EAAE;gBAAEoB,SAAS,EAAE;cAAK,CAAC;YAAC;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEjB,CAAC;EAAA,QA3GetF,QAAQ;AAAA,EA2GvB,CAAC;EAAA,QA3GcA,QAAQ;AAAA,EA2GtB;AAACwI,GAAA,GA5GGtC,YAAY;AA8GlB,SAASuC,SAASA,CAAA,EAAG;EAAAC,GAAA;EACnB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvJ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwJ,UAAU,EAAEC,aAAa,CAAC,GAAGzJ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoD,OAAO,EAAEsG,UAAU,CAAC,GAAG1J,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyH,KAAK,EAAEkC,QAAQ,CAAC,GAAG3J,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAMsD,KAAK,GAAG3C,QAAQ,CAAC,CAAC;EACxB,MAAM4C,QAAQ,GAAGnC,aAAa,CAACkC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,MAAMmG,kBAAkB,GAAG1J,WAAW,CAAC,YAAY;IACjD,IAAI;MACFwJ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACG,aAAa,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5DtH,KAAK,CAACuH,GAAG,CAAC,sBAAsB,CAAC,EACjCvH,KAAK,CAACuH,GAAG,CAAC,kCAAkC,CAAC,CAC9C,CAAC;MACFV,QAAQ,CAACM,aAAa,CAACK,IAAI,CAAC;MAC5BT,aAAa,CAACK,kBAAkB,CAACI,IAAI,CAAC;MACtCP,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZC,OAAO,CAAC3C,KAAK,CAAC,gCAAgC,EAAE0C,GAAG,CAAC;MACpDR,QAAQ,CAAC,wDAAwD,CAAC;IACpE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAENzJ,SAAS,CAAC,MAAM;IACd2J,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,MAAMS,SAAS,GAAGlK,OAAO,CAAC,MAAM,CAC9B;IACE6C,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,CAAAqG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,eAAe,KAAI,CAAC;IAClCpH,IAAI,eAAEJ,OAAA,CAACvB,cAAc;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxB9C,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CAAAqG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,kBAAkB,KAAI,CAAC;IACrCrH,IAAI,eAAEJ,OAAA,CAACrB,YAAY;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtB9C,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAAqG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,iBAAiB,KAAI,CAAC;IACpCtH,IAAI,eAAEJ,OAAA,CAACnB,WAAW;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrB9C,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,CAAAqG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmB,sBAAsB,KAAI,CAAC;IACzCvH,IAAI,eAAEJ,OAAA,CAACjB,gBAAgB;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1B9C,KAAK,EAAE;EACT,CAAC,CACF,EAAE,CAACmG,KAAK,CAAC,CAAC;EAEX,oBACExG,OAAA,CAAC1C,GAAG;IAACoE,EAAE,EAAE;MAAEW,CAAC,EAAE;QAAEuF,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAAE,CAAE;IAAApG,QAAA,gBAC/BzB,OAAA,CAACtC,UAAU;MAAC0F,OAAO,EAAC,IAAI;MAAC1B,EAAE,EAAE;QAAE8B,EAAE,EAAE,CAAC;QAAEF,UAAU,EAAE;MAAI,CAAE;MAAA7B,QAAA,EAAC;IAEzD;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZwB,KAAK,iBACJ3E,OAAA,CAACpC,KAAK;MACJkK,QAAQ,EAAC,OAAO;MAChBpG,EAAE,EAAE;QAAE8B,EAAE,EAAE;MAAE,CAAE;MACduE,MAAM,eACJ/H,OAAA,CAACN,MAAM,CAACkB,GAAG;QAACU,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAAAE,QAAA,eACtCzB,OAAA,CAACzB,MAAM;UAAC8B,KAAK,EAAC,SAAS;UAACqD,IAAI,EAAC,OAAO;UAACsE,OAAO,EAAElB,kBAAmB;UAAArF,QAAA,EAAC;QAElE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb;MAAA1B,QAAA,EAEAkD;IAAK;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAEDnD,OAAA,CAACzC,IAAI;MAAC0K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAzG,QAAA,GACxB8F,SAAS,CAACY,GAAG,CAAC,CAACC,IAAI,EAAEhE,KAAK,kBACzBpE,OAAA,CAACzC,IAAI;QAAC8K,IAAI;QAACT,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACS,EAAE,EAAE,CAAE;QAAA7G,QAAA,eAC9BzB,OAAA,CAACC,QAAQ;UAAA,GAAKmI,IAAI;UAAE9H,OAAO,EAAEA;QAAQ;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GADJiF,IAAI,CAAClI,KAAK;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE1C,CACP,CAAC,eAEFnD,OAAA,CAACzC,IAAI;QAAC8K,IAAI;QAACT,EAAE,EAAE,EAAG;QAAAnG,QAAA,eAChBzB,OAAA,CAACxC,IAAI;UACH6F,SAAS,EAAE3D,MAAM,CAACkB,GAAI;UACtBC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAE2B,KAAK,EAAE;UAAI,CAAE;UAC3BlB,EAAE,EAAE;YACFO,QAAQ,EAAE,SAAS;YACnBN,MAAM,EAAE,MAAM;YACd4G,SAAS,EAAE;UACb,CAAE;UAAA9G,QAAA,eAEFzB,OAAA,CAACvC,WAAW;YAAAgE,QAAA,gBACVzB,OAAA,CAACtC,UAAU;cAAC0F,OAAO,EAAC,IAAI;cAAC1B,EAAE,EAAE;gBAAE8B,EAAE,EAAE,CAAC;gBAAEF,UAAU,EAAE;cAAI,CAAE;cAAA7B,QAAA,EAAC;YAEzD;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ7C,OAAO,gBACNN,OAAA,CAAC1C,GAAG;cAACoE,EAAE,EAAE;gBAAEY,OAAO,EAAE,MAAM;gBAAEE,cAAc,EAAE,QAAQ;gBAAEH,CAAC,EAAE;cAAE,CAAE;cAAAZ,QAAA,eAC3DzB,OAAA,CAACrC,gBAAgB;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,GACJuD,UAAU,CAAC8B,MAAM,GAAG,CAAC,gBACvBxI,OAAA,CAACL,eAAe;cAAA8B,QAAA,eACdzB,OAAA,CAAClC,IAAI;gBAAA2D,QAAA,EACFiF,UAAU,CAACyB,GAAG,CAAC,CAAChE,QAAQ,EAAEC,KAAK,kBAC9BpE,OAAA,CAAC+D,YAAY;kBAEXI,QAAQ,EAAEA,QAAS;kBACnBC,KAAK,EAAEA;gBAAM,GAFRD,QAAQ,CAACsE,WAAW;kBAAAzF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAG1B,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,gBAElBnD,OAAA,CAAC1C,GAAG;cACFoE,EAAE,EAAE;gBACFgH,SAAS,EAAE,QAAQ;gBACnBC,EAAE,EAAE,CAAC;gBACLtI,KAAK,EAAE;cACT,CAAE;cAAAoB,QAAA,eAEFzB,OAAA,CAACtC,UAAU;gBAAA+D,QAAA,EAAC;cAAoB;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACoD,GAAA,CAvIQD,SAAS;EAAA,QAKFzI,QAAQ,EACLS,aAAa;AAAA;AAAAsK,GAAA,GANvBtC,SAAS;AAyIlB,eAAeA,SAAS;AAAC,IAAAxC,EAAA,EAAAI,GAAA,EAAAmC,GAAA,EAAAuC,GAAA;AAAAC,YAAA,CAAA/E,EAAA;AAAA+E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}