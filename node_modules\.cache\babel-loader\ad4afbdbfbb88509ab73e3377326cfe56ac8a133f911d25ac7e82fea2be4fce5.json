{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { color } from '../../../value/types/color/index.mjs';\nimport { filter } from '../../../value/types/complex/filter.mjs';\nimport { numberValueTypes } from './number.mjs';\n\n/**\n * A map of default value types for common values\n */\nconst defaultValueTypes = _objectSpread(_objectSpread({}, numberValueTypes), {}, {\n  // Color props\n  color,\n  backgroundColor: color,\n  outlineColor: color,\n  fill: color,\n  stroke: color,\n  // Border props\n  borderColor: color,\n  borderTopColor: color,\n  borderRightColor: color,\n  borderBottomColor: color,\n  borderLeftColor: color,\n  filter,\n  WebkitFilter: filter\n});\n/**\n * Gets the default ValueType for the provided value key\n */\nconst getDefaultValueType = key => defaultValueTypes[key];\nexport { defaultValueTypes, getDefaultValueType };", "map": {"version": 3, "names": ["color", "filter", "numberValueTypes", "defaultValueTypes", "_objectSpread", "backgroundColor", "outlineColor", "fill", "stroke", "borderColor", "borderTopColor", "borderRightColor", "borderBottomColor", "borderLeftColor", "WebkitFilter", "getDefaultValueType", "key"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/framer-motion/dist/es/render/dom/value-types/defaults.mjs"], "sourcesContent": ["import { color } from '../../../value/types/color/index.mjs';\nimport { filter } from '../../../value/types/complex/filter.mjs';\nimport { numberValueTypes } from './number.mjs';\n\n/**\n * A map of default value types for common values\n */\nconst defaultValueTypes = {\n    ...numberValueTypes,\n    // Color props\n    color,\n    backgroundColor: color,\n    outlineColor: color,\n    fill: color,\n    stroke: color,\n    // Border props\n    borderColor: color,\n    borderTopColor: color,\n    borderRightColor: color,\n    borderBottomColor: color,\n    borderLeftColor: color,\n    filter,\n    WebkitFilter: filter,\n};\n/**\n * Gets the default ValueType for the provided value key\n */\nconst getDefaultValueType = (key) => defaultValueTypes[key];\n\nexport { defaultValueTypes, getDefaultValueType };\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,sCAAsC;AAC5D,SAASC,MAAM,QAAQ,yCAAyC;AAChE,SAASC,gBAAgB,QAAQ,cAAc;;AAE/C;AACA;AACA;AACA,MAAMC,iBAAiB,GAAAC,aAAA,CAAAA,aAAA,KAChBF,gBAAgB;EACnB;EACAF,KAAK;EACLK,eAAe,EAAEL,KAAK;EACtBM,YAAY,EAAEN,KAAK;EACnBO,IAAI,EAAEP,KAAK;EACXQ,MAAM,EAAER,KAAK;EACb;EACAS,WAAW,EAAET,KAAK;EAClBU,cAAc,EAAEV,KAAK;EACrBW,gBAAgB,EAAEX,KAAK;EACvBY,iBAAiB,EAAEZ,KAAK;EACxBa,eAAe,EAAEb,KAAK;EACtBC,MAAM;EACNa,YAAY,EAAEb;AAAM,EACvB;AACD;AACA;AACA;AACA,MAAMc,mBAAmB,GAAIC,GAAG,IAAKb,iBAAiB,CAACa,GAAG,CAAC;AAE3D,SAASb,iBAAiB,EAAEY,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}