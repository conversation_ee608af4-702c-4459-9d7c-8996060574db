{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getPopperUtilityClass(slot) {\n  return generateUtilityClass('MuiPopper', slot);\n}\nexport const popperClasses = generateUtilityClasses('<PERSON>iPopper', ['root']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getPopperUtilityClass", "slot", "popperClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Popper/popperClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getPopperUtilityClass(slot) {\n  return generateUtilityClass('MuiPopper', slot);\n}\nexport const popperClasses = generateUtilityClasses('<PERSON>iPopper', ['root']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOH,oBAAoB,CAAC,WAAW,EAAEG,IAAI,CAAC;AAChD;AACA,OAAO,MAAMC,aAAa,GAAGH,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}