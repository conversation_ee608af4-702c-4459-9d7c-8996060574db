@echo off
setlocal enabledelayedexpansion
color 0A
title Internal Complaints Portal - Startup Manager

echo.
echo ===============================================
echo    INTERNAL COMPLAINTS PORTAL - STARTUP
echo ===============================================
echo.

REM Function to display colored text
echo [92m[INFO][0m Initializing startup sequence...
echo.

REM Kill any existing processes on ports 1976 and 3000
echo [93m[CLEANUP][0m Checking for existing processes...

REM Kill processes on port 1976 (Backend)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :1976') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating process on port 1976 (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)

REM Kill processes on port 3000 (Frontend)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating process on port 3000 (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)

REM Kill any existing node processes related to our project
echo [93m[CLEANUP][0m Cleaning up any remaining Node.js processes...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo [92m[SUCCESS][0m Cleanup completed!
echo.

REM Check if npm is available
echo [94m[CHECK][0m Verifying Node.js and npm installation...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [91m[ERROR][0m npm is not installed or not in PATH!
    echo Please install Node.js and npm first.
    pause
    exit /b 1
)
echo [92m[SUCCESS][0m Node.js and npm are available!
echo.

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo [93m[INSTALL][0m Installing dependencies...
    npm install
    if errorlevel 1 (
        echo [91m[ERROR][0m Failed to install dependencies!
        pause
        exit /b 1
    )
    echo [92m[SUCCESS][0m Dependencies installed!
    echo.
)

REM Start the backend server
echo [94m[BACKEND][0m Starting backend server on port 1976...
start "Internal Complaints Portal - Backend Server" cmd /k "color 0E && title Backend Server - Port 1976 && echo [92mStarting Backend Server...[0m && npm run server"

REM Wait for backend to initialize
echo [93m[WAIT][0m Waiting for backend server to initialize...
timeout /t 8 /nobreak >nul

REM Check if backend is running
echo [94m[CHECK][0m Verifying backend server status...
netstat -an | findstr :1976 >nul
if errorlevel 1 (
    echo [91m[WARNING][0m Backend server may not be running on port 1976
) else (
    echo [92m[SUCCESS][0m Backend server is running on port 1976!
)
echo.

REM Start the frontend development server
echo [94m[FRONTEND][0m Starting frontend development server on port 3000...
start "Internal Complaints Portal - Frontend Server" cmd /k "color 0B && title Frontend Server - Port 3000 && echo [92mStarting Frontend Server...[0m && npm start"

REM Wait for frontend to initialize
echo [93m[WAIT][0m Waiting for frontend server to initialize...
timeout /t 10 /nobreak >nul

echo.
echo ===============================================
echo           STARTUP SEQUENCE COMPLETED
echo ===============================================
echo.
echo [92m[SUCCESS][0m Both servers have been started!
echo.
echo [96m[BACKEND][0m  Backend Server:
echo   - Local:    http://localhost:1976
echo   - Network:  http://***************:1976
echo.
echo [96m[FRONTEND][0m Frontend Server:
echo   - Local:    http://localhost:3000
echo   - Network:  http://***************:3000
echo.
echo [96m[MOBILE][0m   Mobile Test Page:
echo   - URL:      http://***************:3000/mobile-test.html
echo.
echo [93m[INFO][0m Default Login Credentials:
echo   - Employee Code: EMP-M
echo   - Password:      qwerty
echo.
echo [95m[ADMIN][0m Admin Users Available:
echo   - EMP-M (ADMINISTRATOR- M)
echo   - EK0081 (YESHWANTH J)
echo   - E0001 (JAIKUMAR)
echo   - E0002 (KEDARNATH)
echo.
echo ===============================================
echo.
echo [94m[INSTRUCTIONS][0m
echo 1. Wait for both servers to fully initialize
echo 2. Open your browser and navigate to http://localhost:3000
echo 3. For mobile testing, use: http://***************:3000
echo 4. Use the mobile test page for debugging mobile issues
echo.
echo [93m[NOTE][0m Keep this window open to monitor server status
echo Press Ctrl+C to stop all servers
echo.

REM Keep the window open and monitor
:monitor
timeout /t 30 /nobreak >nul
echo [90m[%time%][0m Servers are running... (Press Ctrl+C to stop)
goto monitor
