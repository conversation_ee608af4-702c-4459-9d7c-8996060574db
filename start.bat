@echo off
setlocal enabledelayedexpansion
color 0A
title Internal Complaints Portal - Startup Manager

echo.
echo ===============================================
echo    INTERNAL COMPLAINTS PORTAL - STARTUP
echo ===============================================
echo.

REM Function to display colored text
echo [92m[INFO][0m Initializing startup sequence...
echo.

REM Kill any existing processes on ports 1976 and 3000
echo [93m[CLEANUP][0m Checking for existing processes...

REM Kill processes on port 1976 (Backend)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :1976') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating process on port 1976 (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)

REM Kill processes on ports 3000, 3001, 3002 (Frontend development servers)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating process on port 3000 (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3001') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating process on port 3001 (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3002') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating process on port 3002 (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)

REM Kill any existing node processes related to our project
echo [93m[CLEANUP][0m Cleaning up any remaining Node.js processes...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo [92m[SUCCESS][0m Cleanup completed!
echo.

REM Check if npm is available
echo [94m[CHECK][0m Verifying Node.js and npm installation...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [91m[ERROR][0m npm is not installed or not in PATH!
    echo Please install Node.js and npm first.
    echo [93m[INFO][0m Exiting in 5 seconds...
    timeout /t 5 /nobreak >nul
    exit /b 1
)
echo [92m[SUCCESS][0m Node.js and npm are available!
echo.

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo [93m[INSTALL][0m Installing dependencies...
    npm install
    if errorlevel 1 (
        echo [91m[ERROR][0m Failed to install dependencies!
        echo [93m[INFO][0m Exiting in 5 seconds...
        timeout /t 5 /nobreak >nul
        exit /b 1
    )
    echo [92m[SUCCESS][0m Dependencies installed!
    echo.
)

REM Build the React app for production
echo [94m[BUILD][0m Building React app for production...
npm run build
if errorlevel 1 (
    echo [91m[ERROR][0m Failed to build React app!
    echo [93m[INFO][0m Exiting in 5 seconds...
    timeout /t 5 /nobreak >nul
    exit /b 1
)
echo [92m[SUCCESS][0m React app built successfully!
echo.

REM Start the backend server (which now serves the built React app)
echo [94m[BACKEND][0m Starting backend server with built React app on port 1976...
start "Internal Complaints Portal - Backend Server" cmd /k "color 0E && title Backend Server - Port 1976 && echo [92mStarting Backend Server with React App...[0m && node server.js"

REM Wait for backend to initialize
echo [93m[WAIT][0m Waiting for backend server to initialize...
timeout /t 8 /nobreak >nul

REM Check if backend is running
echo [94m[CHECK][0m Verifying backend server status...
netstat -an | findstr :1976 >nul
if errorlevel 1 (
    echo [91m[WARNING][0m Backend server may not be running on port 1976
    echo [93m[INFO][0m Waiting longer for backend to start...
    timeout /t 5 /nobreak >nul
) else (
    echo [92m[SUCCESS][0m Backend server is running on port 1976!
)
echo.

REM Test if the application is actually responding
echo [94m[TEST][0m Testing application response...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:1976' -TimeoutSec 10 -UseBasicParsing; if ($response.StatusCode -eq 200) { Write-Host '[SUCCESS] Application is responding!' } else { Write-Host '[WARNING] Application returned status:' $response.StatusCode } } catch { Write-Host '[ERROR] Application not responding yet, waiting...' }"

REM Wait a bit more to ensure everything is loaded
timeout /t 3 /nobreak >nul

REM Automatically open browser
echo [96m[BROWSER][0m Opening browser automatically...
echo [94m[INFO][0m Attempting to open http://localhost:1976...

REM Simple and reliable browser opening (original working method)
start "" "http://localhost:1976"

echo [92m[SUCCESS][0m Browser opening command executed!

echo.
echo ===============================================
echo           STARTUP SEQUENCE COMPLETED
echo ===============================================
echo.
echo [93m⚠️  IMPORTANT: IF BROWSER DIDN'T OPEN ⚠️[0m
echo [96m👉 MANUALLY OPEN YOUR BROWSER AND GO TO:[0m
echo [97m   http://localhost:1976[0m
echo [96m👤 LOGIN WITH: EMP-M / qwerty[0m
echo.
echo [92m[SUCCESS][0m Application server has been started!
echo.
echo [96m[APPLICATION][0m Internal Complaints Portal:
echo   - Main URL:     http://localhost:1976
echo   - Backend API:  http://localhost:1976/api
echo   - Production Build: ✅ Optimized React app served by backend
echo.
echo [95m[BROWSER][0m Browser Status:
echo   - Application:  ✅ Multiple opening methods executed
echo   - Target URL:   http://localhost:1976
echo   - Backup methods: PowerShell + rundll32 also attempted
echo.
echo [93m[MOBILE][0m Mobile Access:
echo   - Use the same URL: http://localhost:1976
echo   - Or network IP: http://***************:1976
echo.
echo [93m[INFO][0m Default Login Credentials:
echo   - Employee Code: EMP-M
echo   - Password:      qwerty
echo.
echo [95m[ADMIN][0m Admin Users Available:
echo   - EMP-M (ADMINISTRATOR- M)
echo   - EK0081 (YESHWANTH J)
echo   - E0001 (JAIKUMAR)
echo   - E0002 (KEDARNATH)
echo.
echo ===============================================
echo.
echo [94m[INSTRUCTIONS][0m
echo 1. Browser should open automatically to http://localhost:1976
echo 2. ⚠️  IF BROWSER DOESN'T OPEN: Manually open your browser and go to:
echo    👉 http://localhost:1976
echo 3. Use the same URL on mobile devices: http://localhost:1976
echo 4. For mobile network access, use: http://***************:1976
echo 5. Production build: Faster, more stable than development server
echo.
echo [93m[MANUAL BROWSER][0m If automatic opening failed:
echo   1. Open any browser (Chrome, Firefox, Edge, etc.)
echo   2. Type in address bar: http://localhost:1976
echo   3. Press Enter
echo   4. Login with: EMP-M / qwerty
echo.
echo [93m[NOTE][0m Keep this window open to monitor server status
echo Press Ctrl+C to stop the server
echo.

REM Keep the window open and monitor
:monitor
timeout /t 30 /nobreak >nul
echo [90m[%time%][0m Server is running... (Press Ctrl+C to stop)
goto monitor
