@echo off
setlocal enabledelayedexpansion
color 0A
title Internal Complaints Portal - Startup

echo.
echo ===============================================
echo    INTERNAL COMPLAINTS PORTAL - STARTUP
echo ===============================================
echo.

echo [92m[INFO][0m Starting up...
echo.

REM Kill existing processes on port 1976
echo [93m[CLEANUP][0m Killing existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :1976') do (
    if not "%%a"=="0" (
        echo [91m[KILL][0m Terminating PID: %%a
        taskkill /f /pid %%a >nul 2>&1
    )
)

REM Kill node processes
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo [92m[SUCCESS][0m Cleanup completed!
echo.

REM Build if needed
if not exist "build" (
    echo [94m[BUILD][0m Building React app...
    npm run build
    if errorlevel 1 (
        echo [91m[ERROR][0m Build failed!
        timeout /t 5 /nobreak >nul
        exit /b 1
    )
    echo [92m[SUCCESS][0m Build completed!
    echo.
)

REM Start server in new window
echo [94m[SERVER][0m Starting server...
start "Server" cmd /k "title Server - Port 1976 && node server.js"

REM Wait for server
echo [93m[WAIT][0m Waiting for server to start...
timeout /t 8 /nobreak >nul

REM Check if server is running
netstat -an | findstr :1976 >nul
if errorlevel 1 (
    echo [91m[ERROR][0m Server not running!
    timeout /t 5 /nobreak >nul
    exit /b 1
) else (
    echo [92m[SUCCESS][0m Server is running!
)

REM Open browser
echo [96m[BROWSER][0m Opening browser...
cmd /c start "" "http://localhost:1976"

echo.
echo [92m[SUCCESS][0m Startup completed!
echo [96m[INFO][0m Application: http://localhost:1976
echo [96m[LOGIN][0m Credentials: EMP-M / qwerty
echo.
echo [93m[NOTE][0m Server window opened separately
echo Press any key to exit this window...
pause >nul
