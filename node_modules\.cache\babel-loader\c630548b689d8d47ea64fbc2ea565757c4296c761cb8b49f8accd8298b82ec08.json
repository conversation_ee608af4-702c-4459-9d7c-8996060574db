{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getOptionUtilityClass(slot) {\n  return generateUtilityClass('MuiOption', slot);\n}\nexport const optionClasses = generateUtilityClasses('MuiOption', ['root', 'disabled', 'selected', 'highlighted']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getOptionUtilityClass", "slot", "optionClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/INTERNAL COMPLAINTS/node_modules/@mui/base/Option/optionClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getOptionUtilityClass(slot) {\n  return generateUtilityClass('MuiOption', slot);\n}\nexport const optionClasses = generateUtilityClasses('MuiOption', ['root', 'disabled', 'selected', 'highlighted']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOH,oBAAoB,CAAC,WAAW,EAAEG,IAAI,CAAC;AAChD;AACA,OAAO,MAAMC,aAAa,GAAGH,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}